---
description: 
globs: 
alwaysApply: false
---
# 数据可视化开发指南

## ECharts使用规范

### 基础配置
项目使用ECharts 5.4.3作为主要图表库，配合以下扩展：
- **echarts-gl**: 3D图表支持
- **echarts-liquidfill**: 水球图支持

### 图表初始化模式
```javascript
import * as echarts from 'echarts'

export default {
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.chart.setOption(this.chartOptions)
      
      // 响应式处理
      window.addEventListener('resize', this.handleResize)
    },
    handleResize() {
      this.chart && this.chart.resize()
    }
  },
  beforeDestroy() {
    this.chart && this.chart.dispose()
    window.removeEventListener('resize', this.handleResize)
  }
}
```

### 数据更新
```javascript
// 使用watch监听数据变化
watch: {
  chartData: {
    handler(newData) {
      if (this.chart && newData) {
        this.chart.setOption(this.getChartOptions(newData))
      }
    },
    deep: true
  }
}
```

## DataView组件库

### 安装和使用
项目使用 `@jiaminghi/data-view` 提供大屏组件：
```javascript
import dataV from '@jiaminghi/data-view'
Vue.use(dataV)
```

### 常用组件
- `<dv-border-box>`: 边框装饰
- `<dv-decoration>`: 装饰元素
- `<dv-scroll-ranking-board>`: 排名列表
- `<dv-digital-flop>`: 数字翻牌器

## 3D可视化

### Photo Sphere Viewer
用于全景图展示，配置在 [src/components/Panoramic/](mdc:src/components/Panoramic) 中：
```javascript
import { Viewer } from '@photo-sphere-viewer/core'
import { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin'
import { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin'
```

### 地图集成
- **GeoServer**: 地图瓦片服务
- **@turf/turf**: 地理空间分析
- 代理配置在 [vue.config.js](mdc:vue.config.js) 中

## 性能优化建议
1. **按需加载**: 只引入需要的ECharts模块
2. **内存管理**: 组件销毁时释放图表实例
3. **数据处理**: 大数据集使用虚拟滚动或分页
4. **动画控制**: 合理使用动画，避免性能问题


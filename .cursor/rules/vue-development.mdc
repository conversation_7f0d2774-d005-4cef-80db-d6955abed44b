---
description: 
globs: 
alwaysApply: true
---
# Vue开发规范

## 组件开发规范

### 组件结构
所有Vue组件应遵循以下结构顺序：
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
  beforeDestroy() {}
}
</script>

<style lang="less" scoped>
/* 组件样式 */
</style>
```

### 组件命名规范
- **文件名**: 使用PascalCase，如 `DataChart.vue`
- **组件名**: 与文件名保持一致
- **引用时**: 在template中使用kebab-case，如 `<data-chart>`

### Props定义
```javascript
props: {
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  },
  options: {
    type: Object,
    default: () => ({})
  }
}
```

## 状态管理

### Vuex使用
- 使用 [src/store/](mdc:src/store) 目录管理全局状态
- 模块化管理不同功能的状态
- 异步操作使用actions，同步操作使用mutations

### 组件间通信
1. **父子组件**: props + emit
2. **兄弟组件**: 事件总线或Vuex
3. **跨级组件**: provide/inject 或 Vuex

## 生命周期使用
- **created**: 初始化数据、绑定事件
- **mounted**: DOM操作、初始化第三方插件
- **beforeDestroy**: 清理定时器、解绑事件

## 常用工具
- **HTTP请求**: 使用 [src/api/](mdc:src/api) 中封装的axios实例
- **工具函数**: 存放在 [src/utils/](mdc:src/utils) 目录
- **Cookie操作**: 使用js-cookie库
- **加密**: 使用jsencrypt和crypto-js库


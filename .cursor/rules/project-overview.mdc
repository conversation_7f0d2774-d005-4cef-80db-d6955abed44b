---
description: 
globs: 
alwaysApply: true
---
# 南泥湾屏幕物联网项目概览

## 项目简介
这是一个基于Vue 2的智慧园区大屏展示项目，主要用于轨道物联网设备的实时监控和数据可视化展示。

## 技术栈
- **前端框架**: Vue 2.6.11 + Vuex + Vue Router
- **UI组件库**: Element UI 2.15.14 + View Design 4.7.0
- **数据可视化**: ECharts 5.4.3 + @jiaminghi/data-view 2.10.0
- **地图集成**: GeoServer + @turf/turf 6.5.0
- **构建工具**: Vue CLI 4.5.0
- **样式处理**: Less + postcss-px2rem（移动端适配）

## 项目结构
- [src/main.js](mdc:src/main.js) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/views/](mdc:src/views) - 页面组件目录
- [src/components/](mdc:src/components) - 通用组件目录
- [src/router/index.js](mdc:src/router/index.js) - 路由配置
- [src/store/](mdc:src/store) - Vuex状态管理
- [src/api/](mdc:src/api) - API接口封装
- [src/utils/](mdc:src/utils) - 工具函数
- [src/assets/](mdc:src/assets) - 静态资源
- [src/styles/](mdc:src/styles) - 样式文件

## 主要功能模块
1. **首页大屏** - [src/views/Home.vue](mdc:src/views/Home.vue)
2. **点位屏幕监控** - [src/views/pointScreen/](mdc:src/views/pointScreen)
3. **项目地图** - [src/views/ProjectMap/](mdc:src/views/ProjectMap)
4. **AI识别** - [src/components/ProjectAI/](mdc:src/components/ProjectAI)
5. **起重机监控** - [src/components/ProjectCrane/](mdc:src/components/ProjectCrane)
6. **视频监控** - [src/components/ProjectVideo/](mdc:src/components/ProjectVideo)
7. **全景展示** - [src/components/Panoramic/](mdc:src/components/Panoramic)

## 开发配置
- **开发端口**: 8091
- **API代理**: /api -> http://172.16.10.25:16005/api
- **地图服务**: /geoserver -> http://172.16.10.189:8080/geoserver
- **移动端适配**: remUnit = 16（1rem = 16px）


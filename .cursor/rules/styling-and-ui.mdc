---
description: 
globs: 
alwaysApply: true
---
# 样式与UI开发规范

## CSS预处理器
项目使用Less作为CSS预处理器，配置在 [vue.config.js](mdc:vue.config.js) 中。

### Less使用规范
```less
// 变量定义
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 混合器示例
.flex-center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 组件样式
.data-panel {
  .flex-center();
  background: @primary-color;
  
  &:hover {
    opacity: 0.8;
  }
}
```

## 移动端适配

### Rem适配方案
项目使用postcss-px2rem进行移动端适配：
- **基准值**: 16px (1rem = 16px)
- **配置**: 在 [vue.config.js](mdc:vue.config.js) 中设置remUnit: 16

### 适配使用方式
```less
// 开发时直接写px，构建时自动转换为rem
.container {
  width: 320px;  // 自动转换为 20rem
  height: 240px; // 自动转换为 15rem
  font-size: 16px; // 自动转换为 1rem
}

// 不需要转换的值使用大写PX
.fixed-size {
  border: 1PX solid #ccc; // 保持1px不变
}
```

## UI组件库

### Element UI使用
项目主要使用Element UI 2.15.14：
```javascript
// 按需引入推荐组件
import {
  Button,
  Input,
  Select,
  Table,
  Dialog,
  Message,
  Loading
} from 'element-ui'
```

### View Design集成
同时集成了View Design 4.7.0，主要用于特定组件。

## 大屏UI规范

### 布局原则
1. **全屏显示**: 充分利用屏幕空间
2. **网格布局**: 使用CSS Grid或Flexbox
3. **响应式**: 适配不同分辨率屏幕
4. **数据密度**: 平衡信息量与可读性

### 色彩搭配
```less
// 大屏主题色彩
@screen-bg: #0a1327;
@screen-primary: #00d4ff;
@screen-secondary: #1890ff;
@screen-success: #00ff88;
@screen-warning: #ffaa00;
@screen-danger: #ff4757;

// 渐变色
@gradient-blue: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@gradient-green: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
```

### 动画效果
```less
// 过渡动画
.fade-transition {
  transition: all 0.3s ease;
}

// 数字滚动动画
@keyframes countUp {
  0% { transform: translateY(100%); }
  100% { transform: translateY(0); }
}

.count-animation {
  animation: countUp 0.6s ease-out;
}
```

## 组件样式规范

### 作用域样式
推荐使用scoped样式避免样式污染：
```vue
<style lang="less" scoped>
.component-name {
  // 组件根元素样式
  
  .child-element {
    // 子元素样式
  }
}
</style>
```

### 全局样式
全局样式存放在 [src/styles/](mdc:src/styles) 目录：
- `common.less`: 通用样式
- `variables.less`: 变量定义
- `mixins.less`: 混合器

### 组件库主题定制
```less
// Element UI主题定制
@import '~element-ui/packages/theme-chalk/src/index';

// 覆盖默认变量
$--color-primary: #1890ff;
$--border-radius-base: 4px;
```


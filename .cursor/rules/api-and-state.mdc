---
description: 
globs: 
alwaysApply: true
---
# API接口与状态管理规范

## API接口规范

### HTTP请求配置
项目使用axios 0.24.0进行HTTP请求，配置参考：

```javascript
// src/api/request.js 示例结构
import axios from 'axios'

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(config => {
  // 添加token等认证信息
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)
```

### API接口组织
在 [src/api/](mdc:src/api) 目录按模块组织接口：

```javascript
// src/api/device.js 示例
import request from './request'

export const deviceAPI = {
  // 获取设备列表
  getDeviceList(params) {
    return request.get('/devices', { params })
  },
  
  // 获取设备详情
  getDeviceDetail(id) {
    return request.get(`/devices/${id}`)
  },
  
  // 更新设备状态
  updateDeviceStatus(id, status) {
    return request.put(`/devices/${id}/status`, { status })
  }
}
```

## 状态管理规范

### Vuex模块结构
在 [src/store/](mdc:src/store) 中按功能模块组织：

```javascript
// src/store/modules/device.js 示例
const state = {
  deviceList: [],
  currentDevice: null,
  loading: false
}

const mutations = {
  SET_DEVICE_LIST(state, list) {
    state.deviceList = list
  },
  SET_CURRENT_DEVICE(state, device) {
    state.currentDevice = device
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

const actions = {
  async fetchDeviceList({ commit }) {
    commit('SET_LOADING', true)
    try {
      const data = await deviceAPI.getDeviceList()
      commit('SET_DEVICE_LIST', data)
    } catch (error) {
      console.error('获取设备列表失败:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  onlineDevices: state => state.deviceList.filter(d => d.status === 'online'),
  deviceCount: state => state.deviceList.length
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### 组件中使用Vuex
```javascript
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  computed: {
    ...mapState('device', ['deviceList', 'loading']),
    ...mapGetters('device', ['onlineDevices', 'deviceCount'])
  },
  methods: {
    ...mapActions('device', ['fetchDeviceList']),
    
    async loadData() {
      await this.fetchDeviceList()
    }
  }
}
```

## 数据加密与安全
- **加密库**: 使用JSEncrypt进行RSA加密
- **哈希**: 使用CryptoJS进行数据哈希
- **Cookie**: 使用js-cookie安全存储用户信息

## 错误处理
- 统一在axios拦截器中处理HTTP错误
- 使用Element UI的Message组件显示错误信息
- 在actions中捕获异步操作错误


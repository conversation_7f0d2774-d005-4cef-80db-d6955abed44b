---
description: 
globs: 
alwaysApply: false
---
# Cursor Rules 导航

欢迎使用南泥湾屏幕物联网项目的开发规范！这里是所有开发规则和最佳实践的导航。

## 📚 规则文档索引

### 🏗️ [项目概览](mdc:project-overview.mdc)
- 项目简介和技术栈
- 项目结构说明
- 主要功能模块介绍
- 开发环境配置

### 🔧 [Vue开发规范](mdc:vue-development.mdc)
- Vue组件开发标准
- 组件命名和结构规范
- Vuex状态管理使用
- 组件间通信方式
- 生命周期最佳实践

### 📊 [数据可视化指南](mdc:data-visualization.mdc)
- ECharts图表开发规范
- DataView大屏组件使用
- 3D可视化和全景展示
- 地图集成方案
- 性能优化建议

### 🌐 [API与状态管理](mdc:api-and-state.mdc)
- HTTP请求配置和拦截器
- API接口组织结构
- Vuex模块化管理
- 数据加密和安全处理
- 错误处理机制

### 🎨 [样式与UI规范](mdc:styling-and-ui.mdc)
- Less预处理器使用
- 移动端Rem适配方案
- Element UI组件库规范
- 大屏UI设计原则
- 主题定制和动画效果

## 🚀 快速开始

### 新加入的开发者
1. 先阅读 [项目概览](mdc:project-overview.mdc) 了解整体架构
2. 查看 [Vue开发规范](mdc:vue-development.mdc) 掌握开发标准
3. 根据具体需求查阅对应的专项规范

### 常见开发场景
- **添加新页面**: 参考Vue开发规范中的组件结构
- **集成图表**: 查看数据可视化指南中的ECharts规范
- **调用API**: 参考API与状态管理中的接口规范
- **样式开发**: 遵循样式与UI规范中的Less使用方式

## 📁 核心文件快速链接

### 入口文件
- [src/main.js](mdc:src/main.js) - 应用入口
- [src/App.vue](mdc:src/App.vue) - 根组件
- [vue.config.js](mdc:vue.config.js) - 构建配置

### 路由和状态
- [src/router/index.js](mdc:src/router/index.js) - 路由配置
- [src/store/](mdc:src/store) - Vuex状态管理

### 主要页面
- [src/views/Home.vue](mdc:src/views/Home.vue) - 首页大屏
- [src/views/pointScreen/](mdc:src/views/pointScreen) - 点位监控

## ⚠️ 重要提醒
- 请严格按照规范进行开发，确保代码质量和一致性
- 新增功能时优先考虑现有组件的复用
- 定期查看规范更新，保持最佳实践同步


{"name": "smart-park", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@photo-sphere-viewer/autorotate-plugin": "^5.4.2", "@photo-sphere-viewer/core": "^5.4.2", "@photo-sphere-viewer/gallery-plugin": "^5.4.2", "@photo-sphere-viewer/markers-plugin": "^5.4.2", "@turf/turf": "^6.5.0", "@vue-office/pdf": "^2.0.10", "@vue/composition-api": "^1.7.2", "axios": "^0.24.0", "build": "^0.1.4", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "css-loader": "^6.5.1", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.14", "jquery": "^3.6.0", "js-cookie": "^3.0.5", "jsencrypt": "^3.2.1", "moment": "^2.29.1", "style-loader": "^3.3.1", "swiper": "^5.4.2", "v-viewer": "^1.6.4", "video.js": "^8.21.0", "view-design": "^4.7.0", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-bus": "^1.2.1", "vue-demi": "^0.14.10", "vue-layer": "^1.2.5", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vue-slick-carousel": "^1.0.6", "vuex": "^3.6.2", "x2js": "3.4.4"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-px2rem": "^0.3.0", "prettier": "^2.2.1", "vue-template-compiler": "^2.6.11"}}
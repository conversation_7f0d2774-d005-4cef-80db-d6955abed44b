window.globalConfig = {
  projectConfig: {
    title: '涉铁工程管理驾驶舱', //项目标题
    url: '',
  },
  // 后台跳转配置
  backendConfig: {
    // 默认后台地址
    // defaultBackend: 'http://************:54946',
    defaultBackend: 'http://localhost:8092',
    // 特定环境的配置
    environments: [
      {
        // 内网环境 - ************
        ipPattern: '************',
        backendUrl: 'http://************:16005',
        path: '/#/manage/overview',
        description: '内网测试环境'
      },
      {
        // 第二测试环境 - *************
        ipPattern: '*************',
        backendUrl: 'http://*************:56005',
        path: '/#/manage/overview',
        description: '外网生产环境'
      },
      {
        // 外网环境 - ************
        ipPattern: '************',
        backendUrl: 'http://************:54946',
        path: '/#/manage/overview',
        description: '外网测试环境'
      },
      {
        // 正式环境 - ************
        ipPattern: '**************',
        backendUrl: 'http://**************:16005',
        path: '/#/manage/overview',
        description: '外网生产环境'
      },
      {
        // 新正式环境 - ************
        ipPattern: '**************',
        backendUrl: 'http://**************:16005',
        path: '/#/manage/overview',
        description: '新生产环境'
      },
      // 可以添加更多环境配置
      {
        ipPattern: 'localhost',
        backendUrl: 'http://localhost:8092',
        path: '/#/manage/overview',
        description: '本地开发环境'
      },
      // {
      //   ipPattern: 'test.example.com',
      //   backendUrl: 'https://test-backend.example.com',
      //   path: '/#/manage/overview',
      //   description: '测试环境'
      // }
    ]
  },
  baseUrl: '/',
  map: {
    //是否显示底图`
    isLoadBaseMap: true,
    //是否显示地形
    isAddmodel: false,
    terrainUrl: 'http://data.marsgis.cn/terrain/', //地形数据的配置
    tdtkey: '14ba77517bab8f35dcc7d88916ee09cd',
    proj: "EPSG:4326",
    modeh: 30, //模型高度
    roamingAltitude: 60, //漫游高度
    ////设置初始显示的位置，可以不设置，不设置默认为模型的位置
    initPosition: {
      lon: 114.31158155473231,
      lat: 30.598466736400987,
      alt: 10000, //视角高度
      heading: 0, // 水平偏角，默认正北 0
      pitch: -40, // 俯视角，默认-90，垂直向下
      roll: 0, // 旋转角
    },
    //房屋图层名称和字段
    houseLayersName: "zt:LJTFW1210",
    houseKey: "Shape_Area",
    mapLayer: [
      {
        id: 'NNW',
        title: '工地',//
        type: 'layer.wmtsgeoserver',
        contextmenu: true,
        layername: 'easylinkin:result',
        typeCode: 'layerGeoserverVector',
        loadType: 'layerVector',
        icon: 'point',
        url: '/geoserver/easylinkin/gwc/service/wmts',
        opicity: 100,
        tileMatrixSetID: "EPSG:4326"
      },
    ],
  },
  appConfigBase: {
    base: '/zituSpace',
    baseXbootShp: '/xbootshp',
    baseXbootdwg: '/zituSpace',
    map: {
      center: {
        lat: 4022078.1373999994,
        lng: 40529834.1909
      },
      customTools: {
        tool: [],
      },
      distinRadius: 0,
      extent: {
        XMin: 40517734.2451,
        YMin: 4008012.6437,
        XMax: 40529834.1909,
        YMax: 4022078.1373999994,
      },
      isProj: true,
      maxZoom: 20,
      origin: '3.48768E7,1.00021E7',
      proj: 'EPSG:4326',
      resolutions: {
        reso: [
          305.7486135805605,
          152.874174498349,
          76.43695495724326,
          38.21834518669038,
          19.109040301413938,
          9.554387858775717,
          4.777061637456608,
          2.3886631106595546,
          1.1943315553297773,
          0.5971657776648887,
          0.2984505969011938,
          0.1492252984505969,
        ],
      },
      sourceproject: 'EPSG:4326',
      targetproject: 'EPSG:4326',
      terrainurl: 'https://www.supermapol.com/realspace/services/3D-stk_terrain/rest/realspace/datas/info/data/path',
      zoom: 12,
    },
  },
};
/**
 * @description: 墙体材质js
 * @author: wzd
 * @date: 2021-09-16 15:42:24
 */
(function (global, factory) {
  if (typeof exports === 'object') {
    if (typeof Cesium == 'undefined') return null;
    module.exports = factory(Cesium);
  } else {
    if (typeof window.Cesium == 'undefined') return null;
    global.WallMaterialProperty = factory(Cesium);
  }
}(typeof window !== "undefined" ? window : this, function (Cesium) {

  //动态墙材质
  function PolylineWallLinkMaterialProperty(options) {
    this._definitionChanged = new Cesium.Event();
    this._color = undefined;
    this._colorSubscription = undefined;
    this.color = options.color;
    this.duration = options.duration;
    this.trailImage = options.trailImage;
    this._time = (new Date()).getTime();
    this.cesiumMap = options.cesiumMap;
  }
  Object.defineProperties(PolylineWallLinkMaterialProperty.prototype, {
    isConstant: {
      get: function () {
        return false;
      }
    },
    definitionChanged: {
      get: function () {
        return this._definitionChanged;
      }
    },
    color: Cesium.createPropertyDescriptor('color')
  });
  PolylineWallLinkMaterialProperty.prototype.getType = function (time) {
    return 'PolylineWallLink';
  };

  PolylineWallLinkMaterialProperty.prototype.getValue = function (time, result) {
    if (!Cesium.defined(result)) {
      result = {};
    }
    result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, Cesium.Color.WHITE, result.color);
    if (this.trailImage) {
      result.image = this.trailImage;
    } else {
      result.image = Cesium.Material.PolylineWallLinkImage
    }

    if (this.duration) {
      result.time = (((new Date()).getTime() - this._time) % this.duration) / this.duration;
    }
    this.cesiumMap.getEarth().scene.requestRender();
    return result;
  };
  PolylineWallLinkMaterialProperty.prototype.equals = function (other) {
    return this === other ||
      (other instanceof PolylineWallLinkMaterialProperty &&
        Cesium.Property.equals(this._color, other._color))
  };
  Cesium.PolylineWallLinkMaterialProperty = PolylineWallLinkMaterialProperty;
  Cesium.Material.PolylineWallLinkType = 'PolylineWallLink';
  Cesium.Material.PolylineWallLinkImage = "/img/wall.png";
  Cesium.Material.PolylineWallLinkSource = "czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                                            {\n\
                                            czm_material material = czm_getDefaultMaterial(materialInput);\n\
                                            vec2 st = materialInput.st;\n\
                                            vec4 colorImage = texture2D(image, vec2(fract(st.t - time), st.t));\n\
                                            material.alpha = colorImage.a * color.a;\n\
                                            material.diffuse = color.rgb;\n\
                                            return material;\n\
                                            }";
  Cesium.Material._materialCache.addMaterial(Cesium.Material.PolylineWallLinkType, {
    fabric: {
      type: Cesium.Material.PolylineWallLinkType,
      uniforms: {
        color: new Cesium.Color(1.0, 1.0, 1.0, 1),
        image: Cesium.Material.PolylineWallLinkImage,
        time: 0
      },
      source: Cesium.Material.PolylineWallLinkSource
    },
    translucent: function (material) {
      return true;
    }
  });
}));

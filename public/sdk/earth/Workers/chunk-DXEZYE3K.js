/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.116
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as tt}from"./chunk-VAKC5J5C.js";import{a as Ae}from"./chunk-VKV642QV.js";import{a as mt,b as L,c as ht}from"./chunk-DGCK3LD2.js";import{a as f,b as Re,c as Qt,d as Pe,e as B}from"./chunk-CUOR5F7T.js";import{a as X}from"./chunk-74N6MC2V.js";import{a as Oe}from"./chunk-VHNZBQTR.js";import{a as v}from"./chunk-NPBZI5YA.js";import{a as T,b as y}from"./chunk-7X2YQ6I4.js";import{a as dt,c as pt,d as $e,e as d}from"./chunk-R2AN7EKC.js";var Mn=pt((Ye,Ve)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var t=typeof Ye=="object"&&Ye&&!Ye.nodeType&&Ye,n=typeof Ve=="object"&&Ve&&!Ve.nodeType&&Ve,i=typeof global=="object"&&global;(i.global===i||i.window===i||i.self===i)&&(e=i);var s,o=**********,c=36,u=1,p=26,b=38,g=700,w=72,S=128,R="-",I=/^xn--/,A=/[^\x20-\x7E]/,M=/[\x2E\u3002\uFF0E\uFF61]/g,U={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},k=c-u,q=Math.floor,j=String.fromCharCode,N;function $(h){throw new RangeError(U[h])}function re(h,P){for(var z=h.length,Y=[];z--;)Y[z]=P(h[z]);return Y}function oe(h,P){var z=h.split("@"),Y="";z.length>1&&(Y=z[0]+"@",h=z[1]),h=h.replace(M,".");var ne=h.split("."),ie=re(ne,P).join(".");return Y+ie}function J(h){for(var P=[],z=0,Y=h.length,ne,ie;z<Y;)ne=h.charCodeAt(z++),ne>=55296&&ne<=56319&&z<Y?(ie=h.charCodeAt(z++),(ie&64512)==56320?P.push(((ne&1023)<<10)+(ie&1023)+65536):(P.push(ne),z--)):P.push(ne);return P}function W(h){return re(h,function(P){var z="";return P>65535&&(P-=65536,z+=j(P>>>10&1023|55296),P=56320|P&1023),z+=j(P),z}).join("")}function Q(h){return h-48<10?h-22:h-65<26?h-65:h-97<26?h-97:c}function r(h,P){return h+22+75*(h<26)-((P!=0)<<5)}function a(h,P,z){var Y=0;for(h=z?q(h/g):h>>1,h+=q(h/P);h>k*p>>1;Y+=c)h=q(h/k);return q(Y+(k+1)*h/(h+b))}function l(h){var P=[],z=h.length,Y,ne=0,ie=S,G=w,se,de,he,pe,ue,ye,ge,Te,Me;for(se=h.lastIndexOf(R),se<0&&(se=0),de=0;de<se;++de)h.charCodeAt(de)>=128&&$("not-basic"),P.push(h.charCodeAt(de));for(he=se>0?se+1:0;he<z;){for(pe=ne,ue=1,ye=c;he>=z&&$("invalid-input"),ge=Q(h.charCodeAt(he++)),(ge>=c||ge>q((o-ne)/ue))&&$("overflow"),ne+=ge*ue,Te=ye<=G?u:ye>=G+p?p:ye-G,!(ge<Te);ye+=c)Me=c-Te,ue>q(o/Me)&&$("overflow"),ue*=Me;Y=P.length+1,G=a(ne-pe,Y,pe==0),q(ne/Y)>o-ie&&$("overflow"),ie+=q(ne/Y),ne%=Y,P.splice(ne++,0,ie)}return W(P)}function m(h){var P,z,Y,ne,ie,G,se,de,he,pe,ue,ye=[],ge,Te,Me,Lt;for(h=J(h),ge=h.length,P=S,z=0,ie=w,G=0;G<ge;++G)ue=h[G],ue<128&&ye.push(j(ue));for(Y=ne=ye.length,ne&&ye.push(R);Y<ge;){for(se=o,G=0;G<ge;++G)ue=h[G],ue>=P&&ue<se&&(se=ue);for(Te=Y+1,se-P>q((o-z)/Te)&&$("overflow"),z+=(se-P)*Te,P=se,G=0;G<ge;++G)if(ue=h[G],ue<P&&++z>o&&$("overflow"),ue==P){for(de=z,he=c;pe=he<=ie?u:he>=ie+p?p:he-ie,!(de<pe);he+=c)Lt=de-pe,Me=c-pe,ye.push(j(r(pe+Lt%Me,0))),de=q(Lt/Me);ye.push(j(r(de,0))),ie=a(z,Te,Y==ne),z=0,++Y}++z,++P}return ye.join("")}function _(h){return oe(h,function(P){return I.test(P)?l(P.slice(4).toLowerCase()):P})}function O(h){return oe(h,function(P){return A.test(P)?"xn--"+m(P):P})}if(s={version:"1.3.2",ucs2:{decode:J,encode:W},decode:l,encode:m,toASCII:O,toUnicode:_},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return s});else if(t&&n)if(Ve.exports==t)n.exports=s;else for(N in s)s.hasOwnProperty(N)&&(t[N]=s[N]);else e.punycode=s})(Ye)});var jn=pt((kn,Ot)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Ot=="object"&&Ot.exports?Ot.exports=t():typeof define=="function"&&define.amd?define(t):e.IPv6=t(e)})(kn,function(e){"use strict";var t=e&&e.IPv6;function n(s){var o=s.toLowerCase(),c=o.split(":"),u=c.length,p=8;c[0]===""&&c[1]===""&&c[2]===""?(c.shift(),c.shift()):c[0]===""&&c[1]===""?c.shift():c[u-1]===""&&c[u-2]===""&&c.pop(),u=c.length,c[u-1].indexOf(".")!==-1&&(p=7);var b;for(b=0;b<u&&c[b]!=="";b++);if(b<p)for(c.splice(b,1,"0000");c.length<p;)c.splice(b,0,"0000");for(var g,w=0;w<p;w++){g=c[w].split("");for(var S=0;S<3&&(g[0]==="0"&&g.length>1);S++)g.splice(0,1);c[w]=g.join("")}var R=-1,I=0,A=0,M=-1,U=!1;for(w=0;w<p;w++)U?c[w]==="0"?A+=1:(U=!1,A>I&&(R=M,I=A)):c[w]==="0"&&(U=!0,M=w,A=1);A>I&&(R=M,I=A),I>1&&c.splice(R,I,""),u=c.length;var k="";for(c[0]===""&&(k=":"),w=0;w<u&&(k+=c[w],w!==u-1);w++)k+=":";return c[u-1]===""&&(k+=":"),k}function i(){return e.IPv6===this&&(e.IPv6=t),this}return{best:n,noConflict:i}})});var Fn=pt((Nn,vt)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof vt=="object"&&vt.exports?vt.exports=t():typeof define=="function"&&define.amd?define(t):e.SecondLevelDomains=t(e)})(Nn,function(e){"use strict";var t=e&&e.SecondLevelDomains,n={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return!1;var o=i.lastIndexOf(".",s-1);if(o<=0||o>=s-1)return!1;var c=n.list[i.slice(s+1)];return c?c.indexOf(" "+i.slice(o+1,s)+" ")>=0:!1},is:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return!1;var o=i.lastIndexOf(".",s-1);if(o>=0)return!1;var c=n.list[i.slice(s+1)];return c?c.indexOf(" "+i.slice(0,s)+" ")>=0:!1},get:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return null;var o=i.lastIndexOf(".",s-1);if(o<=0||o>=s-1)return null;var c=n.list[i.slice(s+1)];return!c||c.indexOf(" "+i.slice(o+1,s)+" ")<0?null:i.slice(o+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return n})});var je=pt((Bn,Et)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Et=="object"&&Et.exports?Et.exports=t(Mn(),jn(),Fn()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],t):e.URI=t(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(Bn,function(e,t,n,i){"use strict";var s=i&&i.URI;function o(r,a){var l=arguments.length>=1,m=arguments.length>=2;if(!(this instanceof o))return l?m?new o(r,a):new o(r):new o;if(r===void 0){if(l)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?r=location.href+"":r=""}if(r===null&&l)throw new TypeError("null is not a valid argument for URI");return this.href(r),a!==void 0?this.absoluteTo(a):this}function c(r){return/^[0-9]+$/.test(r)}o.version="1.19.11";var u=o.prototype,p=Object.prototype.hasOwnProperty;function b(r){return r.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function g(r){return r===void 0?"Undefined":String(Object.prototype.toString.call(r)).slice(8,-1)}function w(r){return g(r)==="Array"}function S(r,a){var l={},m,_;if(g(a)==="RegExp")l=null;else if(w(a))for(m=0,_=a.length;m<_;m++)l[a[m]]=!0;else l[a]=!0;for(m=0,_=r.length;m<_;m++){var O=l&&l[r[m]]!==void 0||!l&&a.test(r[m]);O&&(r.splice(m,1),_--,m--)}return r}function R(r,a){var l,m;if(w(a)){for(l=0,m=a.length;l<m;l++)if(!R(r,a[l]))return!1;return!0}var _=g(a);for(l=0,m=r.length;l<m;l++)if(_==="RegExp"){if(typeof r[l]=="string"&&r[l].match(a))return!0}else if(r[l]===a)return!0;return!1}function I(r,a){if(!w(r)||!w(a)||r.length!==a.length)return!1;r.sort(),a.sort();for(var l=0,m=r.length;l<m;l++)if(r[l]!==a[l])return!1;return!0}function A(r){var a=/^\/+|\/+$/g;return r.replace(a,"")}o._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:o.preventInvalidHostname,duplicateQueryParameters:o.duplicateQueryParameters,escapeQuerySpace:o.escapeQuerySpace}},o.preventInvalidHostname=!1,o.duplicateQueryParameters=!1,o.escapeQuerySpace=!0,o.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,o.idn_expression=/[^a-z0-9\._-]/i,o.punycode_expression=/(xn--)/i,o.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,o.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,o.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,o.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},o.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,o.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},o.hostProtocols=["http","https"],o.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,o.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},o.getDomAttribute=function(r){if(!(!r||!r.nodeName)){var a=r.nodeName.toLowerCase();if(!(a==="input"&&r.type!=="image"))return o.domAttributes[a]}};function M(r){return escape(r)}function U(r){return encodeURIComponent(r).replace(/[!'()*]/g,M).replace(/\*/g,"%2A")}o.encode=U,o.decode=decodeURIComponent,o.iso8859=function(){o.encode=escape,o.decode=unescape},o.unicode=function(){o.encode=U,o.decode=decodeURIComponent},o.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},o.encodeQuery=function(r,a){var l=o.encode(r+"");return a===void 0&&(a=o.escapeQuerySpace),a?l.replace(/%20/g,"+"):l},o.decodeQuery=function(r,a){r+="",a===void 0&&(a=o.escapeQuerySpace);try{return o.decode(a?r.replace(/\+/g,"%20"):r)}catch{return r}};var k={encode:"encode",decode:"decode"},q,j=function(r,a){return function(l){try{return o[a](l+"").replace(o.characters[r][a].expression,function(m){return o.characters[r][a].map[m]})}catch{return l}}};for(q in k)o[q+"PathSegment"]=j("pathname",k[q]),o[q+"UrnPathSegment"]=j("urnpath",k[q]);var N=function(r,a,l){return function(m){var _;l?_=function(z){return o[a](o[l](z))}:_=o[a];for(var O=(m+"").split(r),h=0,P=O.length;h<P;h++)O[h]=_(O[h]);return O.join(r)}};o.decodePath=N("/","decodePathSegment"),o.decodeUrnPath=N(":","decodeUrnPathSegment"),o.recodePath=N("/","encodePathSegment","decode"),o.recodeUrnPath=N(":","encodeUrnPathSegment","decode"),o.encodeReserved=j("reserved","encode"),o.parse=function(r,a){var l;return a||(a={preventInvalidHostname:o.preventInvalidHostname}),r=r.replace(o.leading_whitespace_expression,""),r=r.replace(o.ascii_tab_whitespace,""),l=r.indexOf("#"),l>-1&&(a.fragment=r.substring(l+1)||null,r=r.substring(0,l)),l=r.indexOf("?"),l>-1&&(a.query=r.substring(l+1)||null,r=r.substring(0,l)),r=r.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),r=r.replace(/^[/\\]{2,}/i,"//"),r.substring(0,2)==="//"?(a.protocol=null,r=r.substring(2),r=o.parseAuthority(r,a)):(l=r.indexOf(":"),l>-1&&(a.protocol=r.substring(0,l)||null,a.protocol&&!a.protocol.match(o.protocol_expression)?a.protocol=void 0:r.substring(l+1,l+3).replace(/\\/g,"/")==="//"?(r=r.substring(l+3),r=o.parseAuthority(r,a)):(r=r.substring(l+1),a.urn=!0))),a.path=r,a},o.parseHost=function(r,a){r||(r=""),r=r.replace(/\\/g,"/");var l=r.indexOf("/"),m,_;if(l===-1&&(l=r.length),r.charAt(0)==="[")m=r.indexOf("]"),a.hostname=r.substring(1,m)||null,a.port=r.substring(m+2,l)||null,a.port==="/"&&(a.port=null);else{var O=r.indexOf(":"),h=r.indexOf("/"),P=r.indexOf(":",O+1);P!==-1&&(h===-1||P<h)?(a.hostname=r.substring(0,l)||null,a.port=null):(_=r.substring(0,l).split(":"),a.hostname=_[0]||null,a.port=_[1]||null)}return a.hostname&&r.substring(l).charAt(0)!=="/"&&(l++,r="/"+r),a.preventInvalidHostname&&o.ensureValidHostname(a.hostname,a.protocol),a.port&&o.ensureValidPort(a.port),r.substring(l)||"/"},o.parseAuthority=function(r,a){return r=o.parseUserinfo(r,a),o.parseHost(r,a)},o.parseUserinfo=function(r,a){var l=r,m=r.indexOf("\\");m!==-1&&(r=r.replace(/\\/g,"/"));var _=r.indexOf("/"),O=r.lastIndexOf("@",_>-1?_:r.length-1),h;return O>-1&&(_===-1||O<_)?(h=r.substring(0,O).split(":"),a.username=h[0]?o.decode(h[0]):null,h.shift(),a.password=h[0]?o.decode(h.join(":")):null,r=l.substring(O+1)):(a.username=null,a.password=null),r},o.parseQuery=function(r,a){if(!r)return{};if(r=r.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!r)return{};for(var l={},m=r.split("&"),_=m.length,O,h,P,z=0;z<_;z++)O=m[z].split("="),h=o.decodeQuery(O.shift(),a),P=O.length?o.decodeQuery(O.join("="),a):null,h!=="__proto__"&&(p.call(l,h)?((typeof l[h]=="string"||l[h]===null)&&(l[h]=[l[h]]),l[h].push(P)):l[h]=P);return l},o.build=function(r){var a="",l=!1;return r.protocol&&(a+=r.protocol+":"),!r.urn&&(a||r.hostname)&&(a+="//",l=!0),a+=o.buildAuthority(r)||"",typeof r.path=="string"&&(r.path.charAt(0)!=="/"&&l&&(a+="/"),a+=r.path),typeof r.query=="string"&&r.query&&(a+="?"+r.query),typeof r.fragment=="string"&&r.fragment&&(a+="#"+r.fragment),a},o.buildHost=function(r){var a="";if(r.hostname)o.ip6_expression.test(r.hostname)?a+="["+r.hostname+"]":a+=r.hostname;else return"";return r.port&&(a+=":"+r.port),a},o.buildAuthority=function(r){return o.buildUserinfo(r)+o.buildHost(r)},o.buildUserinfo=function(r){var a="";return r.username&&(a+=o.encode(r.username)),r.password&&(a+=":"+o.encode(r.password)),a&&(a+="@"),a},o.buildQuery=function(r,a,l){var m="",_,O,h,P;for(O in r)if(O!=="__proto__"&&p.call(r,O))if(w(r[O]))for(_={},h=0,P=r[O].length;h<P;h++)r[O][h]!==void 0&&_[r[O][h]+""]===void 0&&(m+="&"+o.buildQueryParameter(O,r[O][h],l),a!==!0&&(_[r[O][h]+""]=!0));else r[O]!==void 0&&(m+="&"+o.buildQueryParameter(O,r[O],l));return m.substring(1)},o.buildQueryParameter=function(r,a,l){return o.encodeQuery(r,l)+(a!==null?"="+o.encodeQuery(a,l):"")},o.addQuery=function(r,a,l){if(typeof a=="object")for(var m in a)p.call(a,m)&&o.addQuery(r,m,a[m]);else if(typeof a=="string"){if(r[a]===void 0){r[a]=l;return}else typeof r[a]=="string"&&(r[a]=[r[a]]);w(l)||(l=[l]),r[a]=(r[a]||[]).concat(l)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},o.setQuery=function(r,a,l){if(typeof a=="object")for(var m in a)p.call(a,m)&&o.setQuery(r,m,a[m]);else if(typeof a=="string")r[a]=l===void 0?null:l;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},o.removeQuery=function(r,a,l){var m,_,O;if(w(a))for(m=0,_=a.length;m<_;m++)r[a[m]]=void 0;else if(g(a)==="RegExp")for(O in r)a.test(O)&&(r[O]=void 0);else if(typeof a=="object")for(O in a)p.call(a,O)&&o.removeQuery(r,O,a[O]);else if(typeof a=="string")l!==void 0?g(l)==="RegExp"?!w(r[a])&&l.test(r[a])?r[a]=void 0:r[a]=S(r[a],l):r[a]===String(l)&&(!w(l)||l.length===1)?r[a]=void 0:w(r[a])&&(r[a]=S(r[a],l)):r[a]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},o.hasQuery=function(r,a,l,m){switch(g(a)){case"String":break;case"RegExp":for(var _ in r)if(p.call(r,_)&&a.test(_)&&(l===void 0||o.hasQuery(r,_,l)))return!0;return!1;case"Object":for(var O in a)if(p.call(a,O)&&!o.hasQuery(r,O,a[O]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(g(l)){case"Undefined":return a in r;case"Boolean":var h=!!(w(r[a])?r[a].length:r[a]);return l===h;case"Function":return!!l(r[a],a,r);case"Array":if(!w(r[a]))return!1;var P=m?R:I;return P(r[a],l);case"RegExp":return w(r[a])?m?R(r[a],l):!1:!!(r[a]&&r[a].match(l));case"Number":l=String(l);case"String":return w(r[a])?m?R(r[a],l):!1:r[a]===l;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},o.joinPaths=function(){for(var r=[],a=[],l=0,m=0;m<arguments.length;m++){var _=new o(arguments[m]);r.push(_);for(var O=_.segment(),h=0;h<O.length;h++)typeof O[h]=="string"&&a.push(O[h]),O[h]&&l++}if(!a.length||!l)return new o("");var P=new o("").segment(a);return(r[0].path()===""||r[0].path().slice(0,1)==="/")&&P.path("/"+P.path()),P.normalize()},o.commonPath=function(r,a){var l=Math.min(r.length,a.length),m;for(m=0;m<l;m++)if(r.charAt(m)!==a.charAt(m)){m--;break}return m<1?r.charAt(0)===a.charAt(0)&&r.charAt(0)==="/"?"/":"":((r.charAt(m)!=="/"||a.charAt(m)!=="/")&&(m=r.substring(0,m).lastIndexOf("/")),r.substring(0,m+1))},o.withinString=function(r,a,l){l||(l={});var m=l.start||o.findUri.start,_=l.end||o.findUri.end,O=l.trim||o.findUri.trim,h=l.parens||o.findUri.parens,P=/[a-z0-9-]=["']?$/i;for(m.lastIndex=0;;){var z=m.exec(r);if(!z)break;var Y=z.index;if(l.ignoreHtml){var ne=r.slice(Math.max(Y-3,0),Y);if(ne&&P.test(ne))continue}for(var ie=Y+r.slice(Y).search(_),G=r.slice(Y,ie),se=-1;;){var de=h.exec(G);if(!de)break;var he=de.index+de[0].length;se=Math.max(se,he)}if(se>-1?G=G.slice(0,se)+G.slice(se).replace(O,""):G=G.replace(O,""),!(G.length<=z[0].length)&&!(l.ignore&&l.ignore.test(G))){ie=Y+G.length;var pe=a(G,Y,ie,r);if(pe===void 0){m.lastIndex=ie;continue}pe=String(pe),r=r.slice(0,Y)+pe+r.slice(ie),m.lastIndex=Y+pe.length}}return m.lastIndex=0,r},o.ensureValidHostname=function(r,a){var l=!!r,m=!!a,_=!1;if(m&&(_=R(o.hostProtocols,a)),_&&!l)throw new TypeError("Hostname cannot be empty, if protocol is "+a);if(r&&r.match(o.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(r).match(o.invalid_hostname_characters))throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-:_]')}},o.ensureValidPort=function(r){if(r){var a=Number(r);if(!(c(a)&&a>0&&a<65536))throw new TypeError('Port "'+r+'" is not a valid port')}},o.noConflict=function(r){if(r){var a={URI:this.noConflict()};return i.URITemplate&&typeof i.URITemplate.noConflict=="function"&&(a.URITemplate=i.URITemplate.noConflict()),i.IPv6&&typeof i.IPv6.noConflict=="function"&&(a.IPv6=i.IPv6.noConflict()),i.SecondLevelDomains&&typeof i.SecondLevelDomains.noConflict=="function"&&(a.SecondLevelDomains=i.SecondLevelDomains.noConflict()),a}else i.URI===this&&(i.URI=s);return this},u.build=function(r){return r===!0?this._deferred_build=!0:(r===void 0||this._deferred_build)&&(this._string=o.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new o(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function $(r){return function(a,l){return a===void 0?this._parts[r]||"":(this._parts[r]=a||null,this.build(!l),this)}}function re(r,a){return function(l,m){return l===void 0?this._parts[r]||"":(l!==null&&(l=l+"",l.charAt(0)===a&&(l=l.substring(1))),this._parts[r]=l,this.build(!m),this)}}u.protocol=$("protocol"),u.username=$("username"),u.password=$("password"),u.hostname=$("hostname"),u.port=$("port"),u.query=re("query","?"),u.fragment=re("fragment","#"),u.search=function(r,a){var l=this.query(r,a);return typeof l=="string"&&l.length?"?"+l:l},u.hash=function(r,a){var l=this.fragment(r,a);return typeof l=="string"&&l.length?"#"+l:l},u.pathname=function(r,a){if(r===void 0||r===!0){var l=this._parts.path||(this._parts.hostname?"/":"");return r?(this._parts.urn?o.decodeUrnPath:o.decodePath)(l):l}else return this._parts.urn?this._parts.path=r?o.recodeUrnPath(r):"":this._parts.path=r?o.recodePath(r):"/",this.build(!a),this},u.path=u.pathname,u.href=function(r,a){var l;if(r===void 0)return this.toString();this._string="",this._parts=o._parts();var m=r instanceof o,_=typeof r=="object"&&(r.hostname||r.path||r.pathname);if(r.nodeName){var O=o.getDomAttribute(r);r=r[O]||"",_=!1}if(!m&&_&&r.pathname!==void 0&&(r=r.toString()),typeof r=="string"||r instanceof String)this._parts=o.parse(String(r),this._parts);else if(m||_){var h=m?r._parts:r;for(l in h)l!=="query"&&p.call(this._parts,l)&&(this._parts[l]=h[l]);h.query&&this.query(h.query,!1)}else throw new TypeError("invalid input");return this.build(!a),this},u.is=function(r){var a=!1,l=!1,m=!1,_=!1,O=!1,h=!1,P=!1,z=!this._parts.urn;switch(this._parts.hostname&&(z=!1,l=o.ip4_expression.test(this._parts.hostname),m=o.ip6_expression.test(this._parts.hostname),a=l||m,_=!a,O=_&&n&&n.has(this._parts.hostname),h=_&&o.idn_expression.test(this._parts.hostname),P=_&&o.punycode_expression.test(this._parts.hostname)),r.toLowerCase()){case"relative":return z;case"absolute":return!z;case"domain":case"name":return _;case"sld":return O;case"ip":return a;case"ip4":case"ipv4":case"inet4":return l;case"ip6":case"ipv6":case"inet6":return m;case"idn":return h;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return P}return null};var oe=u.protocol,J=u.port,W=u.hostname;u.protocol=function(r,a){if(r&&(r=r.replace(/:(\/\/)?$/,""),!r.match(o.protocol_expression)))throw new TypeError('Protocol "'+r+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return oe.call(this,r,a)},u.scheme=u.protocol,u.port=function(r,a){return this._parts.urn?r===void 0?"":this:(r!==void 0&&(r===0&&(r=null),r&&(r+="",r.charAt(0)===":"&&(r=r.substring(1)),o.ensureValidPort(r))),J.call(this,r,a))},u.hostname=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r!==void 0){var l={preventInvalidHostname:this._parts.preventInvalidHostname},m=o.parseHost(r,l);if(m!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');r=l.hostname,this._parts.preventInvalidHostname&&o.ensureValidHostname(r,this._parts.protocol)}return W.call(this,r,a)},u.origin=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){var l=this.protocol(),m=this.authority();return m?(l?l+"://":"")+this.authority():""}else{var _=o(r);return this.protocol(_.protocol()).authority(_.authority()).build(!a),this}},u.host=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0)return this._parts.hostname?o.buildHost(this._parts):"";var l=o.parseHost(r,this._parts);if(l!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');return this.build(!a),this},u.authority=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0)return this._parts.hostname?o.buildAuthority(this._parts):"";var l=o.parseAuthority(r,this._parts);if(l!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');return this.build(!a),this},u.userinfo=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){var l=o.buildUserinfo(this._parts);return l&&l.substring(0,l.length-1)}else return r[r.length-1]!=="@"&&(r+="@"),o.parseUserinfo(r,this._parts),this.build(!a),this},u.resource=function(r,a){var l;return r===void 0?this.path()+this.search()+this.hash():(l=o.parse(r),this._parts.path=l.path,this._parts.query=l.query,this._parts.fragment=l.fragment,this.build(!a),this)},u.subdomain=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,l)||""}else{var m=this._parts.hostname.length-this.domain().length,_=this._parts.hostname.substring(0,m),O=new RegExp("^"+b(_));if(r&&r.charAt(r.length-1)!=="."&&(r+="."),r.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return r&&o.ensureValidHostname(r,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(O,r),this.build(!a),this}},u.domain=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r=="boolean"&&(a=r,r=void 0),r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.match(/\./g);if(l&&l.length<2)return this._parts.hostname;var m=this._parts.hostname.length-this.tld(a).length-1;return m=this._parts.hostname.lastIndexOf(".",m-1)+1,this._parts.hostname.substring(m)||""}else{if(!r)throw new TypeError("cannot set domain empty");if(r.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(o.ensureValidHostname(r,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=r;else{var _=new RegExp(b(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(_,r)}return this.build(!a),this}},u.tld=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r=="boolean"&&(a=r,r=void 0),r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.lastIndexOf("."),m=this._parts.hostname.substring(l+1);return a!==!0&&n&&n.list[m.toLowerCase()]&&n.get(this._parts.hostname)||m}else{var _;if(r)if(r.match(/[^a-zA-Z0-9-]/))if(n&&n.is(r))_=new RegExp(b(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(_,r);else throw new TypeError('TLD "'+r+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");_=new RegExp(b(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(_,r)}else throw new TypeError("cannot set TLD empty");return this.build(!a),this}},u.directory=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0||r===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var l=this._parts.path.length-this.filename().length-1,m=this._parts.path.substring(0,l)||(this._parts.hostname?"/":"");return r?o.decodePath(m):m}else{var _=this._parts.path.length-this.filename().length,O=this._parts.path.substring(0,_),h=new RegExp("^"+b(O));return this.is("relative")||(r||(r="/"),r.charAt(0)!=="/"&&(r="/"+r)),r&&r.charAt(r.length-1)!=="/"&&(r+="/"),r=o.recodePath(r),this._parts.path=this._parts.path.replace(h,r),this.build(!a),this}},u.filename=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r!="string"){if(!this._parts.path||this._parts.path==="/")return"";var l=this._parts.path.lastIndexOf("/"),m=this._parts.path.substring(l+1);return r?o.decodePathSegment(m):m}else{var _=!1;r.charAt(0)==="/"&&(r=r.substring(1)),r.match(/\.?\//)&&(_=!0);var O=new RegExp(b(this.filename())+"$");return r=o.recodePath(r),this._parts.path=this._parts.path.replace(O,r),_?this.normalizePath(a):this.build(!a),this}},u.suffix=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0||r===!0){if(!this._parts.path||this._parts.path==="/")return"";var l=this.filename(),m=l.lastIndexOf("."),_,O;return m===-1?"":(_=l.substring(m+1),O=/^[a-z0-9%]+$/i.test(_)?_:"",r?o.decodePathSegment(O):O)}else{r.charAt(0)==="."&&(r=r.substring(1));var h=this.suffix(),P;if(h)r?P=new RegExp(b(h)+"$"):P=new RegExp(b("."+h)+"$");else{if(!r)return this;this._parts.path+="."+o.recodePath(r)}return P&&(r=o.recodePath(r),this._parts.path=this._parts.path.replace(P,r)),this.build(!a),this}},u.segment=function(r,a,l){var m=this._parts.urn?":":"/",_=this.path(),O=_.substring(0,1)==="/",h=_.split(m);if(r!==void 0&&typeof r!="number"&&(l=a,a=r,r=void 0),r!==void 0&&typeof r!="number")throw new Error('Bad segment "'+r+'", must be 0-based integer');if(O&&h.shift(),r<0&&(r=Math.max(h.length+r,0)),a===void 0)return r===void 0?h:h[r];if(r===null||h[r]===void 0)if(w(a)){h=[];for(var P=0,z=a.length;P<z;P++)!a[P].length&&(!h.length||!h[h.length-1].length)||(h.length&&!h[h.length-1].length&&h.pop(),h.push(A(a[P])))}else(a||typeof a=="string")&&(a=A(a),h[h.length-1]===""?h[h.length-1]=a:h.push(a));else a?h[r]=A(a):h.splice(r,1);return O&&h.unshift(""),this.path(h.join(m),l)},u.segmentCoded=function(r,a,l){var m,_,O;if(typeof r!="number"&&(l=a,a=r,r=void 0),a===void 0){if(m=this.segment(r,a,l),!w(m))m=m!==void 0?o.decode(m):void 0;else for(_=0,O=m.length;_<O;_++)m[_]=o.decode(m[_]);return m}if(!w(a))a=typeof a=="string"||a instanceof String?o.encode(a):a;else for(_=0,O=a.length;_<O;_++)a[_]=o.encode(a[_]);return this.segment(r,a,l)};var Q=u.query;return u.query=function(r,a){if(r===!0)return o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof r=="function"){var l=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace),m=r.call(this,l);return this._parts.query=o.buildQuery(m||l,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!a),this}else return r!==void 0&&typeof r!="string"?(this._parts.query=o.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!a),this):Q.call(this,r,a)},u.setQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof r=="string"||r instanceof String)m[r]=a!==void 0?a:null;else if(typeof r=="object")for(var _ in r)p.call(r,_)&&(m[_]=r[_]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.addQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.addQuery(m,r,a===void 0?null:a),this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.removeQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.removeQuery(m,r,a),this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.hasQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.hasQuery(m,r,a,l)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(r){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!r)),this},u.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},u.normalizePort=function(r){return typeof this._parts.protocol=="string"&&this._parts.port===o.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!r)),this},u.normalizePath=function(r){var a=this._parts.path;if(!a)return this;if(this._parts.urn)return this._parts.path=o.recodeUrnPath(this._parts.path),this.build(!r),this;if(this._parts.path==="/")return this;a=o.recodePath(a);var l,m="",_,O;for(a.charAt(0)!=="/"&&(l=!0,a="/"+a),(a.slice(-3)==="/.."||a.slice(-2)==="/.")&&(a+="/"),a=a.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),l&&(m=a.substring(1).match(/^(\.\.\/)+/)||"",m&&(m=m[0]));_=a.search(/\/\.\.(\/|$)/),_!==-1;){if(_===0){a=a.substring(3);continue}O=a.substring(0,_).lastIndexOf("/"),O===-1&&(O=_),a=a.substring(0,O)+a.substring(_+3)}return l&&this.is("relative")&&(a=m+a.substring(1)),this._parts.path=a,this.build(!r),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(r){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(o.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!r)),this},u.normalizeFragment=function(r){return this._parts.fragment||(this._parts.fragment=null,this.build(!r)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var r=o.encode,a=o.decode;o.encode=escape,o.decode=decodeURIComponent;try{this.normalize()}finally{o.encode=r,o.decode=a}return this},u.unicode=function(){var r=o.encode,a=o.decode;o.encode=U,o.decode=unescape;try{this.normalize()}finally{o.encode=r,o.decode=a}return this},u.readable=function(){var r=this.clone();r.username("").password("").normalize();var a="";if(r._parts.protocol&&(a+=r._parts.protocol+"://"),r._parts.hostname&&(r.is("punycode")&&e?(a+=e.toUnicode(r._parts.hostname),r._parts.port&&(a+=":"+r._parts.port)):a+=r.host()),r._parts.hostname&&r._parts.path&&r._parts.path.charAt(0)!=="/"&&(a+="/"),a+=r.path(!0),r._parts.query){for(var l="",m=0,_=r._parts.query.split("&"),O=_.length;m<O;m++){var h=(_[m]||"").split("=");l+="&"+o.decodeQuery(h[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),h[1]!==void 0&&(l+="="+o.decodeQuery(h[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}a+="?"+l.substring(1)}return a+=o.decodeQuery(r.hash(),!0),a},u.absoluteTo=function(r){var a=this.clone(),l=["protocol","username","password","hostname","port"],m,_,O;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(r instanceof o||(r=new o(r)),a._parts.protocol||(a._parts.protocol=r._parts.protocol,this._parts.hostname))return a;for(_=0;O=l[_];_++)a._parts[O]=r._parts[O];return a._parts.path?(a._parts.path.substring(-2)===".."&&(a._parts.path+="/"),a.path().charAt(0)!=="/"&&(m=r.directory(),m=m||(r.path().indexOf("/")===0?"/":""),a._parts.path=(m?m+"/":"")+a._parts.path,a.normalizePath())):(a._parts.path=r._parts.path,a._parts.query||(a._parts.query=r._parts.query)),a.build(),a},u.relativeTo=function(r){var a=this.clone().normalize(),l,m,_,O,h;if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(r=new o(r).normalize(),l=a._parts,m=r._parts,O=a.path(),h=r.path(),O.charAt(0)!=="/")throw new Error("URI is already relative");if(h.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(l.protocol===m.protocol&&(l.protocol=null),l.username!==m.username||l.password!==m.password||l.protocol!==null||l.username!==null||l.password!==null)return a.build();if(l.hostname===m.hostname&&l.port===m.port)l.hostname=null,l.port=null;else return a.build();if(O===h)return l.path="",a.build();if(_=o.commonPath(O,h),!_)return a.build();var P=m.path.substring(_.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return l.path=P+l.path.substring(_.length)||"./",a.build()},u.equals=function(r){var a=this.clone(),l=new o(r),m={},_={},O={},h,P,z;if(a.normalize(),l.normalize(),a.toString()===l.toString())return!0;if(h=a.query(),P=l.query(),a.query(""),l.query(""),a.toString()!==l.toString()||h.length!==P.length)return!1;m=o.parseQuery(h,this._parts.escapeQuerySpace),_=o.parseQuery(P,this._parts.escapeQuerySpace);for(z in m)if(p.call(m,z)){if(w(m[z])){if(!I(m[z],_[z]))return!1}else if(m[z]!==_[z])return!1;O[z]=!0}for(z in _)if(p.call(_,z)&&!O[z])return!1;return!0},u.preventInvalidHostname=function(r){return this._parts.preventInvalidHostname=!!r,this},u.duplicateQueryParameters=function(r){return this._parts.duplicateQueryParameters=!!r,this},u.escapeQuerySpace=function(r){return this._parts.escapeQuerySpace=!!r,this},o})});function yt(e){this._ellipsoid=v(e,Pe.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(yt.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}});yt.prototype.project=function(e,t){let n=this._semimajorAxis,i=e.longitude*n,s=e.latitude*n,o=e.height;return d(t)?(t.x=i,t.y=s,t.z=o,t):new f(i,s,o)};yt.prototype.unproject=function(e,t){if(!d(e))throw new T("cartesian is required");let n=this._oneOverSemimajorAxis,i=e.x*n,s=e.y*n,o=e.z;return d(t)?(t.longitude=i,t.latitude=s,t.height=o,t):new Re(i,s,o)};var Ht=yt;var qr={OUTSIDE:-1,INTERSECTING:0,INSIDE:1},gt=Object.freeze(qr);function zr(e,t){this.start=v(e,0),this.stop=v(t,0)}var An=zr;function D(e,t){this.center=f.clone(v(e,f.ZERO)),this.radius=v(t,0)}var Yt=new f,Vt=new f,Zt=new f,Xt=new f,Jt=new f,Gt=new f,Kt=new f,ae=new f,en=new f,tn=new f,nn=new f,rn=new f,Mr=4/3*X.PI;D.fromPoints=function(e,t){if(d(t)||(t=new D),!d(e)||e.length===0)return t.center=f.clone(f.ZERO,t.center),t.radius=0,t;let n=f.clone(e[0],Kt),i=f.clone(n,Yt),s=f.clone(n,Vt),o=f.clone(n,Zt),c=f.clone(n,Xt),u=f.clone(n,Jt),p=f.clone(n,Gt),b=e.length,g;for(g=1;g<b;g++){f.clone(e[g],n);let oe=n.x,J=n.y,W=n.z;oe<i.x&&f.clone(n,i),oe>c.x&&f.clone(n,c),J<s.y&&f.clone(n,s),J>u.y&&f.clone(n,u),W<o.z&&f.clone(n,o),W>p.z&&f.clone(n,p)}let w=f.magnitudeSquared(f.subtract(c,i,ae)),S=f.magnitudeSquared(f.subtract(u,s,ae)),R=f.magnitudeSquared(f.subtract(p,o,ae)),I=i,A=c,M=w;S>M&&(M=S,I=s,A=u),R>M&&(M=R,I=o,A=p);let U=en;U.x=(I.x+A.x)*.5,U.y=(I.y+A.y)*.5,U.z=(I.z+A.z)*.5;let k=f.magnitudeSquared(f.subtract(A,U,ae)),q=Math.sqrt(k),j=tn;j.x=i.x,j.y=s.y,j.z=o.z;let N=nn;N.x=c.x,N.y=u.y,N.z=p.z;let $=f.midpoint(j,N,rn),re=0;for(g=0;g<b;g++){f.clone(e[g],n);let oe=f.magnitude(f.subtract(n,$,ae));oe>re&&(re=oe);let J=f.magnitudeSquared(f.subtract(n,U,ae));if(J>k){let W=Math.sqrt(J);q=(q+W)*.5,k=q*q;let Q=W-q;U.x=(q*U.x+Q*n.x)/W,U.y=(q*U.y+Q*n.y)/W,U.z=(q*U.z+Q*n.z)/W}}return q<re?(f.clone(U,t.center),t.radius=q):(f.clone($,t.center),t.radius=re),t};var kr=new Ht,jr=new f,Nr=new f,$t=new Re,Wt=new Re;D.fromRectangle2D=function(e,t,n){return D.fromRectangleWithHeights2D(e,t,0,0,n)};D.fromRectangleWithHeights2D=function(e,t,n,i,s){if(d(s)||(s=new D),!d(e))return s.center=f.clone(f.ZERO,s.center),s.radius=0,s;t=v(t,kr),ht.southwest(e,$t),$t.height=n,ht.northeast(e,Wt),Wt.height=i;let o=t.project($t,jr),c=t.project(Wt,Nr),u=c.x-o.x,p=c.y-o.y,b=c.z-o.z;s.radius=Math.sqrt(u*u+p*p+b*b)*.5;let g=s.center;return g.x=o.x+u*.5,g.y=o.y+p*.5,g.z=o.z+b*.5,s};var Fr=[];D.fromRectangle3D=function(e,t,n,i){if(t=v(t,Pe.WGS84),n=v(n,0),d(i)||(i=new D),!d(e))return i.center=f.clone(f.ZERO,i.center),i.radius=0,i;let s=ht.subsample(e,t,n,Fr);return D.fromPoints(s,i)};D.fromVertices=function(e,t,n,i){if(d(i)||(i=new D),!d(e)||e.length===0)return i.center=f.clone(f.ZERO,i.center),i.radius=0,i;t=v(t,f.ZERO),n=v(n,3),y.typeOf.number.greaterThanOrEquals("stride",n,3);let s=Kt;s.x=e[0]+t.x,s.y=e[1]+t.y,s.z=e[2]+t.z;let o=f.clone(s,Yt),c=f.clone(s,Vt),u=f.clone(s,Zt),p=f.clone(s,Xt),b=f.clone(s,Jt),g=f.clone(s,Gt),w=e.length,S;for(S=0;S<w;S+=n){let W=e[S]+t.x,Q=e[S+1]+t.y,r=e[S+2]+t.z;s.x=W,s.y=Q,s.z=r,W<o.x&&f.clone(s,o),W>p.x&&f.clone(s,p),Q<c.y&&f.clone(s,c),Q>b.y&&f.clone(s,b),r<u.z&&f.clone(s,u),r>g.z&&f.clone(s,g)}let R=f.magnitudeSquared(f.subtract(p,o,ae)),I=f.magnitudeSquared(f.subtract(b,c,ae)),A=f.magnitudeSquared(f.subtract(g,u,ae)),M=o,U=p,k=R;I>k&&(k=I,M=c,U=b),A>k&&(k=A,M=u,U=g);let q=en;q.x=(M.x+U.x)*.5,q.y=(M.y+U.y)*.5,q.z=(M.z+U.z)*.5;let j=f.magnitudeSquared(f.subtract(U,q,ae)),N=Math.sqrt(j),$=tn;$.x=o.x,$.y=c.y,$.z=u.z;let re=nn;re.x=p.x,re.y=b.y,re.z=g.z;let oe=f.midpoint($,re,rn),J=0;for(S=0;S<w;S+=n){s.x=e[S]+t.x,s.y=e[S+1]+t.y,s.z=e[S+2]+t.z;let W=f.magnitude(f.subtract(s,oe,ae));W>J&&(J=W);let Q=f.magnitudeSquared(f.subtract(s,q,ae));if(Q>j){let r=Math.sqrt(Q);N=(N+r)*.5,j=N*N;let a=r-N;q.x=(N*q.x+a*s.x)/r,q.y=(N*q.y+a*s.y)/r,q.z=(N*q.z+a*s.z)/r}}return N<J?(f.clone(q,i.center),i.radius=N):(f.clone(oe,i.center),i.radius=J),i};D.fromEncodedCartesianVertices=function(e,t,n){if(d(n)||(n=new D),!d(e)||!d(t)||e.length!==t.length||e.length===0)return n.center=f.clone(f.ZERO,n.center),n.radius=0,n;let i=Kt;i.x=e[0]+t[0],i.y=e[1]+t[1],i.z=e[2]+t[2];let s=f.clone(i,Yt),o=f.clone(i,Vt),c=f.clone(i,Zt),u=f.clone(i,Xt),p=f.clone(i,Jt),b=f.clone(i,Gt),g=e.length,w;for(w=0;w<g;w+=3){let J=e[w]+t[w],W=e[w+1]+t[w+1],Q=e[w+2]+t[w+2];i.x=J,i.y=W,i.z=Q,J<s.x&&f.clone(i,s),J>u.x&&f.clone(i,u),W<o.y&&f.clone(i,o),W>p.y&&f.clone(i,p),Q<c.z&&f.clone(i,c),Q>b.z&&f.clone(i,b)}let S=f.magnitudeSquared(f.subtract(u,s,ae)),R=f.magnitudeSquared(f.subtract(p,o,ae)),I=f.magnitudeSquared(f.subtract(b,c,ae)),A=s,M=u,U=S;R>U&&(U=R,A=o,M=p),I>U&&(U=I,A=c,M=b);let k=en;k.x=(A.x+M.x)*.5,k.y=(A.y+M.y)*.5,k.z=(A.z+M.z)*.5;let q=f.magnitudeSquared(f.subtract(M,k,ae)),j=Math.sqrt(q),N=tn;N.x=s.x,N.y=o.y,N.z=c.z;let $=nn;$.x=u.x,$.y=p.y,$.z=b.z;let re=f.midpoint(N,$,rn),oe=0;for(w=0;w<g;w+=3){i.x=e[w]+t[w],i.y=e[w+1]+t[w+1],i.z=e[w+2]+t[w+2];let J=f.magnitude(f.subtract(i,re,ae));J>oe&&(oe=J);let W=f.magnitudeSquared(f.subtract(i,k,ae));if(W>q){let Q=Math.sqrt(W);j=(j+Q)*.5,q=j*j;let r=Q-j;k.x=(j*k.x+r*i.x)/Q,k.y=(j*k.y+r*i.y)/Q,k.z=(j*k.z+r*i.z)/Q}}return j<oe?(f.clone(k,n.center),n.radius=j):(f.clone(re,n.center),n.radius=oe),n};D.fromCornerPoints=function(e,t,n){y.typeOf.object("corner",e),y.typeOf.object("oppositeCorner",t),d(n)||(n=new D);let i=f.midpoint(e,t,n.center);return n.radius=f.distance(i,t),n};D.fromEllipsoid=function(e,t){return y.typeOf.object("ellipsoid",e),d(t)||(t=new D),f.clone(f.ZERO,t.center),t.radius=e.maximumRadius,t};var Br=new f;D.fromBoundingSpheres=function(e,t){if(d(t)||(t=new D),!d(e)||e.length===0)return t.center=f.clone(f.ZERO,t.center),t.radius=0,t;let n=e.length;if(n===1)return D.clone(e[0],t);if(n===2)return D.union(e[0],e[1],t);let i=[],s;for(s=0;s<n;s++)i.push(e[s].center);t=D.fromPoints(i,t);let o=t.center,c=t.radius;for(s=0;s<n;s++){let u=e[s];c=Math.max(c,f.distance(o,u.center,Br)+u.radius)}return t.radius=c,t};var Lr=new f,Qr=new f,Hr=new f;D.fromOrientedBoundingBox=function(e,t){y.defined("orientedBoundingBox",e),d(t)||(t=new D);let n=e.halfAxes,i=B.getColumn(n,0,Lr),s=B.getColumn(n,1,Qr),o=B.getColumn(n,2,Hr);return f.add(i,s,i),f.add(i,o,i),t.center=f.clone(e.center,t.center),t.radius=f.magnitude(i),t};var $r=new f,Wr=new f;D.fromTransformation=function(e,t){y.typeOf.object("transformation",e),d(t)||(t=new D);let n=L.getTranslation(e,$r),i=L.getScale(e,Wr),s=.5*f.magnitude(i);return t.center=f.clone(n,t.center),t.radius=s,t};D.clone=function(e,t){if(d(e))return d(t)?(t.center=f.clone(e.center,t.center),t.radius=e.radius,t):new D(e.center,e.radius)};D.packedLength=4;D.pack=function(e,t,n){y.typeOf.object("value",e),y.defined("array",t),n=v(n,0);let i=e.center;return t[n++]=i.x,t[n++]=i.y,t[n++]=i.z,t[n]=e.radius,t};D.unpack=function(e,t,n){y.defined("array",e),t=v(t,0),d(n)||(n=new D);let i=n.center;return i.x=e[t++],i.y=e[t++],i.z=e[t++],n.radius=e[t],n};var Yr=new f,Vr=new f;D.union=function(e,t,n){y.typeOf.object("left",e),y.typeOf.object("right",t),d(n)||(n=new D);let i=e.center,s=e.radius,o=t.center,c=t.radius,u=f.subtract(o,i,Yr),p=f.magnitude(u);if(s>=p+c)return e.clone(n),n;if(c>=p+s)return t.clone(n),n;let b=(s+p+c)*.5,g=f.multiplyByScalar(u,(-s+b)/p,Vr);return f.add(g,i,g),f.clone(g,n.center),n.radius=b,n};var Zr=new f;D.expand=function(e,t,n){y.typeOf.object("sphere",e),y.typeOf.object("point",t),n=D.clone(e,n);let i=f.magnitude(f.subtract(t,n.center,Zr));return i>n.radius&&(n.radius=i),n};D.intersectPlane=function(e,t){y.typeOf.object("sphere",e),y.typeOf.object("plane",t);let n=e.center,i=e.radius,s=t.normal,o=f.dot(s,n)+t.distance;return o<-i?gt.OUTSIDE:o<i?gt.INTERSECTING:gt.INSIDE};D.transform=function(e,t,n){return y.typeOf.object("sphere",e),y.typeOf.object("transform",t),d(n)||(n=new D),n.center=L.multiplyByPoint(t,e.center,n.center),n.radius=L.getMaximumScale(t)*e.radius,n};var Xr=new f;D.distanceSquaredTo=function(e,t){y.typeOf.object("sphere",e),y.typeOf.object("cartesian",t);let n=f.subtract(e.center,t,Xr),i=f.magnitude(n)-e.radius;return i<=0?0:i*i};D.transformWithoutScale=function(e,t,n){return y.typeOf.object("sphere",e),y.typeOf.object("transform",t),d(n)||(n=new D),n.center=L.multiplyByPoint(t,e.center,n.center),n.radius=e.radius,n};var Jr=new f;D.computePlaneDistances=function(e,t,n,i){y.typeOf.object("sphere",e),y.typeOf.object("position",t),y.typeOf.object("direction",n),d(i)||(i=new An);let s=f.subtract(e.center,t,Jr),o=f.dot(n,s);return i.start=o-e.radius,i.stop=o+e.radius,i};var In=new f,Gr=new f,Kr=new f,eo=new f,to=new f,no=new Re,Un=new Array(8);for(let e=0;e<8;++e)Un[e]=new f;var ro=new Ht;D.projectTo2D=function(e,t,n){y.typeOf.object("sphere",e),t=v(t,ro);let i=t.ellipsoid,s=e.center,o=e.radius,c;f.equals(s,f.ZERO)?c=f.clone(f.UNIT_X,In):c=i.geodeticSurfaceNormal(s,In);let u=f.cross(f.UNIT_Z,c,Gr);f.normalize(u,u);let p=f.cross(c,u,Kr);f.normalize(p,p),f.multiplyByScalar(c,o,c),f.multiplyByScalar(p,o,p),f.multiplyByScalar(u,o,u);let b=f.negate(p,to),g=f.negate(u,eo),w=Un,S=w[0];f.add(c,p,S),f.add(S,u,S),S=w[1],f.add(c,p,S),f.add(S,g,S),S=w[2],f.add(c,b,S),f.add(S,g,S),S=w[3],f.add(c,b,S),f.add(S,u,S),f.negate(c,c),S=w[4],f.add(c,p,S),f.add(S,u,S),S=w[5],f.add(c,p,S),f.add(S,g,S),S=w[6],f.add(c,b,S),f.add(S,g,S),S=w[7],f.add(c,b,S),f.add(S,u,S);let R=w.length;for(let U=0;U<R;++U){let k=w[U];f.add(s,k,k);let q=i.cartesianToCartographic(k,no);t.project(q,k)}n=D.fromPoints(w,n),s=n.center;let I=s.x,A=s.y,M=s.z;return s.x=M,s.y=I,s.z=A,n};D.isOccluded=function(e,t){return y.typeOf.object("sphere",e),y.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)};D.equals=function(e,t){return e===t||d(e)&&d(t)&&f.equals(e.center,t.center)&&e.radius===t.radius};D.prototype.intersectPlane=function(e){return D.intersectPlane(this,e)};D.prototype.distanceSquaredTo=function(e){return D.distanceSquaredTo(this,e)};D.prototype.computePlaneDistances=function(e,t,n){return D.computePlaneDistances(this,e,t,n)};D.prototype.isOccluded=function(e){return D.isOccluded(this,e)};D.prototype.equals=function(e){return D.equals(this,e)};D.prototype.clone=function(e){return D.clone(this,e)};D.prototype.volume=function(){let e=this.radius;return Mr*e*e*e};var es=D;function oo(e,t,n){y.defined("array",e),y.defined("itemToFind",t),y.defined("comparator",n);let i=0,s=e.length-1,o,c;for(;i<=s;){if(o=~~((i+s)/2),c=n(e[o],t),c<0){i=o+1;continue}if(c>0){s=o-1;continue}return o}return~(s+1)}var ke=oo;function io(e,t,n,i,s){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=i,this.ut1MinusUtc=s}var nt=io;function so(e){if(e===null||isNaN(e))throw new T("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var rt=so;var Dn=[31,28,31,30,31,30,31,31,30,31,30,31];function ao(e,t,n,i,s,o,c,u){e=v(e,1),t=v(t,1),n=v(n,1),i=v(i,0),s=v(s,0),o=v(o,0),c=v(c,0),u=v(u,!1),A(),M(),this.year=e,this.month=t,this.day=n,this.hour=i,this.minute=s,this.second=o,this.millisecond=c,this.isLeapSecond=u;function A(){y.typeOf.number.greaterThanOrEquals("Year",e,1),y.typeOf.number.lessThanOrEquals("Year",e,9999),y.typeOf.number.greaterThanOrEquals("Month",t,1),y.typeOf.number.lessThanOrEquals("Month",t,12),y.typeOf.number.greaterThanOrEquals("Day",n,1),y.typeOf.number.lessThanOrEquals("Day",n,31),y.typeOf.number.greaterThanOrEquals("Hour",i,0),y.typeOf.number.lessThanOrEquals("Hour",i,23),y.typeOf.number.greaterThanOrEquals("Minute",s,0),y.typeOf.number.lessThanOrEquals("Minute",s,59),y.typeOf.bool("IsLeapSecond",u),y.typeOf.number.greaterThanOrEquals("Second",o,0),y.typeOf.number.lessThanOrEquals("Second",o,u?60:59),y.typeOf.number.greaterThanOrEquals("Millisecond",c,0),y.typeOf.number.lessThan("Millisecond",c,1e3)}function M(){let U=t===2&&rt(e)?Dn[t-1]+1:Dn[t-1];if(n>U)throw new T("Month and Day represents invalid date")}}var wt=ao;function co(e,t){this.julianDate=e,this.offset=t}var Z=co;var uo={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},K=Object.freeze(uo);var fo={UTC:0,TAI:1},F=Object.freeze(fo);var zn=new wt,bt=[31,28,31,30,31,30,31,31,30,31,30,31],_t=29;function on(e,t){return C.compare(e.julianDate,t.julianDate)}var We=new Z;function xt(e){We.julianDate=e;let t=C.leapSeconds,n=ke(t,We,on);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let i=t[n].offset;n>0&&C.secondsDifference(t[n].julianDate,e)>i&&(n--,i=t[n].offset),C.addSeconds(e,i,e)}function qn(e,t){We.julianDate=e;let n=C.leapSeconds,i=ke(n,We,on);if(i<0&&(i=~i),i===0)return C.addSeconds(e,-n[0].offset,t);if(i>=n.length)return C.addSeconds(e,-n[i-1].offset,t);let s=C.secondsDifference(n[i].julianDate,e);if(s===0)return C.addSeconds(e,-n[i].offset,t);if(!(s<=1))return C.addSeconds(e,-n[--i].offset,t)}function Ie(e,t,n){let i=t/K.SECONDS_PER_DAY|0;return e+=i,t-=K.SECONDS_PER_DAY*i,t<0&&(e--,t+=K.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function sn(e,t,n,i,s,o,c){let u=(t-14)/12|0,p=e+4800+u,b=(1461*p/4|0)+(367*(t-2-12*u)/12|0)-(3*((p+100)/100|0)/4|0)+n-32075;i=i-12,i<0&&(i+=24);let g=o+(i*K.SECONDS_PER_HOUR+s*K.SECONDS_PER_MINUTE+c*K.SECONDS_PER_MILLISECOND);return g>=43200&&(b-=1),[b,g]}var lo=/^(\d{4})$/,po=/^(\d{4})-(\d{2})$/,mo=/^(\d{4})-?(\d{3})$/,ho=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,yo=/^(\d{4})-?(\d{2})-?(\d{2})$/,an=/([Z+\-])?(\d{2})?:?(\d{2})?$/,go=/^(\d{2})(\.\d+)?/.source+an.source,wo=/^(\d{2}):?(\d{2})(\.\d+)?/.source+an.source,bo=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+an.source,we="Invalid ISO 8601 date.";function C(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=v(e,0),t=v(t,0),n=v(n,F.UTC);let i=e|0;t=t+(e-i)*K.SECONDS_PER_DAY,Ie(i,t,this),n===F.UTC&&xt(this)}C.fromGregorianDate=function(e,t){if(!(e instanceof wt))throw new T("date must be a valid GregorianDate.");let n=sn(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return d(t)?(Ie(n[0],n[1],t),xt(t),t):new C(n[0],n[1],F.UTC)};C.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new T("date must be a valid JavaScript Date.");let n=sn(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return d(t)?(Ie(n[0],n[1],t),xt(t),t):new C(n[0],n[1],F.UTC)};C.fromIso8601=function(e,t){if(typeof e!="string")throw new T(we);e=e.replace(",",".");let n=e.split("T"),i,s=1,o=1,c=0,u=0,p=0,b=0,g=n[0],w=n[1],S,R;if(!d(g))throw new T(we);let I;if(n=g.match(yo),n!==null){if(I=g.split("-").length-1,I>0&&I!==2)throw new T(we);i=+n[1],s=+n[2],o=+n[3]}else if(n=g.match(po),n!==null)i=+n[1],s=+n[2];else if(n=g.match(lo),n!==null)i=+n[1];else{let k;if(n=g.match(mo),n!==null){if(i=+n[1],k=+n[2],R=rt(i),k<1||R&&k>366||!R&&k>365)throw new T(we)}else if(n=g.match(ho),n!==null){i=+n[1];let q=+n[2],j=+n[3]||0;if(I=g.split("-").length-1,I>0&&(!d(n[3])&&I!==1||d(n[3])&&I!==2))throw new T(we);let N=new Date(Date.UTC(i,0,4));k=q*7+j-N.getUTCDay()-3}else throw new T(we);S=new Date(Date.UTC(i,0,1)),S.setUTCDate(k),s=S.getUTCMonth()+1,o=S.getUTCDate()}if(R=rt(i),s<1||s>12||o<1||(s!==2||!R)&&o>bt[s-1]||R&&s===2&&o>_t)throw new T(we);let A;if(d(w)){if(n=w.match(bo),n!==null){if(I=w.split(":").length-1,I>0&&I!==2&&I!==3)throw new T(we);c=+n[1],u=+n[2],p=+n[3],b=+(n[4]||0)*1e3,A=5}else if(n=w.match(wo),n!==null){if(I=w.split(":").length-1,I>2)throw new T(we);c=+n[1],u=+n[2],p=+(n[3]||0)*60,A=4}else if(n=w.match(go),n!==null)c=+n[1],u=+(n[2]||0)*60,A=3;else throw new T(we);if(u>=60||p>=61||c>24||c===24&&(u>0||p>0||b>0))throw new T(we);let k=n[A],q=+n[A+1],j=+(n[A+2]||0);switch(k){case"+":c=c-q,u=u-j;break;case"-":c=c+q,u=u+j;break;case"Z":break;default:u=u+new Date(Date.UTC(i,s-1,o,c,u)).getTimezoneOffset();break}}let M=p===60;for(M&&p--;u>=60;)u-=60,c++;for(;c>=24;)c-=24,o++;for(S=R&&s===2?_t:bt[s-1];o>S;)o-=S,s++,s>12&&(s-=12,i++),S=R&&s===2?_t:bt[s-1];for(;u<0;)u+=60,c--;for(;c<0;)c+=24,o--;for(;o<1;)s--,s<1&&(s+=12,i--),S=R&&s===2?_t:bt[s-1],o+=S;let U=sn(i,s,o,c,u,p,b);return d(t)?(Ie(U[0],U[1],t),xt(t)):t=new C(U[0],U[1],F.UTC),M&&C.addSeconds(t,1,t),t};C.now=function(e){return C.fromDate(new Date,e)};var St=new C(0,0,F.TAI);C.toGregorianDate=function(e,t){if(!d(e))throw new T("julianDate is required.");let n=!1,i=qn(e,St);d(i)||(C.addSeconds(e,-1,St),i=qn(St,St),n=!0);let s=i.dayNumber,o=i.secondsOfDay;o>=43200&&(s+=1);let c=s+68569|0,u=4*c/146097|0;c=c-((146097*u+3)/4|0)|0;let p=4e3*(c+1)/1461001|0;c=c-(1461*p/4|0)+31|0;let b=80*c/2447|0,g=c-(2447*b/80|0)|0;c=b/11|0;let w=b+2-12*c|0,S=100*(u-49)+p+c|0,R=o/K.SECONDS_PER_HOUR|0,I=o-R*K.SECONDS_PER_HOUR,A=I/K.SECONDS_PER_MINUTE|0;I=I-A*K.SECONDS_PER_MINUTE;let M=I|0,U=(I-M)/K.SECONDS_PER_MILLISECOND;return R+=12,R>23&&(R-=24),n&&(M+=1),d(t)?(t.year=S,t.month=w,t.day=g,t.hour=R,t.minute=A,t.second=M,t.millisecond=U,t.isLeapSecond=n,t):new wt(S,w,g,R,A,M,U,n)};C.toDate=function(e){if(!d(e))throw new T("julianDate is required.");let t=C.toGregorianDate(e,zn),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))};C.toIso8601=function(e,t){if(!d(e))throw new T("julianDate is required.");let n=C.toGregorianDate(e,zn),i=n.year,s=n.month,o=n.day,c=n.hour,u=n.minute,p=n.second,b=n.millisecond;i===1e4&&s===1&&o===1&&c===0&&u===0&&p===0&&b===0&&(i=9999,s=12,o=31,c=24);let g;return!d(t)&&b!==0?(g=(b*.01).toString().replace(".",""),`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}.${g}Z`):!d(t)||t===0?`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}Z`:(g=(b*.01).toFixed(t).replace(".","").slice(0,t),`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}.${g}Z`)};C.clone=function(e,t){if(d(e))return d(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new C(e.dayNumber,e.secondsOfDay,F.TAI)};C.compare=function(e,t){if(!d(e))throw new T("left is required.");if(!d(t))throw new T("right is required.");let n=e.dayNumber-t.dayNumber;return n!==0?n:e.secondsOfDay-t.secondsOfDay};C.equals=function(e,t){return e===t||d(e)&&d(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay};C.equalsEpsilon=function(e,t,n){return n=v(n,0),e===t||d(e)&&d(t)&&Math.abs(C.secondsDifference(e,t))<=n};C.totalDays=function(e){if(!d(e))throw new T("julianDate is required.");return e.dayNumber+e.secondsOfDay/K.SECONDS_PER_DAY};C.secondsDifference=function(e,t){if(!d(e))throw new T("left is required.");if(!d(t))throw new T("right is required.");return(e.dayNumber-t.dayNumber)*K.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)};C.daysDifference=function(e,t){if(!d(e))throw new T("left is required.");if(!d(t))throw new T("right is required.");let n=e.dayNumber-t.dayNumber,i=(e.secondsOfDay-t.secondsOfDay)/K.SECONDS_PER_DAY;return n+i};C.computeTaiMinusUtc=function(e){We.julianDate=e;let t=C.leapSeconds,n=ke(t,We,on);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset};C.addSeconds=function(e,t,n){if(!d(e))throw new T("julianDate is required.");if(!d(t))throw new T("seconds is required.");if(!d(n))throw new T("result is required.");return Ie(e.dayNumber,e.secondsOfDay+t,n)};C.addMinutes=function(e,t,n){if(!d(e))throw new T("julianDate is required.");if(!d(t))throw new T("minutes is required.");if(!d(n))throw new T("result is required.");let i=e.secondsOfDay+t*K.SECONDS_PER_MINUTE;return Ie(e.dayNumber,i,n)};C.addHours=function(e,t,n){if(!d(e))throw new T("julianDate is required.");if(!d(t))throw new T("hours is required.");if(!d(n))throw new T("result is required.");let i=e.secondsOfDay+t*K.SECONDS_PER_HOUR;return Ie(e.dayNumber,i,n)};C.addDays=function(e,t,n){if(!d(e))throw new T("julianDate is required.");if(!d(t))throw new T("days is required.");if(!d(n))throw new T("result is required.");let i=e.dayNumber+t;return Ie(i,e.secondsOfDay,n)};C.lessThan=function(e,t){return C.compare(e,t)<0};C.lessThanOrEquals=function(e,t){return C.compare(e,t)<=0};C.greaterThan=function(e,t){return C.compare(e,t)>0};C.greaterThanOrEquals=function(e,t){return C.compare(e,t)>=0};C.prototype.clone=function(e){return C.clone(this,e)};C.prototype.equals=function(e){return C.equals(this,e)};C.prototype.equalsEpsilon=function(e,t){return C.equalsEpsilon(this,e,t)};C.prototype.toString=function(){return C.toIso8601(this)};C.leapSeconds=[new Z(new C(2441317,43210,F.TAI),10),new Z(new C(2441499,43211,F.TAI),11),new Z(new C(2441683,43212,F.TAI),12),new Z(new C(2442048,43213,F.TAI),13),new Z(new C(2442413,43214,F.TAI),14),new Z(new C(2442778,43215,F.TAI),15),new Z(new C(2443144,43216,F.TAI),16),new Z(new C(2443509,43217,F.TAI),17),new Z(new C(2443874,43218,F.TAI),18),new Z(new C(2444239,43219,F.TAI),19),new Z(new C(2444786,43220,F.TAI),20),new Z(new C(2445151,43221,F.TAI),21),new Z(new C(2445516,43222,F.TAI),22),new Z(new C(2446247,43223,F.TAI),23),new Z(new C(2447161,43224,F.TAI),24),new Z(new C(2447892,43225,F.TAI),25),new Z(new C(2448257,43226,F.TAI),26),new Z(new C(2448804,43227,F.TAI),27),new Z(new C(2449169,43228,F.TAI),28),new Z(new C(2449534,43229,F.TAI),29),new Z(new C(2450083,43230,F.TAI),30),new Z(new C(2450630,43231,F.TAI),31),new Z(new C(2451179,43232,F.TAI),32),new Z(new C(2453736,43233,F.TAI),33),new Z(new C(2454832,43234,F.TAI),34),new Z(new C(2456109,43235,F.TAI),35),new Z(new C(2457204,43236,F.TAI),36),new Z(new C(2457754,43237,F.TAI),37)];var ce=C;var lr=$e(je(),1);function _o(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var Ln=_o;function Qn(e,t){if(e===null||typeof e!="object")return e;t=v(t,!1);let n=new e.constructor;for(let i in e)if(e.hasOwnProperty(i)){let s=e[i];t&&(s=Qn(s,t)),n[i]=s}return n}var Ze=Qn;function So(){let e,t,n=new Promise(function(i,s){e=i,t=s});return{resolve:e,reject:t,promise:n}}var Ne=So;var Hn=$e(je(),1);function cn(e,t){let n;return typeof document<"u"&&(n=document),cn._implementation(e,t,n)}cn._implementation=function(e,t,n){if(!d(e))throw new T("relative uri is required.");if(!d(t)){if(typeof n>"u")return e;t=v(n.baseURI,n.location.href)}let i=new Hn.default(e);return i.scheme()!==""?i.toString():i.absoluteTo(t).toString()};var ot=cn;var $n=$e(je(),1);function xo(e,t){if(!d(e))throw new T("uri is required.");let n="",i=e.lastIndexOf("/");return i!==-1&&(n=e.substring(0,i+1)),t&&(e=new $n.default(e),e.query().length!==0&&(n+=`?${e.query()}`),e.fragment().length!==0&&(n+=`#${e.fragment()}`)),n}var Wn=xo;var Yn=$e(je(),1);function Oo(e){if(!d(e))throw new T("uri is required.");let t=new Yn.default(e);t.normalize();let n=t.path(),i=n.lastIndexOf("/");return i!==-1&&(n=n.substr(i+1)),i=n.lastIndexOf("."),i===-1?n="":n=n.substr(i+1),n}var Vn=Oo;var Zn={};function vo(e,t,n){d(t)||(t=e.width),d(n)||(n=e.height);let i=Zn[t];d(i)||(i={},Zn[t]=i);let s=i[n];if(!d(s)){let o=document.createElement("canvas");o.width=t,o.height=n,s=o.getContext("2d",{willReadFrequently:!0}),s.globalCompositeOperation="copy",i[n]=s}return s.drawImage(e,0,0,t,n),s.getImageData(0,0,t,n).data}var un=vo;var Eo=/^blob:/i;function Co(e){return y.typeOf.string("uri",e),Eo.test(e)}var Ct=Co;var _e;function To(e){d(_e)||(_e=document.createElement("a")),_e.href=window.location.href;let t=_e.host,n=_e.protocol;return _e.href=e,_e.href=_e.href,n!==_e.protocol||t!==_e.host}var Xn=To;var Ro=/^data:/i;function Po(e){return y.typeOf.string("uri",e),Ro.test(e)}var Tt=Po;function Ao(e){let t=document.createElement("script");return t.async=!0,t.src=e,new Promise((n,i)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");let s=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,s.removeChild(t),n()},t.onerror=function(o){i(o)},s.appendChild(t)})}var Jn=Ao;function Io(e){if(!d(e))throw new T("obj is required.");let t="";for(let n in e)if(e.hasOwnProperty(n)){let i=e[n],s=`${encodeURIComponent(n)}=`;if(Array.isArray(i))for(let o=0,c=i.length;o<c;++o)t+=`${s+encodeURIComponent(i[o])}&`;else t+=`${s+encodeURIComponent(i)}&`}return t=t.slice(0,-1),t}var Gn=Io;function Uo(e){if(!d(e))throw new T("queryString is required.");let t={};if(e==="")return t;let n=e.replace(/\+/g,"%20").split(/[&;]/);for(let i=0,s=n.length;i<s;++i){let o=n[i].split("="),c=decodeURIComponent(o[0]),u=o[1];d(u)?u=decodeURIComponent(u):u="";let p=t[c];typeof p=="string"?t[c]=[p,u]:Array.isArray(p)?p.push(u):t[c]=u}return t}var Kn=Uo;var Do={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},te=Object.freeze(Do);var qo={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},er=Object.freeze(qo);function Rt(e){e=v(e,v.EMPTY_OBJECT);let t=v(e.throttleByServer,!1),n=v(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=v(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=v(e.type,er.OTHER),this.serverKey=e.serverKey,this.state=te.UNISSUED,this.deferred=void 0,this.cancelled=!1}Rt.prototype.cancel=function(){this.cancelled=!0};Rt.prototype.clone=function(e){return d(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=te.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new Rt(this)};var tr=Rt;function zo(e){let t={};if(!e)return t;let n=e.split(`\r
`);for(let i=0;i<n.length;++i){let s=n[i],o=s.indexOf(": ");if(o>0){let c=s.substring(0,o),u=s.substring(o+2);t[c]=u}}return t}var nr=zo;function rr(e,t,n){this.statusCode=e,this.response=t,this.responseHeaders=n,typeof this.responseHeaders=="string"&&(this.responseHeaders=nr(this.responseHeaders))}rr.prototype.toString=function(){let e="Request has failed.";return d(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var it=rr;var Pt=$e(je(),1);function st(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(st.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});st.prototype.addEventListener=function(e,t){y.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(t);let n=this;return function(){n.removeEventListener(e,t)}};st.prototype.removeEventListener=function(e,t){y.typeOf.func("listener",e);let n=this._listeners,i=this._scopes,s=-1;for(let o=0;o<n.length;o++)if(n[o]===e&&i[o]===t){s=o;break}return s!==-1?(this._insideRaiseEvent?(this._toRemove.push(s),n[s]=void 0,i[s]=void 0):(n.splice(s,1),i.splice(s,1)),!0):!1};function Mo(e,t){return t-e}st.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,t=this._listeners,n=this._scopes,i=t.length;for(e=0;e<i;e++){let o=t[e];d(o)&&t[e].apply(n[e],arguments)}let s=this._toRemove;if(i=s.length,i>0){for(s.sort(Mo),e=0;e<i;e++){let o=s[e];t.splice(o,1),n.splice(o,1)}s.length=0}this._insideRaiseEvent=!1};var or=st;function Fe(e){y.typeOf.object("options",e),y.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(Fe.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){y.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let t=this._length;if(e<t){let n=this._array;for(let i=e;i<t;++i)n[i]=void 0;this._length=e,n.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function fn(e,t,n){let i=e[t];e[t]=e[n],e[n]=i}Fe.prototype.reserve=function(e){e=v(e,this._length),this._array.length=e};Fe.prototype.heapify=function(e){e=v(e,0);let t=this._length,n=this._comparator,i=this._array,s=-1,o=!0;for(;o;){let c=2*(e+1),u=c-1;u<t&&n(i[u],i[e])<0?s=u:s=e,c<t&&n(i[c],i[s])<0&&(s=c),s!==e?(fn(i,s,e),e=s):o=!1}};Fe.prototype.resort=function(){let e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)};Fe.prototype.insert=function(e){y.defined("element",e);let t=this._array,n=this._comparator,i=this._maximumLength,s=this._length++;for(s<t.length?t[s]=e:t.push(e);s!==0;){let c=Math.floor((s-1)/2);if(n(t[s],t[c])<0)fn(t,s,c),s=c;else break}let o;return d(i)&&this._length>i&&(o=t[i],this._length=i),o};Fe.prototype.pop=function(e){if(e=v(e,0),this._length===0)return;y.typeOf.number.lessThan("index",e,this._length);let t=this._array,n=t[e];return fn(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};var ir=Fe;function ko(e,t){return e.priority-t.priority}var V={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},Xe=20,fe=new ir({comparator:ko});fe.maximumLength=Xe;fe.reserve(Xe);var Se=[],ve={},jo=typeof document<"u"?new Pt.default(document.location.href):new Pt.default,At=new or;function ee(){}ee.maximumRequests=50;ee.maximumRequestsPerServer=18;ee.requestsByServer={};ee.throttleRequests=!0;ee.debugShowStatistics=!1;ee.requestCompletedEvent=At;Object.defineProperties(ee,{statistics:{get:function(){return V}},priorityHeapLength:{get:function(){return Xe},set:function(e){if(e<Xe)for(;fe.length>e;){let t=fe.pop();Be(t)}Xe=e,fe.maximumLength=e,fe.reserve(e)}}});function sr(e){d(e.priorityFunction)&&(e.priority=e.priorityFunction())}ee.serverHasOpenSlots=function(e,t){t=v(t,1);let n=v(ee.requestsByServer[e],ee.maximumRequestsPerServer);return ve[e]+t<=n};ee.heapHasOpenSlots=function(e){return fe.length+e<=Xe};function ar(e){return e.state===te.UNISSUED&&(e.state=te.ISSUED,e.deferred=Ne()),e.deferred.promise}function No(e){return function(t){if(e.state===te.CANCELLED)return;let n=e.deferred;--V.numberOfActiveRequests,--ve[e.serverKey],At.raiseEvent(),e.state=te.RECEIVED,e.deferred=void 0,n.resolve(t)}}function Fo(e){return function(t){e.state!==te.CANCELLED&&(++V.numberOfFailedRequests,--V.numberOfActiveRequests,--ve[e.serverKey],At.raiseEvent(t),e.state=te.FAILED,e.deferred.reject(t))}}function cr(e){let t=ar(e);return e.state=te.ACTIVE,Se.push(e),++V.numberOfActiveRequests,++V.numberOfActiveRequestsEver,++ve[e.serverKey],e.requestFunction().then(No(e)).catch(Fo(e)),t}function Be(e){let t=e.state===te.ACTIVE;if(e.state=te.CANCELLED,++V.numberOfCancelledRequests,d(e.deferred)){let n=e.deferred;e.deferred=void 0,n.reject()}t&&(--V.numberOfActiveRequests,--ve[e.serverKey],++V.numberOfCancelledActiveRequests),d(e.cancelFunction)&&e.cancelFunction()}ee.update=function(){let e,t,n=0,i=Se.length;for(e=0;e<i;++e){if(t=Se[e],t.cancelled&&Be(t),t.state!==te.ACTIVE){++n;continue}n>0&&(Se[e-n]=t)}Se.length-=n;let s=fe.internalArray,o=fe.length;for(e=0;e<o;++e)sr(s[e]);fe.resort();let c=Math.max(ee.maximumRequests-Se.length,0),u=0;for(;u<c&&fe.length>0;){if(t=fe.pop(),t.cancelled){Be(t);continue}if(t.throttleByServer&&!ee.serverHasOpenSlots(t.serverKey)){Be(t);continue}cr(t),++u}Bo()};ee.getServerKey=function(e){y.typeOf.string("url",e);let t=new Pt.default(e);t.scheme()===""&&(t=t.absoluteTo(jo),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${t.scheme()==="https"?"443":"80"}`);let i=ve[n];return d(i)||(ve[n]=0),n};ee.request=function(e){if(y.typeOf.object("request",e),y.typeOf.string("request.url",e.url),y.typeOf.func("request.requestFunction",e.requestFunction),Tt(e.url)||Ct(e.url))return At.raiseEvent(),e.state=te.RECEIVED,e.requestFunction();if(++V.numberOfAttemptedRequests,d(e.serverKey)||(e.serverKey=ee.getServerKey(e.url)),ee.throttleRequests&&e.throttleByServer&&!ee.serverHasOpenSlots(e.serverKey))return;if(!ee.throttleRequests||!e.throttle)return cr(e);if(Se.length>=ee.maximumRequests)return;sr(e);let t=fe.insert(e);if(d(t)){if(t===e)return;Be(t)}return ar(e)};function Bo(){ee.debugShowStatistics&&(V.numberOfActiveRequests===0&&V.lastNumberOfActiveRequests>0&&(V.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${V.numberOfAttemptedRequests}`),V.numberOfAttemptedRequests=0),V.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${V.numberOfCancelledRequests}`),V.numberOfCancelledRequests=0),V.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${V.numberOfCancelledActiveRequests}`),V.numberOfCancelledActiveRequests=0),V.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${V.numberOfFailedRequests}`),V.numberOfFailedRequests=0)),V.lastNumberOfActiveRequests=V.numberOfActiveRequests)}ee.clearForSpecs=function(){for(;fe.length>0;){let t=fe.pop();Be(t)}let e=Se.length;for(let t=0;t<e;++t)Be(Se[t]);Se.length=0,ve={},V.numberOfAttemptedRequests=0,V.numberOfActiveRequests=0,V.numberOfCancelledRequests=0,V.numberOfCancelledActiveRequests=0,V.numberOfFailedRequests=0,V.numberOfActiveRequestsEver=0,V.lastNumberOfActiveRequests=0};ee.numberOfActiveRequestsByServer=function(e){return ve[e]};ee.requestHeap=fe;var It=ee;var ur=$e(je(),1);var at={},Je={};at.add=function(e,t){if(!d(e))throw new T("host is required.");if(!d(t)||t<=0)throw new T("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;d(Je[n])||(Je[n]=!0)};at.remove=function(e,t){if(!d(e))throw new T("host is required.");if(!d(t)||t<=0)throw new T("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;d(Je[n])&&delete Je[n]};function Lo(e){let t=new ur.default(e);t.normalize();let n=t.authority();if(n.length!==0){if(t.authority(n),n.indexOf("@")!==-1&&(n=n.split("@")[1]),n.indexOf(":")===-1){let i=t.scheme();if(i.length===0&&(i=window.location.protocol,i=i.substring(0,i.length-1)),i==="http")n+=":80";else if(i==="https")n+=":443";else return}return n}}at.contains=function(e){if(!d(e))throw new T("url is required.");let t=Lo(e);return!!(d(t)&&d(Je[t]))};at.clear=function(){Je={}};var ln=at;var dr=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function E(e){e=v(e,v.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),y.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=xe(e.templateValues,{}),this._queryParameters=xe(e.queryParameters,{}),this.headers=xe(e.headers,{}),this.request=v(e.request,new tr),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=v(e.retryAttempts,0),this._retryCount=0,v(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function xe(e,t){return d(e)?Ze(e):t}E.createIfNeeded=function(e){return e instanceof E?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new E({url:e})};var Ge;E.supportsImageBitmapOptions=function(){return d(Ge)?Ge:typeof createImageBitmap!="function"?(Ge=Promise.resolve(!1),Ge):(Ge=E.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(t){let n={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(t,n),createImageBitmap(t)])}).then(function(t){let n=un(t[0]),i=un(t[1]);return n[1]!==i[1]}).catch(function(){return!1}),Ge)};Object.defineProperties(E,{isBlobSupported:{get:function(){return dr}}});Object.defineProperties(E.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return Vn(this._url)}},isDataUri:{get:function(){return Tt(this._url)}},isBlobUri:{get:function(){return Ct(this._url)}},isCrossOriginUrl:{get:function(){return Xn(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});E.prototype.toString=function(){return this.getUrlComponent(!0,!0)};E.prototype.parseUrl=function(e,t,n,i){let s=new lr.default(e),o=Qo(s.query());this._queryParameters=t?Dt(o,this.queryParameters,n):o,s.search(""),s.fragment(""),d(i)&&s.scheme()===""&&(s=s.absoluteTo(ot(i))),this._url=s.toString()};function Qo(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Kn(e)}function Dt(e,t,n){if(!n)return Ae(e,t);let i=Ze(e,!0);for(let s in t)if(t.hasOwnProperty(s)){let o=i[s],c=t[s];d(o)?(Array.isArray(o)||(o=i[s]=[o]),i[s]=o.concat(c)):i[s]=Array.isArray(c)?c.slice():c}return i}E.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;let n=this._url;e&&(n=`${n}${Ho(this.queryParameters)}`),n=n.replace(/%7B/g,"{").replace(/%7D/g,"}");let i=this._templateValues;return Object.keys(i).length>0&&(n=n.replace(/{(.*?)}/g,function(s,o){let c=i[o];return d(c)?encodeURIComponent(c):s})),t&&d(this.proxy)&&(n=this.proxy.getURL(n)),n};function Ho(e){let t=Object.keys(e);return t.length===0?"":t.length===1&&!d(e[t[0]])?`?${t[0]}`:`?${Gn(e)}`}E.prototype.setQueryParameters=function(e,t){t?this._queryParameters=Dt(this._queryParameters,e,!1):this._queryParameters=Dt(e,this._queryParameters,!1)};E.prototype.appendQueryParameters=function(e){this._queryParameters=Dt(e,this._queryParameters,!0)};E.prototype.setTemplateValues=function(e,t){t?this._templateValues=Ae(this._templateValues,e):this._templateValues=Ae(e,this._templateValues)};E.prototype.getDerivedResource=function(e){let t=this.clone();if(t._retryCount=0,d(e.url)){let n=v(e.preserveQueryParameters,!1);t.parseUrl(e.url,!0,n,this._url)}return d(e.queryParameters)&&(t._queryParameters=Ae(e.queryParameters,t.queryParameters)),d(e.templateValues)&&(t._templateValues=Ae(e.templateValues,t.templateValues)),d(e.headers)&&(t.headers=Ae(e.headers,t.headers)),d(e.proxy)&&(t.proxy=e.proxy),d(e.request)&&(t.request=e.request),d(e.retryCallback)&&(t.retryCallback=e.retryCallback),d(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t};E.prototype.retryOnError=function(e){let t=this.retryCallback;if(typeof t!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let n=this;return Promise.resolve(t(this,e)).then(function(i){return++n._retryCount,i})};E.prototype.clone=function(e){return d(e)?(e._url=this._url,e._queryParameters=Ze(this._queryParameters),e._templateValues=Ze(this._templateValues),e.headers=Ze(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new E({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:d(this.credits)?this.credits.slice():void 0})};E.prototype.getBaseUri=function(e){return Wn(this.getUrlComponent(e),e)};E.prototype.appendForwardSlash=function(){this._url=Ln(this._url)};E.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};E.fetchArrayBuffer=function(e){return new E(e).fetchArrayBuffer()};E.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};E.fetchBlob=function(e){return new E(e).fetchBlob()};E.prototype.fetchImage=function(e){e=v(e,v.EMPTY_OBJECT);let t=v(e.preferImageBitmap,!1),n=v(e.preferBlob,!1),i=v(e.flipY,!1),s=v(e.skipColorSpaceConversion,!1);if(pn(this.request),!dr||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return dn({resource:this,flipY:i,skipColorSpaceConversion:s,preferImageBitmap:t});let o=this.fetchBlob();if(!d(o))return;let c,u,p,b;return E.supportsImageBitmapOptions().then(function(g){return c=g,u=c&&t,o}).then(function(g){if(!d(g))return;if(b=g,u)return E.createImageBitmapFromBlob(g,{flipY:i,premultiplyAlpha:!1,skipColorSpaceConversion:s});let w=window.URL.createObjectURL(g);return p=new E({url:w}),dn({resource:p,flipY:i,skipColorSpaceConversion:s,preferImageBitmap:!1})}).then(function(g){if(d(g))return g.blob=b,u||window.URL.revokeObjectURL(p.url),g}).catch(function(g){return d(p)&&window.URL.revokeObjectURL(p.url),g.blob=b,Promise.reject(g)})};function dn(e){let t=e.resource,n=e.flipY,i=e.skipColorSpaceConversion,s=e.preferImageBitmap,o=t.request;o.url=t.url,o.requestFunction=function(){let u=!1;!t.isDataUri&&!t.isBlobUri&&(u=t.isCrossOriginUrl);let p=Ne();return E._Implementations.createImage(o,u,p,n,i,s),p.promise};let c=It.request(o);if(d(c))return c.catch(function(u){return o.state!==te.FAILED?Promise.reject(u):t.retryOnError(u).then(function(p){return p?(o.state=te.UNISSUED,o.deferred=void 0,dn({resource:t,flipY:n,skipColorSpaceConversion:i,preferImageBitmap:s})):Promise.reject(u)})})}E.fetchImage=function(e){return new E(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};E.prototype.fetchText=function(){return this.fetch({responseType:"text"})};E.fetchText=function(e){return new E(e).fetchText()};E.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(d(e))return e.then(function(t){if(d(t))return JSON.parse(t)})};E.fetchJson=function(e){return new E(e).fetchJson()};E.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};E.fetchXML=function(e){return new E(e).fetchXML()};E.prototype.fetchJsonp=function(e){e=v(e,"callback"),pn(this.request);let t;do t=`loadJsonp${X.nextRandomNumber().toString().substring(2,8)}`;while(d(window[t]));return pr(this,e,t)};function pr(e,t,n){let i={};i[t]=n,e.setQueryParameters(i);let s=e.request,o=e.url;s.url=o,s.requestFunction=function(){let u=Ne();return window[n]=function(p){u.resolve(p);try{delete window[n]}catch{window[n]=void 0}},E._Implementations.loadAndExecuteScript(o,n,u),u.promise};let c=It.request(s);if(d(c))return c.catch(function(u){return s.state!==te.FAILED?Promise.reject(u):e.retryOnError(u).then(function(p){return p?(s.state=te.UNISSUED,s.deferred=void 0,pr(e,t,n)):Promise.reject(u)})})}E.fetchJsonp=function(e){return new E(e).fetchJsonp(e.callbackParameterName)};E.prototype._makeRequest=function(e){let t=this;pn(t.request);let n=t.request,i=t.url;n.url=i,n.requestFunction=function(){let o=e.responseType,c=Ae(e.headers,t.headers),u=e.overrideMimeType,p=e.method,b=e.data,g=Ne(),w=E._Implementations.loadWithXhr(i,o,p,b,c,g,u);return d(w)&&d(w.abort)&&(n.cancelFunction=function(){w.abort()}),g.promise};let s=It.request(n);if(d(s))return s.then(function(o){return n.cancelFunction=void 0,o}).catch(function(o){return n.cancelFunction=void 0,n.state!==te.FAILED?Promise.reject(o):t.retryOnError(o).then(function(c){return c?(n.state=te.UNISSUED,n.deferred=void 0,t.fetch(e)):Promise.reject(o)})})};function pn(e){if(e.state===te.ISSUED||e.state===te.ACTIVE)throw new Oe("The Resource is already being fetched.");e.state=te.UNISSUED,e.deferred=void 0}var $o=/^data:(.*?)(;base64)?,(.*)$/;function Ut(e,t){let n=decodeURIComponent(t);return e?atob(n):n}function fr(e,t){let n=Ut(e,t),i=new ArrayBuffer(n.length),s=new Uint8Array(i);for(let o=0;o<n.length;o++)s[o]=n.charCodeAt(o);return i}function Wo(e,t){t=v(t,"");let n=e[1],i=!!e[2],s=e[3],o,c;switch(t){case"":case"text":return Ut(i,s);case"arraybuffer":return fr(i,s);case"blob":return o=fr(i,s),new Blob([o],{type:n});case"document":return c=new DOMParser,c.parseFromString(Ut(i,s),n);case"json":return JSON.parse(Ut(i,s));default:throw new T(`Unhandled responseType: ${t}`)}}E.prototype.fetch=function(e){return e=xe(e,{}),e.method="GET",this._makeRequest(e)};E.fetch=function(e){return new E(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.delete=function(e){return e=xe(e,{}),e.method="DELETE",this._makeRequest(e)};E.delete=function(e){return new E(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};E.prototype.head=function(e){return e=xe(e,{}),e.method="HEAD",this._makeRequest(e)};E.head=function(e){return new E(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.options=function(e){return e=xe(e,{}),e.method="OPTIONS",this._makeRequest(e)};E.options=function(e){return new E(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.post=function(e,t){return y.defined("data",e),t=xe(t,{}),t.method="POST",t.data=e,this._makeRequest(t)};E.post=function(e){return new E(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.put=function(e,t){return y.defined("data",e),t=xe(t,{}),t.method="PUT",t.data=e,this._makeRequest(t)};E.put=function(e){return new E(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.patch=function(e,t){return y.defined("data",e),t=xe(t,{}),t.method="PATCH",t.data=e,this._makeRequest(t)};E.patch=function(e){return new E(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E._Implementations={};E._Implementations.loadImageElement=function(e,t,n){let i=new Image;i.onload=function(){i.naturalWidth===0&&i.naturalHeight===0&&i.width===0&&i.height===0&&(i.width=300,i.height=150),n.resolve(i)},i.onerror=function(s){n.reject(s)},t&&(ln.contains(e)?i.crossOrigin="use-credentials":i.crossOrigin=""),i.src=e};E._Implementations.createImage=function(e,t,n,i,s,o){let c=e.url;E.supportsImageBitmapOptions().then(function(u){if(!(u&&o)){E._Implementations.loadImageElement(c,t,n);return}let p="blob",b="GET",g=Ne(),w=E._Implementations.loadWithXhr(c,p,b,void 0,void 0,g,void 0,void 0,void 0);return d(w)&&d(w.abort)&&(e.cancelFunction=function(){w.abort()}),g.promise.then(function(S){if(!d(S)){n.reject(new Oe(`Successfully retrieved ${c} but it contained no content.`));return}return E.createImageBitmapFromBlob(S,{flipY:i,premultiplyAlpha:!1,skipColorSpaceConversion:s})}).then(function(S){n.resolve(S)})}).catch(function(u){n.reject(u)})};E.createImageBitmapFromBlob=function(e,t){return y.defined("options",t),y.typeOf.bool("options.flipY",t.flipY),y.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),y.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};function Yo(e,t,n,i,s,o,c){fetch(e,{method:n,headers:s}).then(async u=>{if(!u.ok){let p={};u.headers.forEach((b,g)=>{p[g]=b}),o.reject(new it(u.status,u,p));return}switch(t){case"text":o.resolve(u.text());break;case"json":o.resolve(u.json());break;default:o.resolve(new Uint8Array(await u.arrayBuffer()).buffer);break}}).catch(()=>{o.reject(new it)})}var Vo=typeof XMLHttpRequest>"u";E._Implementations.loadWithXhr=function(e,t,n,i,s,o,c){let u=$o.exec(e);if(u!==null){o.resolve(Wo(u,t));return}if(Vo){Yo(e,t,n,i,s,o,c);return}let p=new XMLHttpRequest;if(ln.contains(e)&&(p.withCredentials=!0),p.open(n,e,!0),d(c)&&d(p.overrideMimeType)&&p.overrideMimeType(c),d(s))for(let g in s)s.hasOwnProperty(g)&&p.setRequestHeader(g,s[g]);d(t)&&(p.responseType=t);let b=!1;return typeof e=="string"&&(b=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),p.onload=function(){if((p.status<200||p.status>=300)&&!(b&&p.status===0)){o.reject(new it(p.status,p.response,p.getAllResponseHeaders()));return}let g=p.response,w=p.responseType;if(n==="HEAD"||n==="OPTIONS"){let R=p.getAllResponseHeaders().trim().split(/[\r\n]+/),I={};R.forEach(function(A){let M=A.split(": "),U=M.shift();I[U]=M.join(": ")}),o.resolve(I);return}if(p.status===204)o.resolve(void 0);else if(d(g)&&(!d(t)||w===t))o.resolve(g);else if(t==="json"&&typeof g=="string")try{o.resolve(JSON.parse(g))}catch(S){o.reject(S)}else(w===""||w==="document")&&d(p.responseXML)&&p.responseXML.hasChildNodes()?o.resolve(p.responseXML):(w===""||w==="text")&&d(p.responseText)?o.resolve(p.responseText):o.reject(new Oe("Invalid XMLHttpRequest response type."))},p.onerror=function(g){o.reject(new it)},p.send(i),p};E._Implementations.loadAndExecuteScript=function(e,t,n){return Jn(e,t).catch(function(i){n.reject(i)})};E._DefaultImplementations={};E._DefaultImplementations.createImage=E._Implementations.createImage;E._DefaultImplementations.loadWithXhr=E._Implementations.loadWithXhr;E._DefaultImplementations.loadAndExecuteScript=E._Implementations.loadAndExecuteScript;E.DEFAULT=Object.freeze(new E({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var Ue=E;function ut(e){e=v(e,v.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=v(e.addNewLeapSeconds,!0),d(e.data)?mr(this,e.data):mr(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}ut.fromUrl=async function(e,t){y.defined("url",e),t=v(t,v.EMPTY_OBJECT);let n=Ue.createIfNeeded(e),i;try{i=await n.fetchJson()}catch{throw new Oe(`An error occurred while retrieving the EOP data from the URL ${n.url}.`)}return new ut({addNewLeapSeconds:t.addNewLeapSeconds,data:i})};ut.NONE=Object.freeze({compute:function(e,t){return d(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new nt(0,0,0,0,0),t}});ut.prototype.compute=function(e,t){if(!d(this._samples))return;if(d(t)||(t=new nt(0,0,0,0,0)),this._samples.length===0)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;let n=this._dates,i=this._lastIndex,s=0,o=0;if(d(i)){let u=n[i],p=n[i+1],b=ce.lessThanOrEquals(u,e),g=!d(p),w=g||ce.greaterThanOrEquals(p,e);if(b&&w)return s=i,!g&&p.equals(e)&&++s,o=s+1,yr(this,n,this._samples,e,s,o,t),t}let c=ke(n,e,ce.compare,this._dateColumn);return c>=0?(c<n.length-1&&n[c+1].equals(e)&&++c,s=c,o=c):(o=~c,s=o-1,s<0&&(s=0)),this._lastIndex=s,yr(this,n,this._samples,e,s,o,t),t};function Zo(e,t){return ce.compare(e.julianDate,t)}function mr(e,t){if(!d(t.columnNames))throw new Oe("Error in loaded EOP data: The columnNames property is required.");if(!d(t.samples))throw new Oe("Error in loaded EOP data: The samples property is required.");let n=t.columnNames.indexOf("modifiedJulianDateUtc"),i=t.columnNames.indexOf("xPoleWanderRadians"),s=t.columnNames.indexOf("yPoleWanderRadians"),o=t.columnNames.indexOf("ut1MinusUtcSeconds"),c=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),p=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||i<0||s<0||o<0||c<0||u<0||p<0)throw new Oe("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let b=e._samples=t.samples,g=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=i,e._yPoleWanderRadiansColumn=s,e._ut1MinusUtcSecondsColumn=o,e._xCelestialPoleOffsetRadiansColumn=c,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=p,e._columnCount=t.columnNames.length,e._lastIndex=void 0;let w,S=e._addNewLeapSeconds;for(let R=0,I=b.length;R<I;R+=e._columnCount){let A=b[R+n],M=b[R+p],U=A+K.MODIFIED_JULIAN_DATE_DIFFERENCE,k=new ce(U,M,F.TAI);if(g.push(k),S){if(M!==w&&d(w)){let q=ce.leapSeconds,j=ke(q,k,Zo);if(j<0){let N=new Z(k,M);q.splice(~j,0,N)}}w=M}}}function hr(e,t,n,i,s){let o=n*i;s.xPoleWander=t[o+e._xPoleWanderRadiansColumn],s.yPoleWander=t[o+e._yPoleWanderRadiansColumn],s.xPoleOffset=t[o+e._xCelestialPoleOffsetRadiansColumn],s.yPoleOffset=t[o+e._yCelestialPoleOffsetRadiansColumn],s.ut1MinusUtc=t[o+e._ut1MinusUtcSecondsColumn]}function ct(e,t,n){return t+e*(n-t)}function yr(e,t,n,i,s,o,c){let u=e._columnCount;if(o>t.length-1)return c.xPoleWander=0,c.yPoleWander=0,c.xPoleOffset=0,c.yPoleOffset=0,c.ut1MinusUtc=0,c;let p=t[s],b=t[o];if(p.equals(b)||i.equals(p))return hr(e,n,s,u,c),c;if(i.equals(b))return hr(e,n,o,u,c),c;let g=ce.secondsDifference(i,p)/ce.secondsDifference(b,p),w=s*u,S=o*u,R=n[w+e._ut1MinusUtcSecondsColumn],I=n[S+e._ut1MinusUtcSecondsColumn],A=I-R;if(A>.5||A<-.5){let M=n[w+e._taiMinusUtcSecondsColumn],U=n[S+e._taiMinusUtcSecondsColumn];M!==U&&(b.equals(i)?R=I:I-=U-M)}return c.xPoleWander=ct(g,n[w+e._xPoleWanderRadiansColumn],n[S+e._xPoleWanderRadiansColumn]),c.yPoleWander=ct(g,n[w+e._yPoleWanderRadiansColumn],n[S+e._yPoleWanderRadiansColumn]),c.xPoleOffset=ct(g,n[w+e._xCelestialPoleOffsetRadiansColumn],n[S+e._xCelestialPoleOffsetRadiansColumn]),c.yPoleOffset=ct(g,n[w+e._yCelestialPoleOffsetRadiansColumn],n[S+e._yCelestialPoleOffsetRadiansColumn]),c.ut1MinusUtc=ct(g,R,I),c}var gr=ut;function le(e,t,n){this.heading=v(e,0),this.pitch=v(t,0),this.roll=v(n,0)}le.fromQuaternion=function(e,t){if(!d(e))throw new T("quaternion is required");d(t)||(t=new le);let n=2*(e.w*e.y-e.z*e.x),i=1-2*(e.x*e.x+e.y*e.y),s=2*(e.w*e.x+e.y*e.z),o=1-2*(e.y*e.y+e.z*e.z),c=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(c,o),t.roll=Math.atan2(s,i),t.pitch=-X.asinClamped(n),t};le.fromDegrees=function(e,t,n,i){if(!d(e))throw new T("heading is required");if(!d(t))throw new T("pitch is required");if(!d(n))throw new T("roll is required");return d(i)||(i=new le),i.heading=e*X.RADIANS_PER_DEGREE,i.pitch=t*X.RADIANS_PER_DEGREE,i.roll=n*X.RADIANS_PER_DEGREE,i};le.clone=function(e,t){if(d(e))return d(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new le(e.heading,e.pitch,e.roll)};le.equals=function(e,t){return e===t||d(e)&&d(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll};le.equalsEpsilon=function(e,t,n,i){return e===t||d(e)&&d(t)&&X.equalsEpsilon(e.heading,t.heading,n,i)&&X.equalsEpsilon(e.pitch,t.pitch,n,i)&&X.equalsEpsilon(e.roll,t.roll,n,i)};le.prototype.clone=function(e){return le.clone(this,e)};le.prototype.equals=function(e){return le.equals(this,e)};le.prototype.equalsEpsilon=function(e,t,n){return le.equalsEpsilon(this,e,t,n)};le.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var mn=le;var wr=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function Xo(){let e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){let i=e[t].getAttribute("src"),s=wr.exec(i);if(s!==null)return s[1]}}var qt;function br(e){return typeof document>"u"?e:(d(qt)||(qt=document.createElement("a")),qt.href=e,qt.href)}var Le;function _r(){if(d(Le))return Le;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:d(import.meta?.url)?e=ot(".",import.meta.url):typeof define=="object"&&d(define.amd)&&!define.amd.toUrlUndefined&&d(dt.toUrl)?e=ot("..",Qe("Core/buildModuleUrl.js")):e=Xo(),!d(e))throw new T("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Le=new Ue({url:br(e)}),Le.appendForwardSlash(),Le}function Jo(e){return br(dt.toUrl(`../${e}`))}function Sr(e){return _r().getDerivedResource({url:e}).url}var zt;function Qe(e){return d(zt)||(typeof define=="object"&&d(define.amd)&&!define.amd.toUrlUndefined&&d(dt.toUrl)?zt=Jo:zt=Sr),zt(e)}Qe._cesiumScriptRegex=wr;Qe._buildModuleUrlFromBaseUrl=Sr;Qe._clearBaseResource=function(){Le=void 0};Qe.setBaseUrl=function(e){Le=Ue.DEFAULT.getDerivedResource({url:e})};Qe.getCesiumBaseUrl=_r;var xr=Qe;function Go(e,t,n){this.x=e,this.y=t,this.s=n}var Mt=Go;function gn(e){e=v(e,v.EMPTY_OBJECT),this._xysFileUrlTemplate=Ue.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=v(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=v(e.sampleZeroJulianEphemerisDate,24423965e-1),this._sampleZeroDateTT=new ce(this._sampleZeroJulianEphemerisDate,0,F.TAI),this._stepSizeDays=v(e.stepSizeDays,1),this._samplesPerXysFile=v(e.samplesPerXysFile,1e3),this._totalSamples=v(e.totalSamples,27426),this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let t=this._interpolationOrder,n=this._denominators=new Array(t+1),i=this._xTable=new Array(t+1),s=Math.pow(this._stepSizeDays,t);for(let o=0;o<=t;++o){n[o]=s,i[o]=o*this._stepSizeDays;for(let c=0;c<=t;++c)c!==o&&(n[o]*=o-c);n[o]=1/n[o]}this._work=new Array(t+1),this._coef=new Array(t+1)}var Ko=new ce(0,0,F.TAI);function hn(e,t,n){let i=Ko;return i.dayNumber=t,i.secondsOfDay=n,ce.daysDifference(i,e._sampleZeroDateTT)}gn.prototype.preload=function(e,t,n,i){let s=hn(this,e,t),o=hn(this,n,i),c=s/this._stepSizeDays-this._interpolationOrder/2|0;c<0&&(c=0);let u=o/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let p=c/this._samplesPerXysFile|0,b=u/this._samplesPerXysFile|0,g=[];for(let w=p;w<=b;++w)g.push(yn(this,w));return Promise.all(g)};gn.prototype.computeXysRadians=function(e,t,n){let i=hn(this,e,t);if(i<0)return;let s=i/this._stepSizeDays|0;if(s>=this._totalSamples)return;let o=this._interpolationOrder,c=s-(o/2|0);c<0&&(c=0);let u=c+o;u>=this._totalSamples&&(u=this._totalSamples-1,c=u-o,c<0&&(c=0));let p=!1,b=this._samples;if(d(b[c*3])||(yn(this,c/this._samplesPerXysFile|0),p=!0),d(b[u*3])||(yn(this,u/this._samplesPerXysFile|0),p=!0),p)return;d(n)?(n.x=0,n.y=0,n.s=0):n=new Mt(0,0,0);let g=i-c*this._stepSizeDays,w=this._work,S=this._denominators,R=this._coef,I=this._xTable,A,M;for(A=0;A<=o;++A)w[A]=g-I[A];for(A=0;A<=o;++A){for(R[A]=1,M=0;M<=o;++M)M!==A&&(R[A]*=w[M]);R[A]*=S[A];let U=(c+A)*3;n.x+=R[A]*b[U++],n.y+=R[A]*b[U++],n.s+=R[A]*b[U]}return n};function yn(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n,i=e._xysFileUrlTemplate;d(i)?n=i.getDerivedResource({templateValues:{0:t}}):n=new Ue({url:xr(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});let s=n.fetchJson().then(function(o){e._chunkDownloadsInProgress[t]=!1;let c=e._samples,u=o.samples,p=t*e._samplesPerXysFile*3;for(let b=0,g=u.length;b<g;++b)c[p+b]=u[b]});return e._chunkDownloadsInProgress[t]=s,s}var Or=gn;function x(e,t,n,i){this.x=v(e,0),this.y=v(t,0),this.z=v(n,0),this.w=v(i,0)}var ft=new f;x.fromAxisAngle=function(e,t,n){y.typeOf.object("axis",e),y.typeOf.number("angle",t);let i=t/2,s=Math.sin(i);ft=f.normalize(e,ft);let o=ft.x*s,c=ft.y*s,u=ft.z*s,p=Math.cos(i);return d(n)?(n.x=o,n.y=c,n.z=u,n.w=p,n):new x(o,c,u,p)};var ei=[1,2,0],ti=new Array(3);x.fromRotationMatrix=function(e,t){y.typeOf.object("matrix",e);let n,i,s,o,c,u=e[B.COLUMN0ROW0],p=e[B.COLUMN1ROW1],b=e[B.COLUMN2ROW2],g=u+p+b;if(g>0)n=Math.sqrt(g+1),c=.5*n,n=.5/n,i=(e[B.COLUMN1ROW2]-e[B.COLUMN2ROW1])*n,s=(e[B.COLUMN2ROW0]-e[B.COLUMN0ROW2])*n,o=(e[B.COLUMN0ROW1]-e[B.COLUMN1ROW0])*n;else{let w=ei,S=0;p>u&&(S=1),b>u&&b>p&&(S=2);let R=w[S],I=w[R];n=Math.sqrt(e[B.getElementIndex(S,S)]-e[B.getElementIndex(R,R)]-e[B.getElementIndex(I,I)]+1);let A=ti;A[S]=.5*n,n=.5/n,c=(e[B.getElementIndex(I,R)]-e[B.getElementIndex(R,I)])*n,A[R]=(e[B.getElementIndex(R,S)]+e[B.getElementIndex(S,R)])*n,A[I]=(e[B.getElementIndex(I,S)]+e[B.getElementIndex(S,I)])*n,i=-A[0],s=-A[1],o=-A[2]}return d(t)?(t.x=i,t.y=s,t.z=o,t.w=c,t):new x(i,s,o,c)};var vr=new x,Er=new x,wn=new x,Cr=new x;x.fromHeadingPitchRoll=function(e,t){return y.typeOf.object("headingPitchRoll",e),Cr=x.fromAxisAngle(f.UNIT_X,e.roll,vr),wn=x.fromAxisAngle(f.UNIT_Y,-e.pitch,t),t=x.multiply(wn,Cr,wn),Er=x.fromAxisAngle(f.UNIT_Z,-e.heading,vr),x.multiply(Er,t,t)};var kt=new f,bn=new f,be=new x,Tr=new x,jt=new x;x.packedLength=4;x.pack=function(e,t,n){return y.typeOf.object("value",e),y.defined("array",t),n=v(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t};x.unpack=function(e,t,n){return y.defined("array",e),t=v(t,0),d(n)||(n=new x),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n};x.packedInterpolationLength=3;x.convertPackedArrayForInterpolation=function(e,t,n,i){x.unpack(e,n*4,jt),x.conjugate(jt,jt);for(let s=0,o=n-t+1;s<o;s++){let c=s*3;x.unpack(e,(t+s)*4,be),x.multiply(be,jt,be),be.w<0&&x.negate(be,be),x.computeAxis(be,kt);let u=x.computeAngle(be);d(i)||(i=[]),i[c]=kt.x*u,i[c+1]=kt.y*u,i[c+2]=kt.z*u}};x.unpackInterpolationResult=function(e,t,n,i,s){d(s)||(s=new x),f.fromArray(e,0,bn);let o=f.magnitude(bn);return x.unpack(t,i*4,Tr),o===0?x.clone(x.IDENTITY,be):x.fromAxisAngle(bn,o,be),x.multiply(be,Tr,s)};x.clone=function(e,t){if(d(e))return d(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new x(e.x,e.y,e.z,e.w)};x.conjugate=function(e,t){return y.typeOf.object("quaternion",e),y.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t};x.magnitudeSquared=function(e){return y.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};x.magnitude=function(e){return Math.sqrt(x.magnitudeSquared(e))};x.normalize=function(e,t){y.typeOf.object("result",t);let n=1/x.magnitude(e),i=e.x*n,s=e.y*n,o=e.z*n,c=e.w*n;return t.x=i,t.y=s,t.z=o,t.w=c,t};x.inverse=function(e,t){y.typeOf.object("result",t);let n=x.magnitudeSquared(e);return t=x.conjugate(e,t),x.multiplyByScalar(t,1/n,t)};x.add=function(e,t,n){return y.typeOf.object("left",e),y.typeOf.object("right",t),y.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n};x.subtract=function(e,t,n){return y.typeOf.object("left",e),y.typeOf.object("right",t),y.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n};x.negate=function(e,t){return y.typeOf.object("quaternion",e),y.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t};x.dot=function(e,t){return y.typeOf.object("left",e),y.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w};x.multiply=function(e,t,n){y.typeOf.object("left",e),y.typeOf.object("right",t),y.typeOf.object("result",n);let i=e.x,s=e.y,o=e.z,c=e.w,u=t.x,p=t.y,b=t.z,g=t.w,w=c*u+i*g+s*b-o*p,S=c*p-i*b+s*g+o*u,R=c*b+i*p-s*u+o*g,I=c*g-i*u-s*p-o*b;return n.x=w,n.y=S,n.z=R,n.w=I,n};x.multiplyByScalar=function(e,t,n){return y.typeOf.object("quaternion",e),y.typeOf.number("scalar",t),y.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n};x.divideByScalar=function(e,t,n){return y.typeOf.object("quaternion",e),y.typeOf.number("scalar",t),y.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n};x.computeAxis=function(e,t){y.typeOf.object("quaternion",e),y.typeOf.object("result",t);let n=e.w;if(Math.abs(n-1)<X.EPSILON6||Math.abs(n+1)<X.EPSILON6)return t.x=1,t.y=t.z=0,t;let i=1/Math.sqrt(1-n*n);return t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t};x.computeAngle=function(e){return y.typeOf.object("quaternion",e),Math.abs(e.w-1)<X.EPSILON6?0:2*Math.acos(e.w)};var _n=new x;x.lerp=function(e,t,n,i){return y.typeOf.object("start",e),y.typeOf.object("end",t),y.typeOf.number("t",n),y.typeOf.object("result",i),_n=x.multiplyByScalar(t,n,_n),i=x.multiplyByScalar(e,1-n,i),x.add(_n,i,i)};var Rr=new x,Sn=new x,xn=new x;x.slerp=function(e,t,n,i){y.typeOf.object("start",e),y.typeOf.object("end",t),y.typeOf.number("t",n),y.typeOf.object("result",i);let s=x.dot(e,t),o=t;if(s<0&&(s=-s,o=Rr=x.negate(t,Rr)),1-s<X.EPSILON6)return x.lerp(e,o,n,i);let c=Math.acos(s);return Sn=x.multiplyByScalar(e,Math.sin((1-n)*c),Sn),xn=x.multiplyByScalar(o,Math.sin(n*c),xn),i=x.add(Sn,xn,i),x.multiplyByScalar(i,1/Math.sin(c),i)};x.log=function(e,t){y.typeOf.object("quaternion",e),y.typeOf.object("result",t);let n=X.acosClamped(e.w),i=0;return n!==0&&(i=n/Math.sin(n)),f.multiplyByScalar(e,i,t)};x.exp=function(e,t){y.typeOf.object("cartesian",e),y.typeOf.object("result",t);let n=f.magnitude(e),i=0;return n!==0&&(i=Math.sin(n)/n),t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t.w=Math.cos(n),t};var ni=new f,ri=new f,lt=new x,Ke=new x;x.computeInnerQuadrangle=function(e,t,n,i){y.typeOf.object("q0",e),y.typeOf.object("q1",t),y.typeOf.object("q2",n),y.typeOf.object("result",i);let s=x.conjugate(t,lt);x.multiply(s,n,Ke);let o=x.log(Ke,ni);x.multiply(s,e,Ke);let c=x.log(Ke,ri);return f.add(o,c,o),f.multiplyByScalar(o,.25,o),f.negate(o,o),x.exp(o,lt),x.multiply(t,lt,i)};x.squad=function(e,t,n,i,s,o){y.typeOf.object("q0",e),y.typeOf.object("q1",t),y.typeOf.object("s0",n),y.typeOf.object("s1",i),y.typeOf.number("t",s),y.typeOf.object("result",o);let c=x.slerp(e,t,s,lt),u=x.slerp(n,i,s,Ke);return x.slerp(c,u,2*s*(1-s),o)};var oi=new x,Pr=1.9011074535173003,Nt=tt.supportsTypedArrays()?new Float32Array(8):[],Ft=tt.supportsTypedArrays()?new Float32Array(8):[],Ee=tt.supportsTypedArrays()?new Float32Array(8):[],Ce=tt.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let t=e+1,n=2*t+1;Nt[e]=1/(t*n),Ft[e]=t/n}Nt[7]=Pr/(8*17);Ft[7]=Pr*8/17;x.fastSlerp=function(e,t,n,i){y.typeOf.object("start",e),y.typeOf.object("end",t),y.typeOf.number("t",n),y.typeOf.object("result",i);let s=x.dot(e,t),o;s>=0?o=1:(o=-1,s=-s);let c=s-1,u=1-n,p=n*n,b=u*u;for(let R=7;R>=0;--R)Ee[R]=(Nt[R]*p-Ft[R])*c,Ce[R]=(Nt[R]*b-Ft[R])*c;let g=o*n*(1+Ee[0]*(1+Ee[1]*(1+Ee[2]*(1+Ee[3]*(1+Ee[4]*(1+Ee[5]*(1+Ee[6]*(1+Ee[7])))))))),w=u*(1+Ce[0]*(1+Ce[1]*(1+Ce[2]*(1+Ce[3]*(1+Ce[4]*(1+Ce[5]*(1+Ce[6]*(1+Ce[7])))))))),S=x.multiplyByScalar(e,w,oi);return x.multiplyByScalar(t,g,i),x.add(S,i,i)};x.fastSquad=function(e,t,n,i,s,o){y.typeOf.object("q0",e),y.typeOf.object("q1",t),y.typeOf.object("s0",n),y.typeOf.object("s1",i),y.typeOf.number("t",s),y.typeOf.object("result",o);let c=x.fastSlerp(e,t,s,lt),u=x.fastSlerp(n,i,s,Ke);return x.fastSlerp(c,u,2*s*(1-s),o)};x.equals=function(e,t){return e===t||d(e)&&d(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w};x.equalsEpsilon=function(e,t,n){return n=v(n,0),e===t||d(e)&&d(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n};x.ZERO=Object.freeze(new x(0,0,0,0));x.IDENTITY=Object.freeze(new x(0,0,0,1));x.prototype.clone=function(e){return x.clone(this,e)};x.prototype.equals=function(e){return x.equals(this,e)};x.prototype.equalsEpsilon=function(e,t){return x.equalsEpsilon(this,e,t)};x.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var He=x;var H={},On={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},et={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},vn={},me={east:new f,north:new f,up:new f,west:new f,south:new f,down:new f},De=new f,qe=new f,ze=new f;H.localFrameToFixedFrameGenerator=function(e,t){if(!On.hasOwnProperty(e)||!On[e].hasOwnProperty(t))throw new T("firstAxis and secondAxis must be east, north, up, west, south or down.");let n=On[e][t],i,s=e+t;return d(vn[s])?i=vn[s]:(i=function(o,c,u){if(!d(o))throw new T("origin is required.");if(isNaN(o.x)||isNaN(o.y)||isNaN(o.z))throw new T("origin has a NaN component");if(d(u)||(u=new L),f.equalsEpsilon(o,f.ZERO,X.EPSILON14))f.unpack(et[e],0,De),f.unpack(et[t],0,qe),f.unpack(et[n],0,ze);else if(X.equalsEpsilon(o.x,0,X.EPSILON14)&&X.equalsEpsilon(o.y,0,X.EPSILON14)){let p=X.sign(o.z);f.unpack(et[e],0,De),e!=="east"&&e!=="west"&&f.multiplyByScalar(De,p,De),f.unpack(et[t],0,qe),t!=="east"&&t!=="west"&&f.multiplyByScalar(qe,p,qe),f.unpack(et[n],0,ze),n!=="east"&&n!=="west"&&f.multiplyByScalar(ze,p,ze)}else{c=v(c,Pe.WGS84),c.geodeticSurfaceNormal(o,me.up);let p=me.up,b=me.east;b.x=-o.y,b.y=o.x,b.z=0,f.normalize(b,me.east),f.cross(p,b,me.north),f.multiplyByScalar(me.up,-1,me.down),f.multiplyByScalar(me.east,-1,me.west),f.multiplyByScalar(me.north,-1,me.south),De=me[e],qe=me[t],ze=me[n]}return u[0]=De.x,u[1]=De.y,u[2]=De.z,u[3]=0,u[4]=qe.x,u[5]=qe.y,u[6]=qe.z,u[7]=0,u[8]=ze.x,u[9]=ze.y,u[10]=ze.z,u[11]=0,u[12]=o.x,u[13]=o.y,u[14]=o.z,u[15]=1,u},vn[s]=i),i};H.eastNorthUpToFixedFrame=H.localFrameToFixedFrameGenerator("east","north");H.northEastDownToFixedFrame=H.localFrameToFixedFrameGenerator("north","east");H.northUpEastToFixedFrame=H.localFrameToFixedFrameGenerator("north","up");H.northWestUpToFixedFrame=H.localFrameToFixedFrameGenerator("north","west");var ii=new He,si=new f(1,1,1),ai=new L;H.headingPitchRollToFixedFrame=function(e,t,n,i,s){y.typeOf.object("HeadingPitchRoll",t),i=v(i,H.eastNorthUpToFixedFrame);let o=He.fromHeadingPitchRoll(t,ii),c=L.fromTranslationQuaternionRotationScale(f.ZERO,o,si,ai);return s=i(e,n,s),L.multiply(s,c,s)};var ci=new L,ui=new B;H.headingPitchRollQuaternion=function(e,t,n,i,s){y.typeOf.object("HeadingPitchRoll",t);let o=H.headingPitchRollToFixedFrame(e,t,n,i,ci),c=L.getMatrix3(o,ui);return He.fromRotationMatrix(c,s)};var fi=new f(1,1,1),li=new f,Ar=new L,di=new L,pi=new B,mi=new He;H.fixedFrameToHeadingPitchRoll=function(e,t,n,i){y.defined("transform",e),t=v(t,Pe.WGS84),n=v(n,H.eastNorthUpToFixedFrame),d(i)||(i=new mn);let s=L.getTranslation(e,li);if(f.equals(s,f.ZERO))return i.heading=0,i.pitch=0,i.roll=0,i;let o=L.inverseTransformation(n(s,t,Ar),Ar),c=L.setScale(e,fi,di);c=L.setTranslation(c,f.ZERO,c),o=L.multiply(o,c,o);let u=He.fromRotationMatrix(L.getMatrix3(o,pi),mi);return u=He.normalize(u,u),mn.fromQuaternion(u,i)};var hi=6*3600+41*60+50.54841,yi=8640184812866e-6,gi=.093104,wi=-62e-7,bi=11772758384668e-32,_i=72921158553e-15,Si=X.TWO_PI/86400,Bt=new ce;H.computeTemeToPseudoFixedMatrix=function(e,t){if(!d(e))throw new T("date is required.");Bt=ce.addSeconds(e,-ce.computeTaiMinusUtc(e),Bt);let n=Bt.dayNumber,i=Bt.secondsOfDay,s,o=n-2451545;i>=43200?s=(o+.5)/K.DAYS_PER_JULIAN_CENTURY:s=(o-.5)/K.DAYS_PER_JULIAN_CENTURY;let u=(hi+s*(yi+s*(gi+s*wi)))*Si%X.TWO_PI,p=_i+bi*(n-24515455e-1),b=(i+K.SECONDS_PER_DAY*.5)%K.SECONDS_PER_DAY,g=u+p*b,w=Math.cos(g),S=Math.sin(g);return d(t)?(t[0]=w,t[1]=-S,t[2]=0,t[3]=S,t[4]=w,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new B(w,S,0,-S,w,0,0,0,1)};H.iau2006XysData=new Or;H.earthOrientationParameters=gr.NONE;var Tn=32.184,xi=2451545;H.preloadIcrfFixed=function(e){let t=e.start.dayNumber,n=e.start.secondsOfDay+Tn,i=e.stop.dayNumber,s=e.stop.secondsOfDay+Tn;return H.iau2006XysData.preload(t,n,i,s)};H.computeIcrfToFixedMatrix=function(e,t){if(!d(e))throw new T("date is required.");d(t)||(t=new B);let n=H.computeFixedToIcrfMatrix(e,t);if(d(n))return B.transpose(n,t)};var Oi=new Mt(0,0,0),vi=new nt(0,0,0,0,0,0),En=new B,Cn=new B;H.computeFixedToIcrfMatrix=function(e,t){if(!d(e))throw new T("date is required.");d(t)||(t=new B);let n=H.earthOrientationParameters.compute(e,vi);if(!d(n))return;let i=e.dayNumber,s=e.secondsOfDay+Tn,o=H.iau2006XysData.computeXysRadians(i,s,Oi);if(!d(o))return;let c=o.x+n.xPoleOffset,u=o.y+n.yPoleOffset,p=1/(1+Math.sqrt(1-c*c-u*u)),b=En;b[0]=1-p*c*c,b[3]=-p*c*u,b[6]=c,b[1]=-p*c*u,b[4]=1-p*u*u,b[7]=u,b[2]=-c,b[5]=-u,b[8]=1-p*(c*c+u*u);let g=B.fromRotationZ(-o.s,Cn),w=B.multiply(b,g,En),S=e.dayNumber,R=e.secondsOfDay-ce.computeTaiMinusUtc(e)+n.ut1MinusUtc,I=S-2451545,A=R/K.SECONDS_PER_DAY,M=.779057273264+A+.00273781191135448*(I+A);M=M%1*X.TWO_PI;let U=B.fromRotationZ(M,Cn),k=B.multiply(w,U,En),q=Math.cos(n.xPoleWander),j=Math.cos(n.yPoleWander),N=Math.sin(n.xPoleWander),$=Math.sin(n.yPoleWander),re=i-xi+s/K.SECONDS_PER_DAY;re/=36525;let oe=-47e-6*re*X.RADIANS_PER_DEGREE/3600,J=Math.cos(oe),W=Math.sin(oe),Q=Cn;return Q[0]=q*J,Q[1]=q*W,Q[2]=N,Q[3]=-j*W+$*N*J,Q[4]=j*J+$*N*W,Q[5]=-$*q,Q[6]=-$*W-j*N*J,Q[7]=$*J-j*N*W,Q[8]=j*q,B.multiply(k,Q,t)};var Ei=new mt;H.pointToWindowCoordinates=function(e,t,n,i){return i=H.pointToGLWindowCoordinates(e,t,n,i),i.y=2*t[5]-i.y,i};H.pointToGLWindowCoordinates=function(e,t,n,i){if(!d(e))throw new T("modelViewProjectionMatrix is required.");if(!d(t))throw new T("viewportTransformation is required.");if(!d(n))throw new T("point is required.");d(i)||(i=new Qt);let s=Ei;return L.multiplyByVector(e,mt.fromElements(n.x,n.y,n.z,1,s),s),mt.multiplyByScalar(s,1/s.w,s),L.multiplyByVector(t,s,s),Qt.fromCartesian4(s,i)};var Ci=new f,Ti=new f,Ri=new f;H.rotationMatrixFromPositionVelocity=function(e,t,n,i){if(!d(e))throw new T("position is required.");if(!d(t))throw new T("velocity is required.");let s=v(n,Pe.WGS84).geodeticSurfaceNormal(e,Ci),o=f.cross(t,s,Ti);f.equalsEpsilon(o,f.ZERO,X.EPSILON6)&&(o=f.clone(f.UNIT_X,o));let c=f.cross(o,t,Ri);return f.normalize(c,c),f.cross(t,c,o),f.negate(o,o),f.normalize(o,o),d(i)||(i=new B),i[0]=t.x,i[1]=t.y,i[2]=t.z,i[3]=o.x,i[4]=o.y,i[5]=o.z,i[6]=c.x,i[7]=c.y,i[8]=c.z,i};var Ir=new L(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Ur=new Re,Rn=new f,Pi=new f,Ai=new B,Pn=new L,Dr=new L;H.basisTo2D=function(e,t,n){if(!d(e))throw new T("projection is required.");if(!d(t))throw new T("matrix is required.");if(!d(n))throw new T("result is required.");let i=L.getTranslation(t,Pi),s=e.ellipsoid,o;if(f.equals(i,f.ZERO))o=f.clone(f.ZERO,Rn);else{let g=s.cartesianToCartographic(i,Ur);o=e.project(g,Rn),f.fromElements(o.z,o.x,o.y,o)}let c=H.eastNorthUpToFixedFrame(i,s,Pn),u=L.inverseTransformation(c,Dr),p=L.getMatrix3(t,Ai),b=L.multiplyByMatrix3(u,p,n);return L.multiply(Ir,b,n),L.setTranslation(n,o,n),n};H.wgs84To2DModelMatrix=function(e,t,n){if(!d(e))throw new T("projection is required.");if(!d(t))throw new T("center is required.");if(!d(n))throw new T("result is required.");let i=e.ellipsoid,s=H.eastNorthUpToFixedFrame(t,i,Pn),o=L.inverseTransformation(s,Dr),c=i.cartesianToCartographic(t,Ur),u=e.project(c,Rn);f.fromElements(u.z,u.x,u.y,u);let p=L.fromTranslation(u,Pn);return L.multiply(Ir,o,n),L.multiply(p,n,n),n};var uu=H;export{Ht as a,gt as b,An as c,es as d,He as e,Ue as f,xr as g,uu as h};

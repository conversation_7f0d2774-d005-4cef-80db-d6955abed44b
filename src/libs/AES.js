import CryptoJS from "crypto-js";
export default {
  //随机生成指定数量的16进制key
  generatekey(num) {
    let library =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let key = "";
    for (var i = 0; i < num; i++) {
      let randomPoz = Math.floor(Math.random() * library.length);
      key += library.substring(randomPoz, randomPoz + 1);
    }
    return key;
  },

  //加密
  encrypt(word, keyStr) {
    keyStr = keyStr ? keyStr : "zhcx@123456"; //判断是否存在ksy，不存在就用定义好的key
    var key = CryptoJS.enc.Utf8.parse(keyStr);
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
  },
  //解密
  decrypt(word, keyStr) {
    keyStr = keyStr ? keyStr : "zhcx@123456";
    var key = CryptoJS.enc.Utf8.parse(keyStr);
    var decrypt = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return CryptoJS.enc.Utf8.stringify(decrypt).toString();
  },

  /**
   * @description: 加密字符串
   * @param str 明文字符串
   * @author: wzd
   * @date: 2021-06-29 18:21:50
   */
  encryptUtils(str) {
    //定义前后端加密与解密的key
    let KEY = "gis2011@D#GIS&12";
    //定义前后端加密与解密的盐值
    let SIGN = "gis2011@D#GIS&34";
    let sign = CryptoJS.enc.Utf8.parse(SIGN);
    let options = {
      iv: sign,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Iso10126,
    };
    let key = CryptoJS.enc.Utf8.parse(KEY);
    let srcs = CryptoJS.enc.Utf8.parse(str);
    let encrypted = CryptoJS.AES.encrypt(srcs, key, options);
    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
  },

  /**
   * @description: 解密字符串
   * @param str 密文字符串
   * @author: wzd
   * @date: 2021-06-29 18:47:34
   */
  decryptUtils(str) {
    //定义前后端加密与解密的key
    let KEY = "gis2011@D#GIS&12";
    //定义前后端加密与解密的盐值
    let SIGN = "gis2011@D#GIS&34";
    let sign = CryptoJS.enc.Utf8.parse(SIGN);
    let options = {
      iv: sign,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Iso10126,
    };
    let key = CryptoJS.enc.Utf8.parse(KEY);
    let base64 = CryptoJS.enc.Base64.parse(str);
    let src = CryptoJS.enc.Base64.stringify(base64);
    let decrypt = CryptoJS.AES.decrypt(src, key, options);
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
  },
};

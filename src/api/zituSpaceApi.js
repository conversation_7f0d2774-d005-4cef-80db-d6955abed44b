import {
    getRequest,
    postRequest,
    postRequestpdfORimg
} from "@/utils/axios";

//获取图层字段
export function getLayerFieldBylayerId(params) {
    return getRequest("/wfs/getSelectDisplayColmsByLayerId", params)
}

// 导出查询结果-Fengxy-230419
export const outPutSearchInfo = params => {
    return postRequestpdfORimg('/ygfx/intersectExportNew', params);
};

// 验证sql是否正确  http://localhost:8899/zituSpace/postGISSta/verifySQL
export const getCheckSql = params => {
    return postRequest('/postGISSta/verifySQL', params);
};

// 缓冲退让分析的接口  POST http://localhost:8899/zituSpace/transformation/buffer
export const postTransformation = params => {
    return postRequest('/transformation/buffer', params);
};
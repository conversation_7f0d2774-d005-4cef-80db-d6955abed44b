import {
    getRequest,
    postRequest,
    postRequestJson
  } from "@/utils/axios";

//获取项目人员概况统计
export const getProjectPersonOverview = () => {
  return getRequest('/screenProgress/personnelOverview')
}

// 获取既有线监测统计
export const getExistingLineMonitoringStat = () => {
  return getRequest('/screenProgress/operationalLineMonitoring')
}

// 统计在场机械类型的数量和占比
export const getMechanicalTypeStatistics = () => {
  return getRequest('/screenProgress/mechanicalTypeStatistics')
}

// 获取大屏总进度信息
export const getScreenProgressInfo = () => {
  return getRequest('/screenProgress/totalProgress')
}

// 项目施工计划
export const getProjectPlan = () => {
  return getRequest('/screenProgress/projectConstructionPlan')
}

// 得到实际进度构件
export const getActualProgressComponent = (data) => {
  return postRequestJson('/progressRealDetail/screenRealProgressIntegrateList', data)
}

// 得到bimface 的viewToken
export const getBimfaceViewToken = (data = {}) => {
  return postRequestJson('/systemBimConfig/getBimFaceViewToken', data)
}

// 总计划任务信息
export const getScreenProgressTaskInfo = (data) => {
  return postRequestJson('/progressInfo/screenProgressTaskInfo', data)
}
// 总计划任务信息-面板
export const getScreenAllProgressTaskInfo = (data) => {
  return postRequestJson('/progressInfo/screenAllProgressTaskInfo', data)
}

// 获取结构件详情（图纸信息和现场照片）
export const getComponentDetail = (resourceId) => {
  return getRequest('/screenProgress/structureComponentDetail', {resourceId})
}

import {
    getRequest,
    postRequest,
    postRequestJson
  } from "@/utils/axios";

//网格安监专务和网格安全员信息获取接口
export const getGridPersonInfo = () => {
  return getRequest('/gridManagementInfo/getGridPersonInfo')
}

//隐患排查统计查询接口
export const getGridHazardStatistics = () => {
    return postRequestJson('/hiddenTrouble/troubleStatisticScreen')
}

// 获取本月跟班任务统计数据
export const getMonthJobTask = () => {
    return getRequest('/railFollowCalendar/getCurrentMonthStats')
}

// 管理穿透-本月作业数量统计
export const getSelectStatisticForScreen = () => {
  return postRequestJson('/processMonitor/selectStatisticForScreen')
}

// 查询最新的一条班前会记录
export const getLatestRailPreMeeting = () => {
  return getRequest('/railPreMeeting/latest')
}

// 右上角培训记录统计饼图合计
export const getTrainingRecordStatistics = () => {
    return getRequest('/safetyEdu/getStatEduNumber')
}

// 安全考核表统计查询
export const getAssessTableStatisticScreen = () => {
  return postRequestJson('/assessTable/assessTableStatisticScreen')
}

// 关键作业清单(安全确认表)统计查询接口
export const getKeyTaskList = () => {
  return postRequestJson('/safeTable/selectSafeTableScreen')
}
import axios from "axios";
import {
  getRequest,
  postRequest,
  postRequestJson,
  postFullUrlRequestJson,
  postRequestpdfORimg
} from "@/utils/axios";

// 获取高精度地图配置
export const getMapConfig = (key) => {
  return axios({
    method: "get",
    url: `/api/config/getByKey/${key}`,
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  });
}

// 获取吊车作业区域/工地
export const getCraneArea = (key) => {
  return axios({
    method: 'post',
    url: '/api/crane/craneOperationArea/listPage',
    data: key
  })
}

// 获取吊车垂直面保护区
export const getCraneVerticalProtection = (key) => {
  return axios({
    url: '/api/crane/craneVerticalProtectionArea/listPage',
    method: 'post',
    data: key
  })
}


// 根据设备id获取设备详情
export const getDeviceInfo = (key) => {
  return axios({
    url: '/api/device/get',
    method: 'post',
    data: key
  })
}

// 获取吊车数据列表信息
export const getMechanical = (key) => {
  return getRequest('/crane/safe/monitor/mechanical', key, true)
}

// 获取吊车数据列表信息
export const getTowerCraneMechanical = (key) => {
  return getRequest('/towerCrane/safe/monitor/mechanical', key, true)
}

// ai综合安防点位接口
export const getAiSafePoint = (key) => {
  return getRequest('/aiHostRecord/geoMapAiHostDevice', key, true)
}

// 全景感知设备点位详情接口,普通设备
export const getDeviceDetail = (param) => {
  return postRequestJson('/device/get', param)
}

// 全景感知设备点位详情接口,机械设备
export const getMechanicalDetail = (param) => {
  return getRequest('/mechanical/mechanical/queryById', param)
}

// 用电安全监测地图设备点位接口
export const getUseElectricDevices = () => {
  return getRequest('/useElectric/geoMapUseEleDevice')
}

// 获取吊车关联的设备信息（包括北斗设备）
export const getMechanicalRefDevice = (mechanicalId) => {
  return axios({
    method: 'get',
    url: `/api/mechanical/mechanicalRefDevice/queryByMechanicalId`,
    params: { mechanicalId }
  })
}

// 吊车安全监测-人员列表
export const getCranePersonList = (param) => {
  return getRequest('/crane/safe/monitor/personList', param)
}
// 人员轨迹列表查询
export const getPersonTrackList = (param) => {
  return postRequestJson('/person/personWorkRecord/trackListPage', param)
}
// 人员轨迹详情查询
export const getPersonTrackDetail = (params) => {
  return axios({
    method: 'get',
    url: `/api/person/personWorkRecord/queryById`,
    params
  })
}
// 人员电子围栏列表
export const getCranePersonAreaList = (param) => {
  return postRequestJson('/person/personOperationArea/listPage', param)
}

// 获取用户菜单权限
export const getUserMenuPrivilege = () => {
  return axios({
    method: 'get',
    url: '/api/linkappPrivilege/selectPrivilegeCustomByUser?type=2'
  })
}

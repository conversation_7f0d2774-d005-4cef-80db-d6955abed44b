import { getRequest, postRequestJson } from "@/utils/axios";

// 左上角根据高支模code统计设备接口
export const getDeviceStatByCode = (highCode) => {
  return getRequest("/highFormworkItem/getHighBoundItemDevSum", { highCode });
};

// 获取高支模列表接口
export const getHighFormworkList = (params) => {
  return getRequest("/highFormworkInfo/getList", params);
};

// 获取高支模实时数据接口
export const getHighFormworkRealTimeData = (params) => {
  return postRequestJson("/highFormworkRecord/newRecordByList", params);
};

// 告警记录
export const getAlarmRecord = (highCode) => {
  return getRequest("/highFormworkInfo/getAlarmByHighCodeThirty", { highCode });
};

// 获取高支模数据趋势接口
export const getHighFormworkDataTrend = (params) => {
  return getRequest(
    "/highFormworkRecord/getitemTypeByStatisticsLine.view",
    params
  );
};
export const getHighFormworkDataTrend2 = (params) => {
  return postRequestJson(
    "/highFormworkRecord/getitemTypeByStatisticsLine",
    params
  );
};

// 得到告警设置
export const getAlarmSetting = () => {
  return postRequestJson("/giveAlarmTypeConfig/selectList", { category: 8 });
};

// 高支模大屏获取监测设备地图点位
export const getHighFormworkMonitoringPoints = () => {
  return getRequest("/highFormworkRecord/geoMapHighDevice");
};

// 通过设备编号获取高支模监测设备信息
export const getHighDeviceByNowRecord = (deviceCode) => {
  return getRequest("/highFormworkRecord/getHighDeviceByNowRecord", { deviceCode });
};

import axios from "axios";

export const getCharts1 = data => {
  return axios({
    method: "post",
    url: `/api/towerCrane/towerCraneBusinessRecord/listCirclePage`,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    data
  });
}

export const getCharts2 = data => {
  return axios({
    method: "post",
    url: `/api/towerCrane/towerCraneBusinessRecord/queryMomentCurve`,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    data
  });
}

export const getCharts3 = data => {
  return axios({
    method: "post",
    url: `/api/towerCrane/towerCraneBusinessRecord/listPage`,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    data
  });
}
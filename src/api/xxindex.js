import {
  getRequest,
  postRequest,
  postRequestJson,
  $service,
  getRequestNew,
  deleteRequest,
  formDatapostRequest,
  getRequestWarn
} from "@/utils/axios";

// 天气
export const getWeather = (url, params) => {
  return getRequestWarn(url, params);
}

// 设备类型列表
export const getEquipmentTypeList = (params) => {
  return getRequest("/mechanical/equipmentLocation/equipmentTypeList", params);
}

// 设备分页查询
export const listPage = (params) => {
  return postRequestJson('/mechanical/equipmentLocation/listPage', params)
}

// 告警统计
export const getCategorytoTotall = (category) => {
  return getRequest('/systemAlarm/totalOldDayByLevelPieChart/' + category)
}

// ai行为告警，根据类型统计接口
export const aiBehaviorAlerts = () => {
  return getRequest('/aiHostRecord/aiBehaviorAlerts')
}

// 获取字典
export const getDict = (params) => {
  return getRequest('/sys/dict/getDicItem/' + params)
}

// 获取违规行为记录
export const getSelectPage = (params) => {
  return postRequestJson('/aiHostRecord/selectPage', params)
}

// 获取ai警告统计
export const totalOldDayByLevelPieChart = (params) => {
  return getRequest('/systemAlarm/totalOldDayByLevelPieChart/' + params)
}

// 吊车基本信息
export const getListPage = (params) => {
  return postRequestJson('/crane/safe/monitor/listPage', params)
}

// 按日统计吊重吊次数量
export const countByDay = (params) => {
  return postRequestJson('/crane/craneBusinessRecord/countByDay', params)
}

// 告警分类统计
export const alarmTypeCount = (params) => {
  return getRequest('/systemAlarm/alarmTypeCount', params)
}

// 今日告警分类统计
export const alarmTypeTodayCount = (params) => {
  return getRequest('/systemAlarm/alarmTypeTodayCount', params)
}

// 指定时间段内的告警分类统计
export const alarmTypeCountWithTime = (params) => {
  return getRequest('/systemAlarm/alarmTypeCountWithTime', params)
}

// 告警记录
export const systemAlarmPage = (params) => {
  return postRequestJson('/systemAlarm/getPage', params)
}

// 设备统计
export const deviceStatistics = (params) => {
  return getRequest('/mechanical/equipmentLocation/deviceStatistics', params);
}

// 吊车倾斜曲线
export const craneBusinessRecordlistPage = (params) => {
  return postRequestJson('/crane/craneBusinessRecord/listPage', params)
}

// 今日侵袭安统计
export const todayInfringementLimit = (params) => {
  return getRequest('/crane/safe/monitor/todayInfringementLimit', params)
}

// 视频列表
export const videoList = (params) => {
  return postRequestJson('/deviceVideoMonitor/page', params);
}

// 点击轨迹
export const queryByCondition = (params) => {
  return getRequest('/mechanical/equipmentLocation/queryByCondition', params)
}
// 点击轨迹-人员
export const queryByConditionPerson = (params) => {
  return getRequest('/person/personLocationData/queryByCondition', params)
}

// 获取近一小时告警信息
export const getAlamInfo = (params) => {
  return getRequest('/aiHostRecord/getNowByOne', params)
}

// 视频树型结构
export const getVideoTree = (params) => {
  return postRequestJson('/area/selectAreaTreeListWithDevice', params)
}

// 获取当日统计
export const getStatistics = (params) => {
  return getRequest('/aiHostRecord/getStatistics', params)
}

// 出勤统计
export const userClockList = (params) => {
  return postRequestJson('/enterpriseEditionBi/laborAttendanceData', params)
}

// ai综合安防视频告警统计图表
export const getByStatisticsLins = (params) => {
  return postRequestJson('/aiHostRecord/byStatisticsLins', params)
}

export const giveAlarmTypeConfig = (params) => {
  return postRequestJson('/giveAlarmTypeConfig/selectList', params)
}

// 告警类饼状图
export const getViolationBehaviorPieChart = (params) => {
  return getRequest('/aiHostRecord/getViolationBehaviorPieChart', params)
}

// 查询计划值
export const getplanValue = (params) => {
  return postRequestJson('/giveAlarmTypeConfig/planValue', params)
}

export const deviceGetStatistics = (params) => {
  return getRequest('/device/statistics/statistics', params)
}

export const getAiAlarmStatistics = (params) => {
  return getRequest('/aiHostRecord/getAiAlarmStatistics', params)
}

// 大屏ai报警提示语
export const getPrompt = (params) => {
  return getRequest('/aiHostRecord/getPrompt', params);
}

// 告警统计 前三名 剩下的为其他
export const getAlarmStatistics = (params) => {
  return getRequest('/crane/safe/monitor/todayAlarmClassification' ,params)
}

// 查询当天天气
export const weathreInfo = (params) => {
  return getRequest('/api/weather/history', params)
}

// 查询来车告警20分钟内的数据
export const getGjPage = (params) => {
  return postRequestJson('/systemAlarm/getPage', params)
}

// 查询设备所有信息
export const selectByTypeNameList = (params) => {
  return getRequest('/useElectric/eleDeviceDetailsByList', params)
}

// 按条件查所有设备
export const selectByTypeNameListSearch = (params) => {
  return postRequestJson('/device/selectByTypeNameList', params)
}

// 用电统计图
export const electricyByDayAndHour = (params) => {
  return postRequestJson('/electricyRecords/electricyByDayAndHour', params)
}

// 断路器统计图
export const faultTypeByColumnar = (params) => {
  return postRequestJson('/circuitGivealarm/faultTypeByColumnar', params)
}

// 总用电
export const electricyAll = (params) => {
  return getRequest('/electricyRecords/selectByYearMoon', params);
}

// 告警记录
export const warnPage = (params) => {
  return postRequestJson('/systemAlarm/getPage', params);
}

// 电缆温度曲线
export const boxRecordByhoursOrMonthly = (params) => {
  return postRequestJson('/electricBoxBi/temperatureLineHursOrMonthly', params);
}

// 电缆温度阈值
export const getThreshold = (params) => {
  return getRequest('/circuitGivealarm/getThreshold', params)
}

// 首页监控平台左侧树形视频列表接口
export const selectByUserAreaAllList = (params) => {
  return postRequestJson('/area/selectByUserAreaAllList', params)
}

// 首页监控平台保存预置视频展示接口
export const saveVideoSelected = (params) => {
  return postRequestJson('/area/saveVideoSelected', params)
}

// 首页监控平台获取视频配置接口
export const getNowVideoSelected = (params) => {
  return getRequest('/area/getNowVideoSelected', params)
}

// 今日塔吊侵袭安统计
export const towerCraneTodayInfringementLimit = (params) => {
  return getRequest('/towerCrane/safe/monitor/todayInfringementLimit', params)
}

// 塔吊告警统计 前三名 剩下的为其他
export const getTowerCraneAlarmStatistics = (params) => {
  return getRequest('/towerCrane/safe/monitor/todayAlarmClassification' ,params)
}

// 塔吊机械统计
export const getTowerCraneAlarmStatisticsByMechanical = (mechanicalId) => {
  return getRequest('/towerCrane/safe/monitor/queryTodayAlarmByMechanicald?mechanicalId=' + mechanicalId)
}

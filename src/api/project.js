import {
  getRequest,
  postFullUrlRequestJson,
  postRequestpdfORimg
} from "@/utils/axios";

/**
 * 获取用户项目
 * @param params
 * @returns {Promise}
 */
export function getUserProject (params) {
  return getRequest("/login/getUserProject", params, true)
}

/**
 * 获取项目完成进度
 */
export function getProjectById (id) {
  return getRequest(`/index/projectById/${id}`, {}, true)
}

/**
 * 选择用户项目
 * @param params
 * @returns {Promise}
*/
export function userSelectProject (params) {
  return postFullUrlRequestJson("/login/userSelectProject", params)
}

/**
 * 获取登录用户信息 /login/info
 */
export function getLoginUserInfo () {
  return getRequest("/login/info", { Pragma: 'no-cache', 'Cache-Control': 'no-cache' }, true)
}
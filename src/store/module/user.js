// 用户模块状态管理
const state = {
  tokenValue: '', //用户token值
  projectInfo: {}
}

const mutations = {
  //设置Token值
  SET_TOKEN_VALUE(state, params) {
    state.tokenValue = params
  },
  SET_PROJECT_INFO(state, params) {
    state.projectInfo = params
  }
}

const actions = {
  setProjectInfo({ commit }, data) {
    commit('SET_PROJECT_INFO', data)
  }
}


const getters = {
  tokenValue: state => state.tokenValue,
  projectInfo: state => state.projectInfo
}
export default {
  namespaced: true, // 开启命名空间
  state,
  mutations,
  actions,
  getters
}

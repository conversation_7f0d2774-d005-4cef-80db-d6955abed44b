import { getUserMenuPrivilege } from '@/api'

// 菜单code映射表
const MENU_CODE_MAP = {
  'screen_image_progress': '形象进度',
  'screen_animation': '转体模拟',
  'screen_construction_tech_defense': '施工技防',
  'screen_crane_safety_monitoring': '吊车安全监测',
  'screen_construction_defense_crack_detection': '施工防侵限',
  'screen_tower_safety_monitoring': '塔吊安全监测',
  'screen_group_tower_collision_prevention': '群塔防碰撞',
  'screen_high_support_monitor': '高支模安全监控',
  'screen_electricity_safety_monitoring': '用电安全监测',
  'screen_electronic_fence': '电子围栏',
  'screen_ai_comprehensive_security': 'AI综合安防',
  'screen_people_defense_through': '人防(穿透式)',
  'screen_panoramic_perception': '全景感知',
  'screen_video_monitoring': '视频监控'
}

// 主菜单code列表
const MAIN_MENU_CODES = [
  'screen_image_progress',
  'screen_animation', 
  'screen_construction_tech_defense',
  'screen_people_defense_through',
  'screen_panoramic_perception',
  'screen_video_monitoring'
]

// 子菜单code列表
const SUB_MENU_CODES = [
  'screen_crane_safety_monitoring',
  'screen_construction_defense_crack_detection',
  'screen_tower_safety_monitoring',
  'screen_group_tower_collision_prevention',
  'screen_high_support_monitor',
  'screen_electricity_safety_monitoring',
  'screen_electronic_fence',
  'screen_ai_comprehensive_security'
]
const defaultMenuList = [
  {
      name: '形象进度',
      active: false,
    },
    {
      name: '转体模拟',
      active: false,
    },
    // {
    //   name: '动画',
    //   active: false,
    // },
    {
      name: '人防（穿透式）',
      active: false,
    },
    {
      name: '全景感知',
      active: false,
    },
    {
      name: '施工技防',
      active: false,
    },
    {
      name: '视频监控',
      active: false,
    }
]
const state = {
  menuList: [],
  activeMenu: '形象进度', // 设置默认选中的菜单
  subMenu: [
    {
      name: '吊车安全监测',
      active: false,
    },
    {
      name: '施工防侵限',
      active: false,
    },
    {
      name: '塔吊安全监测',
      active: true,
    },
    {
      name: '群塔防碰撞',
      active: false,
    },
    {
      name: '用电安全监测',
      active: false
    },
    {
      name: '高支模安全监控',
      active: false,
    },
    {
      name: '混凝土温控',
      active: false,
    },
    {
      name: 'AI综合安防',
      active: false,
    },
    {
      name: '司机行为监控',
      active: false,
    }
  ],
  activeSubMenu: '吊车安全监测',
  loading: false
}

const mutations = {
  setActiveMenu(state, item) {
    console.log(item)
    state.activeMenu = item.menuName ? item.menuName : item.name
    state.menuList.forEach((menu) => {
      if (menu.name === item.menuName || menu.name === item.name) {
        menu.active = true
      } else {
        menu.active = false
      }
    })
    console.log(state.menuList)
  },
  setActiveSubMenu(state, name) {
    state.activeSubMenu = name
    state.subMenu.forEach((menu) => {
      if (menu.name === name) {
        menu.active = true
      } else {
        menu.active = false
      }
    })
  },
  setMenuList(state, menuList) {
    state.menuList = menuList
  },
  setSubMenu(state, subMenu) {
    state.subMenu = subMenu
  },
  setLoading(state, loading) {
    state.loading = loading
  }
}

const actions = {
  // 获取用户菜单权限
  async fetchUserMenuPrivilege({ commit, dispatch }) {
    console.log('开始获取菜单权限...')
    commit('setLoading', true)
    try {
      const response = await getUserMenuPrivilege()
      console.log('接口响应:', response)
      
      if (response.data && response.success) {
        const menuData = response.data || []
        console.log('原始菜单数据:', menuData)
        
        // 过滤出大屏菜单 (flag = 2)
        const screenMenus = menuData.filter(item => item.flag === 2)
        console.log("大屏菜单数据:", screenMenus)
        
        // 处理主菜单
        const mainMenus = screenMenus
          .filter(item => MAIN_MENU_CODES.includes(item.code))
          .sort((a, b) => a.sort - b.sort)
          .map(item => ({
            name: MENU_CODE_MAP[item.code] || item.name,
            active: false,
            code: item.code
          }))
        
        console.log('处理后的主菜单:', mainMenus)
        
        // 处理子菜单
        const subMenus = screenMenus
          .filter(item => SUB_MENU_CODES.includes(item.code))
          .sort((a, b) => a.sort - b.sort)
          .map(item => ({
            name: MENU_CODE_MAP[item.code] || item.name,
            active: false,
            code: item.code
          }))
        
        console.log('处理后的子菜单:', subMenus)
        
        // 更新状态
        commit('setMenuList', mainMenus)
        commit('setSubMenu', subMenus)
        
        // 如果有主菜单，自动选择第一个
        if (mainMenus.length > 0) {
          dispatch('selectFirstMenu', mainMenus[0])
        }
        
        
        console.log('动态菜单加载成功:', { mainMenus, subMenus })
      } else {
        // 接口返回失败，保持默认菜单
        console.log('接口返回失败，保持默认菜单')
        console.log('当前默认菜单状态:', state.menuList)
        // 不调用 setMenuList，保持初始状态
      }
    } catch (error) {
      console.error('获取菜单权限失败:', error)
      // 接口调用失败，保持默认菜单
      console.log('接口调用失败，保持默认菜单')
      console.log('当前默认菜单状态:', state.menuList)
      // 不调用 setMenuList，保持初始状态
    } finally {
      commit('setLoading', false)
    }
  },
  
  // 选择第一个菜单
  selectFirstMenu({ commit }, firstMenu) {
    if (firstMenu) {
      commit('setActiveMenu', firstMenu)
      console.log('自动选择第一个菜单:', firstMenu.name)
    }
  }
}

const getters = {
  menuList: state => state.menuList,
  activeMenu: state => state.activeMenu,
  subMenu: state => state.subMenu,
  activeSubMenu: state => state.activeSubMenu,
  loading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

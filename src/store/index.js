import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import actions from './actions'
import user from './module/user'
import login from './module/login'
import mapShow from './module/mapshow'
import menu from './module/menu'
Vue.use(Vuex)

const store = new Vuex.Store({
  getters,
  actions,
  modules: { // 模块引用
    login,
    user,
    mapShow,
    menu
  }
});

export default store;

import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import * as EarthView from '../public/sdk/earth/earthview.js';
import '../public/sdk/earth/Widgets/widgets.css';
import './styles/el.css';
import iView from 'view-design';
import 'view-design/dist/styles/iview.css';
import Viewer from 'v-viewer';
// import 'viewerjs/dist/viewer.css';
import './styles/reset.less';
import publicMethod from './utils/common.js'; //公共方法
import layer from 'vue-layer'; //引入layui弹框
import 'vue-layer/lib/vue-layer.css';
import VueBus from 'vue-bus';
import dataV from '@jiaminghi/data-view';
import $ from 'jquery';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import x2js from 'x2js';
import Alert from './components/common/alert.js';
import Empty from './components/common/empty.vue'
import globalComponents from './components/globalComponents/index.js';
import './styles/3dCommon.less';

Vue.config.productionTip = false;
Vue.use(iView);
Vue.use(Viewer);
Vue.use(dataV);
Vue.use(VueBus);
Vue.use(ElementUI);
Vue.use(Alert)
Vue.use(globalComponents)
Vue.component('Empty', Empty)
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: 'data-source',
  },
});
Vue.prototype.$EarthView = EarthView;
Vue.prototype.publicMethod = publicMethod;
Vue.prototype.$layer = layer(Vue);
Vue.prototype.$jq = $;
Vue.prototype.$x2js = new x2js({
  attributePrefix: '', //转换完的数据格式 （默认的是带 _ 的）
});

document.title = window.globalConfig.projectConfig.title

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');

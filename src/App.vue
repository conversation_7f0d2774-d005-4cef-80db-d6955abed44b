<template>
<div id="app">
  <router-view :key="$router.fullPath"/>
</div>
</template>
<script>
import Cookies from "js-cookie";
import { mapActions } from "vuex";

export default {
  name: "App",

  mounted() {
    // 初始化动态菜单
    this.initDynamicMenu();

    // let token = Cookies.get("accessToken");
    // if (!token) {
    //   this.$router.push({
    //     path: "/home",
    //   });
    //   localStorage.clear();
    // }

    // 基准大小
    const baseSize = 16;
    // const baseSize = 10;
    // 设置 rem 函数
    function setRem() {
      // 当前页面宽度相对于 750 宽的缩放比例，可根据自己需要修改。
      const scale = document.documentElement.clientWidth / 1920;
      // 设置页面根节点字体大小
      document.documentElement.style.fontSize =
        baseSize * Math.min(scale, 2) + "px";
    }
    // 初始化
    setRem();
    // 改变窗口大小时重新设置 rem
    window.addEventListener("resize", setRem);
    this.$Message.config({
      top: 100,
      duration: 3,
    });
  },
  methods: {
    ...mapActions('menu', ['fetchUserMenuPrivilege']),
    
    // 初始化动态菜单
    async initDynamicMenu() {
      try {
        await this.fetchUserMenuPrivilege();
        console.log('动态菜单初始化完成');
      } catch (error) {
        console.error('动态菜单初始化失败:', error);
      }
    }
  },
};
</script>

<style lang="less">
@import "./styles/index.less";

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  color: #2c3e50;
}

@font-face {
  font-family: "youshebiaotihei";
  src: url("./assets/youshebiaotihei.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

.youshebiaotihei {
  font-family: 'youshebiaotihei' !important;
}

// 引入思源宋体字体文件
@font-face {
  font-family: "syst-Bold";
  src: url("/font/syst/SourceHanSerifCN-Bold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-ExtraLight";
  src: url("/font/syst/SourceHanSerifCN-ExtraLight.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-Heavy";
  src: url("/font/syst/SourceHanSerifCN-Heavy.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-Heavy";
  src: url("/font/syst/SourceHanSerifCN-Heavy.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-Light";
  src: url("/font/syst/SourceHanSerifCN-Light.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-Medium";
  src: url("/font/syst/SourceHanSerifCN-Medium.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-Regular";
  src: url("/font/syst/SourceHanSerifCN-Regular.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "syst-SemiBold";
  src: url("/font/syst/SourceHanSerifCN-SemiBold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

body {
  margin: 0;
  padding: 0;
}

#nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}

/* vue-layer弹窗 */
.vl-notify {
  padding-bottom: 0px !important;
  border: none !important;
  z-index: 222222 !important;
}

.vl-notify.vl-notify-alert h2.vl-notice-title {
  display: none !important;
}

.vl-notify.vl-notify-iframe {
  background-color: transparent;
  // background: url('../public/img/left-bottom-bg.png') no-repeat top left;
  background-size: 100% 100%;
}

.vl-notify-content {
  overflow-y: scroll !important;

  &::-webkit-scrollbar {
    display: none;
  }
}

.vl-notify.vl-notify-main {
  -webkit-box-shadow: unset;
  box-shadow: unset;
}

.point-popup {
  position: absolute;
  left: -50%;
  top: -120%;
  width: 464px;
  height: 300px;
  background: url(/img/zhts/point-model.png) no-repeat;
  background-size: 100% 100%;
  z-index: 200;

  .title {
    position: relative;

    .text {
      font-size: 22px;
      color: #fff;
      font-family: syst-Light;
      line-height: 60px;
      padding-left: 20px;
    }

    .close {
      position: absolute;
      right: 20px;
      top: 20px;
      cursor: pointer;

      img {
        width: 13px;
      }
    }
  }

  .content {
    height: calc(100% - 65px);
    overflow: auto;

    .content-item {
      display: flex;
      align-items: center;
      height: 40px;

      .left-icon {
        img {
          width: 15px;
          vertical-align: middle;
        }
      }

      .right-text {
        padding-left: 10px;
        display: flex;
        align-items: center;

        .title {
          font-size: 14px;
          color: #699fa8;
          font-family: syst-Light;
          line-height: 30px;
        }

        .value {
          color: #fff;
          font-size: 16px;
          font-family: syst-Regular;
        }
      }
    }
  }

  .content::-webkit-scrollbar {
    display: none;
  }
}

.point-popup-jqyy {
  position: absolute;
  left: -50%;
  top: -110%;
  width: 464px;
  height: 300px;
  background: url(/img/zcgl/视频弹框.png) no-repeat;
  background-size: 100% 100%;
  z-index: 200;

  .title {
    position: relative;
    top: 1.2rem;
    left: 1.7rem;

    .text {
      font-size: 1rem;
      color: #fff;
      font-family: syst-Light;
      line-height: 60px;
      padding-left: 20px;
    }

    .close {
      position: absolute;
      right: 4.25rem;
      top: 1.3rem;
      cursor: pointer;

      img {
        width: 13px;
      }
    }
  }

  .content {
    height: calc(100% - 65px);
    overflow: auto;

    .content-item {
      display: flex;
      align-items: center;
      height: 40px;

      .left-icon {
        img {
          width: 15px;
          vertical-align: middle;
        }
      }

      .right-text {
        padding-left: 10px;
        display: flex;
        align-items: center;

        .title {
          font-size: 14px;
          color: #699fa8;
          font-family: syst-Light;
          line-height: 30px;
        }

        .value {
          color: #fff;
          font-size: 16px;
          font-family: syst-Regular;
        }
      }
    }
  }

  .content {
    position: relative;
    width: 100%;
    // height: 100%;

    .video-player {
      position: absolute;
      top: 1rem;
      left: 2.5rem;
      width: 83%;
      /* ; /* 视频宽度 */
      height: 73%;
      border-radius: 10px;
      /* 圆角边框 */
    }
  }
}

//媒体查询
@media screen and (min-width: 2880px) {
  .point-popup {
    position: absolute;
    left: -50%;
    top: -110%;
    width: 464px;
    height: 300px;
    background: url(/img/zhts/point-model.png) no-repeat;
    background-size: 100% 100%;
    z-index: 200;

    .title {
      position: relative;

      .text {
        font-size: 22px;
        color: #fff;
        font-family: syst-Light;
        line-height: 60px;
        padding-left: 20px;
      }

      .close {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;

        img {
          width: 13px;
        }
      }
    }

    .content {
      height: calc(100% - 65px);
      overflow: auto;

      .content-item {
        display: flex;
        align-items: center;
        height: 40px;

        .left-icon {
          img {
            width: 15px;
            vertical-align: middle;
          }
        }

        .right-text {
          padding-left: 10px;
          display: flex;
          align-items: center;

          .title {
            font-size: 14px;
            color: #699fa8;
            font-family: syst-Light;
            line-height: 30px;
          }

          .value {
            color: #fff;
            font-size: 16px;
            font-family: syst-Regular;
          }
        }
      }
    }

    .content::-webkit-scrollbar {
      display: none;
    }
  }

  .point-popup-jqyy {
    position: absolute;
    left: -50%;
    top: -100%;
    width: 464px;
    height: 300px;
    background: url(/img/zcgl/视频弹框.png) no-repeat;
    background-size: 100% 100%;
    z-index: 200;

    .title {
      position: relative;
      top: 1.2rem;
      left: 1.7rem;

      .text {
        font-size: 1rem;
        color: #fff;
        font-family: syst-Light;
        line-height: 60px;
        padding-left: 20px;
      }

      .close {
        position: absolute;
        right: 4.25rem;
        top: 1.3rem;
        cursor: pointer;

        img {
          width: 13px;
        }
      }
    }

    .content {
      height: calc(100% - 65px);
      overflow: auto;

      .content-item {
        display: flex;
        align-items: center;
        height: 40px;

        .left-icon {
          img {
            width: 15px;
            vertical-align: middle;
          }
        }

        .right-text {
          padding-left: 10px;
          display: flex;
          align-items: center;

          .title {
            font-size: 14px;
            color: #699fa8;
            font-family: syst-Light;
            line-height: 30px;
          }

          .value {
            color: #fff;
            font-size: 16px;
            font-family: syst-Regular;
          }
        }
      }
    }

    .content {
      position: relative;
      width: 100%;
      // height: 100%;

      .video-player {
        position: absolute;
        top: 1rem;
        left: 2.1rem;
        width: 86%;
        /* ; /* 视频宽度 */
        height: 73%;
        border-radius: 10px;
        /* 圆角边框 */
      }
    }
  }
}
</style>

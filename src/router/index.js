import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

const router = new Router({
  mode: "hash",
  routes: [
    {
      path: '/',
      redirect: '/home'
    },
  // {
  //   path: "/login",
  //   name: "login",
  //   component: () => import("../views/login/login.vue"),
  //   meta: {
  //     isToken: false,
  //   }
  // },
    {
      path: "/home",
      name: "home",
      component: () => import("../views/Home.vue"),
      meta: {
        isToken: true
      }
    },
    {
      path: "/pointScreen",
      name: "pointScreen",
      component: () => import("../views/pointScreen/index.vue"),
      meta: {
        isToken: false
      }
    },
    {
      path: "/:path(.*)*",
      name: "notFound",
      component: () => import("../views/error-page/404.vue"),
      // meta: {
      //     isToken: true
      // }
    }
  ],
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const routerExits = router.options.routes.find(route => route.name === to.name)
  if (to.path === '/login' || to.path == '/view') {
    return next()
  }
  next()
  const isAuthenticated = window.localStorage.getItem("token") // 是否获取到token
  let isHasToken = to.meta.isToken; // 是否需要页面token
  if (isHasToken && !isAuthenticated) {
    // return next({
    //   name: "login"
    // })
  } else {
    next()
  }
});

export default router;
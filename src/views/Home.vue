<template>
  <div class="home">
    <div class="home_page">
      <headerItem ref="headerItem" @activeModel="getActiveModel"></headerItem>
      <div class="base3dmap">
        <CesiumBaseMap ref="CesiumBaseMap"></CesiumBaseMap>
      </div>

      <!-- 保存视角按钮 -->
      <div class="save-view-controls" v-if="activeModel == '项目地图'">
        <div
          class="save-view-btn"
          @click="handleSaveView"
          :title="saveViewTooltip"
        >
          <i class="el-icon-camera"></i>
          <span>保存视角</span>
        </div>
      </div>

      <!-- 保存成功提示 -->
      <div v-if="showSaveSuccess" class="save-success-tip">
        <i class="el-icon-success"></i>
        <span>视角保存成功！</span>
      </div>
    </div>
    <Modal
      v-model="attractionsModalVisible"
      :title="attractionsModalTitle"
      :styles="{ top: '96px' }"
      class-name="vertical-center-modal"
      width="35%"
      footer-hide
    >
      <attractionsModal
        v-if="attractionsModalVisible"
        :attractions-img-list="attractionsImgList"
        :attractions-desc="attractionsDesc"
      ></attractionsModal>
    </Modal>
  </div>
</template>

<script>
import JSEncrypt from "jsencrypt";
import { mapState } from "vuex";
import CesiumBaseMap from "../components/map3dbase/CesiumBaseMap.vue";
import headerItem from "./ProjectMap/headerItem";
import { removeSessionStore } from '@/utils/storage';
import cameraViewManager from '@/utils/CameraViewManager.js'; // 导入相机视角管理器

export default {
  name: "Home",
  components: {
    CesiumBaseMap,
    headerItem,
    // HomePage
  },
  data() {
    return {
      navList: [
        { name: "项目地图", active: true },
        // { name: '监控平台', active: false }
      ],
      attractionsModalTitle: "",
      attractionsImgList: [],
      attractionsModalVisible: false,
      attractionsDesc: "",
      activeModel: "项目地图",
      // 保存视角相关
      hasSavedView: false,
      showSaveSuccess: false,
      saveViewTooltip: "保存当前地图视角",
    };
  },
  created() {
  },
  mounted() {
    this.checkSavedView();
  },
  computed: {
    ...mapState("mapShow", ["mapShow"]),
  },
  methods: {
    /**
     * @description: 初始化鉴权
     * @author: wzd
     * @date: 2022-01-06 09:43:47
     */
    initaAuthority() {
      //获取公钥
      getPublicKey(globalConfig.apiList.getPublicKeyUrl).then((res) => {
        if (res.success === true) {
          this.publicKey = res.data.publicKey;
          //加密用户及密码
          const userName = this.encryption(
            this.publicKey,
            globalConfig.authorityParam.username
          );
          const password = this.encryption(
            this.publicKey,
            globalConfig.authorityParam.password
          );
          //开始进行密码模式鉴权
          let data = {
            username: userName,
            password: password,
            grant_type: "password",
            client_id: globalConfig.authorityParam.client_id,
            client_secret: globalConfig.authorityParam.client_secret,
            public_key: this.publicKey,
          };
          //获取token
          getToken(globalConfig.apiList.getTokenUrl, data).then((res1) => {
            if (res1.success === true) {
              //设置token值
              this.SET_TOKEN_VALUE(
                res1.data.token_type + " " + res1.data.access_token
              );
              //过期时间
              let seconds =
                res1.data.expires_in - 15 > 0 ? res1.data.expires_in - 15 : 15;
              //快到过期时间时重新获取token
              let that = this;
              setTimeout(function () {
                that.initaAuthority();
              }, seconds * 1000);
            }
          });
        }
      });
    },

    /**
     * @description: 加密密文
     * @author: wzd
     * @date: 2022-01-06 10:07:13
     */
    encryption(publicKey, password) {
      let encryptor = new JSEncrypt();
      encryptor.setPublicKey(publicKey);
      let psw = encryptor.encrypt(password);
      return psw;
    },

    // 保存视角功能
    handleSaveView() {
      if (this.$refs.CesiumBaseMap && cameraViewManager) {
        const pageName = "home";
        console.log("🎯 [Home] 保存视角:", pageName);

        // 设置当前页面名称
        cameraViewManager.setCurrentPageName(pageName);

        // 保存视角并检查结果 - 集团级总览页面不包含项目名称
        const success = cameraViewManager.saveCameraView(pageName, this.$route, false);

        if (success) {
          this.showSaveSuccess = true;
          this.hasSavedView = true;
          console.log("✅ [Home] 视角保存成功（集团级通用）");

          // 3秒后隐藏成功提示
          setTimeout(() => {
            this.showSaveSuccess = false;
          }, 3000);
        } else {
          console.error("❌ [Home] 视角保存失败");
          this.$message && this.$message.error("保存视角失败，请重试");
        }
      } else {
        console.error("❌ [Home] 地图组件未初始化，无法保存视角");
        this.$message && this.$message.error("地图组件未初始化，无法保存视角");
      }
    },

    // 恢复视角功能
    handleRestoreView() {
      if (
        this.$refs.CesiumBaseMap &&
        cameraViewManager
      ) {
        const pageName = "home";
        console.log("🔄 [Home] 恢复视角:", pageName);

        // 设置当前页面名称
        cameraViewManager.setCurrentPageName(pageName);

        // 恢复视角并检查结果 - 集团级总览页面不包含项目名称
        const success = cameraViewManager.restoreCameraView(
          pageName,
          true,
          this.$route,
          false
        );

        if (!success) {
          console.warn("⚠️ [Home] 没有找到保存的视角");
          this.$message && this.$message.warning("没有找到保存的视角");
        } else {
          console.log("✅ [Home] 视角恢复成功（集团级通用）");
        }
      } else {
        console.error("❌ [Home] 地图组件未初始化，无法恢复视角");
      }
    },

    // 检查是否有保存的视角
    checkSavedView() {
      const pageName = "home";

      // 使用新的CameraViewManager方法检查保存的视角
      if (
        this.$refs.CesiumBaseMap &&
        cameraViewManager
      ) {
        // 设置当前页面名称
        cameraViewManager.setCurrentPageName(pageName);

        // 检查保存的视角 - 集团级总览页面不包含项目名称
        const savedView = cameraViewManager.getSavedCameraView(pageName, this.$route, false);
        this.hasSavedView = !!savedView;

        if (this.hasSavedView) {
          console.log("✅ [Home] 检测到保存的视角（集团级通用）:", {
            "📄 页面名称": pageName,
            "📅 保存时间": new Date(savedView.timestamp).toLocaleString(
              "zh-CN"
            ),
          });

          // 延迟一小段时间后尝试自动恢复视角（确保地图状态已更新）
          setTimeout(() => {
            this.handleRestoreView();
          }, 500);
        } else {
          console.log("📍 [Home] 未找到保存的视角（集团级通用）");
        }
      } else {
        // 如果地图组件还没有准备好，延迟检查
        setTimeout(() => {
          this.checkSavedView();
        }, 300);
      }
    },

    getActiveModel(name) {
      this.activeModel = name;
    },
  },
};
</script>
<style lang="less" scoped>
.project-map-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../assets/images/ProjectMap/top-bg2.png") no-repeat center
      center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../assets/images/ProjectMap/top-bg.png") no-repeat center
        center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../assets/images/ProjectMap/active-jt.png) no-repeat
              center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../assets/images/ProjectMap/active-jt.png) no-repeat
              center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
  }
}

.home {
  height: 100vh;
  width: 100vw;
  position: relative;
  background: linear-gradient(181deg, #051f1f, #194440);
  overflow: hidden;
  .base3dmap {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 0;
  }

  .home_page {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 20;

    // pointer-events: auto;
  }

  /deep/::-webkit-scrollbar {
    width: 3px;
    height: 3px;
    background: transparent;
  }

  /deep/::-webkit-scrollbar-thumb {
    border-radius: 4px;
    box-shadow: inset 0 0 2px #01effa;
    -webkit-box-shadow: inset 0 0 2px #01effa;
    background: #01effa;
  }

  /deep/::-webkit-scrollbar-button {
    display: none;
  }

  /deep/::-webkit-scrollbar-corner {
    display: none;
  }
}

/deep/.earthview-popup-body {
  //   height: 300px !important;
  overflow: hidden;
}

/* 保存视角控制按钮样式 */
.save-view-controls {
  position: fixed !important;
  top: 120px;
  left: 420px;
  z-index: 9999 !important;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto !important;
}

.save-view-btn,
.restore-view-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 3px;
  background: linear-gradient(135deg, #1890ff, #0a2032);
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  user-select: none;
  min-width: 70;
  justify-content: center;
  pointer-events: auto !important;
  position: relative;
  z-index: 10000 !important;

  &:hover {
    background: linear-gradient(135deg, #40a9ff, #1890ff);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
  }

  i {
    font-size: 16px;
  }

  span {
    font-size: 13px;
    letter-spacing: 0.5px;
  }
}

.restore-view-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d);

  &:hover {
    background: linear-gradient(135deg, #73d13d, #52c41a);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
  }
}

.save-success-tip {
  position: fixed;
  top: 120px;
  left: 560px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
  padding: 12px 18px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideInLeft 0.5s ease-out, fadeOutLeft 0.5s ease-out 2.5s;
  pointer-events: none;

  i {
    font-size: 16px;
  }

  span {
    font-size: 13px;
    letter-spacing: 0.5px;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .save-view-controls {
    top: 80px;
    left: 10px;
    gap: 8px;
  }

  .save-view-btn,
  .restore-view-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 100px;

    i {
      font-size: 14px;
    }

    span {
      font-size: 11px;
    }
  }

  .save-success-tip {
    top: 80px;
    left: 120px;
    padding: 10px 14px;
    font-size: 12px;

    i {
      font-size: 14px;
    }

    span {
      font-size: 11px;
    }
  }
}
</style>

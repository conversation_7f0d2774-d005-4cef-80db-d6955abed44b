<template>
  <div class="construction">
    <div class="submenu">
      <el-menu
        :default-active="activeSubMenu"
        class="el-menu-demo"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item
          v-for="(sub, index) in submenuList"
          :key="index"
          :index="sub.name"
          :disabled="isHandlingSelect"
        >
          {{ sub.name }}
        </el-menu-item>
      </el-menu>
    </div>
    <div class="component-container">
      <transition name="component-fade" mode="out-in">
        <component :is="activeComponent" :key="activeSubMenu"></component>
      </transition>
    </div>
  </div>
</template>

<script>
import ProjectAI from "@/components/ProjectAI/index.vue";
import ProjectAnti from "@/components/ProjectAnti/index.vue";
import ProjectCrane from "@/components/ProjectCrane/index.vue";
import ProjectTowerCrane from "@/components/ProjectTowerCrane/index.vue";
import TowerCollisionPrevention from "@/components/towerCollisionPrevention/index.vue";
import ProjectElectrical from "@/components/ProjectElectrical/index.vue";
import HighFormworkMonitoring from "@/components/HighFormworkMonitoring/index.vue";
export default {
  name: "construction",
  data() {
    return {
      activeComponent: null,
      isHandlingSelect: false, // 截流控制变量
      throttleDelay: 1000, // 截流延迟时间(毫秒)
      debounceTimer: null, // 防抖定时器
      debounceDelay: 300, // 防抖延迟时间(毫秒)
      componentTransitionDelay: 150, // 组件切换动画延迟
    };
  },
  components: {
    ProjectAI,
    ProjectAnti,
    ProjectCrane,
    ProjectTowerCrane,
    ProjectElectrical,
    TowerCollisionPrevention,
    HighFormworkMonitoring,
  },
  computed: {
    submenuList() {
      return this.$store.state.menu.subMenu;
    },
    activeSubMenu() {
      return this.$store.state.menu.activeSubMenu;
    },
  },
  mounted() {
    console.log("=== Construction组件初始化 ===");
    console.log("submenuList:", this.submenuList);
    console.log("activeSubMenu:", this.activeSubMenu);

    // 定义组件映射（与executeSelect中保持一致）
    const componentMap = {
      吊车安全监测: "ProjectCrane",
      塔吊安全监测: "ProjectTowerCrane",
      群塔防碰撞: "TowerCollisionPrevention",
      施工防侵限: "ProjectAnti",
      AI综合安防: "ProjectAI",
      用电安全监测: "ProjectElectrical",
      高支模安全监控: "HighFormworkMonitoring",
    };

    // 直接设置初始组件，不经过防抖处理
    if (this.activeSubMenu && componentMap[this.activeSubMenu]) {
      this.activeComponent = componentMap[this.activeSubMenu];
      console.log("初始化设置组件:", this.activeComponent);

      // 发送初始子页面切换事件
      this.$bus.emit("constructionSubPageChange", this.activeComponent);

      // 发送初始化事件
      this.handleSubMenuEvents(this.activeSubMenu);
    } else {
      console.warn("未找到对应的组件映射:", this.activeSubMenu);
      // 如果没有匹配的组件，设置默认组件
      if (this.submenuList && this.submenuList.length > 0) {
        const defaultMenu = this.submenuList[0].name;
        this.activeComponent = componentMap[defaultMenu];
        console.log("设置默认组件:", this.activeComponent);
      }
    }

    // 添加调试信息
    this.$nextTick(() => {
      console.log("组件渲染完成，当前activeComponent:", this.activeComponent);
      console.log(
        "DOM中的component元素:",
        this.$el.querySelector(".component-container component")
      );
    });
  },
  methods: {
    handleSelect(key) {
      // 切换子菜单组件
      // 如果正在处理中，直接阻止
      if (this.isHandlingSelect) {
        console.log("正在处理中，操作被阻止");
        return;
      }

      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 防抖处理：延迟执行，如果在延迟时间内再次触发则重新计时
      this.debounceTimer = setTimeout(() => {
        this.executeSelect(key);
      }, this.debounceDelay);
    },

    executeSelect(key) {
      // 检查是否是相同的选项，避免重复处理
      if (key === this.activeSubMenu && this.activeComponent) {
        console.log("已选中当前选项，无需重复处理");
        return;
      }

      // 设置loading状态
      this.isHandlingSelect = true;

      console.log("切换到:", key);

      // 使用nextTick确保DOM更新
      this.$nextTick(async () => {
        try {
          // 先更新store状态
          this.$store.commit("menu/setActiveSubMenu", key);

          // 定义组件映射
          const componentMap = {
            吊车安全监测: "ProjectCrane",
            施工防侵限: "ProjectAnti",
            AI综合安防: "ProjectAI",
            用电安全监测: "ProjectElectrical",
            塔吊安全监测: "ProjectTowerCrane",
            群塔防碰撞: "TowerCollisionPrevention",
            高支模安全监控: "HighFormworkMonitoring",
          };

          // 延迟组件切换，让用户看到loading状态
          await this.delay(this.componentTransitionDelay);

          // 发送子页面切换事件到父组件
          this.$bus.emit(
            "constructionSubPageChange",
            componentMap[key] || "Construction"
          );

          // 根据不同的选项发送相应的事件
          this.handleSubMenuEvents(key);

          // 设置活动组件
          this.activeComponent = componentMap[key] || null;

          // 再等待一下确保组件渲染完成
          await this.delay(100);
        } catch (error) {
          console.error("切换菜单时发生错误:", error);
        } finally {
          // 设置定时器恢复截流标志
          setTimeout(() => {
            this.isHandlingSelect = false;
          }, this.throttleDelay - this.componentTransitionDelay - 100);
        }
      });
    },

    handleSubMenuEvents(key) {
      const eventMap = {
        吊车安全监测: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: model");
          this.$bus.emit("switchConstructionView", "model");
        },
        施工防侵限: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: high");
          this.$bus.emit("switchConstructionView", "high");
        },
        AI综合安防: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: ai");
          this.$bus.emit("switchConstructionView", "ai");
        },
        用电安全监测: () => {
          console.log("🚀 发送 useElectricDevices 事件，触发用电安全设备加载");
          this.$bus.emit("useElectricDevices", true);
        },
        塔吊安全监测: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: tower");
          this.$bus.emit("switchConstructionView", "tower");
        },
        群塔防碰撞: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: prevention");
          this.$bus.emit("switchConstructionView", "prevention");
        },
        高支模安全监控: () => {
          console.log("🚀 发送 switchConstructionView 事件，值: highFormwork");
          this.$bus.emit("switchConstructionView", "highFormwork");
        },
      };

      const eventHandler = eventMap[key];
      if (eventHandler) {
        eventHandler();
      }
    },

    // 延迟工具函数
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },

    createRipple(event) {
      const button = event.currentTarget;
      const ripple = document.createElement("span");
      ripple.classList.add("ripple");
      button.appendChild(ripple);

      const diameter = Math.max(button.clientWidth, button.clientHeight);
      const radius = diameter / 2;

      ripple.style.width = ripple.style.height = `${diameter}px`;
      ripple.style.left = `${event.clientX - button.offsetLeft - radius}px`;
      ripple.style.top = `${event.clientY - button.offsetTop - radius}px`;

      ripple.addEventListener("animationend", () => {
        ripple.remove();
      });
    },
  },

  beforeDestroy() {
    // 组件销毁时清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // 清理残留的涟漪元素
    const ripples = this.$el.querySelectorAll(".ripple");
    ripples.forEach((ripple) => ripple.remove());
  },
};
</script>

<style lang="less" scoped>
.construction {
  .submenu {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    .el-menu-demo {
      transition: none;
      border-bottom: none;
      display: flex;
      align-items: center;
      justify-content: center;
      background: inherit;

      .el-menu-item {
        background: url(../../../../assets/images/pointScreen/header/Subtract.png)
          no-repeat;
        background-size: 100% 100%;
        color: #ffffff;
        font-size: 16px;
        font-family: "PingFang SC";
        border-bottom: none;
        width: 140px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24px;
      }

      .is-active {
        border-bottom: none;
        background: url(../../../../assets/images/pointScreen/header/Subtract_active.png)
          no-repeat;
        background-size: 100% 100%;
        box-shadow: 0 0 0 rgba(0, 0, 0, 0.25);
      }
    }
  }
}
</style>

<template>
  <div class="pointScreen">
    <div class="screen_top">
      <ScreenHeader />
    </div>
    
    <!-- 地图始终显示，不受菜单影响 -->
    <div class="base3dmap" v-show="isShow">
      <CesiumBaseMap ref="CesiumBaseMap"></CesiumBaseMap>
    </div>

    <!-- bimface模型 -->
    <div class="bimMask" v-show="isShowBimMask">
      <bimMask ref="bimMaskRef"></bimMask>
    </div>

    <!-- 保存视角按钮 -->
    <div class="save-view-controls" v-show="isShow">
      <div class="save-view-btn" @click="handleSaveView" :title="dynamicSaveViewTooltip">
        <i class="el-icon-camera"></i>
        <span>保存视角</span>
      </div>
    </div>

    <!-- 保存成功提示 -->
    <div v-if="showSaveSuccess" class="save-success-tip">
      <i class="el-icon-success"></i>
      <span>{{ currentPageName + (currentSubPage ? '-' + currentSubPage : '') }}视角保存成功！</span>
    </div>

    <!-- 只有当有菜单且选中了菜单时才显示组件内容 -->
    <div class="home" v-if="menuList.length > 0 && activeComponent">
      <div class="screen-content">
        <component :is="activeComponent"></component>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenHeader from '@/components/commonView/screenHeader.vue'
import ProjectVideo from '@/components/ProjectVideo/index.vue'
import Panoramic from '@/components/Panoramic/index.vue'
import ProjectAI from '@/components/ProjectAI/index.vue'
import CesiumBaseMap from "@/components/map3dbase/CesiumBaseMap.vue"; // 地图组件
import Construction from './components/construction/index.vue' // 施工技防
import ProjectTurn from '@/components/ProjectTurn/index.vue'
import VisualProgress from '@/components/VisualProgress/index.vue' // 形象进度
import bimMask from '@/components/bimMask/index.vue' // 形象进度
import Cookies from "js-cookie";
import CivilAirDefense from '@/components/civilAirDefense/index.vue' // 人防（穿透式）
import { mapState, mapGetters } from 'vuex'
import axios from 'axios'

export default{
  name: 'point-screen',
  data() {
    return {
      projectId: '',
      activeComponent: '',
      isShow: true,
      // 保存视角相关
      hasSavedView: false,
      showSaveSuccess: false,
      saveViewTooltip: "保存当前地图视角",
      // 当前子页面名称，用于区分不同页面的视角保存
      currentSubPage: ''
    }
  },
  components: {
    ScreenHeader,
    ProjectVideo,
    Panoramic,
    CesiumBaseMap,
    ProjectAI,
    Construction,
    ProjectTurn,
    VisualProgress,
    bimMask,
    CivilAirDefense
  },
  computed: {
    ...mapState('menu', ['loading']),
    ...mapGetters('menu', ['menuList', 'activeMenu']),
    
    // 动态显示当前子页面的保存视角提示
    currentPageName() {
      const pageNameMap = {
        'Panoramic': '全景感知',
        'ProjectCrane': '吊车安全监测',
        'ProjectAnti': '施工防侵限',
        'ProjectAI': 'AI综合安防',
        'ProjectVideo': '视频监控',
        'Construction': '施工技防'
      };
      return pageNameMap[this.currentSubPage] || '当前页面';
    },
    // 动态生成保存视角的提示文本
    dynamicSaveViewTooltip() {
      return `保存${this.currentPageName}的地图视角`;
    },
    isShowBimMask() {
      return this.currentSubPage == 'VisualProgress'
    }
  },
  watch: {
    menuList: {
      handler(newVal) { // 监听菜单列表变化
        if (newVal&&newVal.length>0) {
          const activeItem = newVal.find(item => item.active)
          console.log("监听菜单列表变化: ", activeItem)
          if (activeItem) {
            this.switchMenu(activeItem)
          } else if (newVal.length > 0) {
            // 如果没有选中的菜单但有菜单列表，选择第一个
            this.switchMenu(newVal[0])
          }
        } else {
          this.activeComponent = ''
          this.currentSubPage = ''
          this.$refs.CesiumBaseMap?.removePoints()
          this.isShow = true
        }
      },
      deep: true,
      immediate: true,
    }
  },
  created() {
    this.projectId = this.$route.query.projectId
    this.userName = this.$route.query.userName;
    //获取这个用户下所有的项目
    this.getUserProjectWithoutWindowToken().then( response => {
      if (response && response.data && response.data.success) {
          //通过当前用户名进行匹配
          const project = response.data.data.projectList.find(item => item.linkappUser.username == this.userName);
          if(!project){
            //证明没有权限，则跳转到登录页面
            this.handleUnauthorizedAccess();
          }
        }
    }).catch(error => {
      console.error('获取用户项目失败:', error);
      this.handleUnauthorizedAccess();
    })
  },
  mounted() {
    // 页面挂载完成后，直接调用地图组件的模型加载方法
    console.log('🎯 pointScreen 页面已挂载，开始加载模型和绘制施工区域')

    // 检查保存的视角
    this.checkSavedView();

    // 监听施工技防子页面切换
    this.$bus.on('constructionSubPageChange', (subPageName) => {
      console.log('🎯 施工技防子页面切换:', subPageName);
      this.currentSubPage = subPageName;

      // 设置地图组件的当前页面名称
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.setCurrentPageName) {
        this.$refs.CesiumBaseMap.setCurrentPageName(this.currentSubPage || 'pointScreen');
      }

      this.checkSavedView();

      // 延迟一小段时间后尝试自动恢复视角（确保地图状态已更新）
      setTimeout(() => {
        this.autoRestoreView();
      }, 800);
    });

    // 延迟一点时间确保地图组件完全初始化
    setTimeout(() => {
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.addModelLayer) {
        this.$refs.CesiumBaseMap.addModelLayer()
      } else {
        // 如果直接调用失败，使用事件总线作为后备方案
        console.log('直接调用失败，使用事件总线')
        this.$bus.emit('enterPointScreen')
      }

      // 设置地图组件的当前页面名称
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.setCurrentPageName) {
        this.$refs.CesiumBaseMap.setCurrentPageName(this.currentSubPage || 'pointScreen');
      }
    }, 500)
  },
  methods: {
    // 获取用户项目（不包含windowToken）
    getUserProjectWithoutWindowToken() {
      // 创建一个完全独立的axios实例，不经过任何拦截器
      const independentAxios = axios.create({
        timeout: 10000,
        headers: {
          "Content-type": "application/json;charset=utf-8",
          'Accesstoken': Cookies.get("accessToken") || window.localStorage.getItem("token"),
        },
      });
      
      return independentAxios({
        method: "get",
        url: "/api/login/getUserProject",
      });
    },
    switchMenu(item) { // 切换菜单组件
      console.log(item)
      this.isShow = true;
      switch (item.name) {
        case '视频监控':
          this.activeComponent = 'ProjectVideo'
          this.isShow = false
          this.currentSubPage = 'ProjectVideo'
          break;
        case '全景感知':
          this.activeComponent = 'Panoramic'
          this.currentSubPage = 'Panoramic'
          // this.$bus.emit('changeMap', '全景感知')
          // this.$bus.emit('changeMap', 'high')
          break;
        case '施工技防':
          this.activeComponent = 'Construction'
          this.currentSubPage = 'ProjectCrane'
          this.$bus.emit('changeMap', 'construction')
          break;
        case '转体模拟':
          this.activeComponent = 'ProjectTurn'
          this.currentSubPage = 'ProjectTurn'
          this.isShow = false
          this.$bus.emit('changeMap', 'ProjectTurn')
          break;
        case '形象进度':
          this.activeComponent = 'VisualProgress'
          this.currentSubPage = 'VisualProgress'
          this.isShow = false
          this.$refs.CesiumBaseMap?.removePoints()
          this.$nextTick(() => {
            this.$refs.bimMaskRef?.init()
          })
          break;
        case '人防(穿透式)':
          this.activeComponent = 'CivilAirDefense'
          this.currentSubPage = 'CivilAirDefense'
          this.$bus.emit('changeMap', 'civilAirDefense')
          break;
        default:
          this.activeComponent = ''
          this.currentSubPage = ''
          this.$refs.CesiumBaseMap?.removePoints()
          break;
      }

      // 设置地图组件的当前页面名称
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.setCurrentPageName) {
        this.$refs.CesiumBaseMap.setCurrentPageName(this.currentSubPage || 'pointScreen');
      }

      // 切换页面后检查该页面是否有保存的视角
      this.checkSavedView();

      // 延迟一小段时间后尝试自动恢复视角（确保地图状态已更新）
      setTimeout(() => {
        if(this.activeComponent != 'Construction'){
          this.autoRestoreView();
        }
      }, 800);
    },

    // 保存视角功能
    handleSaveView() {
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.saveCameraView) {
        // 使用当前子页面名称作为保存标识
        const pageName = this.currentSubPage || 'pointScreen';
        console.log('🎯 保存视角 - 当前子页面:', this.currentSubPage, '页面名称:', this.currentPageName, '存储键:', pageName);
        const success = this.$refs.CesiumBaseMap.saveCameraView(pageName);

        if (success) {
          this.showSaveSuccess = true;
          this.hasSavedView = true;

          // 3秒后隐藏成功提示
          setTimeout(() => {
            this.showSaveSuccess = false;
          }, 3000);
        } else {
          this.$message.error('保存视角失败，请重试');
        }
      } else {
        this.$message.error('地图组件未初始化，无法保存视角');
      }
    },

    // 恢复视角功能
    handleRestoreView() {
      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.restoreCameraView) {
        // 使用当前子页面名称作为恢复标识
        const pageName = this.currentSubPage || 'pointScreen';
        console.log('🎯 恢复视角 - 当前子页面:', this.currentSubPage, '页面名称:', this.currentPageName, '存储键:', pageName);
        const success = this.$refs.CesiumBaseMap.restoreCameraView(pageName, true);

        if (!success) {
          this.$message.warning(`没有找到${this.currentPageName}的保存视角`);
        }
      } else {
        this.$message.error('地图组件未初始化，无法恢复视角');
      }
    },

    // 自动恢复视角功能
    autoRestoreView() {

      // 使用当前子页面名称检查保存的视角
      const pageName = this.currentSubPage || 'pointScreen';

      if (this.$refs.CesiumBaseMap && this.$refs.CesiumBaseMap.getSavedCameraView) {
        const savedView = this.$refs.CesiumBaseMap.getSavedCameraView(pageName);

        if (savedView) {
          console.log('🔄 自动恢复视角:', {
            '📄 页面名称': this.currentPageName,
            '🔑 存储键': pageName,
            '📅 保存时间': new Date(savedView.timestamp).toLocaleString('zh-CN')
          });

          // 自动恢复视角，使用动画效果
          this.$refs.CesiumBaseMap.restoreCameraView(pageName, true);

          // 显示提示信息
          this.$message.success(`已恢复${this.currentPageName}的保存视角`);
        }
      }
    },

    // 检查是否有保存的视角
    checkSavedView() {
      // 使用当前子页面名称检查保存的视角
      const pageName = this.currentSubPage || 'pointScreen';
      const savedView = localStorage.getItem(`cesium_camera_view_${pageName}`);
      this.hasSavedView = !!savedView;

      // 如果有保存的视角，记录日志
      if (this.hasSavedView) {
        try {
          const viewData = JSON.parse(savedView);
          console.log('✅ 检测到保存的视角:', {
            '📄 页面名称': this.currentPageName,
            '🔑 存储键': pageName,
            '📅 保存时间': new Date(viewData.timestamp).toLocaleString('zh-CN')
          });
        } catch (error) {
          console.warn('解析保存的视角数据时出错:', error);
          this.hasSavedView = false;
        }
      }
    }
  },
  beforeDestroy() {
    // 清理可能的事件监听器（虽然这个页面主要是发送事件）
    console.log('🧹 pointScreen 页面即将销毁，清理资源')
    this.$bus.off('constructionSubPageChange')
  }
}
</script>

<style lang="less" scoped>
.pointScreen {
  min-width: 100vw;
  min-height: 100vh;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.screen_top {
  position: relative;
  z-index: 1;
}
.base3dmap,
.bimMask {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 0;
  background-color: black;
}

/* 保存视角控制按钮样式 */
.save-view-controls {
  position: fixed !important;
  top: 90px;
  left: 420px;
  z-index: 9999 !important;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto !important;
}

.save-view-btn,
.restore-view-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 3px;
  background: linear-gradient(135deg, #1890ff, #0a2032);
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  user-select: none;
  min-width: 70px;
  justify-content: center;
  pointer-events: auto !important;
  position: relative;
  z-index: 10000 !important;

  &:hover {
    background: linear-gradient(135deg, #40a9ff, #1890ff);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
  }

  i {
    font-size: 16px;
  }

  span {
    font-size: 13px;
    letter-spacing: 0.5px;
  }
}

.restore-view-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d);

  &:hover {
    background: linear-gradient(135deg, #73d13d, #52c41a);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
  }
}

.save-success-tip {
  position: fixed;
  top: 100px;
  left: 560px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
  padding: 12px 18px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideInLeft 0.5s ease-out, fadeOutLeft 0.5s ease-out 2.5s;
  pointer-events: none;

  i {
    font-size: 16px;
  }

  span {
    font-size: 13px;
    letter-spacing: 0.5px;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .save-view-controls {
    top: 80px;
    left: 10px;
    gap: 8px;
  }

  .save-view-btn,
  .restore-view-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 100px;

    i {
      font-size: 14px;
    }

    span {
      font-size: 11px;
    }
  }

  .save-success-tip {
    top: 80px;
    left: 120px;
    padding: 10px 14px;
    font-size: 12px;

    i {
      font-size: 14px;
    }

    span {
      font-size: 11px;
    }
  }
}
</style>

<template>
   
  <div>
    <div class="container">
      <Row style="height: 100%">
        <Col :span="5" style="height: 100%">
          <div class="leftTop">
            <Title
              title="摄像头列表"
              desc="&nbsp;&nbsp;&nbsp;CAMERA LIST"
            ></Title>
            <div class="leftContent">
              <div class="tree">
                <Tree
                  ref="tree"
                  :data="data"
                  show-checkbox
                  check-directly
                  @on-check-change="handleCheckChange"
                  @on-toggle-expand="handleToggleExpand"
                  @on-select-change="handleSelectChange"
                ></Tree>
              </div>
            </div>
            <div class="leftFloor">
              <button class="primary" @click="saveSelection">保存</button>
              <button class="info" @click="resetSelection">重置</button>
            </div>
          </div>
        </Col>
        <Col :span="19" style="height: 100%">
          <div class="video-display-container">
            <DynamicVideoGrid :cameraList="videoInfoList" />
          </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import Title from "../../components/commonView/title.vue";
import {
  selectByUserAreaAllList,
  saveVideoSelected,
  getNowVideoSelected,
} from "@/api/xxindex";
import DynamicVideoGrid from "../../components/ProjectVideo/components/DynamicVideoGrid.vue";

// 图片
const tab1 = require("../../assets/images/icon/tab1.png");
const tab2 = require("../../assets/images/icon/tab2.png");
const tab3 = require("../../assets/images/icon/tab3.png");
const tab4 = require("../../assets/images/icon/tab4.png");
const tab1Active = require("../../assets/images/icon/tab1-1.png");
const tab2Active = require("../../assets/images/icon/tab2-1.png");
const tab3Active = require("../../assets/images/icon/tab3-1.png");
const tab4Active = require("../../assets/images/icon/tab4-1.png");
export default {
  components: {
    Title,
    DynamicVideoGrid,
  },
  data() {
    return {
      data: [],
      videoInfoList: [],
      Dict: [],
      tabList: [
        {
          id: 1,
          tab: tab1,
          tabActive: tab1Active,
          component: "DomTab1",
        },
        {
          id: 2,
          tab: tab2,
          tabActive: tab2Active,
          component: "DomTab2",
        },
        {
          id: 3,
          tab: tab3,
          tabActive: tab3Active,
          component: "DomTab3",
        },
        {
          id: 4,
          tab: tab4,
          tabActive: tab4Active,
          component: "DomTab4",
        },
      ],
      activeTab: {},
      changeData: "全部",
      aiVideoList: [],
      totalInfo: {},
      resetVideoInfoList: [],
      // localStorage键名常量
      STORAGE_KEY: "nanniwan_selected_camera_ids",
      aiVideoTitle: "",
    };
  },
  mounted() {
    this.initRequest();
  },
  computed: {
    srcList() {
      return this.aiVideoList.map((i) => i.image);
    },
  },
  methods: {
    changeTab(item) {
      this.activeTab = item;
    },
    async initRequest() {
      try {
        // 1. 首先获取区域树结构
        await this.loadVideoTree();

        // 4. 验证数据结构完整性
        this.validateDataStructure();

        // 5. 加载保存的选择状态（在数据完全加载后）
        this.$nextTick(() => {
          this.loadSavedSelection();
        });
      } catch (error) {
        console.error("初始化失败:", error);
      }
    },

    // 获取区域树结构
    async loadVideoTree() {
      try {
        const res = await selectByUserAreaAllList({});
        console.log(res, "res");
        if (res.code == "success" && res.data) {
          this.data = res.data;
          this.renameNameToTitleInArray(this.data);
          // 调试：打印区域树数据结构
          if (res.data.length > 0) {
            console.log("区域树数据示例:", res.data[0]);
            console.log("区域字段:", Object.keys(res.data[0]));
          }

          console.log("区域树加载成功:", this.data);

          // 打印处理后的树形结构
          this.printAreaTreeStructure(this.data, 0);
        } else {
          console.error("获取区域树失败: 响应数据无效", res);
          this.data = []; // 设置默认值
        }
      } catch (error) {
        console.error("获取区域树失败:", error);
        this.data = []; // 设置默认值
        throw error;
      }
    },

    // 根据areaId精确查找区域节点
    findAreaById(nodes, areaId) {
      if (Array.isArray(nodes)) {
        for (let node of nodes) {
          // 检查当前节点是否匹配
          if (node.id === areaId) {
            return node;
          }

          // 递归查找 children 子节点
          if (
            node.children &&
            Array.isArray(node.children) &&
            node.children.length > 0
          ) {
            const result = this.findAreaById(node.children, areaId);
            if (result) {
              return result;
            }
          }
        }
      }
      return null;
    },

    // 打印区域树结构（调试用）
    printAreaTreeStructure(nodes, level) {
      const indent = "  ".repeat(level);
      nodes.forEach((node) => {
        if (node.isCamera) {
          console.log(`${indent}📹 ${node.title} (摄像头)`);
        } else {
          const cameraCount = node.children
            ? node.children.filter((child) => child.isCamera).length
            : 0;
          console.log(
            `${indent}📁 ${node.title || node.name} (摄像头: ${cameraCount})`
          );

          // 递归打印 children 子节点
          if (
            node.children &&
            Array.isArray(node.children) &&
            node.children.length > 0
          ) {
            this.printAreaTreeStructure(node.children, level + 1);
          }
        }
      });
    },

    // 将 videoDeviceList 中的摄像头添加为区域节点的子节点
    addVideoDevicesToAreaNode(areaNode, videoDevices) {
      if (!areaNode.children) {
        areaNode.children = [];
      }

      console.log(
        `为区域 ${areaNode.title} 添加 ${videoDevices.length} 个摄像头`
      );

      videoDevices.forEach((device) => {
        // 检查是否已经存在该摄像头，避免重复添加
        const existingCamera = areaNode.children.find(
          (child) =>
            child.isCamera &&
            child.cameraData &&
            child.cameraData.id === device.id
        );

        if (existingCamera) {
          console.warn(
            `摄像头 ${device.deviceName} 已存在于区域 ${areaNode.title} 中，跳过重复添加`
          );
          return;
        }

        // 从childAttributeList中提取视频地址和accessToken
        const videoUrl = this.extractVideoUrl(device);
        const accessToken = this.extractAccessToken(device);

        // 从childAttributeList中提取最近一帧图片地址
        const lastFrameImage = this.extractLastFrameImage(device);

        // 增强摄像头数据
        const enhancedCamera = {
          ...device,
          streamUrl: videoUrl, // 添加视频流地址
          hasValidStream: !!videoUrl, // 标记是否有有效的视频流
          accessToken: accessToken, // 添加accessToken
          lastFrameImage: lastFrameImage, // 添加最近一帧图片地址
        };

        // 创建摄像头节点
        const cameraNode = {
          title: `📹 ${device.deviceName}`, // 使用emoji图标
          id: `camera_${device.id}`,
          isCamera: true,
          cameraData: enhancedCamera,
          checked: false,
          disabled: false,
          className: "camera-tree-node", // 添加CSS类名
        };

        areaNode.children.push(cameraNode);
        console.log(
          `摄像头 ${device.deviceName} 已添加到区域 ${areaNode.title}`
        );
      });

      // 删除原始的 videoDeviceList，避免数据冗余
      delete areaNode.videoDeviceList;
    },

    // 从设备的childAttributeList中提取视频地址
    extractVideoUrl(camera) {
      if (
        !camera.childAttributeList ||
        !Array.isArray(camera.childAttributeList)
      ) {
        console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
        return null;
      }

      // 查找propCode为'ezopen'的属性
      const ezOpenAttr = camera.childAttributeList.find(
        (attr) => attr.propCode === "ezopen"
      );

      if (ezOpenAttr && ezOpenAttr.propValue) {
        console.log(
          `摄像头 ${camera.deviceName} 视频地址: ${ezOpenAttr.propValue}`
        );
        return ezOpenAttr.propValue;
      } else {
        console.warn(`摄像头 ${camera.deviceName} 未找到ezopen视频地址`);
        return null;
      }
    },

    // 从设备的childAttributeList中提取accessToken
    extractAccessToken(camera) {
      if (
        !camera.childAttributeList ||
        !Array.isArray(camera.childAttributeList)
      ) {
        console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
        return null;
      }

      // 查找propCode为'accessToken'的属性
      const accessTokenAttr = camera.childAttributeList.find(
        (attr) => attr.propCode === "accessToken"
      );

      if (accessTokenAttr && accessTokenAttr.propValue) {
        console.log(
          `摄像头 ${camera.deviceName} accessToken: ${accessTokenAttr.propValue}`
        );
        return accessTokenAttr.propValue;
      } else {
        console.warn(`摄像头 ${camera.deviceName} 未找到accessToken`);
        return null;
      }
    },

    // 从设备的childAttributeList中提取最近一帧图片地址
    extractLastFrameImage(camera) {
      if (
        !camera.childAttributeList ||
        !Array.isArray(camera.childAttributeList)
      ) {
        console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
        return null;
      }

      // 查找propCode为'originImageKey'的属性
      const imageKeyAttr = camera.childAttributeList.find(
        (attr) => attr.propCode === "originImageKey"
      );

      if (imageKeyAttr && imageKeyAttr.propValue) {
        console.log(
          `摄像头 ${camera.deviceName} 最近一帧图片: ${imageKeyAttr.propValue}`
        );
        return imageKeyAttr.propValue;
      } else {
        console.warn(
          `摄像头 ${camera.deviceName} 未找到originImageKey图片地址`
        );
        return null;
      }
    },

    handleCheckChange(item) {
      // 处理勾选变化
      // 收集所有勾选的摄像头
      const selectedCameras = this.collectSelectedCameras(item);

      // 无论是否有选中摄像头，都直接传递选中的摄像头列表
      // 空数组时显示单个空播放器，有摄像头时按需显示
      this.videoInfoList = selectedCameras;
      console.log(this.videoInfoList, "57290e8");
      console.warn(`选中摄像头数量: ${selectedCameras.length}`);
    },

    // 处理展开/折叠事件
    handleToggleExpand(item) {
      console.log('展开/折叠事件触发:', item);
      
      // 不直接修改数据，让Tree组件自己处理展开状态
      // 这样可以避免干扰Tree组件的正常展开功能
      console.log(`节点 ${item.title} 展开/折叠状态变化`);
    },

    // 处理选择变化事件（点击节点时触发）
    handleSelectChange(selectedNodes, item) {
      console.log('选择变化事件触发:', selectedNodes, item);
      
      // 如果点击的是区域节点且有子节点，让Tree组件自己处理展开/折叠
      if (item && !item.isCamera && item.children && item.children.length > 0) {
        console.log(`点击区域节点: ${item.title}，让Tree组件处理展开/折叠`);
      }
    },

    // 收集所有勾选的摄像头
    collectSelectedCameras(selectedNodes) {
      const cameras = [];

      selectedNodes.forEach((node) => {
        if (node.isCamera) {
          // 如果是摄像头节点，直接添加
          cameras.push(node.cameraData);
        } else {
          // 如果是区域节点，收集其下的所有摄像头
          this.collectCamerasFromNode(node, cameras);
        }
      });

      // 去重处理
      const uniqueCameras = cameras.filter(
        (camera, index, self) =>
          index === self.findIndex((c) => c.id === camera.id)
      );

      return uniqueCameras;
    },

    // 从节点中收集摄像头
    collectCamerasFromNode(node, cameraList) {
      // 收集 children 中的摄像头
      if (
        node.children &&
        Array.isArray(node.children) &&
        node.children.length > 0
      ) {
        node.children.forEach((child) => {
          if (child.isCamera) {
            cameraList.push(child.cameraData);
          } else {
            // 递归收集子区域的摄像头
            this.collectCamerasFromNode(child, cameraList);
          }
        });
      }
    },

    renameNameToTitleInArray(array) {
      array.forEach((item) => {
        // 处理当前节点的 platformProjectName
        if (item.platformProjectName) {
          item.title = item.platformProjectName;
          delete item.platformProjectName;
        }

        // 将 junior 转换为 children，以便 Tree 组件正确显示
        if (item.junior && Array.isArray(item.junior)) {
          item.children = item.junior;
          delete item.junior;
          // 递归处理转换后的 children 子节点
          this.renameNameToTitleInArray(item.children);
        } else if (item.children && Array.isArray(item.children)) {
          item.title = item.name;
          delete item.name;
          // 递归处理 children 子节点
          this.renameNameToTitleInArray(item.children);
        }

        // 处理 videoDeviceList，将摄像头添加为子节点
        if (
          item.videoDeviceList &&
          Array.isArray(item.videoDeviceList) &&
          item.videoDeviceList.length > 0
        ) {
          this.addVideoDevicesToAreaNode(item, item.videoDeviceList);
        }
      });
    },
    // 验证数据结构完整性
    validateDataStructure() {
      console.log("=== 数据结构验证 ===");
      console.log(`区域树节点数: ${this.data ? this.data.length : 0}`);
      console.log(
        `摄像头总数: ${this.videoInfoList ? this.videoInfoList.length : 0}`
      );

      if (this.data && this.data.length > 0) {
        let totalAttachedCameras = 0;
        this.countAttachedCameras(this.data, (count) => {
          totalAttachedCameras += count;
        });
        console.log(`已挂接到区域的摄像头数: ${totalAttachedCameras}`);

        if (totalAttachedCameras < this.videoInfoList.length) {
          console.warn(
            `有 ${
              this.videoInfoList.length - totalAttachedCameras
            } 个摄像头未能挂接到区域`
          );
        }
      }
      console.log("=== 验证完成 ===");
    },

    // 统计挂接到区域的摄像头数量
    countAttachedCameras(nodes, callback) {
      nodes.forEach((node) => {
        // 统计 children 中的摄像头
        if (
          node.children &&
          Array.isArray(node.children) &&
          node.children.length > 0
        ) {
          const cameraCount = node.children.filter(
            (child) => child.isCamera
          ).length;
          if (cameraCount > 0) {
            callback(cameraCount);
          }

          // 递归统计 children 子区域
          const subAreas = node.children.filter((child) => !child.isCamera);
          if (subAreas.length > 0) {
            this.countAttachedCameras(subAreas, callback);
          }
        }
      });
    },
    async saveSelection() {
      try {
        // 收集当前选中的摄像头ID列表
        const selectedCameraIds = this.collectSelectedCameraIds(this.data);
        console.log(selectedCameraIds, "selectedCameraIds");
        // 检查是否有选择的摄像头
        if (selectedCameraIds.length === 0) {
          this.$Message.warning("请先选择要保存的摄像头");
          return;
        }
        console.log(selectedCameraIds, "selectedCameraIds");

        const res = await saveVideoSelected({
          selectIds: selectedCameraIds.join(","),
        });

        if (res.code == "success") {
          this.$Message.success("保存成功");
        } else {
          console.error("保存失败，响应数据:", res);
          this.$Message.error(`保存失败: ${res.message || "未知错误"}`);
        }
      } catch (error) {
        console.error("保存选择失败:", error);
        this.$Message.error(`保存失败: ${error.message || "网络错误"}`);
      }
    },
    resetSelection() {
      // 添加确认对话框
      this.$confirm(
        "确定要重置所有摄像头选择吗？此操作将清除所有已保存的选择状态。",
        "确认重置",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          try {
            const res = await saveVideoSelected({
              selectIds: "0",
            });
            if (res.code == "success") {
              // 清除所有勾选状态
              this.clearAllSelections(this.data);

              // 清空视频显示列表
              this.videoInfoList = [];
              this.$Message.success("重置成功");
            } else {
              this.$Message.error("重置失败");
            }

            console.log("已重置所有摄像头选择");
          } catch (error) {
            console.error("重置选择失败:", error);
          }
        })
        .catch(() => {
          // 用户取消重置
          console.log("用户取消重置操作");
        });
    },
    collectSelectedCameraIds(nodes) {
      const selectedIds = [];

      const traverseNodes = (nodeList) => {
        nodeList.forEach((node) => {
          if (node.isCamera && node.checked) {
            // 如果是选中的摄像头节点，添加摄像头ID
            if (node.cameraData && node.cameraData.id) {
              // 尝试转换为数字类型，如果失败则保持原样
              const id = node.cameraData.id;
              selectedIds.push(id);
            }
          } else if (!node.isCamera) {
            // 如果是区域节点，递归遍历子节点
            // 遍历 children 子节点
            if (
              node.children &&
              Array.isArray(node.children) &&
              node.children.length > 0
            ) {
              traverseNodes(node.children);
            }
          }
        });
      };

      traverseNodes(nodes);
      return selectedIds;
    },
    clearAllSelections(nodes) {
      const clearNodes = (nodeList) => {
        nodeList.forEach((node) => {
          // 清除节点的选中状态
          if (node.checked !== undefined) {
            node.checked = false;
          }

          // 递归清除子节点
          // 清除 children 子节点
          if (
            node.children &&
            Array.isArray(node.children) &&
            node.children.length > 0
          ) {
            clearNodes(node.children);
          }
        });
      };

      clearNodes(nodes);
    },
    async loadSavedSelection() {
      // 加载保存的选择
      try {
        const res = await getNowVideoSelected({});
        console.log(res);
        if (res.code == "success" && res.data) {
          console.log(res.data, "res.data, 加载保存的选择");

          // 从返回的数据中提取 selectId 字段
          if (res.data.selectId) {
            // 将 selectId 字符串按逗号分割成数组
            const selectedIds = res.data.selectId
              .split(",")
              .filter((id) => id.trim() !== "");
            console.log("解析的选中ID数组:", selectedIds);
            console.log("原始 selectId 字符串:", res.data.selectId);

            // 恢复选择状态
            this.restoreSelectionByIds(this.data, selectedIds);

            // 延迟展开包含选中摄像头的父节点，确保Tree组件完全渲染
            this.$nextTick(() => {
              setTimeout(() => {
                this.expandParentNodes(selectedIds);
              }, 300);
            });

            // 更新视频显示
            const selectedCameras = this.getSelectedCamerasByIds(selectedIds);
            this.videoInfoList = selectedCameras;

            console.log(`已恢复 ${selectedCameras.length} 个摄像头的选择状态`);
          } else {
            console.log("没有找到 selectId 字段或为空");
          }
        }
      } catch (error) {
        console.error("加载保存的选择失败:", error);
      }
    },

    // 展开包含选中摄像头的父节点
    expandParentNodes(selectedIds) {
      console.log("开始展开包含选中摄像头的父节点");

      // 直接遍历树形结构，找到包含选中摄像头的父节点并展开
      this.expandNodesWithSelectedCameras(this.data, selectedIds);

      console.log("展开完成");
    },

    // 展开包含选中摄像头的父节点
    expandNodesWithSelectedCameras(nodes, selectedIds) {
      nodes.forEach((node) => {
        if (node.isCamera && node.cameraData && node.cameraData.id) {
          // 如果是摄像头节点，检查是否被选中
          const nodeId = node.cameraData.id;
          const isSelected = selectedIds.some((selectedId) => {
            if (!isNaN(selectedId) && !isNaN(nodeId)) {
              return parseInt(selectedId) === parseInt(nodeId);
            }
            return selectedId === nodeId;
          });

          if (isSelected) {
            console.log(`找到选中的摄像头: ${node.title}`);
          }
        } else if (
          node.children &&
          Array.isArray(node.children) &&
          node.children.length > 0
        ) {
          // 检查当前节点是否包含选中的摄像头
          const hasSelectedCamera = this.hasSelectedCamera(
            node.children,
            selectedIds
          );

          if (hasSelectedCamera) {
            // 如果包含选中的摄像头，标记需要展开的节点
            // 但不直接设置expand属性，避免干扰Tree组件的正常展开功能
            node._shouldExpand = true; // 使用自定义标记
            console.log(`标记需要展开的节点: ${node.title}`);
          }

          // 递归处理子节点
          this.expandNodesWithSelectedCameras(node.children, selectedIds);
        }
      });
      
      // 延迟执行展开操作，确保Tree组件已经完全渲染
      this.$nextTick(() => {
        setTimeout(() => {
          this.expandMarkedNodes();
        }, 500); // 增加延迟时间，确保Tree组件完全渲染
      });
    },

    // 展开标记的节点
    expandMarkedNodes() {
      if (this.$refs.tree) {
        // 遍历数据，找到标记为需要展开的节点
        this.traverseAndExpandNodes(this.data);
      } else {
        // 如果Tree组件还没有准备好，延迟重试
        console.log('Tree组件未准备好，延迟重试展开操作');
        setTimeout(() => {
          this.expandMarkedNodes();
        }, 200);
      }
    },

    // 遍历并展开标记的节点
    traverseAndExpandNodes(nodes) {
      nodes.forEach((node) => {
        if (node._shouldExpand && node.children && node.children.length > 0) {
          // 清除标记
          delete node._shouldExpand;
          
          // 使用Tree组件的展开方法（如果存在）
          if (this.$refs.tree && this.$refs.tree.expand) {
            this.$refs.tree.expand(node);
          } else {
            // 如果没有直接的展开方法，尝试通过DOM操作
            this.expandNodeByDOM(node);
          }
        }
        
        // 递归处理子节点
        if (node.children && Array.isArray(node.children)) {
          this.traverseAndExpandNodes(node.children);
        }
      });
    },

    // 通过DOM操作展开节点
    expandNodeByDOM(node) {
      // 查找对应的DOM元素
      const treeElement = this.$refs.tree.$el;
      const nodeElements = treeElement.querySelectorAll('.ivu-tree-children li');
      
      let found = false;
      nodeElements.forEach((element) => {
        const titleElement = element.querySelector('.ivu-tree-title');
        if (titleElement && titleElement.textContent.trim() === node.title) {
          // 查找箭头元素并模拟点击
          const arrowElement = element.querySelector('.ivu-tree-arrow');
          if (arrowElement) {
            arrowElement.click();
            found = true;
            console.log(`通过DOM操作展开节点: ${node.title}`);
          }
        }
      });
      
      if (!found) {
        console.log(`未找到节点对应的DOM元素: ${node.title}`);
        // 如果没找到，延迟重试
        setTimeout(() => {
          this.expandNodeByDOM(node);
        }, 200);
      }
    },

    // 检查节点是否包含选中的摄像头
    hasSelectedCamera(nodes, selectedIds) {
      for (const node of nodes) {
        if (node.isCamera && node.cameraData && node.cameraData.id) {
          const nodeId = node.cameraData.id;
          const isSelected = selectedIds.some((selectedId) => {
            if (!isNaN(selectedId) && !isNaN(nodeId)) {
              return parseInt(selectedId) === parseInt(nodeId);
            }
            return selectedId === nodeId;
          });

          if (isSelected) {
            return true;
          }
        } else if (
          node.children &&
          Array.isArray(node.children) &&
          node.children.length > 0
        ) {
          // 递归检查子节点
          if (this.hasSelectedCamera(node.children, selectedIds)) {
            return true;
          }
        }
      }

      return false;
    },
    restoreSelectionByIds(nodes, selectedIds) {
      const restoreNodes = (nodeList) => {
        nodeList.forEach((node) => {
          if (node.isCamera && node.cameraData && node.cameraData.id) {
            // 如果是摄像头节点，检查是否在选中列表中
            // 支持字符串和数字类型的ID比较
            const nodeId = node.cameraData.id;
            const isSelected = selectedIds.some((selectedId) => {
              // 尝试数字比较
              if (!isNaN(selectedId) && !isNaN(nodeId)) {
                return parseInt(selectedId) === parseInt(nodeId);
              }
              // 字符串比较
              return selectedId === nodeId;
            });
            node.checked = isSelected;
          } else if (!node.isCamera) {
            // 递归处理子节点
            // 处理 children 子节点
            if (
              node.children &&
              Array.isArray(node.children) &&
              node.children.length > 0
            ) {
              restoreNodes(node.children);
            }
          }
        });
      };

      restoreNodes(nodes);
    },
    getSelectedCamerasByIds(selectedIds) {
      const cameras = [];

      const findCameras = (nodeList) => {
        nodeList.forEach((node) => {
          if (node.isCamera && node.cameraData && node.cameraData.id) {
            // 支持字符串和数字类型的ID比较
            const nodeId = node.cameraData.id;
            const isSelected = selectedIds.some((selectedId) => {
              // 尝试数字比较
              if (!isNaN(selectedId) && !isNaN(nodeId)) {
                return parseInt(selectedId) === parseInt(nodeId);
              }
              // 字符串比较
              return selectedId === nodeId;
            });

            if (isSelected) {
              cameras.push(node.cameraData);
            }
          } else if (!node.isCamera) {
            // 递归查找子节点
            // 查找 children 子节点
            if (
              node.children &&
              Array.isArray(node.children) &&
              node.children.length > 0
            ) {
              findCameras(node.children);
            }
          }
        });
      };

      findCameras(this.data);
      return cameras;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-carousel--horizontal {
  overflow: hidden;
}
/deep/ .ivu-select-selection {
  background-color: transparent;
  border: none;
  color: #fff;
}
/deep/ .ivu-select-arrow::before {
  content: "";
  display: block;
  width: 15px;
  height: 10px;
  background: url(../../assets//images/icon/down.png) no-repeat center center;
  background-size: contain;
}
.tooltip-content {
  width: 108px;
  height: 108px;
  .tooltip-item {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .items {
      width: 42%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 30px;
        height: 30px;
      }
    }
  }
}

.grid-three {
  height: 98%;
  display: flex;
  align-items: flex-end;
  justify-content: center;

  .grid-three-active {
    width: 26px;
    height: 26px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

.leftTop {
  width: 100%;
  height: calc(90vh - 20px);
  position: relative;
  overflow: hidden;
}

.rightTop {
  width: 100%;
  height: 50%;
}

.rightBottom {
  width: 100%;
  height: 100%;
  margin-top: 1px;
}

.video-item2 {
  width: 100%;
  height: 99% !important;
  background: url(../../assets/images/icon/border_back2.png) no-repeat center
    center !important;
  background-size: 100% 100% !important;
}

.video-item1 {
  width: 100%;
  height: 100%;
  background: url(../../assets/images/icon/border_back.png) no-repeat center
    center;
  background-size: 100% 100%;
  position: relative;

  .default-content {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    letter-spacing: 2px;

    .video {
      width: 100%;
      height: calc(100% - 45px);
    }

    .text-flex {
      width: 100%;
      display: flex;
      height: 45px;
      justify-content: space-between;
      color: #fff;

      div {
        width: 50%;
        display: flex;
        align-items: center;
        margin-left: 20px;
        padding: 10px;

        img {
          width: 22px;
          height: 20px;
        }

        span {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.leftButtom {
  width: 100%;
  height: calc(30vh - 28px);
  margin-top: 10px;
  padding: 10px;
  box-sizing: border-box;

  .icon-flex {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;

    .icon-item {
      width: 50%;
      display: flex;
      align-items: center;
      gap: 10px;

      .item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &:first-child {
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
        }

        &:last-child {
          color: #fff;
          font-size: 16px;
        }
      }
    }
  }

  .nav {
    width: 100%;
    height: 28px;
    margin-top: 20px;
    border-bottom: 1px dashed #ccc;

    .nav-title {
      display: flex;
      justify-content: space-between;
      gap: 40px;
      align-items: center;

      .back {
        width: 101px;
        height: 28px;
        background: url(../../assets/images/icon/smallTitle.png) no-repeat
          center center;
        background-size: contain;
        background-position-y: -2px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 18px;
        font-weight: 600;
      }

      .detail {
        width: 60%;
        display: flex;
        align-items: center;

        span {
          width: 90%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #fff;
          font-size: 14px;
        }

        img {
          width: 15px;
          height: 10px;
        }
      }
    }
  }

  .small-title {
    width: 100%;
    margin-top: 10px;

    .title-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        color: #fff;
        font-size: 14px;
      }

      .time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
      }
    }

    .video-img {
      width: 100%;
      height: calc(30vh - 28px - 40px - 10px);
      object-fit: cover;
      display: flex;
      flex-direction: column;
      gap: 10px;
      border: 1px solid;
      .image {
        flex-shrink: 0;
      }
    }
  }
}

.grid-three {
  height: 98%;
  display: flex;
  align-items: flex-end;
  justify-content: center;

  .grid-three-active {
    width: 26px;
    height: 26px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

.leftFloor {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #111b2c;

  .primary {
    padding: 8px 30px;
    background-color: #b48641;
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    outline: none;
    border: 1px solid #b48641;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: #c49652;
      border-color: #c49652;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(180, 134, 65, 0.3);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 1px 4px rgba(180, 134, 65, 0.3);
    }
  }

  .info {
    padding: 8px 30px;
    background-color: rgba(255, 255, 255, 0.15);
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    outline: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 1px 4px rgba(255, 255, 255, 0.1);
    }
  }
}

.container {
  width: 100%;
  height: calc(100vh - 100px);
  background-color: #111b2c;
  pointer-events: auto;
  padding: 16px;
  box-sizing: border-box;
}

.leftContent {
  height: calc(100% - 60px - 20px);
  overflow: hidden;
  overflow-y: auto;
  padding: 20px;
  padding-top: 0;
  box-sizing: border-box;
}

/deep/ .ivu-tree-children {
  font-size: 16px;

  li {
    .ivu-checkbox-inner {
      background-color: transparent;
    }

    .ivu-checkbox-inner:hover {
      border-color: #b88340;
    }

    .ivu-tree-title {
      color: #fff;
    }
  }
}

/deep/ .ivu-icon-ios-arrow-forward::before {
  content: "▶";
  color: #fff;
  font-size: 10px;
}

/deep/ .ivu-tree-children li:not(.ivu-tree-children[visible]) {
  margin: 20px 0 !important;
}

/deep/ .ivu-tree-title {
  background: transparent !important;
  pointer-events: auto !important; /* 确保标题可以点击 */
  cursor: pointer; /* 确保标题显示手型光标 */
}

/deep/ .ivu-tree li ul {
  padding: 0;
  margin-left: 24px;
}

/deep/ .ivu-tree li ul .ivu-tree-arrow-open {
  margin-left: 0;
}

/deep/ .ivu-tree li ul .ivu-tree-arrow:not(.ivu-tree-arrow-open) {
  margin-left: 0;
}

/deep/ .ivu-tree-children li {
  position: relative;
  line-height: 42px;
  cursor: pointer; /* 确保节点可点击 */
}

/deep/ .ivu-tree-arrow::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 50px;
  top: -3px;
  left: -10px;
  background-image: linear-gradient(
    to right,
    #4b83b700,
    #4b83b71a 52.5%,
    #4b83b700
  );
  pointer-events: none; /* 防止阻止点击事件 */
}

/deep/ .ivu-tree-arrow:hover::before {
  border: 1px solid #fff;
}

/deep/ .ivu-tree-title:hover {
  background-color: transparent;
}

/* 摄像头节点样式 */
/deep/ .camera-tree-node .ivu-tree-title,
/deep/ .ivu-tree li[data-camera="true"] .ivu-tree-title {
  color: #00d4ff !important; /* 摄像头节点使用蓝色 */
  background: rgba(0, 212, 255, 0.1) !important;
  padding: 4px 8px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: normal;

  &:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: #40e0ff !important;
  }
}

/* 区域节点样式增强 */
/deep/ .ivu-tree li .ivu-tree-title:not(.camera-tree-node) {
  color: #fff !important;
  font-weight: 500;
  padding: 2px 4px;

  &:hover {
    color: #b88340 !important;
  }
}

/* 摄像头节点的复选框样式 */
/deep/ .camera-tree-node .ivu-checkbox-inner,
/deep/ .ivu-tree li[data-camera="true"] .ivu-checkbox-inner {
  border-color: #00d4ff;

  &:hover {
    border-color: #40e0ff;
  }
}

/deep/ .camera-tree-node .ivu-checkbox-checked .ivu-checkbox-inner,
/deep/
  .ivu-tree
  li[data-camera="true"]
  .ivu-checkbox-checked
  .ivu-checkbox-inner {
  background-color: #00d4ff;
  border-color: #00d4ff;
}

/* 修复树形结构的层级缩进 */
/deep/ .ivu-tree {
  /* 重置所有默认的margin和padding */
  .ivu-tree-children {
    margin-left: 0;
    padding-left: 0;
  }

  /* 为每一级添加递增的缩进 */
  .ivu-tree-children .ivu-tree-children {
    margin-left: 24px;
  }

  .ivu-tree-children .ivu-tree-children .ivu-tree-children {
    margin-left: 24px;
  }

  .ivu-tree-children .ivu-tree-children .ivu-tree-children .ivu-tree-children {
    margin-left: 24px;
  }

  /* 确保箭头和内容对齐 */
  .ivu-tree-arrow {
    margin-right: 8px;
    width: 14px;
    height: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
  }

  /* 重置原有的不合理margin设置 */
  li ul {
    padding: 0;
    margin-left: 0;
  }

  li ul .ivu-tree-arrow-open,
  li ul .ivu-tree-arrow:not(.ivu-tree-arrow-open) {
    margin-left: 0;
  }

  /* 添加层级指示线 */
  .ivu-tree-children .ivu-tree-children li {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: -12px;
      top: 0;
      height: 21px;
      width: 1px;
      background: rgba(255, 255, 255, 0.15);
    }

    &:last-child::before {
      height: 21px;
    }
  }
}

.video-display-container {
  width: 100%;
  height: 100%;
}
</style>

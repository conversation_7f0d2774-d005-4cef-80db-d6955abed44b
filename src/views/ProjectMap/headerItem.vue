<template>
  <div class="project-map-page">
    <!-- 顶部导航栏 -->
    <div class="top-bar">
      <div class="tabs">
        <div class="tab-nav">
          <div class="tab-nav-item" v-for="(item, index) in navList" :key="index" :class="item.active ? 'active' : ''">
            <el-button type="primary" size="mini" class="tab-btn" plain @click="navClick(item)">
              {{ item.name }}
            </el-button>
          </div>
          <!-- <el-button type="primary" size="mini" class="tab-btn" plain>监控平台</el-button> -->
        </div>
      </div>
      <!-- <div class='back-system-btn' @click="goToSystem">进入后台</div> -->
    </div>
    <div class="ProjectMap" v-if="active == '项目地图'">
      <div class="content">
        <div class="left_component">
          <transition name="el-fade-in-linear">
            <component :is="leftComponent"></component>
          </transition>
          <div v-if="isShow || leftComponent == 'ProjectMap'" class="positRight" :class="{'isShow': isShow}" @click="leftShowClick">  
            <img src="../../assets/images/icon/posiright.png" alt="">
          </div>
        </div>
        <div class="right_component">
          <transition name="el-fade-in-linear">
            <component :is="rightComponent"></component>
          </transition>
        </div>
      </div>
    </div>
    <div class="ProjectVideo" v-if="active == '监控平台'">
      <VideoSurveillance />
    </div>
  </div>
</template>

<script>
import ProjectMap from './ProjectMap.vue'
import ProjectInfo from './ProjectInfo.vue'
import ProjectInfoRight from './ProjectInfoRight.vue';
import VideoSurveillance from '../VideoSurveillance/index.vue'; // 后续可删除
import { getUserProject } from '@/api/project'; // 引入API
import { removeSessionStore } from '@/utils/storage';
export default {
  name: 'headerItem',
  components: {
    ProjectMap,
    ProjectInfo,
    ProjectInfoRight,
    VideoSurveillance
  },
  data() {
    return {
      navList: [
        { name: '项目地图', active: true },
        { name: '监控平台', active: false }
      ],
      leftComponent: 'ProjectMap',
      rightComponent: '',
      active: '项目地图',
      isShow: false,
      projectList: [], // 项目列表数据
      IntervalHelper: null,
    };
  },
  created() {
    
    this.getUserProject(); // 获取用户项目信息
    // 每10秒自动加载一次数据
    // this.$timerManager.start('getUserProject', () => {
    //   this.getUserProject();
    // }, 10000);
  },
  mounted() {
    this.$bus.on('clickPoint', (data) => { // 监听点击地图点位点击事件
      console.log(data)
      this.$store.commit('mapShow/setGeoInfo', { lon: data.longitude, lat: data.latitude, cityName: data.projectArea})
      this.leftComponent = 'ProjectInfo'
      this.rightComponent = 'ProjectInfoRight'
      this.isShow = false
      this.emitProjectInfo(data); // 触发项目详情事件
    })
    this.$bus.on('clickMap', () => { // 监听点击地图事件
      this.leftComponent = 'ProjectMap'
      this.rightComponent = '';
      this.emitProjectList(); // 触发项目列表事件
    })
  },
  methods: {
    navClick(item) { // 导航栏点击事件
      this.active = item.name;
      this.navList.forEach((nav) => {
        nav.active = false;
      });
      item.active = true;
      this.$emit('activeModel', item.name);
    },
    leftShowClick() { // 点击隐藏左侧组件
      if (this.isShow) {
        this.leftComponent = 'ProjectMap'
        this.emitProjectList(); // 触发项目列表事件
      } else {
        this.leftComponent = ''
      }
      this.isShow = !this.isShow;
      this.$bus.emit('closeLeft', this.isShow, this.projectList)
      console.log(this.isShow)
    },
    getUserProject () {
      getUserProject().then(response => {
        // 处理获取到的项目信息
        console.log('项目数据:', response);
        if (response && response.success) {
          this.projectList = response.data.projectList.map(item => {
            item.menuName = ''; // 设置菜单名称
            item.text = item.projectShortName;  // 项目简称
            item.lon = item.longitude;
            item.lan = item.latitude;
            item.weather = item.weather || '晴'; // 确保天气信息存在
            item.celsius = item.temperature || 0; // 确保温度信息存在
            item.level = item.weatherLevel || '三'; // 确保天气等级
            item.name = item.platformProjectName || '未知项目'; // 确保项目名称
            item.build = item.projectBuildUnit || '未知建设单位'; // 确保建设单位信息存在
            item.design = item.designUnit || '未知设计单位'; // 确保设计单位信息存在
            item.construct = item.shigongUnit || '未知施工单位'; // 确保施工单位信息存在
            item.supervise = item.constructionControlUnit || '未知监理单位'; // 确保监理单位信息存在
            item.address = item.location || '未知地址'; // 确保项目地址信息存在

            return item
          }) || [];
          this.emitProjectList(); // 触发项目列表事件
          this.$bus.emit('closeLeft', false, this.projectList);
        }

      }).catch(error => {
        console.error('获取项目数据失败:', error);
      });
    },
    emitProjectList() { // 触发项目列表事件
      this.$nextTick(() => {
        this.$bus.emit('projectList', this.projectList);
      });
    },
    emitProjectInfo(data) { // 触发项目详情事件
      this.$nextTick(() => {
        this.$bus.emit('projectInfo', data);
      });
    },
    goToSystem() {
      window.open('/#/manage/overview', '_blank')
    }
  },

  beforeDestroy() {
    this.$bus.off('closeLeft')
    this.$bus.off('clickPoint')
    this.$bus.off('clickMap')
    // this.$timerManager.stop('getUserProject');
  }
};
</script>

<style lang="less" scoped>
.positRight{
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  cursor: pointer;
  pointer-events: auto;
  margin-left: 1px;
  transform: translateX(410px);
}
.isShow {
  transform: translateX(0px) rotate(180deg);
  transition: transform 0.3s ease;
}
.org-project-list-query{
  margin-top: 15px;
  padding-right: 5px;
  margin-bottom: 10px;
  .input {
    /deep/ .ivu-input{
      background-color: transparent;
    }
  }
}
.org-project-list{
  flex: 1;
  display: flex;
  flex-direction: column;
  .org-project-list-nav {
    margin-top: 10px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .pro-status-item{
      position: relative;
      width: 69px;
      height: 21px;
      text-align: center;
      color: #fff;
      font-size: 12px;
      line-height: 21px;
      cursor: pointer;
      background: url('../../assets/images/ProjectMap/nav-item.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .pro-status-item.active {
      background: url('../../assets/images/ProjectMap/nav-active.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
.echartsContainer {
  width: 100%;
  display: flex;
  align-items: center;

  .echarts {
    width: 40%;
    height: 186px;
    display: flex;
    justify-content: center;
    align-items: center;

    #Echarts1 {
      width: 100px;
      height: 100px;
    }
  }

  .list {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 5px;
  }
  .chart-item-lb-right {
    width: 65%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .chart-item-lb-right-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .item-right {
          display: flex;
          align-items: center;
          width: 100%;

          .point {
            width: 5px;
            height: 5px;
            border-radius: 50px;
            background-color: #225EDD;
            margin-right: 10px;
          }

          .name-title {
            width: 50px;
            color: #ffffffe6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .chart-div {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  

/deep/ .ivu-progress-text-inner {
  font-size: 16px;
  color: #fff;
}

.project-map-page {
  width: 100%;
  // height: 100%;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url('../../assets/images/ProjectMap/top-bg2.png') no-repeat;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    pointer-events: auto;
    position: relative;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url('../../assets/images/ProjectMap/top-bg.png') no-repeat center center;
      background-size: cover;
      height: 87.14px;


      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png) no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: '';
            background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: '';
            background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
    .back-system-btn {
      // margin-left: 30px;
      cursor: pointer;
      width: 84px;
      height: 28px;
      background-size: 100% 100%;
      color: #fff;
      position: absolute;
      bottom: 10px;
      right: 20px;
      z-index: 2;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    // height: calc(100% - 100px);
  }
}



.stat-list {
  margin-bottom: 10px;
}

.stat-item {
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px 0;
  background-image: linear-gradient(to right, transparent, rgba(75, 131, 183, .1), transparent);

  .stat-name {
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 10px;

    .text {
      width: 80%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .point {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #2fe0e0;
    }
  }

  .stat-value {
    flex: 1;
  }
}

.circle-progress {
  margin: 10px 0;
}

.project-list {
  margin-top: 10px;
  overflow-y: auto;
  height: 28vh;
}

.project-item {
  background: rgba(0, 88, 204, .2);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
  .pro-name{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    .pro-name-text{
      flex: 1;
      display: flex;
      align-items: center;
      .pro-name-text-icon-up{
        width: 20px;
        height: 20px;
        background: url('../../assets/images/ProjectMap/title-left.png') no-repeat center center;
        background-size: 100% 100%;
      }
      .pro-name-text-title{
        flex: 1;
        margin-left: 5px;
        letter-spacing: .5px;
        font-size: 14px;
        color: #fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .pro-name-icon-jsz {
      width: 17px;
      height: 17px;
      background: url('../../assets/images/ProjectMap/title-right.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .pro-info  {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    width: 100%;
    letter-spacing: .5px;
    .pro-info-title {
      width: 100px;
      color: #ffffff80;
      line-height: 25px;
    }
    .pro-info-text{
      flex: 1;
      color: #fff;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.map-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.map-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.95) contrast(1.1);
}

.map-marker {
  position: absolute;
  /* 这里的top/left根据实际项目点调整 */
  transform: translate(-50%, -100%);
  background: rgba(20, 40, 70, 0.85);
  color: #fff;
  border-radius: 8px;
  padding: 8px 16px;
  box-shadow: 0 2px 8px #0008;
  text-align: center;
}

.marker-info span {
  font-size: 14px;
  margin-bottom: 2px;
  display: block;
}

.ProjectMap {
  height: calc(100% - 100px);
  .content {
    height: 100%;
    .left_component, .right_component {
      height: 100%;
    }
  }
}
</style>

/**
 * 获取近七天的日期数组
 * @param {string} format - 日期格式，可选值：'YYYY-MM-DD'、'MM/DD'、'MM-DD'等
 * @returns {Array} 近七天的日期数组
 */
export function getRecentSevenDays(format = 'YYYY-MM-DD') {
    const dates = []
    const today = new Date()

    for (let i = 6; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)

        let formattedDate = ''

        switch (format) {
            case 'YYYY-MM-DD':
                formattedDate = date.getFullYear() + '-' +
                    String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0')
                break
            case 'MM-DD':
                formattedDate = String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0')
                break
            case 'MM/DD':
                formattedDate = String(date.getMonth() + 1).padStart(2, '0') + '/' +
                    String(date.getDate()).padStart(2, '0')
                break
            case 'chinese':
                formattedDate = (date.getMonth() + 1) + '月' + date.getDate() + '日'
                break
            default:
                formattedDate = date.getFullYear() + '-' +
                    String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0')
        }

        dates.push(formattedDate)
    }

    return dates
}

// 使用示例：
// console.log(getRecentSevenDays()) 
// 输出：['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05', '2024-01-06', '2024-01-07']

// console.log(getRecentSevenDays('MM-DD'))
// 输出：['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07']

// console.log(getRecentSevenDays('chinese'))
// 输出：['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日']
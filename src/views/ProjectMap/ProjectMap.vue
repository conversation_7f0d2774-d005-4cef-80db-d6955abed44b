<template>
  <div class="project-map-page">
    <!-- 顶部导航栏 -->
    <!-- <div class="top-bar">
      <div class="tabs">
        <div class="tab-nav">
          <div class="tab-nav-item" v-for="(item, index) in navList" :key="index" :class="item.active ? 'active' : ''">
            <el-button type="primary" size="mini" class="tab-btn" plain @click="navClick(item)">
              {{ item.name }}
            </el-button>
          </div>
          <el-button type="primary" size="mini" class="tab-btn" plain>监控平台</el-button>
        </div>
      </div>
    </div> -->
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="side-panel" style="pointer-events: auto; height: 100%;">
        <!-- 项目信息统计 -->
        <div class="panel-section">
          <!-- <div class="section-title">项目信息统计 <span>PROJECT STATISTICS</span></div> -->
          <Title title="项目信息统计" desc="PROJECT STATISTICS"></Title>
          <!-- 这里可以用ECharts实现环形进度条和进度条 -->
          <div class="stat-list">
            <div class="echartsContainer">
              <div class="echarts" ref="echarts1">
                <div id="Echarts1" ref="echarts1s"></div>
              </div>
              <div class="list">
                <div class="stat-item" v-for="(item, index) in data1" :key="index">
                  <div class="stat-name"><span class="point" :style="{ background: item.color }"></span><span class="text">{{ item.title }}</span></div>
                  <div class="stat-value">{{ item.value }}%</div>
                </div>
              </div>
            </div>
          </div>
          <div class="stat-list">
            <div class="echartsContainer">
              <div class="echarts" ref="echarts2">
                <div id="Echarts2" ref="echarts2s"></div>
              </div>
              <div class="chart-item-lb-right">
                <div class="chart-item-lb-right-item" v-for="(item, index) in data2" :key="index">
                  <div class="item-right">
                    <div class="point" :style="{background: item.color }"></div>
                    <div class="name-title">{{ item.title }}</div>
                    <div class="chart-div">
                      <Progress :percent="Number(item.value)" status="active" :stroke-width="6" style="width: 100%;">
                        <span v-if="Number(item.value) === 100" style="font-size: 1rem;">100%</span>
                      </Progress>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目列表 -->
        <div class="panel-section" style="margin-top: -20px;">
          <Title title="项目列表" desc="PROJECT LIST"></Title>
          <div class="org-project-list-query">
            <Input v-model='searchProName' type="text" prefix="ios-search" placeholder="请输入项目名称" class="input" />
          </div>
          <div class="org-project-list">
            <div class="org-project-list-nav">
              <div :class="'pro-status-item' + (navIndex === 0 ? ' active' : '')" @click="proTabClick(0)">
                全部
              </div>
              <div :class="'pro-status-item' + (navIndex === 1 ? ' active' : '')" @click="proTabClick(1)">
                <el-badge :value="projectList.filter(item => item.isFollowed === true).length" :max='99' class="item">
                  重点关注
                </el-badge>
              </div>
              <div :class="'pro-status-item' + (navIndex === 2 ? ' active' : '')" @click="proTabClick(2)">
                <el-badge :value="projectList.filter(item => item.projectStatus == 2).length" :max='99' class="item">
                  建设中
                </el-badge>
              </div>
              <div :class="'pro-status-item' + (navIndex === 3 ? ' active' : '')" @click="proTabClick(3)">
                <el-badge :value="projectList.filter(item => item.projectStatus == 3).length" :max='99' class="item">
                  已完工
                </el-badge>
              </div>
              <div :class="'pro-status-item' + (navIndex === 4 ? ' active' : '')" @click="proTabClick(4)">
                <el-badge :value="projectList.filter(item => item.projectStatus == 4).length" :max='99' class="item">
                  停工中
                </el-badge>
              </div>
            </div>
          </div>
          <div class="project-list">
            <div class="project-item" v-for="item in filteredProjectList" :key="item.id">
              <div class="pro-name">
                <div class="pro-name-text" @click="enterProject(item)">
                  <div class="pro-name-text-icon-up"></div>
                  <div class="pro-name-text-title">
                    {{ item.platformProjectName }}
                  </div>
                </div>
                <div class="pro-name-actions">
                  <div class="follow-btn" @click.stop="toggleFollow(item)" :class="{ 'followed': item.isFollowed }">
                    {{ item.isFollowed ? '已关注' : '关注' }}
                  </div>
                  <div class="pro-name-icon-jsz"></div>
                </div>
              </div>
              <div class="pro-info">
                <div class="pro-info-title">
                  所属片区
                </div>
                <div class="pro-info-text">
                  {{ item.belongArea }}
                </div>
              </div>
              <div class="pro-info">
                <div class="pro-info-title">
                  涉铁类别
                </div>
                <div class="pro-info-text">
                  {{ item.projectCrossType == 1 ? '上跨' : item.projectCrossType == 2 ? '下穿' : '临近' }}
                </div>
              </div>
              <div class="pro-info">
                <div class="pro-info-title">
                  涉及线别
                </div>
                <div class="pro-info-text">
                  {{ item.projectLine }}
                </div>
              </div>
              <div class="pro-info">
                <div class="pro-info-title">
                  影响运输
                </div>
                <div class="pro-info-text">
                  {{ item.projectTransport }}
                </div>
              </div>
              <div class="pro-info">
                <div class="pro-info-title">
                  预计建设周期
                </div>
                <div class="pro-info-text">
                  {{ item.estimateTime ? item.estimateTime.split(' ')[0] : '' }} 至 {{ item.completionTime ? item.completionTime.split(' ')[0] : '' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 

<script>
import Title from '@/components/commonView/title.vue';
import * as echarts from 'echarts';
import { followProject, userSelectProject, getLoginUserInfo } from '@/api/project';
import { setStore } from '@/utils/storage';
export default {
  name: 'ProjectMap',
  components: {
    Title,
  },
  data() {
    return {
      navList: [
        { name: '项目地图', active: true },
        // { name: '监控平台', active: false }
      ],
      echartsList: {
        echarts1: null,
        echarts2: null
      },
      data1: [
        // {
        //   title: '这是片区一',
        //   value: 20,
        //   color: '#023084'
        // },
        // {
        //   title: '这是片区二',
        //   value: 20,
        //   color: '#0090FF'
        // },
        // {
        //   title: '这是片区三',
        //   value: 20,
        //   color: '#5DD9FF'
        // },
        // {
        //   title: '这是片区四',
        //   value: 20,
        //   color: '#FF9F39'
        // },
        // {
        //   title: '这是片区五',
        //   value: 20,
        //   color: '#FFCE00'
        // }
      ],
      data2: [
        {
          title: '上跨',
          value: 0,
          color: '#023289',
          type: 1
        },
        {
          title: '下穿',
          value: 0,
          color: '#0090FF',
          type: 2
        },
        {
          title: '临近',
          value: 0,
          color: '#51DDFE',
          type: 3
        }
      ],
      navIndex: 0,
      projectList: [], // 项目列表数据
      searchProName: '',
      currentBelongArea: '', // 当前选中的片区
    };
  },
  computed: {
    filteredProjectList() {
      console.log(this.projectList)
      // 如果没有输入搜索条件，返回完整的项目列表
      let list = [];
      if (this.navIndex === 0) {
        list = this.projectList;
      } else if (this.navIndex === 1) {
        // navIndex === 1 时显示重点关注的项目
        list = this.projectList.filter(item => item.isFollowed === true);
      } else {
        list = this.projectList.filter(item => item.projectStatus == this.navIndex);
      }
      if (!this.searchProName) {
        return list;
      }
      console.log(this.data2)
      // 根据搜索条件过滤项目列表
      return list.filter(item => {
        return item.platformProjectName.toLowerCase().includes(this.searchProName.toLowerCase());
      });
    },

  },
  created() {
    console.log('项目地图组件创建');
    // 监听项目列表数据变化
    this.$bus.on('projectList', (data) => {
      console.log('项目列表数据变化', data);
      this.projectList = data.map(item => {
        return {
          ...item,
          belongArea: item.belongArea || '未知片区' // 如果没有片区则设置为'未知片区'
        };
      });

      // 将项目列表中的片区先提取出来，然后将每个片区的项目数量计算出来
      const areaSet = new Set();
      this.projectList.forEach(item => {
        areaSet.add(item.belongArea); // 如果没有片区则设置为'未知片区'
      });
      this.data1 = Array.from(areaSet).map((area, index) => {
        return {
          title: area,
          value: 0, // 初始值为0
          color: ['#023084', '#0090FF', '#5DD9FF', '#FF9F39', '#FFCE00'][index % 5] // 循环使用颜色
        };
      });
      this.data1.forEach(item => {
        // 计算每个片区的项目数量 取整数百分比
        item.value = Math.floor(this.projectList.filter(pro => pro.belongArea === item.title).length / this.projectList.length * 100);
      });
      this.$nextTick(() => {
        this.initEcharts1();
      });
      this.formatData2Data();
    });
  },
  methods: {
    initEcharts1() {
      let data = this.data1.map(i => {
        return {
          value: i.value,
          name: i.title,
          itemStyle: {
            color: i.color
          }
        }
      })
      // 获取dom元素的高度
      this.$refs.echarts1s.style.height = this.$refs.echarts1.offsetHeight + 'px';
      this.$refs.echarts1s.style.width = this.$refs.echarts1.offsetWidth + 'px';
      this.echartsList.echarts1 && this.echartsList.echarts1.dispose();
      this.echartsList.echarts1 = echarts.init(document.getElementById('Echarts1'));
      // 画一个饼状图
      const option = {
        title: [
          {
            text: '片区占比',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 12,
              fontWeight: 'bold',
              color: '#A1A5AB'
            },
          }
        ],
        tooltip: {
          trigger: 'item',
          position: 'right',
          // 值显示百分比
          formatter: function(params) {
            return params.name + '：' + params.value + '%';
          }
        },
        series: [
          {
            name: '片区占比',
            type: 'pie',
            radius: ['60%', '90%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            data: data
          }
        ]
      }
      this.echartsList.echarts1.setOption(option);

      let that = this;
      this.echartsList.echarts1.on('click', 'series', (params) => {
        // 点击饼图时，打印点击的片区名称
        that.currentBelongArea = params.name; // 设置当前选中的片区
        that.formatData2Data(); // 更新第二个图表数据
        console.log('Clicked on area:', params.name);
      });
    },
    initEcharts2() {
      console.log(this.data2)
      console.log(this.projectList, '432890482');
      // 计算各类型项目数量
      const total = this.projectList.length;
      const skCount = this.projectList.filter(pro => pro.projectCrossType == 1).length;
      const xcCount = this.projectList.filter(pro => pro.projectCrossType == 2).length;
      const ljCount = this.projectList.filter(pro => pro.projectCrossType != 1 && pro.projectCrossType != 2).length;
      this.data2.forEach(item => {
        if (total === 0) {
          item.value = 0;
        } else if (item.title === '上跨') {
          item.value = (skCount / total * 100).toFixed(0);
        } else if (item.title === '下穿') {
          item.value = (xcCount / total * 100).toFixed(0);
        } else if (item.title === '临近') {
          item.value = (ljCount / total * 100).toFixed(0);
        }
      });
      // 获取dom元素的高度
      this.$refs.echarts2s.style.height = this.$refs.echarts2.offsetHeight + 'px';
      this.$refs.echarts2s.style.width = this.$refs.echarts2.offsetWidth + 'px';
      this.echartsList.echarts2 && this.echartsList.echarts2.dispose();
      this.echartsList.echarts2 = echarts.init(document.getElementById('Echarts2'));
      // 画一个饼状图
      const option = {
        title: [
          {
            text: '项目种类',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 12,
              fontWeight: 'bold',
              color: '#A1A5AB'
            },
          }
        ],
        tooltip: {
          trigger: 'item',
          position: 'right',
          // 值显示百分比
          formatter: function(params) {
            console.log(params)
            return params.data.title + '：' + params.value + '%';
          }
        },
        series: [
          {
            name: '项目种类',
            type: 'pie',
            radius: ['50%', '80%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            data: this.data2
          }
        ]
      }
      this.echartsList.echarts2.setOption(option);
    },
    navClick(item) { // 导航栏点击事件
      this.navList.forEach((nav) => {
        nav.active = false;
      });
      item.active = true;
    },
    proTabClick(index) {
      this.navIndex = index;
    },
    async toggleFollow(item) {
      try {
        const params = {
          tenantId: item.id, // 传递项目ID
          follow: !item.isFollowed // 切换关注状态
        };

        const response = await followProject(params);

        if (response && response.success) {
          // 更新本地状态
          item.isFollowed = !item.isFollowed;
          this.$message.success(item.isFollowed ? '关注成功' : '取消关注成功');

          // 触发项目列表更新事件，通知其他组件
          this.$bus.emit('projectList', this.projectList);
        } else {
          this.$message.error(response?.message || '操作失败');
        }
      } catch (error) {
        console.error('关注操作失败:', error);
        this.$message.error('操作失败，请稍后重试');
      }
    },
    enterProject(item) {
      console.log('点击进入项目', item);

      // 切换菜单到全景感知
      try {
        this.$store.commit('menu/setActiveMenu', {
          active: true,
          name: "全景感知"
        });
        console.log('✅ 菜单切换成功');
      } catch (error) {
        console.error('❌ 菜单切换失败:', error);
      }

      this.userSelectProjectAndEnter(item);
    },
    userSelectProjectAndEnter(item) {
      let linkappUser = item.linkappUser;
      if (!linkappUser || !linkappUser.username) {
        this.$message.error('项目用户信息不完整，无法进入项目');
        return;
      }

      // 将当前项目名存入缓存
      setStore('projectName', item.platformProjectName);
      setStore('projectId', item.id);

      userSelectProject({ username: linkappUser.username }).then((res) => {
        console.log('用户选择项目返回', res);
        if (res.success) {
          this.loginSuccessAndEnter(item);
        } else {
          this.$message.error('选择项目失败');
        }
      }).catch(error => {
        console.error('选择项目失败:', error);
        this.$message.error('选择项目失败，请稍后重试');
      });
    },
    loginSuccessAndEnter(projInfo) {
      getLoginUserInfo().then((res) => {
        console.log('获取用户信息成功', res);
        if (res.success) {
          const data = res.data;
          const authData = res.data.auth;
          let auth = {};
          authData.forEach((item) => {
            auth[item.code] = true;
          });

          // 将用户信息存入缓存
          setStore('auth', JSON.stringify(auth || '{}'));
          setStore('user', JSON.stringify(data.user));

          // 打开新窗口，添加userName参数
          const route = this.$router.resolve({
            name: 'pointScreen',
            query: {
              projectId: projInfo.id,
              userName: projInfo.linkappUser.username
            }
          });
          window.open(route.href, '_blank');
        } else {
          this.$message.error('获取用户信息失败');
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        this.$message.error('获取用户信息失败，请稍后重试');
      });
    },
    formatData2Data() {
      // 先根据当前选择的判断，过滤出当前片区的项目
      let projectList = this.projectList.map(item => item);
      if (this.currentBelongArea) {
         projectList = projectList.filter(item => item.belongArea === this.currentBelongArea);
      }
      // 清空data2数据  
      this.data2.forEach(item => {
        item.value = 0; // 重置值
      });
      // 计算每种类型的项目数量
      this.data2 = this.data2.map(item => {
        return {
          ...item,
          value: 0 // 初始化值为0
        };
      }); 

      this.data2.forEach(item => {
        // 计算每种类型的项目数量 取整数百分比
        item.value = Math.floor(projectList.filter(pro => pro.projectCrossType === item.type).length / projectList.length * 100);
        this.$nextTick(() => {
          // 初始化第二个图表
          this.initEcharts2();
        })
      });
    }
  },
  beforeDestroy() {
    this.echartsList.echarts1 && this.echartsList.echarts1.dispose();
    this.echartsList.echarts2 && this.echartsList.echarts2.dispose();
    this.$bus.off('projectList'); // 移除监听
    console.log('项目地图组件销毁');
  },
};
</script>

<style lang="less" scoped>
.panel-section {
  height: 50%;
}
.posiRight{
  height: 95vh;
  display: flex;
  align-items: center;
  cursor: pointer;
  pointer-events: auto;
  margin-left: 1px;
}
.org-project-list-query{
  margin-top: 15px;
  padding-right: 5px;
  margin-bottom: 10px;
  .input {
    /deep/ .ivu-input{
      background-color: transparent;
    }
  }
}
.org-project-list{
  flex: 1;
  display: flex;
  flex-direction: column;
  .org-project-list-nav {
    margin-top: 10px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .pro-status-item{
      position: relative;
      width: 69px;
      height: 21px;
      text-align: center;
      color: #fff;
      font-size: 12px;
      line-height: 21px;
      cursor: pointer;
      background: url('../../assets/images/ProjectMap/nav-item.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .pro-status-item.active {
      background: url('../../assets/images/ProjectMap/nav-active.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
.echartsContainer {
  width: 100%;
  display: flex;
  align-items: center;

  .echarts {
    width: 40%;
    height: 186px;
    display: flex;
    justify-content: center;
    align-items: center;

    #Echarts1 {
      width: 100px;
      height: 100px;
    }
  }

  .list {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 5px;
  }
  .chart-item-lb-right {
    width: 65%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .chart-item-lb-right-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .item-right {
          display: flex;
          align-items: center;
          width: 100%;

          .point {
            width: 5px;
            height: 5px;
            border-radius: 50px;
            background-color: #225EDD;
            margin-right: 10px;
          }

          .name-title {
            width: 50px;
            color: #ffffffe6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .chart-div {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  

/deep/ .ivu-progress-text-inner {
  font-size: 16px;
  color: #fff;
}

.project-map-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  // top: 0;
  left: 0;
  z-index: 1;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url('../../assets/images/ProjectMap/top-bg2.png') no-repeat center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url('../../assets/images/ProjectMap/top-bg.png') no-repeat center center;
      background-size: cover;
      height: 87.14px;


      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;
          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png) no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: '';
            background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: '';
            background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }
}



.stat-list {
  height: 45%;
  margin-bottom: 10px;
}

.stat-item {
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px 0;
  background-image: linear-gradient(to right, transparent, rgba(75, 131, 183, .1), transparent);

  .stat-name {
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 10px;

    .text {
      width: 80%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .point {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #2fe0e0;
    }
  }

  .stat-value {
    flex: 1;
  }
}

.circle-progress {
  margin: 10px 0;
}

.project-list {
  margin-top: 10px;
  overflow-y: auto;
  height: 75%;
}

.project-item {
  background: rgba(0, 88, 204, .2);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
  .pro-name{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    .pro-name-text{
      flex: 1;
      display: flex;
      align-items: center;
      .pro-name-text-icon-up{
        width: 20px;
        height: 20px;
        background: url('../../assets/images/ProjectMap/title-left.png') no-repeat center center;
        background-size: 100% 100%;
      }
      .pro-name-text-title{
        flex: 1;
        margin-left: 5px;
        letter-spacing: .5px;
        font-size: 14px;
        color: #fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .pro-name-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      .follow-btn {
        padding: 4px 12px;
        background: rgba(0, 144, 255, 0.8);
        border: 1px solid #0090FF;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        &:hover {
          background: rgba(0, 144, 255, 1);
          transform: scale(1.05);
        }
        &.followed {
          background: rgba(81, 221, 254, 0.8);
          border-color: #51DDFE;
          &:hover {
            background: rgba(81, 221, 254, 1);
          }
        }
      }
      .pro-name-icon-jsz {
        width: 17px;
        height: 17px;
        background: url('../../assets/images/ProjectMap/title-right.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
  .pro-info  {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    width: 100%;
    letter-spacing: .5px;
    .pro-info-title {
      width: 100px;
      color: #ffffff80;
      line-height: 25px;
    }
    .pro-info-text{
      flex: 1;
      color: #fff;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.map-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}
.map-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.95) contrast(1.1);
}
.map-marker {
  position: absolute;
  /* 这里的top/left根据实际项目点调整 */
  transform: translate(-50%, -100%);
  background: rgba(20, 40, 70, 0.85);
  color: #fff;
  border-radius: 8px;
  padding: 8px 16px;
  box-shadow: 0 2px 8px #0008;
  text-align: center;
}
.marker-info span {
  font-size: 14px;
  margin-bottom: 2px;
  display: block;
}

/deep/ .el-badge__content.is-fixed {
  top: -5px;
  right: 0;
}
</style>

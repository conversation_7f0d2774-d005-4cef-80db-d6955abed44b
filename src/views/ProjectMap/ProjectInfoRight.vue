<template>
   
  <div class="w-full">
    <div class="right-project">
      <Title title="项目进度" desc="&nbsp;&nbsp;PROJECT PROGRESS"></Title>
      <div class="wrap-theme1-content">
        <Row style="height: 100%">
          <Col :span="12" style="display: flex; flex-direction: column">
            <div class="projectProgress-row">剩余竣工时间</div>
            <div class="projectProgress-time">
              <!-- 已完工 -->
              <div v-if="projectDetail.projectStatus == 3">
                0<span class="projectProgress-day">天</span>
              </div>
              <template v-else>
                <div v-if="projectDetail.surplusDay >= 0">
                  {{ projectDetail.surplusDay }}
                </div>
                <div v-else>
                  已延期
                  {{ Math.abs(projectDetail.surplusDay) }}
                </div>
                <span class="projectProgress-day"> 天 </span>
              </template>
            </div>
          </Col>
          <Col :span="12" style="display: flex; flex-direction: column">
            <div class="projectProgress-row">项目已完成</div>
            <div class="projectProgress-chart">
              <div class="back">
                <ProgressCircle
                  :stroke-width="8"
                  stroke-color="#BBCCE3"
                  :trail-width="8"
                  trail-color="#131b29"
                  :percent="
                    projectDetail.progress * 100 > 100
                      ? 100
                      : projectDetail.progress * 100
                  "
                >
                  <span class="demo-Circle-inner" style="font-size: 24px"
                    >{{
                      Number(projectDetail.progress * 100).toFixed(2)
                    }}%</span
                  >
                </ProgressCircle>
              </div>
            </div>
          </Col>
        </Row>
      </div>
      <Title title="天气概况" desc="&nbsp;&nbsp;&nbsp;WEATER FORECAST"></Title>
      <div class="wrap-theme1-content1">
        <div class="nav">
          <div
            :class="'nav-item' + (navItem === 0 ? ' active' : '')"
            @click="(navItem = 0), getWeatherInfo(0)"
          >
            实时
          </div>
          <div
            :class="'nav-item' + (navItem === 1 ? ' active' : '')"
            @click="(navItem = 1), getWeatherInfo(1)"
          >
            预报
          </div>
        </div>
        <div v-show="navItem === 0" class="wrap-theme1-content-detail">
          <div class="weather-info">
            <div class="empty" style="height: 20px"></div>
            <div class="weather-info-temp">
              {{ rainInfo.temp }}
              <div class="weather-info-temp-unit">℃</div>
            </div>
            <div class="weather-info-desc">
              <div class="weather-infos">
                <div class="weather-info-icon">
                  <img
                    :src="getWeatherIcon(rainInfo.icon)"
                    style="width: 25px"
                  />
                </div>
                <div class="weather-info-text">{{ rainInfo.text }}</div>
              </div>
              <div class="weather-infos">
                <div class="weather-info-icon">
                  <img src="../../assets/images/icon/feng.png" />
                </div>
                <div class="weather-info-text">{{ rainInfo.windScale }}级</div>
              </div>
              <div class="weather-infos">
                <div class="weather-info-icon">
                  <img src="../../assets/images/icon/fang.png" />
                </div>
                <div class="weather-info-text">{{ rainInfo.windDir }}</div>
              </div>
            </div>
          </div>
          <div class="weather-divider-horizontal"></div>
          <div class="weather-address">
            <div class="weather-address-icon">
              <img src="../../assets/images/icon/Vector.png" />
            </div>
            <div class="weather-address-text">
              {{ $store.state.mapShow.geoInfo.cityName }}
            </div>
          </div>
          <div class="weather-address">
            <div class="weather-address-icon">
              <img src="../../assets/images/icon/location.png" />
            </div>
            <div class="weather-address-text">
              {{ initDate(rainInfo.obsTime) }}
            </div>
          </div>
          <div class="weather-warn">
            <div class="weather-warn-text">
              {{ warningInfo[0] && warningInfo[0].typeName }}
            </div>
            <div class="weather-warn-icon-warn"></div>
          </div>
        </div>
        <div
          v-show="navItem == 1"
          class="wrap-theme1-content-detail"
          style="padding: 5px 8px"
        >
          <table border="0" cellspacing="0" class="table table1">
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                {{ item.week }}
              </td>
            </tr>
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                {{ item.month }}
              </td>
            </tr>
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                <img :src="item.iconDay" style="width: 25px" />
              </td>
            </tr>
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                {{ item.textDay }}<br />{{ item.tempMax }}℃
              </td>
            </tr>
          </table>
          <div class="line" style="height: 15%; width: 100%">
            <div
              id="echartsLine"
              class="echart"
              style="width: 100%; height: 100%"
            ></div>
          </div>
          <table border="0" cellspacing="0" class="table">
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                {{ item.tempMin }}℃<br />
                <img :src="item.iconNight" style="width: 25px" />
              </td>
            </tr>
            <tr>
              <td v-for="(item, index) in weekInfoList" :key="index">
                {{ item.textNight }}
              </td>
            </tr>
          </table>
        </div>
      </div>
      <Title title="出勤统计" desc="&nbsp;&nbsp;&nbsp;ATTENDANCE STATS"></Title>
      <div class="wrap-theme2-content2">
        <div class="attendance">
          <div class="attendance-title">
            <div class="attendance-title-left">
              <!-- 图标 -->
              <div class="attendance-title-left-icon"></div>
              <div class="attendance-title-left-text">
                <div class="attendance-title-left-text-label">在场人数</div>
                <div class="attendance-title-left-text-value">
                  {{ projectDetail.in }}
                </div>
              </div>
            </div>
          </div>
          <div class="attendance-title">
            <div class="attendance-title-left">
              <!-- 图标 -->
              <div class="attendance-title-left-icon"></div>
              <div class="attendance-title-left-text">
                <div class="attendance-title-left-text-label">今日出勤人数</div>
                <div class="attendance-title-left-text-value">0</div>
              </div>
            </div>
          </div>
        </div>
        <div class="attendance-content">
          <div class="attendance-chart-title">
            <div class="attendance-chart-title-icon"></div>
            <div class="attendance-chart-title-text">
              现场近7日出勤人数变化趋势
            </div>
          </div>
          <div class="attendance-chart">
            <div
              class="echarts"
              id="echats"
              style="width: 100%; height: 100%"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRecentSevenDays } from "./get7Days";
import Title from "@/components/commonView/title.vue";
import * as echats from "echarts";
import { Circle as ProgressCircle } from "view-design";
import { getProjectById } from "@/api/project"; // 引入API
import { getWeather, userClockList } from "@/api/xxindex";
export default {
  components: {
    Title,
    ProgressCircle,
  },
  data() {
    return {
      navItem: 0,
      myChart: null,
      myEchartsLine: null,
      projectInfo: {},
      projectDetail: {
        surplusDay: "", // 剩余竣工时间，如果为负数，显示已延期
        progress: "",
      },
      rainInfo: {},
      monthGroupData: {},
      weatherForecast: [],
      weekInfoList: [],
      geoInfo: {},
      warningInfo: {},
    };
  },
  mounted() {
    this.initRequest();
    this.initchartRuquest();
  },
  created() {
    this.$bus.on("projectInfo", (data) => {
      this.projectInfo = data;
      getProjectById(data.id).then((res) => {
        if (res.success) {
          this.projectDetail = res.data;
        }
      });
    });
  },
  methods: {
    initRequest() {
      console.log(this.$store.state.mapShow.geoInfo, "572908");
      let host = "np2mtdt4fv.re.qweatherapi.com";
      // 版本英文 v3
      let v = "/v7";
      // 武汉地理位置
      let WHlocation =
        Number(this.$store.state.mapShow.geoInfo.lon).toFixed(2) +
        "," +
        Number(this.$store.state.mapShow.geoInfo.lat).toFixed(2);

      getWeather(
        `https://${host}${v}/weather/now/?location=${WHlocation}`
      ).then((res) => {
        if (res.code == "200") {
          this.rainInfo = res.now;
        }
      });

      getWeather(
        `https://${host}${v}/warning/now/?location=${WHlocation}`
      ).then((res) => {
        if (res.code == "200") {
          this.warningInfo = res.warning;
        }
      });

      userClockList({
        startTime: "",
        endTime: "",
        dataType: "",
        tenantId: "",
        id: "",
      }).then((res) => {
        if (res.code == "success") {
          this.monthGroupData = res.data;
          this.initCharts1();
        }
      });
    },
    initDate(val) {
      // 检查输入值是否有效
      if (!val || isNaN(new Date(val).getTime())) {
        return "--";
      }

      const date = new Date(val);

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return "--";
      }

      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours();
      const minutes = date.getMinutes();

      // 格式化日期字符串，确保所有值都是有效数字
      const formatted1 = `${year}年${month}月${day}日 ${hours}:${String(
        minutes
      ).padStart(2, "0")}`;
      return formatted1;
    },
    getWeatherInfo(index) {
      if (index == 1) {
        this.initchartRuquest();
      }
    },
    initchartRuquest() {
      let host = "np2mtdt4fv.re.qweatherapi.com";
      // 版本英文 v3
      let v = "/v7";
      // 武汉地理位置
      let WHlocation =
        Number(this.$store.state.mapShow.geoInfo.lon).toFixed(2) +
        "," +
        Number(this.$store.state.mapShow.geoInfo.lat).toFixed(2);

      getWeather(`https://${host}${v}/weather/7d/?location=${WHlocation}`).then(
        (res) => {
          if (res.code == "200") {
            this.weatherForecast = res.daily;
            let weeks = [
              "周日",
              "周一",
              "周二",
              "周三",
              "周四",
              "周五",
              "周六",
            ];
            this.weekInfoList = this.weatherForecast.map((item) => {
              let string = "";
              if (new Date(item.fxDate).getDay() == new Date().getDay()) {
                string = "今天";
              } else {
                string = weeks[new Date(item.fxDate).getDay()];
              }
              return {
                week: string,
                month:
                  item.fxDate.split("-")[1] + "-" + item.fxDate.split("-")[2],
                textDay: item.textDay,
                textNight: item.textNight,
                iconDay: require(`@/assets/rainIcon/${item.iconDay}.png`),
                iconNight: require(`@/assets/rainIcon/${item.iconNight}.png`),
                tempMax: item.tempMax,
                tempMin: item.tempMin,
              };
            });
            this.initCharts2();
          }
        }
      );
    },
    initCharts1() {
      // 初始化echarts实例
      this.myChart = echats.init(document.getElementById("echats"));
      const option = {
        // 修改图表大小
        grid: {
          top: "7%",
          left: "0%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        // 提示框组件
        xAxis: {
          type: "category",
          // data: Object.keys(this.monthGroupData.monthGroupData),
          data: getRecentSevenDays("MM-DD"),
          splitLine: {
            show: true, // 隐藏网格线
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)", // 网格线颜色
            },
          },
          axisTick: {
            show: false, // 隐藏坐标轴刻度
          },
        },
        tooltip: {
          trigger: "axis", // 触发方式为坐标轴触发
          formatter: function (params) {
            return `
                        <div style="width: 140px; height: 62px; background-color: rgba(255, 255, 255, 0.1); backdrop-filter: blur(3px);border-radius: 10px;display: flex;
                        flex-direction: column;justify-content: space-between;padding: 10px;color: #fff;box-sizing: border-box;">
                            <div>${params[0].name}</div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>数值</span>
                                <span>${params[0].value}</span>
                            </div>
                        </div>
                        `;
          },
          backgroundColor: "transparent", // 提示框背景颜色
          // textStyle: {
          //     color: '#fff'
          // },
          borderColor: "transparent",
          // renderMode: 'html'
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true, // 隐藏网格线\
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)", // 网格线颜色
              type: "dashed",
            },
          },
          axisLine: {
            show: true,
          },
        },
        series: [
          {
            data: [0, 0, 0, 0, 0, 0, 0],
            type: "line",
            itemStyle: {
              opacity: 0,
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0.2,
                      color: "rgba(255, 255, 255)", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "rgba(36, 76, 173)", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(36, 76, 173, 0.1)", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#95B6F9",
                borderWidth: 1,
              },
              scale: 3,
            },
            smooth: true,
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#225BD3", // 0% 处的颜色
                  },
                  { offset: 0.3, color: "#70D4F7" },
                  {
                    offset: 1,
                    color: "#225BD3", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      };
      // 指定图表的配置项和数据
      this.myChart.setOption(option);
    },
    initCharts2() {
      // 销毁
      this.myEchartsLine && this.myEchartsLine.dispose();
      // 基于准备好的dom，初始化echarts实例
      this.myEchartsLine = echats.init(document.getElementById("echartsLine"));
      console.log(this.weekInfoList.map((i) => i.textDay));
      // 指定图表的配置项和数据
      let option = {
        // 修改图表大小
        grid: {
          top: "7%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        // 提示框组件
        xAxis: {
          type: "category",
          data: this.weekInfoList.map((i) => i.week),
          splitLine: {
            show: true, // 隐藏网格线
            lineStyle: {
              color: "transparent", // 网格线颜色
            },
          },
          axisTick: {
            show: false, // 隐藏坐标轴刻度
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        tooltip: {
          trigger: "axis", // 触发方式为坐标轴触发
          formatter: function (params) {
            return `
                        <div style="width: 140px; height: 80px; background-color: rgba(255, 255, 255, 0.1); backdrop-filter: blur(3px);border-radius: 10px;display: flex;
                        flex-direction: column;justify-content: space-between;padding: 10px;color: #fff;box-sizing: border-box;">
                            <div>${params[0].axisValue}</div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>白天温度</span>
                                <span>${params[0].value}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>夜间温度</span>
                                <span>${params[1].value}</span>
                            </div>
                        </div>
                        `;
          },
          backgroundColor: "transparent", // 提示框背景颜色
          borderColor: "transparent",
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: false, // 隐藏网格线
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false, // 隐藏坐标轴刻度
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
          {
            data: this.weekInfoList.map((i) => i.tempMax),
            type: "line",
            smooth: true,
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#225BD3", // 0% 处的颜色
                  },
                  { offset: 0.3, color: "#70D4F7" },
                  {
                    offset: 1,
                    color: "#225BD3", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            data: this.weekInfoList.map((i) => i.tempMin),
            type: "line",
            smooth: true,
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#568487", // 0% 处的颜色
                  },
                  { offset: 0.3, color: "#568487" },
                  {
                    offset: 1,
                    color: "#568487", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      };
      this.myEchartsLine.setOption(option);
    },
    getWeatherIcon(iconCode) {
      if (!iconCode) {
        try {
          return require("@/assets/rainIcon/100.png");
        } catch (e) {
          return "";
        }
      }

      try {
        return require(`@/assets/rainIcon/${iconCode}.png`);
      } catch (error) {
        try {
          return require("@/assets/rainIcon/100.png");
        } catch (e) {
          return "";
        }
      }
    },
  },
  beforeDestroy() {
    this.$bus.off("projectInfo");
  },
};
</script>

<style lang="less" scoped>
.table {
  width: 100%;
  tr {
    font-size: 14px;
    text-align: center;
    color: #aeb1b5;
  }
  td {
    padding: 2px;
    line-height: 16px;
  }
}
.table1 {
  width: 100%;
  tr:first-child {
    font-weight: bold;
    font-size: 16px;
    color: #fff;
  }
}
.w-full {
  width: 100%;
  height: calc(100vh - 100px);
  position: absolute;
  top: 100px;
  right: 0;
  color: #fff;

  .right-project {
    width: 405px;
    height: 100%;
    background: linear-gradient(90deg, #19263ecc, #101a29);
    padding: 5px;
    position: absolute;
    right: 0;
    top: 0;
    pointer-events: auto;

    .wrap-theme1-content {
      width: 100%;
      height: calc(25%);
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
    }

    .wrap-theme1-content1 {
      width: 100%;
      height: 28%;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
    }

    .wrap-theme2-content2 {
      width: 100%;
      height: calc(100% - 25% - 28% - 17%);
      display: flex;
      flex-direction: column;
    }
  }
}

.projectProgress-row {
  font-size: 20px;
  font-weight: 800;
  line-height: 28px;
  color: #fff;
  text-align: center;
}

.projectProgress-time {
  font-size: 48px;
  height: 100%;
  font-weight: 800;
  text-align: left;
  background: linear-gradient(180deg, #fff, #bee1ff);
  -webkit-background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.projectProgress-day {
  font-size: 24px;
  font-weight: 800;
  line-height: 33.6px;
  text-align: left;
  color: #fff;
}

.projectProgress-chart {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .back {
    --size: 175px;
    width: var(--size);
    height: var(--size);
    background: url(../../assets/images/backProcess.png) no-repeat center center;
    background-size: contain;
    background-position-x: -3px;
    background-position-y: -3px;
    position: relative;

    .ivu-chart-circle {
      width: calc(var(--size) - 40px) !important;
      height: calc(var(--size) - 40px) !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.nav {
  width: 100%;
  display: flex;
  justify-content: space-around;

  .nav-item {
    width: 83px;
    height: 24px;
    color: #fff;
    cursor: pointer;
    background: url(../../assets/images/ProjectMap/nav-item.png) no-repeat
      center center;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .nav-item.active {
    background: url(../../assets/images/ProjectMap/nav-active.png) no-repeat
      center center;
    background-size: 100% 100%;
  }
}

.wrap-theme1-content-detail {
  width: 100%;
  height: calc(100% - 20px);
  margin-top: 5px;
  overflow: auto;

  .weather-info {
    padding-left: 5%;
    display: flex;
    flex-direction: column;
    margin: 0px auto;

    .weather-info-temp {
      width: 100%;
      font-size: 32px;
      font-weight: 700;
      color: #fff;
      display: flex;
      align-items: flex-end;

      .weather-info-temp-unit {
        font-size: 16px;
        font-weight: 400;
        padding: 0 0 7px 5px;
      }
    }

    .weather-info-desc {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 60px 6px 0;

      .weather-infos {
        display: flex;
        align-items: center;

        .weather-info {
          outline: none !important;
        }

        .weather-info-text {
          font-size: 14px;
          color: #fff;
          margin-left: 8px;
        }
      }
    }
  }
}

.weather-divider-horizontal {
  width: 90%;
  margin: 5px auto;
  height: 1px;
  min-height: 2px;
  background-image: linear-gradient(90deg, #f68f211a, #574c35, #f68f211a);
  background-size: 5px 30px;
}

.weather-address {
  display: flex;
  align-items: center;
  padding-left: 3%;
  margin: 0px auto;
  margin-top: 10px;

  .weather-address-text {
    flex: 1;
    font-size: 14px;
    color: #ffffffb3;
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.weather-warn {
  width: 200px;
  padding: 10px 20px;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  background: linear-gradient(270deg, #134a9300, #134a93cc);
  position: relative;
  margin-top: 15px;

  .weather-warn-text {
    font-size: 14px;
    color: #ffffffe6;
  }

  .weather-warn-icon-warn {
    position: absolute;
    right: 20px;
    top: -10px;
    width: 24px;
    height: 24px;
    background: url(../../assets/images/icon/warn.png) no-repeat center center;
    background-size: 100% 100%;
  }
}

.attendance {
  width: 100%;
  height: 20%;
  display: flex;
  justify-content: space-between;

  .attendance-title {
    width: 100%;
    height: 55px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .attendance-title-left {
      height: 100%;
      display: flex;
      align-items: center;

      .attendance-title-left-icon {
        width: 40px;
        height: 40px;
        background: url(../../assets/images/icon/people.png) no-repeat center
          center;
        background-size: 100% 100%;
        margin-right: 10px;
      }

      .attendance-title-left-text {
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 5px 0;

        .attendance-title-left-text-label {
          height: 20px;
          font-size: 14px;
          line-height: 20px;
          color: #ffffff80;
          letter-spacing: 1px;
        }

        .attendance-title-left-text-value {
          height: 20px;
          font-size: 20px;
          font-weight: 700;
          line-height: 20px;
          color: #fff;
          letter-spacing: 1.5px;
        }
      }
    }
  }
}

.attendance-content {
  width: 100%;
  height: 80%;

  .attendance-chart-title {
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    margin-top: 10px;

    .attendance-chart-title-icon {
      width: 6px;
      height: 6px;
      background-color: #fff;
      rotate: 45deg;
    }

    .attendance-chart-title-text {
      font-size: 14px;
      margin-left: 10px;
      background: linear-gradient(180deg, #fff, #c5e5ff);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

  .attendance-chart {
    width: 100%;
    height: calc(100% - 50px);
  }
}
</style>

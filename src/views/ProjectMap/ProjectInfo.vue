<template>
     <div class="w-full">
        <div class="left-project">
            <Title title="项目信息" desc="PROJECT OVERVIEW"></Title>
            <div class="project-content">
                <div class="project-item" v-for="(item, index) in data1" :key="index">
                    <div class="project-item-title">
                        <div :class="'icon-project icon-pro-' + item.icon"></div>
                        <span>{{ item.title }}</span>
                    </div>
                    <div class="project-item-content">
                        {{ item.desc }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Title from '@/components/commonView/title.vue';
export default {
    components: {
        Title
    },
    data() {
        return {
            data1: [
                {
                    title: '项目名称',
                    icon: 'name',
                    type: 'name',
                    desc: '邹城至济宁高速公路上跨京沪铁路项目'
                },
                {
                    title: '项目地址',
                    icon: 'city',
                    type: 'city',
                    desc: '邹城至济宁高速公路上跨京沪铁路项目'
                },
                {
                    title: '项目类型',
                    icon: 'type',
                    type: 'type',
                    desc: '上跨桥梁'
                },
                {
                    title: '影响运输',
                    icon: 'safe',
                    type: 'safe',
                    desc: '上跨封锁'
                },
                {
                    title: '计划工期',
                    icon: 'time',
                    type: 'time',
                    desc: '2024-12-04 至 2024-12-04'
                },
                {
                    title: '涉及线别',
                    icon: 'qy',
                    type: 'qy',
                    desc: '兖石线K143+245.71'
                },
                {
                    title: '项目介绍',
                    icon: 'intriduce',
                    type: 'intriduce',
                    desc: '交叉铁路里程为京沪上行线K674+912.8，夹角约为66°，公路里程为K8+128.45。交叉处京沪铁路位于两下店至界河区间，距离两下店站约2.8km。项目交叉处京沪铁路为双线电气化铁路，线间距约12.0m，为圆曲线，线路允许速度140km/h。  拟建项目道路等级为高速公路，设计速度120km/h。项目主要控制点为京沪铁路，设计桥梁跨度布置为30m小箱梁+2×85m预应力混凝土T型刚构，桥面宽度30.8m。跨铁路主桥采用2×85m整幅预应力混凝土T型刚构跨越京沪铁路，转体主墩位于京沪铁路西侧，东侧铁路相邻孔采用30m小箱梁。桥梁设计长度200m，主桥整幅横断面宽度30.8m，30m小箱梁分幅宽度14.95m，整个桥梁建筑面积6133m2。为减少施工期间对京沪铁路的影响，转体T构主桥采用支架现浇转体施工。'
                }
            ],
          projectInfo: {}
        }
    },
    created() {
      this.$bus.on('projectInfo', (data) => {
        this.projectInfo = data;
        this.data1.forEach(el => {
          if (el.type === 'name') {
            el.desc = data.name || '未知项目';
          } else if (el.type === 'city') {
            el.desc = data.location || '未知地址';
          } else if (el.type === 'type') {
            el.desc = data.projectType == 1 ? "代管" : "代建";
          } else if (el.type === 'safe') {
            el.desc = data.projectTransport || '未知安全信息';
          } else if (el.type === 'time') {
            // 格式化日期，去掉时分秒
            const formatDate = (dateStr) => {
              if (!dateStr) return '未知时间';
              return dateStr.split(' ')[0]; // 只保留日期部分，去掉时间部分
            };
            el.desc = `${formatDate(data.estimateTime)} - ${formatDate(data.completionTime)}`;
          } else if (el.type === 'qy') {
            el.desc = data.projectLine || '未知平台项目';
          } else if (el.type === 'intriduce') {
            el.desc = data.projectDesc || '无项目介绍';
          }
        });
      });
    },
    methods: {

    },
    beforeDestroy () {
      this.$bus.off('projectInfo');
    }
}
</script>

<style lang="less" scoped>
.w-full {
    width: 100%;
    height: calc(100vh - 100px);
    position: relative;
    color: #fff;

    .left-project {
        width: 369px;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(270deg, #19263ecc, #101a29);
        padding: 5px 5px 0;
        pointer-events: auto;
    }

    .right-project {
        width: 405px;
        height: 100%;
        background: linear-gradient(90deg, #19263ecc, #101a29);
        padding: 5px;
        position: absolute;
        right: 0;
        top: 0;
    }
}

.project-content {
    width: 100%;
    height: calc(100% - 40px);
    background-size: 100% 100%;
    margin-left: 5px;
    padding: 10px 5px 0;
    display: flex;
    flex-direction: column;

    .project-item {
        width: 100%;
        min-height: 57px;
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
        background: linear-gradient(90deg, #7fa0be00 .06%, #4b83b73e, #7fa0be00 98.23%);

        .project-item-title {
            width: 150px;
            height: 26px;
            background: url(../../assets/images/icon/title.png) no-repeat center center;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .icon-project {
                width: 17px;
                height: 16px;
                background-size: 100% 100%;
                margin-left: 5px;
                margin-right: 5px;
            }

            .icon-pro-name {
                background: url(../../assets/images/icon/name.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-city {
                background: url(../../assets/images/icon/city.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-safe {
                background: url(../../assets/images/icon/safe.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-time {
                background: url(../../assets/images/icon/time.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-qy {
                background: url(../../assets/images/icon/qy.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-intriduce {
                background: url(../../assets/images/icon/intriduce.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .icon-pro-type {
                background: url(../../assets/images/icon/type.png) no-repeat center center;
                background-size: 100% 100%;
            }
        }

        .project-item-content{
            width: 100%;
            padding: 5px 20px 5px 37px;
            font-family: Segoe UI;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: #fff;
            // white-space: nowrap;
            // overflow: hidden;
            // text-overflow: ellipsis;
        }
    }
}
</style>

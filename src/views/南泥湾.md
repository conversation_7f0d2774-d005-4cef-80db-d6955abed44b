---
title: 南泥湾
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 南泥湾

Base URLs:

# Authentication

# 登录管理

## POST 关注/取消关注项目

POST /login/followProject

关注/取消关注项目

> Body 请求参数

```json
{
  "tenantId": "string",
  "follow": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[FollowProjectRequest](#schemafollowprojectrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

# 数据模型

<h2 id="tocS_RestMessage">RestMessage</h2>

<a id="schemarestmessage"></a>
<a id="schema_RestMessage"></a>
<a id="tocSrestmessage"></a>
<a id="tocsrestmessage"></a>

```json
{
  "success": true,
  "code": "string",
  "level": "string",
  "message": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||成功标识|
|code|string|false|none||状态码|
|level|string|false|none||消息级别|
|message|string|false|none||消息内容|
|data|[](#schema)|false|none||数据|

<h2 id="tocS_FollowProjectRequest">FollowProjectRequest</h2>

<a id="schemafollowprojectrequest"></a>
<a id="schema_FollowProjectRequest"></a>
<a id="tocSfollowprojectrequest"></a>
<a id="tocsfollowprojectrequest"></a>

```json
{
  "tenantId": "string",
  "follow": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|string|true|none||项目ID(租户ID)|
|follow|boolean|true|none||操作类型：true-关注，false-取消关注|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*


<template>
  <div></div>
</template>

<script>
import { getUserDataByOther, loginByUsername } from "@/api/jcyj/index";
import { login, userInfo } from "@/api/index";
import { Session } from "@/utils/storage";
export default {
  data() {
    return {};
  },
  mounted() {
    //调用第三方登录
    this.getUserDataByOther();
  },

  methods: {
    // 拿取token
    getAssessLine() {
      // 获取当前 URL
      const url = window.location.href;
      // 使用正则表达式获取 token 值
      const match = url.match(/[?&]token=([^&#]*)/);
      const accessLinkValue = match ? match[1] : "";
      return accessLinkValue;
    },
    //调用接口获取用户信息
    getUserDataByOther() {
      let params = {
        token: this.getAssessLine(),
      };
      getUserDataByOther(params).then((res) => {
        if (res.code && res.code === 200) {
          this.login(res.data).then(() => {
            this.$router.push("/home");
          });
        }
      });
    },
    //登录接口
    login(data) {
      const { loginName } = data;
      return new Promise((resolve, reject) => {
        loginByUsername({ username: loginName }).then((res) => {
          if (res.code && res.code === 200) {
            Session.set("accessToken", res.result);
            userInfo().then((resInfo) => {
              Session.set("userInfo", resInfo.result);
            });
            resolve(res);
          } else {
            reject(err);
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>

.login-view {
  width: 100%;
  height: 100vh;
  position: relative;
  background-size: 100% 100%;
}
.titileCol {
  width: 100%;
  height: 100px;
}
.logoMain {
  margin-top: 150px;
}
.logoMain .leftLogin {
  position: relative;
  top: -16%;
  left: 5%;
  width: 900px;
  height: 681px;
}
.logoMain .login-view2 /deep/ .ivu-form {
  width: 100%;
}
.logoMain .login-view2 .loginTitle {
  padding: 40px 0;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #ffffff;
}
.logoMain .login-view2 .usernameCal {
  margin: 0 80px;
}
.logoMain .login-view2 .paswordCal {
  margin: 0 80px;
}
.logoMain /deep/ .ivu-icon {
  color: #0f4d80;
  font-size: 18px;
}
.logoMain .code-row-bg {
  margin: 5px 80px;
  color: #0f4d80;
}
.logoMain .code-row-bg .pasCheck {
  color: #ffffff;
}
.logoMain .code-row-bg .forgetPas {
  font-size: 14px;
  color: #51d2ff;
}
 /deep/ .ivu-checkbox-large .ivu-checkbox-inner {
  width: 16px;
  height: 16px;
}
 /deep/ .ivu-checkbox-wrapper .ivu-checkbox-large {
  font-size: 16px;
}
 /deep/ .ivu-checkbox-checked {
  width: 16px;
  height: 16px;
}
 /deep/ .ivu-checkbox-input {
  width: 16px;
  height: 16px;
}
.btnsCal {
  padding: 20px 80px 0px 80px;
}
.btnsCal .login-btn {
  height: 54px;
  font-size: 24px;
  background-image: linear-gradient(to bottom, #0dccce, #00a1e4);
}
 /deep/ .ivu-input {
  background-color: transparent !important;
  color: #ffffff;
  font-size: 16px;
  border-color: #51d2ff;
}
 /deep/ .ivu-form-item-error-tip {
  left: 13% !important;
  color: #0dccce;
}

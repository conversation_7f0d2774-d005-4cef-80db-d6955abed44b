.login-view {
  width: 100%;
  height: 100vh;
  position: relative;
  background-size: 100% 100%;
}

.titileCol {
  width: 100%;
  height: 100px;
}

.logoMain {
  margin-top: 150px;

  .leftLogin {
    position: relative;
    top: -5%;
    left: 5%;
    width: 900px;
    height: 561px;
  }

  .login-view2 {
    background: url("/img/login/2.png") no-repeat !important;
    background-size: 100% 100% !important;
    width: 650px;
    height: 505px;

    /deep/ .ivu-form {
      width: 100%;
    }

    .loginTitle {
      padding: 40px 0;
      font-size: 28px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }

    .usernameCal {
      margin: 0 80px;
    }

    .paswordCal {
      margin: 20px 80px 0 80px;

    }
  }

  /deep/ .ivu-icon {
    color: #0f4d80;
    font-size: 18px;
  }

  .code-row-bg {
    margin: 5px 80px;
    color: #0f4d80;

    .pasCheck {
      color: #ffffff;
    }

    .forgetPas {
      font-size: 14px;
      color: #51d2ff;
    }
  }
}

/deep/ .ivu-checkbox-large .ivu-checkbox-inner {
  width: 16px;
  height: 16px;
}

/deep/ .ivu-checkbox-wrapper .ivu-checkbox-large {
  font-size: 16px;
}

/deep/ .ivu-checkbox-checked {
  width: 16px;
  height: 16px;
}

/deep/ .ivu-checkbox-input {
  width: 16px;
  height: 16px;
}

.btnsCal {
  padding: 20px 80px 0px 80px;

  .login-btn {
    height: 54px;
    font-size: 24px;
    background-image: linear-gradient(to bottom, #0dccce, #00a1e4);
  }
}

/deep/ .ivu-input {
  background-color: transparent !important;
  color: #ffffff;
  font-size: 16px;
  border-color: #51d2ff;
}

/deep/ .ivu-form-item-error-tip {
  left: 13% !important;
  color: #0dccce;
}
<template>
    <div class="civil-air-defense">
        <div class="civil-air-defense-mask">
            <div class="mask-left"></div>
            <div class="mask-right"></div>
            <div class="mask-motton"></div>
        </div>
        <div class="civil-air-defense-bg"></div>
        <div class="civil-air-defense-body">
            <div class="civil-air-defense-left">
                <GridWorker ref="gridWorker" />
                <GridHazardInvestigation ref="gridHazardInvestigation" />
                <MonthJobTask ref="monthJobTask" />
            </div>
            <div class="civil-air-defense-center">
                <div class="civil-air-defense-center-bottom">
                    <ManagementPenetration ref="managementPenetration" />
                </div>
            </div>
            <div class="civil-air-defense-right">
                <TrainingStatistics ref="trainingStatistics" />
                <AssessmentStatistics ref="assessmentStatistics" />
                <KeyTaskList ref="keyTaskList" />
            </div>
        </div>
    </div>
</template>
<script>
import GridWorker from './components/gridWorker.vue'
import GridHazardInvestigation from './components/gridHazardInvestigation.vue'
import MonthJobTask from './components/monthJobTask.vue'
import ManagementPenetration from './components/managementPenetration.vue'
import TrainingStatistics from './components/trainingStatistics.vue'
import AssessmentStatistics from './components/assessmentStatistics.vue'
import KeyTaskList from './components/keyTaskList.vue'
export default {
    name: 'civilAirDefense',
    components: {
        GridWorker,
        GridHazardInvestigation,
        MonthJobTask,
        ManagementPenetration,
        TrainingStatistics,
        AssessmentStatistics,
        KeyTaskList
    },
    data() {
        return {
            activeComponent: ''
        }
    },
    mounted(){
        this.timer = setInterval(() => {
            this.refresh()
        }, 10 * 1000)
    },
    beforeDestroy(){
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        // 定时刷新
        refresh(){
            this.$refs.gridWorker?.refresh()
            this.$refs.gridHazardInvestigation?.refresh()
            this.$refs.monthJobTask?.refresh()
            this.$refs.managementPenetration?.refresh()
            this.$refs.trainingStatistics?.refresh()
            this.$refs.assessmentStatistics?.refresh()
            this.$refs.keyTaskList?.refresh()
        }
    }
}
</script>

<style lang="less" scoped>
.civil-air-defense{
    height: ~"calc(100vh - 4.75rem)";
    position: relative;
    width: 100%;
    position: relative;
    pointer-events: none;
    padding: 0 1.48vh 1.57vh;
    .civil-air-defense-mask,
    .civil-air-defense-bg{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }
    .civil-air-defense-mask{
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        z-index: 1;
        .mask-left,
        .mask-right{
            width: 22.39vw;
            height: 100%;
            position: relative;
            z-index: 1;
        }
        .mask-left{
            background: linear-gradient(90deg, #000D1E 0%, rgba(0, 13, 30, 0) 100%);
        }
        .mask-right{
            background: linear-gradient(270deg, #000D1E 0%, rgba(0, 13, 30, 0) 100%);
        }
        .mask-motton{
            position: absolute;
            width: 100%;
            height: 33.33vh;
            bottom: 0;
            left: 0;
            background: linear-gradient(0deg, #000D1E 0%, rgba(0, 13, 30, 0) 50.48%);
        }
    }
    .civil-air-defense-bg{
        background: url('~@/assets/images/pointScreen/point_frame.png') no-repeat center center;
        background-size: 100% 100%;
        z-index: 2;
    }
    .civil-air-defense-body{
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        column-gap: 0.74vh;
        z-index: 5;
    }
    .civil-air-defense-left,
    .civil-air-defense-right{
        width: 21.57vw;
        height: 100%;
        display: grid;
        pointer-events: auto;
        row-gap: 0.74vh;
    }
    .civil-air-defense-left{
        grid-template-rows: 34% 30.3% auto;
        background: linear-gradient(270deg, rgba(0, 13, 30, 0) 0%, rgba(0, 9, 30, 0.16) 7%, #00071E 100%);
    }
    .civil-air-defense-right{
        grid-template-rows: 28.78% 39.72% auto;
        background: linear-gradient(90deg, rgba(0, 13, 30, 0) 0%, rgba(0, 9, 30, 0.16) 7%, #00071E 100%);
    }
    .civil-air-defense-center{
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        overflow: hidden;
    }
    .civil-air-defense-center-bottom{
        height: 23.33vh;
        width: 100%;
        background: linear-gradient(180deg, rgba(0, 13, 30, 0) 0%, rgba(0, 9, 30, 0.16) 7%, rgba(0, 7, 30, 0.5) 100%);
        pointer-events: auto;
    }
}
</style>
<template>
    <div class="grid-hazard-investigation">
        <Title3 title="网格隐患排查" style="margin-bottom: 1.48vh;" />
        <div class="grid-hazard-main">
            <div class="hazard-total">
                <div class="name">
                    <span>隐患总数量</span>
                </div>
                <div class="num-list">
                    <div class="num-item" v-for="item in totalList" :key="item">
                        <span>{{item}}</span>
                    </div>
                    <div class="num-item unit-item">
                        <span>次</span>
                    </div>
                </div>
            </div>
            <div class="hazard-list">
                <div class="hazard-item" v-for="item in hazardList" :class="`level-${item.level}`" :key="item.key">
                    <div class="num"><span>{{item.sum}}</span></div>
                    <div class="name-box">
                        <div class="level-icon"></div>
                        <div class="name">{{item.levelDesc}}隐患</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hazard-status-main">
            <div class="status-progress">
                <div v-for="n in 5" :key="n" class="box"></div>
            </div>
            <div class="status-list">
                <div class="status-item" v-for="item in statusList" :key="item.key">
                    <div class="box" :class="`status-${item.status}`">
                        <div class="info-box">
                            <div class="name">{{item.statusDesc}}</div>
                            <div class="num">{{item.sum}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { getGridHazardStatistics } from "@/api/civilAirDefense";
export default {
    name: 'gridHazardInvestigation',
    components: {
        Title3
    },
    data() {
        return {
            total: 0,
            hazardList: [],
            statusList: []
        }
    },
    computed: {
        totalList() {
            if (this.total || this.total === 0) {
                let str = this.total + '';
                return str.split('');
            }
            return [];
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        // 刷新
        refresh(){
            this.getData()
        },
        // 获取网格隐患排查
        getData() {
            getGridHazardStatistics().then(res => {
                console.log(res)
                let totalObj = res.data.levelList.find(item => !item.level && item.level !== 0);
                this.total = totalObj.sum;
                this.hazardList = res.data.levelList.filter(item => item.level || item.level === 0);
                let statusList = res.data.statusList;
                statusList.sort((a, b) => {
                    if (!a.status && a.status !== 0) {
                        return -1;
                    } else {
                        return a.status - b.status;
                    }
                });
                this.statusList = statusList;
            })
        }
    }
}
</script>
<style lang="less" scoped>
.grid-hazard-main{
    background: linear-gradient(180deg, rgba(0, 13, 183, 0.25) 0%, rgba(1, 0, 31, 0) 100%);
    padding: 0 1.85vh 0.74vh;
    margin-bottom: 1.48vh;
}
.hazard-total{
    height: 8.148vh;
    display: flex;
    align-items: center;
    .name{
        width: 9.814vh;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url('~@/assets/images/civilAirDefense/box_bg.png') no-repeat center center;
        background-size: 100% 100%;
        font-size: 1.48vh;
        color: #fff;
        line-height: 2vh;
        margin-right: 1.48vh;
    }
    .num-list{
        display: flex;
        align-items: center;
        column-gap: 0.648vh;
        .num-item{
            width: 3.33vh;
            height: 4.44vh;
            font-size: 3.33vh;
            color: #fff;
            line-height: 4vh;
            background: linear-gradient(180deg, #000848 0%, #049AFF 48.5%, #000848 100%);
            border: 0.8px solid;
            border-image-source: linear-gradient(180deg, #66B4FF 0%, #FFFFFF 50.57%, #66B4FF 100%);
            border-image-slice: 0.8;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.185vh;
        }
        .unit-item{
            background: linear-gradient(180deg, rgba(0, 26, 73, 0.22) 0%, rgba(0, 57, 160, 0.22) 50%, rgba(0, 26, 73, 0.22) 100%);
            border-image-source: linear-gradient(180deg, rgba(102, 180, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(102, 180, 255, 0.4) 100%);
            color: rgba(255, 255, 255, 0.4);
            font-size: 1.48vh;
            line-height: 2vh;
            align-items: flex-end;
            padding-bottom: 0.74vh;
        }
    }
}
.hazard-list{
    display: flex;
    align-items: center;
    column-gap: 0.74vh;
    .hazard-item{
        flex: 1;
        .num{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 3.425vh;
            position: relative;
            margin-bottom: 0.185vh;
            &::before{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('~@/assets/images/civilAirDefense/box_bg2.png') no-repeat center center;
                background-size: 100%;
            }
            span{
                font-size: 2.77vh;
                color: #fff;
                line-height: 3vh;
                position: relative;
                z-index: 1;
                font-family: 'DIN Pro';
            }
        }
        .name-box{
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: 0.37vh;
            height: 2.59vh;
            font-size: 1.29vh;
            line-height: 1.66vh;
            color: #fff;
            border-radius: 0.185vh;
            border-bottom: 1px solid transparent;
            .level-icon{
                width: 1.48vh;
                height: 1.48vh;
                background: url('~@/assets/images/civilAirDefense/icon_box1.png') no-repeat 50% 0;
                background-size: 100%;
            }
        }
        &.level-0{
            .num{
                background: linear-gradient(270deg, rgba(61, 19, 5, 0) 0%, #775B0D 51.02%, rgba(61, 19, 5, 0) 100%);
            }
            .name-box{
                background: linear-gradient(360deg, #452900 0%, rgba(74, 49, 12, 0) 100%);
                border-color: rgba(255, 186, 83, 1);
            }
        }
        &.level-1{
            .num{
                background: linear-gradient(270deg, rgba(61, 26, 5, 0) 0%, #77360D 51.02%, rgba(61, 26, 5, 0) 100%);
            }
            .name-box{
                background: linear-gradient(360deg, #451300 0%, rgba(74, 30, 12, 0) 100%);
                border-color: rgba(255, 132, 83, 1);
                .level-icon{
                    background-image: url('~@/assets/images/civilAirDefense/icon_box2.png');
                }
            }
        }
        &.level-2{
            .num{
                background: linear-gradient(270deg, rgba(119, 24, 13, 0) 0%, #77180D 51.17%, rgba(119, 24, 13, 0) 100.29%);
            }
            .name-box{
                background: linear-gradient(360deg, #450000 55.72%, rgba(74, 12, 12, 0) 100%);
                border-color: rgba(255, 83, 83, 1);
                .level-icon{
                    background-image: url('~@/assets/images/civilAirDefense/icon_box3.png');
                }
            }
        }
    }
}
.hazard-status-main{
    .status-progress{
        display: flex;
        margin-bottom: 0.648vh;
        .box{
            flex: 1;
            height: 0.74vh;
            border-radius: 0.74vh;
            border: 0.8px solid transparent;
            border-image-slice: 0.8;
            &:nth-child(1){
                background: linear-gradient(90deg, rgba(105, 202, 235, 0.3) 0%, #69CAEB 100%);
                border-image-source: linear-gradient(90deg, rgba(105, 202, 235, 0) 0%, #ADEAFF 100%);
            }
            &:nth-child(2){
                background: linear-gradient(90deg, rgba(235, 173, 105, 0.3) 0%, #EBAD69 100%);
                border-image-source: linear-gradient(90deg, rgba(235, 173, 105, 0) 0%, #FFD8AD 100%);
            }
            &:nth-child(3){
                background: linear-gradient(90deg, rgba(218, 228, 101, 0.3) 0%, #DAE465 100%);
                border-image-source: linear-gradient(90deg, rgba(245, 255, 154, 0) 0%, #F5FF9A 100%);
            }
            &:nth-child(4){
                background: linear-gradient(90deg, rgba(105, 144, 235, 0.3) 0%, #4379FB 100%);
                border-image-source: linear-gradient(90deg, rgba(105, 144, 235, 0) 0%, #79A1FF 100%);
            }
            &:nth-child(5){
                background: linear-gradient(90deg, rgba(188, 193, 209, 0.3) 0%, #BCC1D1 100%);
                border-image-source: linear-gradient(90deg, rgba(230, 236, 255, 0) 0%, #E6ECFF 100%)
            }
        }
    }
    .status-list{
        display: flex;
        align-items: flex-start;
        .status-item{
            flex: 1;
            display: flex;
            justify-content: center;
        }
        .box{
            width: 4.259vh;
            height: 4.537vh;
            background: url('~@/assets/images/civilAirDefense/status_box_bg1.png') no-repeat center center;
            background-size: 100% 100%;
            .info-box{
                text-align: center;
                padding-top: 1.11vh;
                .name{
                    font-size: 1.11vh;
                    color: #fff;
                    line-height: 1.574vh;
                }
                .num{
                    color: rgba(115, 250, 254, 1);
                    font-size: 1.48vh;
                    line-height: 1.48vh;
                    font-family: 'DIN Pro';
                    text-shadow: 0px 2px 2px rgba(5, 10, 23, 0.25);
                }
            }
            &.status-0{
                background-image: url('~@/assets/images/civilAirDefense/status_box_bg2.png');
                .info-box .num{
                    color: rgba(254, 163, 115, 1);
                }
            }
            &.status-1{
                background-image: url('~@/assets/images/civilAirDefense/status_box_bg3.png');
                .info-box .num{
                    color: rgba(254, 228, 115, 1);
                }
            }
            &.status-2{
                background-image: url('~@/assets/images/civilAirDefense/status_box_bg4.png');
                .info-box .num{
                    color: rgba(115, 157, 254, 1);
                }
            }
            &.status-3{
                background-image: url('~@/assets/images/civilAirDefense/status_box_bg5.png');
                .info-box .num{
                    color: rgba(255, 255, 255, 1);
                }
            }
        }
    }
}
</style>
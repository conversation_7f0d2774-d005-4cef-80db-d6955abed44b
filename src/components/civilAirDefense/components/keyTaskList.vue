<template>
    <div>
        <Title3 title="关键作业清单" style="margin-bottom: 1.48vh;" />
        <div class="key-task-list">
            <RankTemplate :list="list" unit="个" />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import RankTemplate from "@/components/commonView/rankTemplate.vue";
import { getKeyTaskList } from "@/api/civilAirDefense";
export default {
    components: {
        Title3,
        RankTemplate
    },
    data(){
        return {
            list: []
        }
    },
    mounted(){
        this.getData()
    },
    methods: {
        // 刷新
        refresh(){
            this.getData()
        },
        // 获取关键作业清单
        getData(){
            getKeyTaskList().then(res => {
                let max = Math.max(...res.data.map(item => item.keyorkSum))
                this.list = res.data.map(item => {
                    let rate = max ? parseFloat((item.keyorkSum / max * 100).toFixed(2)) : 0
                    return {
                        name: item.keyworkName,
                        num: item.keyorkSum,
                        rate: rate
                    }
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.key-task-list{
    padding: 0 1.48vh;
}
</style>
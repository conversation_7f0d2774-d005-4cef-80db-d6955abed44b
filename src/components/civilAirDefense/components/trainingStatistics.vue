<template>
    <div>
        <Title3 title="培训统计" />
        <div class="training-statistics">
            <div class="total-list">
                <div class="total-item" v-for="item in totalList" :key="item.key">
                    <div class="name">{{ item.name }}</div>
                    <div class="value">{{ item.value }}</div>
                </div>
            </div>
            <PieEcharts1 :list="statList" />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { getTrainingRecordStatistics } from "@/api/civilAirDefense";
import PieEcharts1 from "@/components/echarts/pieEcharts/pieEcharts1.vue";
export default {
    name: 'trainingStatistics',
    components: {
        Title3,
        PieEcharts1
    },
    data() {
        return {
            statList: [],
            totalList: [
                { name: '总培训次数', value: 0, key: 'sum' },
                { name: '总培训人次', value: 0, key: 'sumDetail' },
                { name: '培训合格率', value: 0, key: 'passRate' },
            ]
        }
    },
    mounted() {
        this.refresh()
    },
    methods: {
        refresh() {
            this.getData()
        },
        getData() {
            getTrainingRecordStatistics().then(res => {
                this.totalList = this.totalList.map(item => {
                    return {
                        ...item,
                        value: res.data[item.key]
                    }
                })
                this.statList = res.data.piChar.map(item => {
                    return {
                        name: item.name,
                        value: item.sums
                    }
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.training-statistics{
    padding: 0 1.48vh;
    .total-list{
        display: flex;
        align-items: flex-start;
        column-gap: 1.48vh;
        margin-bottom: 1.48vh;
        .total-item{
            flex: 1;
            background: linear-gradient(90deg, rgba(8, 86, 255, 0.3) 0%, rgba(8, 86, 255, 0) 100%);
            border-radius: 0.185vh;
            padding-left: 1.11vh;
            height: 5vh;
            border-left: 1px solid rgba(0, 81, 255, 1);
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            .name{
                font-size: 1.29vh;
                line-height: 1.85vh;
                color: #fff;
            }
            .value{
                font-size: 2.22vh;
                line-height: 2.77vh;
                font-family: 'DIN Pro';
                color: rgba(255, 255, 255, 1);
                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            }
            &:nth-child(2){
                background: linear-gradient(90deg, rgba(8, 86, 255, 0.3) 0%, rgba(8, 86, 255, 0) 100%);
                border-color: rgba(0, 81, 255, 1);
            }
            &:nth-child(3){
                background: linear-gradient(90deg, rgba(8, 255, 255, 0.3) 0%, rgba(8, 255, 255, 0) 100%);
                border-color: rgba(0, 255, 255, 1);
            }
        }
    }
}
</style>
<template>
    <div>
        <title3Long title="管理穿透-本月作业" style="margin-bottom: 1.48vh;" />
        <div class="management-penetration">
            <div class="management-penetration-left">
                <Title5 title="完成数" style="width: 55%;" />
                <div class="stat-list">
                    <template v-for="(item, index) in statList">
                        <div v-if="index !== 0" class="arrow" :key="index">
                            <img src="@/assets/images/civilAirDefense/arrow.png" alt="" />
                        </div>
                        <div class="stat-item" :key="item.tableName">
                            <img :src="require(`@/assets/images/civilAirDefense/${item.icon}`)" alt="" />
                            <div class="stat-item-title">{{ item.typeName }}</div>
                            <div class="stat-item-value">{{ item.recordCount }}</div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="management-penetration-right">
                <Title5 title="班前会" />
                <div class="meet-item">
                    <div class="meet-item-title">
                        <img src="@/assets/images/icon/book_open.png" class="icon-box" alt="" />
                        <div class="name">
                            <TooltipAutoShow :content="meetObj.content" />
                        </div>
                    </div>
                    <div class="meet-item-title">
                        <img src="@/assets/images/icon/time1.png" class="icon-box" alt="" />
                        <div class="time">{{ meetObj.meetingTime }}</div>
                    </div>
                    <div class="meet-item-file">
                        <img v-if="fileObj.type === 'image'" :src="fileObj.url" @click="showImgModal" alt="" />
                        <video v-else-if="fileObj.type === 'video'" :src="fileObj.url" controls></video>
                        <div v-else class="no-file">
                            <p>暂无文件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ImgModal ref="imgModal" />
    </div>
</template>
<script>
import title3Long from "@/components/commonView/title3Long.vue";
import Title5 from "@/components/commonView/title5.vue";
import { getSelectStatisticForScreen, getLatestRailPreMeeting } from "@/api/civilAirDefense";
import ImgModal from "@/components/modal/imgModal.vue";
export default {
    name: 'managementPenetration',
    components: {
        title3Long,
        Title5,
        ImgModal
    },
    data() {
        return {
            statList: [],
            meetObj: {},
            fileObj: {
                type: '',
                url: ''
            }
        }
    },
    mounted() {
        this.refresh()
    },
    methods: {
        // 刷新
        refresh() {
            this.getData()
            this.getPreMeetingRecord()
        },
        // 显示图片
        showImgModal() {
            this.$refs.imgModal.show([this.fileObj.url])
        },
        // 获取数据
        getData() {
            getSelectStatisticForScreen().then(res => {
                let iconObj = {
                    'preMeeting': 'icon_box4.png',
                    'processMonitor': 'icon_box5.png',
                    'eveningMeeting': 'icon_box6.png',
                    'gridSecurityLog': 'icon_box7.png',
                    'followRecord': 'icon_box8.png',
                }
                this.statList = res.data.map(item => {
                    return {
                        ...item,
                        icon: iconObj[item.tableName]
                    }
                })
            })
        },
        // 获取班前会记录
        getPreMeetingRecord() {
            getLatestRailPreMeeting().then(res => {
                this.meetObj = res.data
                let url = res.data.mediaFilesUrl?.split(',')?.[0] || ''
                if (url) {
                    this.fileObj.url = url
                    if (url.includes('.jpg') || url.includes('.png') || url.includes('.jpeg')) {
                        this.fileObj.type = 'image'
                    } else if (url.includes('.mp4') || url.includes('.avi') || url.includes('.mov')) {
                        this.fileObj.type = 'video'
                    } else {
                        this.fileObj.type = ''
                    }
                } else {
                    this.fileObj.type = ''
                    this.fileObj.url = ''
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.management-penetration{
    display: flex;
    flex-direction: row;
    column-gap: 0.74vh;
    padding: 0 2.22vh;
    .management-penetration-left{
        margin-right: 2.5vh;
        min-width: 65%;
        overflow: hidden;
    }
    .management-penetration-right{
        flex: 1;
        overflow: hidden;
    }
    .stat-list{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        img{
            display: block;
            width: 100%;
        }
        .arrow{
            width: 3.98vh;
            margin: 3vh 0.44vh 0;
        }
        .stat-item{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #fff;
            img{
                width: 9.53vh;
            }
            .stat-item-title{
                font-size: 1.29vh;
                text-shadow: 0px 4px 4px rgba(0, 0, 34, 1);
                line-height: 1.85vh;
                margin-bottom: 0.185vh;
            }
            .stat-item-value{
                font-family: 'DIN Pro';
                font-weight: 700;
                font-size: 2vh;
                text-shadow: 0px 0 2px rgba(5, 10, 23, 0.25);
                line-height: 2.59vh;
            }
        }
    }
    .meet-item{
        .meet-item-title{
            display: flex;
            align-items: center;
            color: #fff;
            margin-bottom: 0.37vh;
            .icon-box{
                width: 1.48vh;
                margin-right: 0.37vh;
            }
            .name{
                font-size: 1.38vh;
                line-height: 1.94vh;
                flex: 1;
                overflow: hidden;
            }
            .time{
                font-size: 1.11vh;
                line-height: 1.48vh;
            }
        }
        .meet-item-file{
            width: 100%;
            height: 10.56vh;
            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
                cursor: pointer;
            }
            video{
                width: 100%;
                height: 100%;
            }
            .no-file{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                color: #fff;
                font-size: 1.29vh;
            }
        }
    }
}
</style>
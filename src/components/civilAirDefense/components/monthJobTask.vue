<template>
    <div class="month-job-task">
        <Title3 title="本月跟班任务" style="margin-bottom: 1.48vh;" />
        <div class="month-main">
            <TableTemplate :columns="columns" :list="taskList">
                <template #percent="{row}">
                    <div class="progress">
                        <div class="progress-bar" :style="{width: row.percent + '%'}"></div>
                    </div>
                </template>
            </TableTemplate>
        </div>
        
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import TableTemplate from "@/components/commonView/tableTemplate.vue";
import { getMonthJobTask } from "@/api/civilAirDefense";
export default {
    name: 'monthJobTask',
    components: {
        Title3,
        TableTemplate
    },
    data() {
        return {
            columns: [
                { type: 'index', title: '序号', width: '6.4vh' },
                { title: '人员', key: 'userIdI18n', tooltip: true },
                { title: '', key: 'percent', slot: 'percent' },
                { title: '已执行', key: 'filledCount', tooltip: true, color: 'rgba(65, 211, 233, 1)' },
                { title: '计划', key: 'planCount', tooltip: true },
            ],
            taskList: []
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        // 刷新
        refresh(){
            this.getData()
        },
        // 获取本月跟班任务
        getData() {
            getMonthJobTask().then(res => {
                this.taskList = res.data.map(item => {
                    item.percent = item.planCount ? parseFloat((100 * item.filledCount / item.planCount).toFixed(2)) : 0
                    return item
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.month-main{
    padding: 0 1.48vh;
}
.progress{
    height: 0.55vh;
    background: rgba(203, 203, 203, 0.15);
    display: flex;
    align-items: center;
    width: 100%;
    .progress-bar{
        height: 0.37vh;
        background: linear-gradient(270deg, #4ACBFA 0%, #003CFF 100%);
        box-shadow: 0px 0px 4px 0px rgba(0, 132, 255, 0.5);
    }
}
</style>
<template>
    <div>
        <Title3 title="考核统计" style="margin-bottom: 1.48vh;" />
        <div class="assessment-statistics">
            <div class="best-main">
                <div class="best-main-left">
                    <div class="img-box">
                        <img src="@/assets/images/civilAirDefense/assess_icon.png" alt="" />
                    </div>
                    <div class="title-box">
                        <img src="@/assets/images/civilAirDefense/arrow1.png" class="arrow l" alt="" />
                        <div class="name">上月最佳</div>
                        <img src="@/assets/images/civilAirDefense/arrow1.png" class="arrow r" alt="" />
                    </div>
                </div>
                <div class="best-main-line"></div>
                <div class="best-main-right">
                    <div class="best-info">
                        <img src="@/assets/images/icon/person.png" alt="" class="best-img" />
                        <div class="type-name">人员</div>
                        <div class="best-info-right">
                            <AssessmentSwiper :list="personMaxScore" />
                        </div>
                    </div>
                    <div class="best-info">
                        <img src="@/assets/images/icon/person1.png" alt="" class="best-img" />
                        <div class="type-name">施工队伍</div>
                        <div class="best-info-right">
                            <AssessmentSwiper :list="groupMaxScore" />
                        </div>
                    </div>
                </div>
            </div>
            <Title5 title="总考核次数分数分布图" />
            <div class="records-total">
                <div class="num-main">
                    <div class="num-item">
                        <div class="img-box">
                            <img src="@/assets/images/civilAirDefense/assess_icon1.png" alt="" />
                        </div>
                        <div class="name">总考核班次</div>
                        <div class="value">{{ recordsTotal.groupTotal }}</div>
                    </div>
                    <div class="num-item">
                        <div class="img-box">
                            <img src="@/assets/images/civilAirDefense/assess_icon2.png" alt="" />
                        </div>
                        <div class="name">总考核人次</div>
                        <div class="value">{{ recordsTotal.personTotal }}</div>
                    </div>
                </div>
                <div class="score-main">
                    <RadarEchart :list="actualScore" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import Title5 from "@/components/commonView/title5.vue";
import AssessmentSwiper from "./assessmentSwiper.vue";
import RadarEchart from "@/components/echarts/radarEcharts/radarEcharts.vue";
import { getAssessTableStatisticScreen } from "@/api/civilAirDefense";
export default {
    components: {
        Title3,
        AssessmentSwiper,
        Title5,
        RadarEchart
    },
    data(){
        return {
            personMaxScore: [], // 人员最高分
            groupMaxScore: [], // 施工队伍最高分
            recordsTotal: {},
            actualScore: []
        }
    },
    mounted(){
        this.refresh()
    },
    methods: {
        //刷新
        refresh(){
            this.getData()
        },
        // 获取考核统计数据
        getData(){
            getAssessTableStatisticScreen().then(res => {
                this.personMaxScore = res.data.personMaxScore
                this.groupMaxScore = res.data.groupMaxScore
                this.recordsTotal = res.data.recordsTotal
                this.actualScore = res.data.actualScore.map(item => {
                    return {
                        name: item.scoreRange + '分',
                        value: item.recordCount
                    }
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.assessment-statistics{
    padding: 0 1.48vh;
    .best-main{
        width: 100%;
        height: 6.388vh;
        background: linear-gradient(270.22deg, rgba(0, 68, 255, 0) 12.24%, rgba(48, 90, 201, 0.27) 93.37%);
        border-radius: 0.37vh;
        display: flex;
        align-items: center;
        padding: 0 0.74vh;
        margin-bottom: 1.48vh;
        .best-main-left{
            width: 7.68vh;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .img-box{
                width: 4.9vh;
                height: 4.35vh;
                img{
                    margin-top: -0.55vh;
                }
            }
            .title-box{
                width: 100%;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .arrow{
                    width: 0.46vh;
                    &.r{
                        transform: rotate(180deg);
                    }
                }
                .name{
                    font-size: 1.48vh;
                    line-height: 1.85vh;
                    color: #fff;
                }
            }
        }
        .best-main-line{
            width: 1px;
            height: 3.98vh;
            background: linear-gradient(180deg, rgba(124, 170, 214, 0) 0%, #7CAAD6 50%, rgba(124, 170, 214, 0) 100%);
            margin: 0 1vh;
        }
        .best-main-right{
            flex: 1;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 1.5px 0;
            .best-info{
                width: 100%;
                height: 2.685vh;
                display: flex;
                align-items: center;
                background: rgba(59, 101, 161, 0.31);
                padding: 0 0.37vh;
                line-height: 1.94vh;
                font-size: 1.38vh;
                .best-img{
                    width: 1.85vh;
                    margin-right: 0.74vh;
                }
                .type-name{
                    color: rgba(255, 255, 255, 0.8);
                    width: 7vh;
                }
                .best-info-right{
                    flex: 1;
                    overflow: hidden;
                }
            }
        }
    }
    .records-total{
        display: flex;
        align-items: center;
        .num-main{
            .num-item{
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 8.98vh;
                height: 8.61vh;
                background: linear-gradient(360deg, rgba(8, 86, 255, 0.3) 0%, rgba(8, 86, 255, 0) 100%);
                border-bottom: 1px solid rgba(0, 81, 255, 1);
                border-radius: 0.185vh;
                color: #fff;
                .img-box{
                    width: 2.96vh;
                    height: 2.96vh;
                    img{
                        width: 100%;
                    }
                }
                .name{
                    font-size: 1.29vh;
                    line-height: 1.85vh;
                }
                .value{
                    font-size: 1.85vh;
                    line-height: 2.31vh;
                    font-family: 'DIN Pro';
                    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                }
                &:first-child{
                    margin-bottom: 0.74vh;
                }
            }
        }
        .score-main{
            flex: 1;
            overflow: hidden;
        }
    }
}
</style>
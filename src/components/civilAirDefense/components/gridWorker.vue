<template>
    <div class="grid-worker">
        <Title3 title="在场网格员" style="margin-bottom: 1.48vh;" />
        <div class="grid-worker-swiper">
            <Swiper v-if="gridWorkerList.length > 0" :options="swiperOptions" ref="mySwiper" class="grid-swiper">
                <SwiperSlide v-for="item in gridWorkerList" :key="item.id">
                    <div class="grid-item">
                        <div class="grid-item-top">
                            <div class="type">{{item.post}}</div>
                            <div v-if="item.statusDev" class="online">
                                <img v-if="item.statusDevValue === 1" src="@/assets/images/icon/status_open1.png" class="online-icon" alt="">
                                <img v-else src="@/assets/images/icon/status_close1.png" class="online-icon" alt="">
                                <span>{{item.statusDev}}</span>
                            </div>
                            <div class="status">
                                <img v-if="item.onLineStatusValue === 1" src="@/assets/images/icon/status_open.png" class="status-icon" alt="">
                                <img v-else-if="item.onLineStatusValue === 0" src="@/assets/images/icon/status_close.png" class="status-icon" alt="">
                            </div>
                        </div>
                        <div class="grid-item-info">
                            <div class="avatar">
                                <img :src="item.pict" class="avatar-icon" alt="">
                            </div>
                            <div class="right">
                                <div class="name"><span>{{item.name}}</span> <span>{{item.phone}}</span></div>
                                <div v-if="item.post !== '安监专务'" class="position">负责网格: {{item.gridNames}}</div>
                            </div>
                        </div>
                    </div>
                </SwiperSlide>
            </Swiper>
            <no-data v-else :loading="loading" />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
import 'swiper/css/swiper.min.css';
import { getGridPersonInfo } from '@/api/civilAirDefense'
export default {
    name: 'gridWorker',
    components: {
        Title3,
        Swiper, SwiperSlide
    },
    data() {
        return {
            swiperOptions: {
                direction: 'vertical',
                slidesPerView: 3.5,
                loop: false,
                observer: true,
                observeParents: true,
                speed: 600,
                autoHeight: true,
                autoplay: {
                    disableOnInteraction: false,
                    delay: 5000
                }
            },
            gridWorkerList: [],
            loading: false
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        // 刷新
        refresh() {
            this.getData()
        },
        // 获取数据
        getData() {
            this.loading = true
            getGridPersonInfo().then(res => {
                this.gridWorkerList = res.data
                this.loading = false
            }).catch(() => {
                this.loading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
.grid-swiper{
    height: 100%;
}
.grid-worker-swiper{
    padding: 0 1.48vh;
    height: 25.96vh;
}
.grid-item{
    color: #fff;
    margin-bottom: 0.74vh;
    img{
        display: block;
        width: 100%;
    }
    .grid-item-top{
        display: flex;
        align-items: center;
        background: linear-gradient(90deg, #00329E 0.05%, rgba(0, 26, 66, 0) 100%);
        height: 2.037vh;
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
        padding: 0 0.74vh;
        line-height: 2vh;
        column-gap: 0.74vh;
        margin-bottom: 0.37vh;
        .type{
            flex: 1;
            font-size: 1.38vh;
        }
        .online{
            display: flex;
            align-items: center;
            column-gap: 0.185vh;
            font-size: 1.11vh;
            .online-icon{
                width: 1.48vh;
            }
        }
        .status{
            width: 3.88vh;
        }
    }
    .grid-item-info{
        height: 3.33vh;
        display: flex;
        align-items: center;
        border-left: 1px solid rgba(1, 50, 156, 1);
        column-gap: 0.74vh;
        padding-left: 0.74vh;
        font-size: 1.11vh;
        line-height: 1.66vh;
        .avatar{
            width: 3.33vh;
            height: 3.33vh;
            img{
                height: 100%;
                object-fit: cover;
            }
        }
    }
}
</style>
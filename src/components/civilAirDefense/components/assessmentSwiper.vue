<template>
    <div class="assessment-swiper">
        <swiper
            v-if="list.length > 0"
            :options="swiperOptions"
            ref="mySwiper"
            class="assess-swiper"
        >
            <swiper-slide v-for="(item, index) in list" :key="index" class="assess-slide">
                <div class="assess-box">
                    <div class="name">{{ item.assessGroup }}</div>
                    <div class="type">
                        <TooltipAutoShow :content="item.assessType" />
                    </div>
                    <div class="score">{{ item.actualScore }}</div>
                </div>
            </swiper-slide>
        </swiper>
        <div v-else class="empty-box">暂无数据</div>
    </div>
</template>
<script>
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
export default {
    name: 'AssessmentSwiper',
    components: {
        Swiper,
        SwiperSlide
    },
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data(){
        return {
            swiperOptions: {
                direction: "vertical",
                loop: false,
                observer: true,
                observeParents: true,
                speed: 600,
                autoHeight: true,
                autoplay: {
                    disableOnInteraction: false,
                    delay: 5000,
                },
            }
        }
    }
}
</script>
<style lang="less" scoped>
.assess-swiper,
.assessment-swiper{
    width: 100%;
    height: 100%;
}
.assess-box{
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    column-gap: 0.37vh;
    .type{
        flex: 1;
        overflow: hidden;
    }
}
.empty-box{
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}
</style>
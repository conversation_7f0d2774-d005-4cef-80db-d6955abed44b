<template>
  <div class="panel-header">
    <span class="header-text">{{ title }}</span>
    <slot></slot>
    <slot name="address" class="right"></slot>
    <slot name="btn" class="right"></slot>
  </div>
</template>


<script>
export default {
  name: "PanelHeader",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
};
</script>


<style lang="less" scoped>
.panel-header {
  width: 100%;
  height: 45px;
  background: url("/img/screen-main/panel-header.png") no-repeat;
  background-size: 100% 100%;
  padding-left: 65px;
  position: relative;

  .header-text {
    font-family: "syst-Bold";
    font-size: 20px;
    line-height: 47px;
  }

  .right {
    font-family: syst-Regular;
    position: absolute;
    right: 7%;
    top: 56%;
    transform: translate(0%, -50%);
    color: #e9c44f;
  }

  .btn-group {
    position: absolute;
    display: flex;
    right: 7%;
    top: 56%;
    transform: translate(0%, -50%);
    width: 40%;

    ul {
      width: 100%;
    }

    li {
      margin-right: 5px;
      position: relative;
      font-family: syst-Regular;
      width: 33%;
      text-align: center;
      margin-top: 3px;
      cursor: pointer;
    }

    li::after {
      content: "";
      position: absolute;
      height: 15px;
      top: 50%;
      right: -5px;
      transform: translate(0%, -50%);
      width: 1px;
      background: #ccc;
    }

    li:last-child::after {
      display: none;
    }

    .active {
      color: rgb(244, 169, 7);
    }
  }
}
</style>
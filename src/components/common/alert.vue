<template>
  <transition name="alert-slide">
    <div class="global-alert" 
      v-if="!isClosing" :class="[type, position]"
      :style="computedStyle">
      <div class="alert-title">
        <div class="alert-left">
          <img :src="iconClass" />
          <h4 v-if="(propsData && propsData.name) || title" class="alert-title-h4">{{ (propsData && propsData.name) || title }}</h4>
        </div>
        <Icon type="md-close" color='#ffffff' size='16' @click="close"/>
      </div>
      <div class="alert-content">
        <div class="alert-message">{{ (propsData && propsData.newlestLimitAlarm.bigScreenAlarmContent) || message }}</div>
        <div class="alert-message">{{ (propsData && propsData.newlestLimitAlarm.createTime) || time }}</div>
      </div>
    </div>
  </transition>
</template>

<script>
import warning from '@/assets/images/icon/alert-warn.png'

const BASE_OFFSET = 120 //每个弹窗间隔120px
export default {
  name: 'alert',
  props: {
    type: {
      type: String,
      default: 'info'
    },
    position: {
      type: String,
      default: 'top-right'
    },
    title: String,
    message: String,
    time: String,
    duration: {
      type: Number,
      default: 4500
    },
    rightMargin: {
      type: Number,
      default: 20
    },
    propsData: {
      type: Object,
      default: () => {},
    }
  },
  data() {
    return {
      isClosing: false,
      verticalOffset: 0,
      isInBubble: false, // 标记是否在 Bubble 容器中
      localDuration: this.duration // 用本地变量代理 props
    }
  },
  computed: {
    iconClass() {
      return {
        // info: 'icon-info',
        // success: 'icon-success',
        warning: warning,
        error: warning,
      }[this.type]
    },
    computedRight() {
      // 动态计算右侧位置
      const sidebarElement = document.querySelector('right-sidebar')
      const sidebarWidth = sidebarElement ? sidebarElement.offsetWidth : 0
      return this.rightMargin + sidebarWidth
    },
    computedStyle() {
      // 如果在Bubble容器中，返回空样式对象，让Bubble.js控制定位
      if (this.isInBubble) {
        return {};
      }
      // 否则返回默认的靠右定位样式
      return { 
        right: this.computedRight + 'px',
        top: this.getTopPosition() + 'px'
      };
    }
  },
  mounted() {
    // 检查是否在 Bubble 容器中
    const isInBubble = this.$el && typeof this.$el.closest === 'function' && this.$el.closest('.bubble-content');
    
    console.log('🎯 alert组件mounted，检查容器环境:', {
      hasEl: !!this.$el,
      isInBubble: isInBubble,
      elParent: this.$el ? this.$el.parentNode : null,
      elParentId: this.$el && this.$el.parentNode ? this.$el.parentNode.id : null
    });
    
    if (isInBubble) {
      // 如果在 Bubble 容器中，简化处理逻辑
      console.log('🎯 alert 组件在 Bubble 容器中，禁用自主定位');
      this.isInBubble = true;
      
      // 只重置必要的样式，不影响Bubble的定位
      if (this.$el) {
        // 只移除可能冲突的定位样式，保留其他样式
        this.$el.style.position = 'relative';
        this.$el.style.transform = 'none';
        this.$el.style.left = 'auto';
        this.$el.style.top = 'auto';
        this.$el.style.right = 'auto';
        this.$el.style.bottom = 'auto';
        this.$el.style.margin = '0';
        
        // 确保关闭按钮可以点击
        this.$el.style.pointerEvents = 'auto';
        this.$el.style.zIndex = 'auto';
        
        console.log('🎯 alert 组件样式已重置，保留关闭按钮功能');
        
        // 禁用定时器，让 Bubble.js 完全控制
        // this.duration = 0;
        this.localDuration = 0;
      }
    } else {
      // 如果不在 Bubble 容器中，使用原有的定位逻辑
      console.log('🎯 alert 组件不在 Bubble 容器中，使用自主定位');
      
      if (this.propsData && this.propsData.coordinates) {
        // 如果有坐标数据，启用地图固定定位
        this.calculateMapPosition()
        this.setupMapPositionListener()
      } else {
        // 如果没有坐标数据，使用默认的靠右定位
        // 动态计算垂直偏移量，避免alert重叠
        const existingAlerts = document.querySelectorAll('.global-alert');
        this.verticalOffset = existingAlerts.length * BASE_OFFSET;
        
        // 应用垂直偏移量
        if (this.$el) {
          this.$el.style.transform = `translateY(${this.verticalOffset}px)`;
        }
      }
    }

    // 只有在非 Bubble 容器中才设置定时器
    if (!this.isInBubble && this.localDuration > 0) {
      this.timer = setTimeout(() => {
        this.close()
      }, this.localDuration)
    }
  },
  methods: {
    close() {
      console.log('🔔 alert组件关闭按钮被点击')
      try {
        // 如果在 Bubble 容器中，触发Bubble的关闭事件
        if (this.isInBubble) {
          console.log('🎯 在Bubble容器中，触发close事件');
          this.$emit('close');
          return;
        }
        
        // 如果不在 Bubble 容器中，使用原有的关闭逻辑
        this.$nextTick(() => {
          const alerts = document.querySelectorAll('.global-alert')
          alerts.forEach((alert, index) => {
            alert.style.transform = `translateY(${index * BASE_OFFSET}px)`
          })
        })
        
        // 增加销毁状态标记
        if (this._isDestroyed) return

        clearTimeout(this.timer)

        // 只有在非 Bubble 容器中才从 body 移除
        if (this.$el && this.$el.parentNode === document.body && !this.isInBubble) {
          document.body.removeChild(this.$el)
        }
        
        // 统一销毁入口
        this.$destroy()
        this._isDestroyed = true
      } catch (e){
        console.warn('alert组件销毁失败', e)
      }
    },
    pauseTimer() {
      // 如果在 Bubble 容器中，不执行定时器相关操作
      if (this.isInBubble || this.localDuration === 0) return
      clearTimeout(this.timer)
    },
    resumeTimer() {
      // 如果在 Bubble 容器中，不执行定时器相关操作
      if (this.isInBubble || this.localDuration === 0) return
      this.timer = setTimeout(() => {
        this.close()
      }, this.localDuration);
    },
    getAlertCount() {
      return document.querySelectorAll('.global-alert').length
    },
    
    // 计算顶部位置
    getTopPosition() {
      // 如果有propsData中的top属性，使用它
      if (this.propsData && typeof this.propsData.top === 'number') {
        return this.propsData.top;
      }
      // 否则使用默认的top值
      return this.position === 'top-right' ? 120 : 20;
    },
    calculateMapPosition() {
      // 如果在 Bubble 容器中，不执行定位逻辑
      if (this.isInBubble) {
        console.log('🎯 alert 在 Bubble 中，跳过自主定位');
        return;
      }
      
      // 额外检查：如果在 bubble-content 容器中，也跳过
      if (this.$el && typeof this.$el.closest === 'function' && this.$el.closest('.bubble-content')) {
        console.log('🎯 alert 在 bubble-content 中，跳过自主定位');
        return;
      }
      
      if (!window.viewer || !this.propsData.coordinates) return

      try {
        // 确保坐标格式正确
        const [lng, lat] = this.propsData.coordinates
        if (typeof lng !== 'number' || typeof lat !== 'number') {
          console.warn('坐标格式错误:', this.propsData.coordinates)
          return
        }

        // 转换为笛卡尔坐标
        const cartesian = Cesium.Cartesian3.fromDegrees(lng, lat)
        
        // 转换为屏幕坐标
        const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)

        if (canvasPosition) {
          // 检查坐标是否在可视区域内
          const canvas = window.viewer.scene.canvas
          const isVisible = canvasPosition.x >= 0 && canvasPosition.x <= canvas.clientWidth &&
                          canvasPosition.y >= 0 && canvasPosition.y <= canvas.clientHeight

          if (isVisible) {
            // 计算弹窗尺寸和边界
            const alertWidth = 280 // 弹窗宽度
            const alertHeight = 100 // 弹窗高度
            const margin = 20 // 边距
            
            // 计算调整后的位置，确保弹窗不超出屏幕边界
            let adjustedX = canvasPosition.x
            let adjustedY = canvasPosition.y
            
            // 水平边界检查
            if (adjustedX < alertWidth / 2 + margin) {
              adjustedX = alertWidth / 2 + margin
            } else if (adjustedX > canvas.clientWidth - alertWidth / 2 - margin) {
              adjustedX = canvas.clientWidth - alertWidth / 2 - margin
            }
            
            // 垂直边界检查
            if (adjustedY < alertHeight + margin) {
              // 如果上方空间不足，将弹窗显示在坐标点下方
              adjustedY = canvasPosition.y + 20
            } else if (adjustedY > canvas.clientHeight - margin) {
              adjustedY = canvas.clientHeight - margin
            }
            
            // 设置弹窗位置，固定在地图坐标点上
            this.$el.style.cssText = `
              position: fixed;
              left: ${adjustedX}px;
              top: ${adjustedY}px;
              transform: translate(-50%, -100%);
              z-index: 9999;
              pointer-events: auto;
              display: block;
            `
          } else {
            // 如果坐标不在可视区域内，隐藏弹窗
            this.$el.style.display = 'none'
          }
        }
      } catch (e) {
        console.error('坐标转换失败:', e)
      }
    },

    /**
     * 监听地图视图变化，实时更新弹窗位置
     */
    setupMapPositionListener() {
      // 如果在 Bubble 容器中，不设置监听器
      if (this.isInBubble) {
        console.log('🎯 alert 在 Bubble 中，跳过地图监听器');
        return;
      }
      
      // 额外检查：如果在 bubble-content 容器中，也跳过
      if (this.$el && typeof this.$el.closest === 'function' && this.$el.closest('.bubble-content')) {
        console.log('🎯 alert 在 bubble-content 中，跳过地图监听器');
        return;
      }
      
      if (!window.viewer) return

      // 移除之前的监听器
      if (this._mapPositionHandler) {
        window.viewer.camera.changed.removeEventListener(this._mapPositionHandler)
      }

      // 添加防抖功能，避免频繁更新
      let updateTimer = null
      this._mapPositionHandler = () => {
        if (updateTimer) {
          clearTimeout(updateTimer)
        }
        updateTimer = setTimeout(() => {
          this.calculateMapPosition()
        }, 100) // 100ms 防抖
      }

      window.viewer.camera.changed.addEventListener(this._mapPositionHandler)
    },

    /**
     * 清理地图位置监听器
     */
    cleanupMapPositionListener() {
      if (this._mapPositionHandler && window.viewer) {
        window.viewer.camera.changed.removeEventListener(this._mapPositionHandler)
        this._mapPositionHandler = null
      }
    }
  },
  beforeDestroy() {
    // 确保在组件销毁时清理所有监听器
    this.cleanupMapPositionListener()
    
    // 只有在非 Bubble 容器中才清理定时器
    if (!this.isInBubble && this.timer) {
      clearTimeout(this.timer)
    }
  }
}
</script>

<style lang="less" scoped>
.global-alert {
  position: fixed; /* 默认使用fixed定位，让alert可以自由定位 */
  z-index: 9999;
  width: 280px;
  height: auto;
  min-height: 100px;
  padding: 0 8px;
  background: url(../../assets/images/components/popupBg.png) no-repeat;
  background-size: 100% auto;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  transition: all 0.3s;
  pointer-events: all;
  
  /* 当在 Bubble 容器中时的特殊样式 */
  .bubble-content & {
    position: relative !important;
    transform: none !important;
    left: auto !important;
    top: auto !important;
    right: auto !important;
    bottom: auto !important;
    margin: 0 !important;
    z-index: auto !important;
    transition: none !important;
    pointer-events: auto !important; /* 确保可以点击 */
    
    /* 禁用所有可能影响定位的动画 */
    &.alert-slide-enter-active,
    &.alert-slide-leave-active {
      transition: none !important;
    }
    
    &.alert-slide-enter,
    &.alert-slide-leave-to {
      opacity: 1 !important;
      transform: none !important;
    }
  }

  .alert-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 35px;
    color: #ffffff;
    .alert-left {
      display: flex;
      align-items: center;
      
      .alert-title-h4 {
        font-size: 14px;
        font-family: 'HuXiaoBo_KuHei';
        margin-left: 4px;
      }
    }
  }

  /* 移除top-right样式，现在由computedStyle控制定位 */

  .alert-content {
    font-size: 13px;
    font-family: 'PingFang SC';
    color: #ffffff;
    height: auto;
    min-height: calc(100px - 25px);
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .alert-message {
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 4px;
        height: 4px;
        display: block;
        background-color: #ffffff;
        transform: rotate(45deg);
        transform-origin: center;
        margin-right: 8px;
      }
    }
  }

  // 不同类型图标样式
  @icon-size: 24px;
  [class^="icon-"] {
    position: absolute;
    left: 15px;
    font-size: @icon-size;
  }
}

.alert-slide-enter-active, .alert-slide-leave-active {
  transition: all 0.3s;
}
.alert-slide-enter, .alert-slide-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
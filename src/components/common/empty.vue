<template>
  <div class="empty">
    <div class="box">
        <img src="../../assets/images/ProjectAI/empty.png">
        <span>{{ title }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['title'],
    data() {
        return {

        }
    }
  }
</script>

<style lang="less" scoped>
.empty {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .box {
        width: 207px;
        height: 95px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
        span {
            // 渐变文字
            background-image: linear-gradient(90deg, #9EBBF9 0%, #F2F6FE 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 22px;
            color: transparent
        }
    }
}


</style>

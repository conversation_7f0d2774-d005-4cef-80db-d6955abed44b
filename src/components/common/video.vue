<template>
    <div class="root">
        <div id="container" ref="container"></div>
    </div>
  
</template>

<script>
  export default {
    props: {
        videoUrl: {
            type: String,
            default: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043'
        }
    },
    data() {
        return {
            jessibuca: null
        }
    },
    mounted() {
        this.initVideo();
    },
    methods: {
        initVideo() {
            this.jessibuca = new JessibucaPro({
                container: document.querySelector('#container'),
                // 其他配置...
                showBandwidth: false,  // 隐藏带宽信息
                showPerformance: false, // 隐藏性能信息
                operateBtns: {
                    fullscreen: true,
                    screenshot: true,
                    play: true,
                    audio: true,
                    ptz: true,
                    quality: true,
                    performance: false, // 隐藏性能按钮
                },
            })
            this.play();
        },
        play() {
            if (this.videoUrl) {
                this.jessibuca.play(this.videoUrl)
            } else {
                this.jessibuca.showErrorMessageTips('播放地址不能为空');
            }
        },
    }

  }
</script>

<style lang="less" scoped>
.root {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    #container {
        width: 98%;
        height: 90%;
        background-color: transparent;
        position: relative;
    }
}
</style>

// 导入 Vue 框架
import Vue from 'vue'
// 导入全局提示框组件
import GlobalAlert from './alert.vue'

// 使用 Vue.extend 方法创建一个全局提示框组件的构造器
const AlertConstructor = Vue.extend(GlobalAlert)

/**
 * 显示全局提示框
 * @param {Object|string} options - 提示框的配置选项，如果是字符串，则会被当作提示信息
 * @returns {Object} - 返回一个包含关闭方法和提示框实例的对象
 */
function showAlert(options) {
  // 根据构造器创建一个提示框实例，并传入配置选项
  const instance = new AlertConstructor({
    propsData: {
      ...options,
      persistent: options.duration === 0 // 添加持久化标记
    },
  }).$mount()
  
  // 将提示框实例的 DOM 元素添加到页面的 body 中
  document.body.appendChild(instance.$el)
  // 返回一个对象，包含关闭提示框的方法和提示框实例
  return {
    close: () => instance.close(),
    component: instance
  }
}

// 定义提示框的类型数组
const types = ['success', 'warning', 'info', 'error']
// 定义一个用于存储不同类型提示框方法的对象
const alert = {}

// 遍历提示框类型数组，为 alert 对象添加不同类型的提示框方法
types.forEach(type => {
  alert[type] = options => {
    // 调用 showAlert 方法，并传入包含类型和配置选项的对象
    return showAlert({
      type,
      ...(typeof options === 'string' ? { message: options, time: options } : options)
    })
  }
})

// 添加全局关闭方法
alert.close = () => {
  // 查找并关闭所有全局弹窗
  const alerts = document.querySelectorAll('.global-alert');
  alerts.forEach(alert => {
    if (alert.parentNode) {
      alert.parentNode.removeChild(alert);
    }
  });
}

/**
 * 插件安装方法，将 $alert 方法添加到 Vue 原型上
 * @param {Object} Vue - Vue 构造函数
 */
export default {
  install(Vue) {
    // 将 alert 对象挂载到 Vue 原型上，以便在组件中可以通过 this.$alert 调用
    Vue.prototype.$alert = alert
  }
}
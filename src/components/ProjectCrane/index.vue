<template>
  <div class="project-map-page">
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div
        class="side-panel"
        style="pointer-events: auto"
        @mouseover="autoplay = false"
        @mouseleave="autoplay = true"
      >
        <Title title="吊车工效分析"></Title>
        <Carousel
          v-model="CarouselValue"
          dots="none"
          arrow="never"
          :autoplay="autoplay"
          :autoplay-speed="3000"
          :style="{ minHeight: Object.keys(dataInfo).length <= 0 ? '45%' : '' }"
        >
          <CarouselItem v-for="(item, index) in dataInfo" :key="index">
            <div class="padcontent">
              <div class="statistics">
                <div class="title">
                  {{ item.workPointName ? item.workPointName : "-" }}
                  {{ item.name }}
                </div>
                <div class="totalNum">
                  <div class="num">
                    <div class="youshebiaotihei">总吊重</div>
                    <div class="youshebiaotihei">
                      {{
                        item.totalWeight != null
                          ? (item.totalWeight / 1000).toFixed(2)
                          : "-"
                      }}<span>KT</span>
                    </div>
                  </div>
                  <div class="num">
                    <div class="youshebiaotihei">总吊次</div>
                    <div class="youshebiaotihei">
                      {{
                        item.totalHoistingTimes &&
                        item.totalHoistingTimes != null
                          ? item.totalHoistingTimes
                          : "-"
                      }}<span>次</span>
                    </div>
                  </div>
                </div>
                <div class="time">
                  <div class="item">
                    <div class="top">作业时长</div>
                    <div class="hour">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.workHour != null
                          ? item.craneBusinessRecord.workHour
                          : "-"
                      }}<sub>h</sub>
                    </div>
                  </div>
                  <div class="item">
                    <div class="top">怠速时长</div>
                    <div class="hour">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.idlingHour != null
                          ? item.craneBusinessRecord.idlingHour
                          : "-"
                      }}<sub>h</sub>
                    </div>
                  </div>
                  <div class="item">
                    <div class="top">总工作时长</div>
                    <div class="hour">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.workTotalHour != null
                          ? item.craneBusinessRecord.workTotalHour
                          : "-"
                      }}<sub>h</sub>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CarouselItem>
        </Carousel>
        <Title title="吊车实时数据" style="margin-top: 10px"></Title>
        <Carousel
          v-model="CarouselValue"
          dots="none"
          arrow="never"
          :autoplay="autoplay"
          :autoplay-speed="3000"
        >
          <CarouselItem v-for="(item, index) in dataInfo" :key="index">
            <div class="padcontent">
              <div class="realData">
                <div class="title">
                  <div class="online">
                    <span
                      >{{ item.workPointName ? item.workPointName : "-" }}
                      {{ item.name }}</span
                    >
                    <img
                      :src="
                        item.onlineState == 1
                          ? require('@/assets/images/icon/online.png')
                          : require('@/assets/images/icon/offline.png')
                      "
                    />
                  </div>
                  <div class="online time">
                    <span>{{
                      item.craneBusinessRecord &&
                      item.craneBusinessRecord.createTime != null
                        ? item.craneBusinessRecord.createTime
                        : "-"
                    }}</span>
                    <span class="photo" @click="showPic(item)">机械五定图</span>
                  </div>
                </div>

                <div class="peopleDetail">
                  <div class="profile">
                    <img
                      :src="
                        item.driverList ? item.driverList[0].profilePict : empty
                      "
                    />
                  </div>
                  <div class="profileInfo">
                    <div class="info">
                      <span class="titles"> 司机姓名： </span>
                      <span class="text">
                        {{
                          item.driverList ? item.driverList[0].realName : "-"
                        }}
                      </span>
                    </div>
                    <div class="info">
                      <span class="titles"> 电话号码： </span>
                      <span class="text">
                        {{
                          item.driverList ? item.driverList[0].linkPhone : "-"
                        }}
                      </span>
                    </div>
                    <div class="info">
                      <span class="titles"> 司机证书： </span>
                      <div class="text" @click="search(item)">查看</div>
                    </div>
                  </div>
                </div>
                <div class="information">
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmWind) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">吊重</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.weight != null
                          ? item.craneBusinessRecord.weight
                          : "-"
                      }}<sub>t</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmWindSpeed) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">风速</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.windSpeed != null
                          ? item.craneBusinessRecord.windSpeed
                          : "-"
                      }}<sub>m/s</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmPitch) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">俯仰角</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.pitch != null
                          ? item.craneBusinessRecord.pitch
                          : "-"
                      }}<sub>°</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmMoment) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">力矩</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.moment != null
                          ? item.craneBusinessRecord.moment
                          : "-"
                      }}<sub>t·m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmBigArmLen) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">大臂长度</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.bigArmLen != null
                          ? item.craneBusinessRecord.bigArmLen
                          : "-"
                      }}<sub>m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmRotation) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">回转角</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.rotation != null
                          ? item.craneBusinessRecord.rotation
                          : "-"
                      }}<sub>m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmArmForce) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">力臂</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.armForce != null
                          ? item.craneBusinessRecord.armForce
                          : "-"
                      }}<sub>m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmLevelX) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">水平度X</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.levelX != null
                          ? item.craneBusinessRecord.levelX
                          : "-"
                      }}<sub>m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmLevelY) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">水平度Y</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.levelY != null
                          ? item.craneBusinessRecord.levelY
                          : "-"
                      }}<sub>m</sub>
                    </div>
                  </div>
                  <div
                    :class="
                      'item ' +
                      (((item.craneBusinessRecord &&
                        item.craneBusinessRecord.alarmInclination) ||
                        0) == 1
                        ? 'active'
                        : '')
                    "
                  >
                    <div class="top">倾斜度</div>
                    <div class="bottom">
                      {{
                        item.craneBusinessRecord &&
                        item.craneBusinessRecord.inclination != null
                          ? item.craneBusinessRecord.inclination
                          : "-"
                      }}<sub>°</sub>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CarouselItem>
        </Carousel>
      </div>
    </div>
    <div class="right-content">
      <div
        class="side-panel side-panel-right"
        style="pointer-events: auto; height: 100%"
      >
        <Title title="吊车吊次曲线"></Title>
        <div class="echartsContent">
          <div class="ehcharts-title">
            <Select v-model="craneId1" @on-change="changeCrane1($event)">
              <Option
                v-for="(item, index) in dataInfo"
                :key="index"
                :label="item.name"
                :value="item.id"
                >{{ item.name }}</Option
              >
            </Select>
            <div class="btn">
              <div
                :class="'myButton ' + (weekandmonth1 == 'week' ? 'active' : '')"
                @click="onweek1('week')"
              >
                近1周
              </div>
              <div
                :class="
                  'myButton ' + (weekandmonth1 == 'month' ? 'active' : '')
                "
                @click="onmonth1('month')"
              >
                近1月
              </div>
            </div>
          </div>
          <div class="echarts">
            <div id="dc_echarts1" style="width: 100%; height: 100%"></div>
          </div>
        </div>

        <Title title="吊车吊重统计"></Title>
        <div class="echartsContent">
          <div class="ehcharts-title">
            <Select v-model="craneId2" @on-change="changeCrane2($event)">
              <Option
                v-for="(item, index) in dataInfo"
                :key="index"
                :label="item.name"
                :value="item.id"
                >{{ item.name }}</Option
              >
            </Select>
            <div class="btn">
              <div
                :class="'myButton ' + (weekandmonth2 == 'week' ? 'active' : '')"
                @click="onweek2('week')"
              >
                近1周
              </div>
              <div
                :class="
                  'myButton ' + (weekandmonth2 == 'month' ? 'active' : '')
                "
                @click="onmonth2('month')"
              >
                近1月
              </div>
            </div>
          </div>
          <div class="echarts">
            <div id="dc_echarts2" style="width: 100%; height: 100%"></div>
          </div>
        </div>

        <Title title="今日风速监测"></Title>
        <div class="monitorContent">
          <div class="monitor" v-if="allWordSpeed.futureSpeed > 8">
            <div class="text youshebiaotihei">风力预警：</div>
            <div class="detail" style="white-space: nowrap">
              未来{{ allWordSpeed.futureTime }}小时，风力可能会达到{{
                allWordSpeed.futureSpeed
              }}m/s，请注意施工安全！
            </div>
          </div>
          <div class="monitor monitorNo" v-else>
            <div class="text youshebiaotihei">风力预警：</div>
            <div class="detail" style="white-space: nowrap">
              今日暂无风力预警
            </div>
          </div>
          <div class="wind">
            <div class="windContent">
              <div class="windNum">当前风速：</div>
              <div class="num">
                {{ rainInfo.windSpeed }}<span style="font-size: 10px">m/s</span>
                <!-- <sub>m/s</sub> -->
              </div>
            </div>
            <div class="windContent">
              <div class="windNum">当前风级：</div>
              <div class="num">
                {{ rainInfo.windScale }}<span style="font-size: 10px">级</span>
              </div>
            </div>
          </div>
          <div class="echarts">
            <div id="dc_echarts3" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="myButton active"
      style="
        position: absolute;
        left: calc(50% - 25%);
        transform: translateX(-50%);
        bottom: 38%;
        width: auto;
        height: auto;
        padding: 2px 8px;
        margin-left: 8px;
        color: #fff;
        pointer-events: auto;
      "
      @click="PlainShow = true"
    >
      吊车施工计划值
    </div>

    <div class="floor-content">
      <div class="floorFlex" style="pointer-events: auto">
        <div class="floorItem">
          <Title title="吊车倾斜曲线" style="margin-bottom: 0" />
          <div class="echartsContents">
            <div class="ehcharts-title">
              <Select v-model="craneId1" @on-change="changeCrane3($event)">
                <Option
                  v-for="(item, index) in dataInfo"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
            <div class="echarts">
              <div id="dc_echarts4" style="width: 100%; height: 100%"></div>
            </div>
            <!-- <Empty title="暂无数据" v-else></Empty> -->
          </div>
        </div>
        <div class="floorItem">
          <Title title="今日告警" style="margin-bottom: 0" />
          <div class="warnNum">
            <div class="left">
              <div class="item">
                <div class="icon">告警数量</div>
                <div class="value youshebiaotihei">{{ DCInfo.todayWarn }}</div>
              </div>
              <div class="item">
                <div class="icon">倾倒告警数量</div>
                <div class="value youshebiaotihei">{{ DCInfo.fqd }}</div>
              </div>
            </div>
            <div class="right">
              <div class="timeLine">
                <Timeline v-if="warnInfoList.length > 0">
                  <TimelineItem
                    v-for="(item, index) in warnInfoList"
                    :key="index"
                  >
                    <p class="time">{{ item.createTime }}</p>
                    <div class="timeConetent">
                      <div class="title">
                        <span
                          ><img
                            src="../../assets/images/ProjectAnti/warnIcon.png"
                          />{{ item.testingItem }} --
                          {{ item.alarmTypeText }}</span
                        >
                      </div>
                      <div class="content">
                        <div>{{ item.content }}</div>
                      </div>
                    </div>
                  </TimelineItem>
                </Timeline>
                <Empty v-else title="暂无告警记录"></Empty>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ModalW610H380 :visibleW610h380.sync="isshow" title="司机证书">
      <el-carousel indicator-position="none" arrow="hover">
        <el-carousel-item v-for="(item, index) in currentCertList" :key="index">
          <div class="cardContent">
            <div class="card">
              <Empty
                title="暂无图片"
                v-if="!item.specialAuthBookContent"
              ></Empty>
              <img :src="item.specialAuthBookContent" v-else />
            </div>
            <div class="content">
              <div class="beforeTime" v-if="false">
                <div>证书已过期</div>
              </div>
              <!-- <div class="contentItem">
                                <span>证书名称：</span>
                                <span>这里是一个证书的名称</span>
                                <div class="line"></div>
                            </div>
                            <div class="contentItem">
                                <span>证书编号：</span>
                                <span>这里是一个证书的编号</span>
                                <div class="line"></div>
                            </div> -->
              <div class="contentItem">
                <span>证书类型：</span>
                <span>{{ initDict(item.specialAuthBookType) }}</span>
                <div class="line"></div>
              </div>
              <!-- <div class="contentItem">
                                <span>证书等级：</span>
                                <span>这里是一个证书的等级</span>
                                <div class="line"></div>
                            </div> -->
              <div class="contentItem">
                <span>证书有效起始日期：</span>
                <span>{{ item.specialAuthBookDate.split("~")[0] }}</span>
                <div class="line"></div>
              </div>
              <div class="contentItem">
                <span>证书有效截止：</span>
                <span>{{ item.specialAuthBookDate.split("~")[1] }}</span>
                <div class="line"></div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </ModalW610H380>
    <ModalW610H380 :visibleW610h380.sync="picShow" title="机械五定图">
      <div class="picContent">
        <span>
          {{ picData.name }}
        </span>
        <span> 时间：{{ picData.fiveChartTime }} </span>
        <span> 作业内容： {{ picData.fiveChartContent }} </span>
        <div class="pic">
          <el-image
            :src="item"
            v-for="(item, index) in picData.fiveChartImg"
            :key="index"
            :preview-src-list="picData.fiveChartImg"
          />
        </div>
      </div>
    </ModalW610H380>
    <ModalW403H304 :visibleW403h304.sync="PlainShow" title="吊车施工计划值">
      <div class="information2">
        <div
          :class="'item'"
          v-for="(item, index) in planValueList"
          :key="index"
          :style="{ width: index == planValueList.length - 1 ? 'auto' : '30%' }"
        >
          <div class="top">
            {{ item.text }}
          </div>
          <div class="bottom">
            {{ item.value
            }}<sub>
              <span
                v-if="
                  item.type == 12 ||
                  item.type == 16 ||
                  item.type == 17 ||
                  item.type == 19
                "
                >m</span
              >
              <span
                v-if="
                  item.type == 9 ||
                  item.type == 20 ||
                  item.type == 13 ||
                  item.type == 14 ||
                  item.type == 15
                "
                >°</span
              >
              <span v-if="item.type == 10">t*m</span>
              <span v-if="item.type == 11 || item.type == 18">t</span>
              <span v-if="item.type == 8">m/s</span>
            </sub>
          </div>
        </div>
      </div>
    </ModalW403H304>
  </div>
</template>

<script>
function extractTimeFromISO(isoString) {
  const date = new Date(isoString);
  return `${date.getHours().toString().padStart(2, "0")}:${date
    .getMinutes()
    .toString()
    .padStart(2, "0")}`;
}
import Title from "@/components/commonView/title3.vue";
import ModalW610H380 from "@/components/modal/w610h380.vue";
import ModalW403H304 from "@/components/modal/w403h304Active.vue";
import * as echarts from "echarts";
import {
  getListPage,
  getDict,
  getCategorytoTotall,
  countByDay,
  systemAlarmPage,
  craneBusinessRecordlistPage,
  getWeather,
  giveAlarmTypeConfig,
  getplanValue,
  getAlarmStatistics,
  weathreInfo,
} from "@/api/xxindex";
import { getLastWeekDateRange, getLast30DaysRange } from "./index";
import { getProjectById } from "@/api/project";

const empty = require("../../assets/images/ProjectCrane/people.png");
export default {
  name: "ProjectCrane",
  components: {
    Title,
    ModalW610H380,
    ModalW403H304,
  },
  data() {
    return {
      myEcharts1: null,
      myEcharts2: null,
      myEcharts3: null,
      myEcharts4: null,
      allWordSpeed: {},
      echarts4Data: [
        {
          data: [7, 7, 5, 5, 4, 4, 3],
          name: "水平度x",
          color: "#7DD7FB",
        },
        {
          data: [10, 10, 8, 9, 7, 7, 6],
          name: "水平度y",
          color: "#2461F6",
        },
        {
          data: [17, 17, 14, 15, 13, 11, 11],
          name: "倾斜度",
          color: "#F2A250",
        },
      ],
      isshow: false,
      dataInfo: {},
      CarouselValue: 0,
      CarouselValue2: 1,
      empty: empty,
      autoplay: true,
      currentCertList: [],
      Dict: [],
      picShow: false,
      picData: {},
      warnInfo: {},
      craneId1: "",
      craneId2: "",
      changeValue1: "",
      changeValue2: "",
      weekandmonth1: "week",
      weekandmonth2: "week",
      initCharts1Data: [],
      warnInfoList: [],
      xyzline: [],
      rainInfoList: [],
      rainInfo: {},
      warnLineInfo: 0,
      DCInfo: {},
      PlainShow: false,
      geoInfo: {},
      weatherWarn: {},
      planValueList: [
        {
          text: "作业半径",
          threshValue: 16,
        },
        {
          text: "倾覆半径",
          threshValue: 12,
        },
        {
          text: "距离安全界限",
          threshValue: 17,
        },
        {
          text: "作业展臂最不利工况可起重吊重",
          threshValue: 18,
        },
      ],
    };
  },
  mounted() {
    this.getPointInfo();
    this.getPlanValueList();

        console.log('🚀 ProjectCrane mounted - 发送 switchConstructionView 事件，值: model');
        // this.$bus.emit('switchConstructionView', 'model'); // 调用吊车模型视图切换
    },
    destroyed() { 
        this.myEcharts1 && this.myEcharts1.dispose();
        this.myEcharts2 && this.myEcharts2.dispose();
        this.myEcharts3 && this.myEcharts3.dispose();
        this.myEcharts4 && this.myEcharts4.dispose();
    },
    methods: {
        // 风速单位转换：km/h 转 m/s
        convertWindSpeed(kmhValue) {
            if (kmhValue === null || kmhValue === undefined || kmhValue === '') {
                return 0;
            }
            // km/h 转 m/s：除以 3.6
            return Math.round((parseFloat(kmhValue) / 3.6) * 10) / 10; // 保留1位小数
        },
        // 计算总吊次数据
        async calculateTotalHoistingTimes() {
            // 获取近一个月的数据范围
            const monthRange = getLast30DaysRange();
            
            // 为每个吊车计算总吊次和总吊重
            const promises = this.dataInfo.map(async (crane, index) => {
                try {
                    const data = {
                        startTime: monthRange.startDate,
                        endTime: monthRange.endDate,
                        mechanicalId: crane.id
                    };
                    
                    const res = await countByDay(data);
                    if (res.code === 'success' && res.data) {
                        // 计算总吊次
                        // const totalHoistingTimes = res.data.reduce((sum, item) => {
                        //     return sum + (item.totalHoistingTimes || 0);
                        // }, 0);
                        
                        // 计算总吊重
                        // const totalWeight = res.data.reduce((sum, item) => {
                        //     return sum + (item.totalWeight || 0);
                        // }, 0);
                        
                        // 更新吊车数据
                        this.$set(this.dataInfo, index, {
                            ...crane,
                            totalHoistingTimes: crane.totalHoistingTimes,
                            totalWeight: crane.totalWeight
                        });
                    }
                } catch (error) {
                    console.error(`计算吊车${crane.name}总吊次失败:`, error);
                }
            });
            
            await Promise.all(promises);
        },
        getPlanValueList() {
            getplanValue({
                category: 1
            }).then(res => {
                if (res.code == 'success') {
                    this.planValueList = this.planValueList.map(i => {
                        let option = {};
                        option.text = i.text;
                        let item = res.data.find(item => item.type == String(i.threshValue));
                        if (item) {
                            option.value = item.threshValue;
                            option.type = item.type;
                        } else {
                            option.value = 0;
                            option.type = String(i.threshValue);
                        }
                        return option;
                    })
                }
            })
        },
        getPointInfo() {
            let pointInfo = this.$route.query.projectId;
            getProjectById(pointInfo).then(res => {
                if (res.code == 'success') {
                    this.geoInfo = res.data;
                    this.initRequest();
                }
            })
        },
        initRequest() {
            let data = {
                customQueryParams: {
                    alarmState: null,
                    codeOrName: '',
                    onlineState: null,
                    workPointId: null
                },
                page: {
                    current: 1,
                    size: -1
                }
            }
            getListPage(data).then(res => {
                if (res.code == 'success') {
                    this.dataInfo = res.data.records;
                    this.craneId1 = this.dataInfo[0].id;
                    this.craneId2 = this.dataInfo[0].id;
                    
                    // 为每个吊车计算总吊次数据
                    this.calculateTotalHoistingTimes();
                    
                    this.changeCrane1(this.craneId1, 'week', 'craneId1');
                    this.changeCrane2(this.craneId2, 'week', 'craneId2');
                    this.changeCrane3(this.craneId1);
                }
            })
            getCategorytoTotall(1).then(res => {
                if (res.code == 'success') {
                    this.warnInfo = res.data;
                }
            })
            let datas = {
                customQueryParams: {
                    // alarmType: 20,
                    category: 1,
                    // 获取当天的0:00 格式要求yyyy-mm-dd hh:mm:ss
                    startDate: this.getTodayStart() + ' 00:00:00',
                    endDate: this.getTodayStart() + ' 23:59:59'
                },
                page: {
                    current: 1,
                    size: 30
                },
                sorts: []
            }
            systemAlarmPage(datas).then(res => {
                if (res.code == 'success') {
                    this.warnInfoList = res.data.records;
                }
            })
            let DCwarn = {
                customQueryParams: {
                    category: "1",//告警大类 告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护
                    uniKey: "", //关键词
                    alarmTypes: [21,22,23], //告警类型，查询告警类型列表，输入id
                    status: "",// 0未处理，1已处理，2误报
                    startDate: "2025-06-28 00:00:00",
                    endDate: "2025-06-28 23:59:59"
                },
                page: {
                    current: 1,
                    size: 9999
                },
                sorts: []
            }
            // systemAlarmPage(DCwarn).then(res => {
            //     if (res.code == 'success') {
            //         this.DCInfo.fqd = res.data.total; //防倾倒
            //     }
            // })
            let todayWarn = {
                "page": {
                    "size": 9999,
                    "current": 1
                },
                "customQueryParams": {
                    "category": 1,//告警大类 告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护
                    "uniKey": "", //关键词
                    "alarmType": "", //告警类型，查询告警类型列表，输入id
                    "status": "",// 0未处理，1已处理，2误报
                    "startDate":"2025-01-01",//开始时间
                    "endDate":"2025-12-02"//结束时间

                }
            }
            // systemAlarmPage(todayWarn).then(res => {
            //     if (res.code == 'success') {
            //         this.DCInfo.todayWarn = res.data.total; //防倾倒
            //     }
            // })
            getAlarmStatistics().then(res => {
                if (res.code == 'success') {
                    this.DCInfo.todayWarn = res.data.craneTotalCount;
                    this.DCInfo.fqd = res.data.alarmInclinationCount;
                }
            })
            let paramsType = {
                category: 1
            }
            giveAlarmTypeConfig(paramsType).then(res => {
                if (res.code == 'success') {
                    this.warnLineInfo = res.data.filter(i => i.text == '倾倒告警')[0];
                }
            })
            let host = 'np2mtdt4fv.re.qweatherapi.com';
            // 版本 v7
            let v = '/v7'
            // 武汉地理位置
            let WHlocation = Number(this.geoInfo.longitude).toFixed(2) + ',' + Number(this.geoInfo.latitude).toFixed(2);
            // 获取并合并当日完整的天气数据
            this.getMergedWeatherData();
            // 获取天气预警
            // getWeather(`https://${host}${v}/warning/now?location=${WHlocation}`).then(res => {
            //     if (res.code == '200') {
            //         this.weatherWarn = res.now;
            //     }
            // })
            
        },
        getTodayStart() {
            const now = new Date();
            const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            
            // 格式化为 yyyy-mm-dd hh:mm:ss
            const year = todayStart.getFullYear();
            const month = String(todayStart.getMonth() + 1).padStart(2, '0');
            const day = String(todayStart.getDate()).padStart(2, '0');
            
            return `${year}-${month}-${day}`;
        },
        onweek1(val) {
            this.weekandmonth1 = 'week';
            this.initcountByDay(this.changeValue1, 'week', 'craneId1');
        },
        onweek2(val) {
            this.weekandmonth2 = 'week';
            this.initcountByDay(this.changeValue2, 'week', 'craneId2');
        },
        onmonth1(val) {
            this.weekandmonth1 = 'month';
            this.initcountByDay(this.changeValue1, 'month', 'craneId1');
        },
        onmonth2(val) {
            this.weekandmonth2 = 'month';
            this.initcountByDay(this.changeValue2, 'month', 'craneId2');
        },
        changeCrane1(val) {
            let changeName = 'craneId1';
            this.changeValue1 = val;
            this.initcountByDay(val, this.weekandmonth1, changeName);
        },
        changeCrane2(val) {
            let changeName = 'craneId2';
            this.changeValue2 = val;
            this.initcountByDay(val, this.weekandmonth2, changeName);
        },
        changeCrane3(val) {
            this.RecordlistPage(val);
        },
        RecordlistPage(val) {
            let data = {
                customQueryParams: {
                    // 获取当日0:00
                    startTime: new Date(new Date().setHours(0, 0, 0, 0)).toLocaleString().replace(/\//g, '-'),
                    // 获取当日23:59:59
                    endTime: new Date(new Date().setHours(23, 59, 59, 999)).toLocaleString().replace(/\//g, '-'),
                    keyword: "",
                    mechanicalId: val,
                    interval: 10,
                },
                page: {
                    current: 1,
                    size: -1,
                }
            }
            craneBusinessRecordlistPage(data).then(res => {
                if (res.code == 'success') {
                    let arr = [];
                    arr.push(res.data.records.map(i => i.levelX || 0));
                    arr.push(res.data.records.map(i => i.levelY || 0));
                    arr.push(res.data.records.map(i => i.inclination || 0));
                    let time = res.data.records.map(i => i.modifyTime);
                    this.echarts4Data = this.echarts4Data.map((i, index) => {
                        let item = arr[index].map((v, indexs)=> {
                            return [
                                time[indexs],
                                v
                            ]
                        })
                        return {
                            ...i,
                            data: item,
                        }
                    })

          this.initCharts4(time[0]);
        }
      });
    },
    // 合并天气数据：获取当日0:00到23:00的完整天气数据
    async getMergedWeatherData() {
      try {
        // 获取API配置
        const host = "np2mtdt4fv.re.qweatherapi.com";
        // 版本 v7
        const v = "/v7";
        const WHlocation =
          Number(this.geoInfo.longitude).toFixed(2) +
          "," +
          Number(this.geoInfo.latitude).toFixed(2);

        // 获取当前时间
        const now = new Date();
        const currentHour = now.getHours();

        // 准备历史数据请求参数
        const historyParams = {
          startTime: new Date(new Date().setHours(0, 0, 0, 0))
            .toLocaleString()
            .replace(/\//g, "-"),
          endTime: new Date(new Date().setHours(currentHour, 59, 59, 999))
            .toLocaleString()
            .replace(/\//g, "-"),
        };

        // 并发请求历史数据、未来数据和当前天气
        const [historyResponse, futureResponse, currentResponse] =
          await Promise.all([
            // 获取历史天气数据(0:00到当前时间)
            weathreInfo(historyParams),
            // 获取未来24小时天气数据
            getWeather(
              `https://${host}${v}/weather/24h/?location=${WHlocation}`
            ),
            // 获取当前天气
            getWeather(
              `https://${host}${v}/weather/now?location=${WHlocation}`
            ),
          ]);

        // 创建24小时的时间映射表
        const hourlyWeatherMap = new Map();

        // 处理历史数据
        if (historyResponse.code === "success" && historyResponse.data) {
          historyResponse.data.forEach((item) => {
            const itemTime = new Date(item.obsTime);
            const itemHour = itemTime.getHours();
            const isToday = itemTime.toDateString() === now.toDateString();

            // 只保留今天的数据，并且避免重复
            if (isToday && !hourlyWeatherMap.has(itemHour)) {
              hourlyWeatherMap.set(itemHour, {
                ...item,
                obsTime: item.obsTime,
                source: "history",
                windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
              });
            }
          });
        }

        // 处理未来数据
        if (futureResponse.code === "200" && futureResponse.hourly) {
          futureResponse.hourly.forEach((item) => {
            const itemTime = new Date(item.fxTime);
            const itemHour = itemTime.getHours();
            const isToday = itemTime.toDateString() === now.toDateString();

            // 只保留今天的数据，并且避免重复（优先使用历史数据）
            if (isToday && !hourlyWeatherMap.has(itemHour)) {
              hourlyWeatherMap.set(itemHour, {
                ...item,
                obsTime: item.fxTime, // 统一使用obsTime字段
                source: "future",
                windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
              });
            }
          });
        }

        // 生成完整的24小时数据，按小时顺序
        const normalizedData = [];
        for (let hour = 0; hour < 24; hour++) {
          if (hourlyWeatherMap.has(hour)) {
            const item = hourlyWeatherMap.get(hour);
            normalizedData.push({
              id: item.id || `generated-${hour}`,
              tenantId: item.tenantId || "",
              obsTime: item.obsTime,
              temp: item.temp,
              feelsLike: item.feelsLike,
              icon: item.icon,
              text: item.text,
              wind360: item.wind360,
              windDir: item.windDir,
              windScale: item.windScale,
              windSpeed: item.windSpeed,
              humidity: item.humidity,
              precip: item.precip,
              pressure: item.pressure,
              vis: item.vis,
              cloud: item.cloud,
              dew: item.dew,
              createTime:
                item.createTime ||
                new Date().toISOString().replace("T", " ").substr(0, 19),
              source: item.source,
            });
          } else {
            // 如果某个小时没有数据，使用默认值或插值
            normalizedData.push({
              id: `default-${hour}`,
              tenantId: "",
              obsTime: new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate(),
                hour,
                0,
                0
              ).toISOString(),
              temp: "0",
              feelsLike: "0",
              icon: "100",
              text: "无数据",
              wind360: "0",
              windDir: "无风",
              windScale: "0",
              windSpeed: 0, // 默认值直接设为0，已经是m/s单位
              humidity: "0",
              precip: "0.0",
              pressure: "1000",
              vis: "0",
              cloud: "0",
              dew: "0",
              createTime: new Date()
                .toISOString()
                .replace("T", " ")
                .substr(0, 19),
              source: "default",
            });
          }
        }

        // 更新组件数据
        this.rainInfoList = normalizedData;

        // 设置当前天气信息
        if (currentResponse.code === "200") {
          this.rainInfo = {
            ...currentResponse.now,
            windSpeed: this.convertWindSpeed(currentResponse.now.windSpeed), // 转换风速单位
          };
        }

        // 初始化图表
        this.initCharts3();

        console.log("合并后的天气数据:", normalizedData);
        console.log("数据时间范围:", {
          start: normalizedData[0]?.obsTime,
          end: normalizedData[normalizedData.length - 1]?.obsTime,
          total: normalizedData.length,
        });
      } catch (error) {
        console.error("获取天气数据失败:", error);

        // 如果合并失败，使用原有逻辑作为备用方案
        this.getFallbackWeatherData();
      }
    },
    // 备用天气数据获取方法
    getFallbackWeatherData() {
      const host = "np2mtdt4fv.re.qweatherapi.com";
      const v = "/v7";
      const WHlocation =
        Number(this.geoInfo.longitude).toFixed(2) +
        "," +
        Number(this.geoInfo.latitude).toFixed(2);

      // 获取24小时天气数据
      getWeather(`https://${host}${v}/weather/24h/?location=${WHlocation}`)
        .then((res) => {
          if (res.code == "200") {
            this.rainInfoList = res.hourly.map((item) => ({
              ...item,
              windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
            }));
            this.initCharts3();
          }
        })
        .catch((err) => {
          console.error("获取未来天气数据失败:", err);
        });

      // 获取当前天气
      getWeather(`https://${host}${v}/weather/now?location=${WHlocation}`)
        .then((res) => {
          if (res.code == "200") {
            this.rainInfo = {
              ...res.now,
              windSpeed: this.convertWindSpeed(res.now.windSpeed), // 转换风速单位
            };
          }
        })
        .catch((err) => {
          console.error("获取当前天气数据失败:", err);
        });
    },
    initcountByDay(val, time, type) {
      let weeksandmonth = null;
      if (time == "month") {
        weeksandmonth = getLast30DaysRange();
      }
      if (time == "week") {
        weeksandmonth = getLastWeekDateRange();
      }
      let data = {
        startTime: weeksandmonth.startDate,
        endTime: weeksandmonth.endDate,
        mechanicalId: val,
      };
      countByDay(data).then((res) => {
        if (res.code == "success") {
          let datacarneId1 = res.data.map((i) => {
            return {
              day: i.day,
              totalHoistingTimes: i.totalHoistingTimes,
            };
          });
          let datacarneId2 = res.data.map((i) => {
            return {
              day: i.day,
              totalWeight: i.totalWeight,
            };
          });
          if (type == "craneId1") {
            this.initCharts1(datacarneId1);
          }
          if (type == "craneId2") {
            this.initCharts2(datacarneId2);
          }
        }
      });
    },
    initCharts1(data) {
      this.myEcharts1 && this.myEcharts1.dispose();
      this.myEcharts1 = echarts.init(document.getElementById("dc_echarts1"));
      let option = {
        grid: {
          top: "17%",
          left: "2%",
          right: "12%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: data.map((i) => i.day),
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
          },
        },
        yAxis: {
          name: "单位：次",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          {
            itemStyle: {
              opacity: 0,
            },
            smooth: true,
            emphasis: {
              itemStyle: {
                opacity: 1,
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0.2,
                      color: "rgba(255, 255, 255)", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "rgba(36, 76, 173)", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(36, 76, 173, 0.1)", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#95B6F9",
                borderWidth: 1,
              },
              scale: 3,
            },
            data: data.map((i) => i.totalHoistingTimes),
            type: "line",
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FF9800", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(85, 71, 35, .2)", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            lineStyle: {
              color: "#FF9800",
            },
          },
        ],
      };
      this.myEcharts1.setOption(option);
    },
    initCharts2(data) {
      this.myEcharts2 && this.myEcharts2.dispose();
      this.myEcharts2 = echarts.init(document.getElementById("dc_echarts2"));
      let option = {
        grid: {
          left: "2%",
          right: "9%",
          bottom: 0,
          top: "21%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
          },
          data: data.map((i) => i.day),
        },
        yAxis: {
          name: "单位：T",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          {
            data: data.map((i) => i.totalWeight),
            type: "bar",
            barWidth: "40%",
            label: {
              show: true,
              position: "top",
              color: "#fff",
            },
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#336CEF", // 0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100%处的颜色
                  },
                ],
              },
              borderColor: "#4872DD",
            },
          },
        ],
      };
      this.myEcharts2.setOption(option);
    },
    initCharts3() {
      this.myEcharts3 && this.myEcharts3.dispose();
      this.myEcharts3 = echarts.init(document.getElementById("dc_echarts3"));

      // 获取当前时间
      const currentHour = new Date().getHours();

      // 准备数据
      const allData = this.rainInfoList.map((i, index) => ({
        value: i.windSpeed,
        temp: i.temp,
        time: index + 1,
      }));

      // 分割数据：历史数据和未来数据
      const historicalData = [];
      const futureData = [];

      // 时间标签
      const timeLabels = [
        "0:00",
        "1:00",
        "2:00",
        "3:00",
        "4:00",
        "5:00",
        "6:00",
        "7:00",
        "8:00",
        "9:00",
        "10:00",
        "11:00",
        "12:00",
        "13:00",
        "14:00",
        "15:00",
        "16:00",
        "17:00",
        "18:00",
        "19:00",
        "20:00",
        "21:00",
        "22:00",
        "23:00",
      ];

      // 构建数据，确保连续性
      for (let i = 0; i < 24; i++) {
        if (i <= currentHour) {
          // 历史数据（包含当前时间）
          historicalData.push(allData[i] || null);
          futureData.push(i === currentHour ? allData[i] : null); // 在当前时间点重复数据以确保连续
        } else {
          // 未来数据
          historicalData.push(null);
          futureData.push(allData[i] || null);
        }
      }

      let arr = futureData.filter((i) => i);

      for (let i = 0; i < arr.length; i++) {
        console.log(arr[i], "arr[i]");
        if (arr[i].value > 8) {
          // 修改为8m/s阈值，与CustomPopup组件保持一致
          this.allWordSpeed.futureTime = i + 1;
          this.allWordSpeed.futureSpeed = arr[i].value;
          break;
        }
      }

      let option = {
        grid: {
          top: "19%",
          left: "3%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis", // 触发方式为坐标轴触发
          formatter: function (params) {
            // 找到非空的数据点
            const dataPoint = params.find((p) => p.data && p.data.temp);
            if (dataPoint) {
              return `
                            <div style="width: 140px; height: 62px; background-color: rgba(255, 255, 255, 0.1); backdrop-filter: blur(3px);border-radius: 10px;display: flex;
                            flex-direction: column;justify-content: space-between;padding: 10px;color: #fff;box-sizing: border-box;">
                                <div>${dataPoint.name}</div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>风速</span>
                                    <span>${dataPoint.value}m/s</span>
                                </div>
                            </div>
                            `;
            }
            return "";
          },
          backgroundColor: "transparent", // 提示框背景颜色
          position: "left",
          borderColor: "transparent",
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: timeLabels,
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
          },
        },
        yAxis: {
          name: "单位：m/s",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          // 历史数据系列（实线）
          {
            name: "历史数据",
            type: "line",
            smooth: true,
            data: historicalData,
            itemStyle: {
              opacity: 0,
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0.2,
                      color: "rgba(255, 255, 255)", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "rgba(36, 76, 173)", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(36, 76, 173, 0.1)", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#95B6F9",
                borderWidth: 1,
              },
              scale: 3,
            },
            lineStyle: {
              color: "#FF9800",
              width: 2,
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FF9800", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(85, 71, 35, .2)", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            connectNulls: false, // 不连接空值
          },
          // 未来数据系列（虚线）
          {
            name: "预测数据",
            type: "line",
            smooth: true,
            data: futureData,
            itemStyle: {
              opacity: 0,
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0.2,
                      color: "rgba(255, 255, 255)", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "rgba(36, 76, 173)", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(36, 76, 173, 0.1)", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: "#95B6F9",
                borderWidth: 1,
              },
              scale: 3,
            },
            lineStyle: {
              color: "#FF9800",
              width: 2,
              type: "dashed", // 虚线
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255, 152, 0, 0.3)", // 未来数据的填充色更透明
                  },
                  {
                    offset: 1,
                    color: "rgba(85, 71, 35, .1)", // 更透明的底色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            connectNulls: false, // 不连接空值
            markLine: {
              data: [
                {
                  yAxis: 8, // 固定值预警线
                  lineStyle: {
                    width: 2,
                    type: "dashed", // 虚线
                    color: "#ff0000", // 红色
                  },
                  label: {
                    position: "middle",
                    formatter: "风速预警线 8m/s",
                  },
                },
              ],
            },
          },
        ],
      };
      this.myEcharts3.setOption(option);
    },
    initCharts4(startTime) {
      this.myEcharts4 && this.myEcharts4.dispose();
      this.myEcharts4 = echarts.init(document.getElementById("dc_echarts4"));

      // 获取数据的时间范围
      let dataStartTime = startTime;
      let dataEndTime = startTime;

      // 从数据中找到实际的时间范围
      if (this.echarts4Data && this.echarts4Data.length > 0) {
        const allTimePoints = [];
        this.echarts4Data.forEach((series) => {
          if (series.data && series.data.length > 0) {
            series.data.forEach((point) => {
              if (point && point[0]) {
                allTimePoints.push(new Date(point[0]).getTime());
              }
            });
          }
        });

        if (allTimePoints.length > 0) {
          dataStartTime = new Date(Math.min(...allTimePoints));
          dataEndTime = new Date(Math.max(...allTimePoints));
        }
      }

      let option = {
        legend: {
          data: ["水平度x", "水平度y", "倾斜度"],
          right: 0,
          textStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
        },
        grid: {
          top: "17%",
          left: "2%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        xAxis: {
          type: "time",
          boundaryGap: false,
          min: dataStartTime,
          max: dataEndTime,
          axisLabel: {
            formatter: function (value) {
              // 格式化为 HH:mm，使用标准的日期格式化
              const date = new Date(value);
              const hours = date.getHours().toString().padStart(2, "0");
              const minutes = date.getMinutes().toString().padStart(2, "0");
              return `${hours}:${minutes}`;
            },
            // 移除interval设置，让ECharts自动计算合适的间隔
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
            // 设置标签旋转角度避免重叠
            rotate: 0,
            // 控制标签显示的最大数量
            showMaxLabel: true,
            showMinLabel: true,
          },
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          // 添加刻度线配置
          axisTick: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          // 设置分割数量，避免标签过密
          splitNumber: 6,
        },
        yAxis: {
          name: "单位：度",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: this.initServer(),
      };
      this.myEcharts4.setOption(option);
    },
    initServer() {
      return this.echarts4Data.map((i, index) => {
        let serversData = {
          name: i.name,
          itemStyle: {
            opacity: 0,
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              color: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0.2,
                    color: "rgba(255, 255, 255)", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: i.color, // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              borderColor: i.color,
              borderWidth: 1,
            },
            scale: 3,
          },
          data: i.data,
          type: "line",
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: i.color, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "transparent", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },

          lineStyle: {
            color: i.color,
          },
        };
        if (index == 0) {
          let data = {
            data: [
              {
                yAxis: this.warnLineInfo.threshValue, // 固定值预警线
                name: "预警线",
              },
            ],
            lineStyle: {
              color: "#ff0000",
              width: 2,
              type: "dashed",
            },
            label: {
              formatter: this.warnLineInfo.text + ": {c}",
              position: "middle",
            },
          };
          serversData.markLine = data;
        }
        return serversData;
      });
    },
    search(item) {
      // 查看
      try {
        if (
          !item.driverList ||
          !item.driverList[0] ||
          !item.driverList[0].specialAuthBookJson
        ) {
          this.$Message.warning("暂无证书信息");
          return;
        }
        const specialAuthBookJson = item.driverList[0].specialAuthBookJson;
        let certList = [];

        if (typeof specialAuthBookJson === "string") {
          certList = JSON.parse(specialAuthBookJson);
        } else {
          certList = specialAuthBookJson;
        }

        // 确保是数组格式
        if (!Array.isArray(certList)) {
          certList = [certList];
        }
        getDict("special_book_type").then((res) => {
          if (res.code == "success") {
            this.Dict = res.data;
            this.currentCertList = certList;
            this.isshow = true;
            console.log(this.currentCertList);
          }
        });
      } catch (error) {
        console.error("解析证书数据失败:", error);
        this.$Message.error("证书数据格式错误");
      }
    },
    initDict(item) {
      let data = this.Dict.filter((i) => i.itemValue == item);
      return data[0].itemText;
    },
    showPic(item) {
      console.log(item);
      this.picShow = true;
      this.picData = {
        fiveChartContent: item.fiveChartContent,
        fiveChartImg: item.fiveChartImg,
        fiveChartTime: item.fiveChartTime,
        workPointName: item.workPointName,
        name: item.name,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.picContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;

  .pic {
    height: calc(100% - 90px);
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    overflow-y: auto;
  }
}

/deep/ .side-panel {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  );
  height: 100%;
  gap: 0px;
}

.side-panel-right {
  background: linear-gradient(
    to left,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
  gap: 0px;
}

/deep/ .ivu-timeline-item-head {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/ProjectAnti/point.png) no-repeat center
    center;
  background-size: 100% auto;
  border: none;
  position: absolute;
  left: 0px;
  top: -2px;
}

/deep/ .ivu-timeline-item-tail {
  position: absolute;
  left: 10px;
  top: 5px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

/deep/ .ivu-timeline-item {
  padding: 0;
}

.cardContent {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  gap: 10px;

  .card {
    width: 35%;
    height: 100%;
    background: url(../../assets/images/ProjectCrane/border.png) no-repeat
      center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 99%;
      height: 100%;
      object-fit: contain;
    }
  }

  .content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .beforeTime {
      width: 100%;
      height: 40px;
      color: #fff;
      background: url(../../assets/images/ProjectCrane/titleBack.png) no-repeat
        center center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .beforeTime::before {
      content: "";
      display: inline-block;
      width: 25px;
      height: 25px;
      background: url(../../assets/images/ProjectCrane/errorIcon.png) no-repeat
        center center;
      background-size: contain;
      background-position-y: 2px;
      margin-right: 5px;
      margin-left: 2px;
    }

    .contentItem {
      width: 100%;
      height: 12.5%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      margin-bottom: 10px;
      padding-bottom: 10px;

      span:first-child {
        color: rgba(255, 255, 255, 0.5);
      }

      span:first-child::before {
        content: "";
        display: inline-block;
        vertical-align: middle;
        background-color: #fff;
        width: 5px;
        height: 5px;
        transform: rotate(45deg);
        margin-right: 7px;
      }

      span:last-child {
        color: #fff;
        font-weight: bold;
      }

      .line {
        width: 100%;
        height: 2px;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(255, 255, 255, 0.3) 50%,
          transparent 100%
        );
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .contentItem:last-child {
      .line {
        background: transparent !important;
      }
    }
  }
}

.timeConetent {
  width: 100%;
  height: 75px;
  display: flex;
  flex-direction: column;
  background: url(../../assets/images/ProjectAnti/warn.png) no-repeat center
    center;
  background-size: 100% 100%;
  color: #fff;

  .title {
    height: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 5px;
    height: 35px;
    align-items: center;

    span {
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }

    // span:last-child {
    //     width: 68px;
    //     height: 80%;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     color: #fff;
    //     background: url(../../assets/images/ProjectAnti/Button.png) no-repeat center center;
    //     background-size: 100% 100%;
    //     cursor: pointer;
    // }
  }

  .content {
    height: calc(75px - 35px);
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    div {
      width: 100%;
      text-align: left;
    }

    div::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      margin: 0 8px;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.floorFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom, transparent, #1d2d47);

  .floorItem {
    width: 48%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .echartsContents {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0 10px;

      .echarts {
        width: 100%;
        height: calc(100% - 30px - 40px);
      }
    }

    .warnNum {
      width: 100%;
      height: 88%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5%;

      .left {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .item {
          width: 100%;
          height: 48%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 4px;

          .icon {
            width: 100%;
            height: 30px;
            background-image: linear-gradient(
              to right,
              transparent 1%,
              #102f97 10%,
              transparent 100%
            );
            display: flex;
            align-items: center;
            color: #fff;
          }

          .icon::before {
            content: "";
            width: 20px;
            height: 20px;
            background: url(../../assets/images/ProjectCrane/iconLeft.png)
              no-repeat center center;
            background-size: contain;
            background-position-y: 3px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
          }

          .value {
            width: 80px;
            height: calc(100% - 30px - 10px);
            background: url(../../assets/images/ProjectCrane/warnNumBack.png)
              no-repeat center center;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 29px;
            color: #fff;
            padding-bottom: 25px;
            box-sizing: border-box;
          }
        }
      }

      .right {
        flex: 1;
        height: 100%;

        .timeLine {
          width: 100%;
          height: 100%;
          overflow-y: auto;

          .time {
            color: #fff;
          }
        }
      }
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }

  .right-content {
    width: 408px;
    position: absolute;
    right: 0;
    top: 0;
    height: calc(100%);
    flex: 1;
  }

  .floor-content {
    width: calc(100% - 408px - 408px - 40px);
    position: absolute;
    height: 35%;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../assets/images/icon/s_title.png) no-repeat center center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -5px;
}

.padcontent {
  width: 100%;
  padding-top: 0;
  padding: 5px;
  box-sizing: border-box;
  margin-left: 5px;
}

.myButton {
  width: 62px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center center;
  // background-size: 100% 100%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  border-radius: 3px;
}

.myButton.active {
  background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center
    center;
  background-size: 100% 100%;
}

.realData {
  width: 100%;
  height: calc(100vh - 22vh - 10vh - 100px);
  background: url(../../assets/images/ProjectCrane/h617Card.png) no-repeat
    center center;
  background-size: 100% 100%;
  padding: 0px 8px;
  box-sizing: border-box;

  .title {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: column;

    .online {
      width: 100%;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        background: url(../../assets/images/ProjectCrane/icon.png) no-repeat
          center center;
        background-size: contain;
      }

      span:last-child {
        cursor: pointer;
        color: #184099;
      }

      img {
        width: 42px;
        height: auto;
        margin-top: 5px;
      }
    }

    .time {
      span:before {
        background: url(../../assets/images/icon/time.png) no-repeat center
          center !important;
        background-size: contain !important;
      }
    }
  }

  .worktime {
    width: 100%;
    height: 18%;
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 10px;

    .worktime-item {
      width: 47%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon {
        width: 50px;
        height: 50px;
        background: url(../../assets/images/ProjectCrane/leftIcon.png) no-repeat
          center center;
        background-size: contain;
      }

      .times {
        height: 100%;
        width: calc(100% - 40px - 10%);
        display: flex;
        flex-direction: column;
        gap: 5px;

        .text {
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
        }

        .hours {
          width: 100%;
          height: calc(100% - 30px);
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(26, 44, 76, 0.9),
            transparent
          );
          text-shadow: 0 0 10px #20549b;
          color: #fff;
          font-size: 29px;
          font-weight: bold;

          sub {
            margin-left: 5px;
            margin-bottom: 2px;
            font-size: 16px;
          }
        }
      }
    }
  }

  .peopleDetail {
    width: 100%;
    height: 16vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 10px;
    gap: 10px;

    .profile {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .profileInfo {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .info {
        width: 100%;
        height: calc(100% / 4);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .titles {
          color: rgba(255, 255, 255, 0.5);
          white-space: nowrap;
        }

        .text {
          color: #fff;
          white-space: nowrap;
        }

        div.text {
          width: 62px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: url(../../assets/images/ProjectAI/activeBack.png)
            no-repeat center center;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }
    }
  }

  .information {
    width: 100%;
    height: calc(100% - 16vh - 18%);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: auto;

    .item {
      width: 30%;
      height: 70px;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .top {
        width: 100%;
        height: 45%;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(13, 41, 89, 0.563),
          transparent
        );
        display: flex;
        align-items: center;
        padding-left: 10px;
      }

      .bottom {
        width: 100%;
        height: 55%;
        background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }

    .item.active {
      .top {
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(179, 55, 55, 0.612),
          transparent
        ) !important;
      }

      .bottom {
        background: url(../../assets/images/ProjectAnti/red.png) no-repeat
          center center !important;
        background-size: 100% 100% !important;
      }
    }
  }
}

.information2 {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-y: auto;

  .item {
    width: 31%;
    height: 70px;
    display: flex;
    flex-direction: column;
    gap: 5px;

    .top {
      width: 100%;
      height: 45%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(13, 41, 89, 0.563),
        transparent
      );
      display: flex;
      align-items: center;
      padding-left: 10px;
    }

    .bottom {
      width: 100%;
      height: 55%;
      background: url(../../assets/images/ProjectAnti/blue.png) no-repeat center
        center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      box-sizing: border-box;
      padding-left: 15px;

      sub {
        margin-left: 5px;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .item.active {
    .top {
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(179, 55, 55, 0.612),
        transparent
      ) !important;
    }

    .bottom {
      background: url(../../assets/images/ProjectAnti/red.png) no-repeat center
        center !important;
      background-size: 100% 100% !important;
    }
  }
}

.statistics {
  width: 100%;
  height: 22vh;
  background: url(../../assets/images/ProjectCrane/h201Card.png) no-repeat
    center center;
  background-size: 100% 105%;
  background-position-y: 5%;

  .title {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 5px;
  }

  .title::before {
    content: "";
    background: url(../../assets/images/ProjectCrane/icon.png) no-repeat center
      center;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
  }

  .totalNum {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 8px;

    .num {
      width: 48%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 8px;

      div:first-child {
        color: transparent;
        // 渐变字体
        background-image: linear-gradient(to right, #adc6fa, #e2ebfd);
        background-image: -webkit-linear-gradient(to right, #adc6fa, #e2ebfd);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 18px;
      }

      div:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../../assets/images/icon/work.png) no-repeat center
          center;
        background-size: contain;
        vertical-align: middle;
        display: inline-block;
        text-align: center;
      }

      div:last-child {
        width: 70px;
        height: 65px;
        font-size: 25px;
        color: #fff;
        background: url(../../assets/images/icon/cardIcon.png) no-repeat center
          center;
        background-size: 80% auto;
        display: flex;
        justify-content: center;
        display: inline-block;
        vertical-align: top;
        text-align: center;

        span {
          font-size: 15px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  .time {
    width: 100%;
    height: calc(100% - 38px - 45%);
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;

    .item {
      width: 32%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .top {
        width: 100%;
        height: 35%;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(38, 82, 132, 0.4),
          transparent
        );
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hour {
        width: 100%;
        height: calc(100% - 55%);
        background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

.echartsContent {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-direction: column;
  margin-top: -10px;
  margin-bottom: 15px;

  .echarts {
    width: 100%;
    height: calc(100% - 20%);
  }
}

.ehcharts-title {
  height: 20%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/ .ivu-select {
    width: 40%;
  }

  /deep/.ivu-select-selection {
    background-image: linear-gradient(to bottom, #223d71, #1d3051);
    border: 2px solid #2e5bbf;
    height: 30px;
    color: #fff;
  }

  /deep/ .ivu-select-placeholder {
    color: #fff;
  }

  /deep/ .ivu-select-arrow {
    color: #fff;
  }

  /deep/ .ivu-select-item {
    background-image: linear-gradient(
      to bottom,
      rgba(34, 61, 113, 3),
      rgba(29, 48, 81, 0.9)
    );
    color: #fff;
  }

  /deep/ .ivu-select-dropdown {
    padding: 0;
  }

  .btn {
    display: flex;
    gap: 10px;
  }
}

.monitorContent {
  height: calc(100vh - 25vh - 35vh - 10vh);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .monitor {
    height: 70px;
    width: 100%;
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    flex-direction: column;
    background: url(../../assets/images/ProjectCrane/titleBack.png) no-repeat
      center center;
    background-size: 100% 100%;

    .text {
      color: #fff;
    }

    .text::before {
      content: "";
      width: 30px;
      height: 30px;
      display: inline-block;
      vertical-align: middle;
      background: url(../../assets/images/ProjectCrane/errorIcon.png) no-repeat
        center center;
      background-size: contain;
    }
  }

  .monitorNo {
    background: url(../../assets/images/ProjectCrane/titleBack2.png) no-repeat
      center center;
    background-size: 100% 100%;
    .text::before {
      background: url(../../assets/images/ProjectCrane/correctIcon.png)
        no-repeat center center;
      background-size: contain;
    }
  }

  .wind {
    width: 100%;
    height: 40px;
    background: url(../../assets/images/ProjectCrane/titleBlueBack.png)
      no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .windContent {
      display: flex;
      width: 45%;
      align-items: flex-end;
    }

    .windNum {
      // width: 110px;
      height: 100%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(12, 52, 98, 0.9),
        transparent
      );
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .windNum::before {
      content: "";
      width: 20px;
      height: 20px;
      background: url(../../assets/images/ProjectCrane/windIcon.png) no-repeat
        center center;
      background-size: contain;
    }

    .num {
      // width: 110px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      background: url(../../assets/images/ProjectAnti/blue.png) no-repeat center
        center;
      background-size: 100% 100%;
    }
  }

  .echarts {
    width: 100%;
    height: calc(100% - 60px);
  }
}
</style>

<template>
    <div class="visual-progress">
        <div class="visual-progress-top">
            <div class="visual-progress-left">
                <projectInfo />
                <projectProgress />
            </div>
            <div class="visual-progress-right">
                <bigMechanicalStat />
                <projectPersonOverview />
                <monitoringStat />
            </div>
        </div>
        <div class="visual-progress-bottom">
            <progressAnimation />
        </div>
        <componentDetailModal ref="componentDetailModalRef" />
    </div>
</template>
<script>
import projectInfo from './components/projectInfo.vue';
import projectProgress from './components/projectProgress.vue';
import bigMechanicalStat from './components/bigMechanicalStat.vue';
import projectPersonOverview from './components/projectPersonOverview.vue';
import monitoringStat from './components/monitoringStat.vue';
import progressAnimation from './components/progressAnimation.vue';
import componentDetailModal from './components/componentDetailModal.vue';
export default {
    name: 'visual-progress',
    components: {
        projectInfo,
        projectProgress,
        bigMechanicalStat,
        projectPersonOverview,
        monitoringStat,
        progressAnimation,
        componentDetailModal,
    },
    data() {
        return {
        }
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.visual-progress{
    height: ~"calc(100vh - 4.75rem)";
    display: flex;
    flex-direction: column;
    position: relative;
    pointer-events: none;
    .visual-progress-top{
        flex: 1;
        display: flex;
        justify-content: space-between;
        overflow: hidden;
    }
    .visual-progress-left,
    .visual-progress-right{
        width: 21.57vw;
        height: 100%;
        background: linear-gradient(270deg, rgba(25, 38, 62, 0.9) 0%, #101A29 100%);
        backdrop-filter: blur(4px);
        display: grid;
        padding: 1.48vh;
        row-gap: 1.48vh;
        pointer-events: auto;
    }
    .visual-progress-left{
        grid-template-rows: 57.45% auto;
    }
    .visual-progress-right{
        background: linear-gradient(90deg, rgba(25, 38, 62, 0.9) 0%, #101A29 100%);
        grid-template-rows: 30.9% 38.82% auto;
    }
    .visual-progress-bottom{
        height: 12.96vh;
        background: linear-gradient(360deg, #101A29 0%, rgba(25, 38, 62, 0.9) 92%);
        backdrop-filter: blur(4px);
        
    }
}
</style>
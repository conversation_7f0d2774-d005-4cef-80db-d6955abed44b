<template>
    <div class="monitoring-stat">
        <Title title="既有线监测统计" desc="MONITORING STAT" style="margin-bottom: 4vh;" />
        <div class="stat-main">
            <div class="stat-head">
                <div class="stat-head-title"><span>今日监测告警总数</span></div>
                <div class="arrow">
                    <img src="@/assets/images/VisualProgress/arrow_b.png" alt="">
                </div>
            </div>
            <div class="num-list">
                <div class="num-item has" v-for="i in zeroNum" :key="i"><span>0</span></div>
                <div class="num-item" v-for="(item, index) in totalNumArr" :key="index"><span>{{ item }}</span></div>
                <div class="num-item unit-item"><span>个</span></div>
            </div>
        </div>
        <div class="person-list">
            <div class="person-list-item" v-for="(item, index) in overviewList" :key="index">
                <div class="icon-box">
                    <img :src="require(`@/assets/images/VisualProgress/${item.icon}`)" alt="">
                </div>
                <div class="right">
                    <div class="name">{{ item.name }}</div>
                    <div class="num">{{ item.num }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Title from "@/components/commonView/title.vue";
import { getExistingLineMonitoringStat } from "@/api/visualProgress";
export default {
    name: 'monitoring-stat',
    components: {
        Title
    },
    data() {
        return {
            statData: {},
            overviewList: [
                { name: '监测报告', num: 0, key: 'monitoringReportCount', icon: 'stat_icon1.png' },
                { name: '监测设备', num: 0, key: 'monitoringDeviceCount', icon: 'stat_icon2.png' },
            ],
        }
    },
    computed: {
        totalNumArr() {
            if (this.statData.todayAlarmCount || this.statData.todayAlarmCount === 0) {
                let str = this.statData.todayAlarmCount + '';
                return str.split('');
            }
            return [];
        },
        // 前面补位0的个数
        zeroNum() {
            let num = 6 - this.totalNumArr.length;
            if (num > 0) {
                return num
            }
            return 0;
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            getExistingLineMonitoringStat().then(res => {
                this.statData = res.data;
                this.overviewList.forEach(item => {
                    item.num = this.statData[item.key];
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.stat-main{
    position: relative;
    height: 7.59vh;
    background: url('~@/assets/images/VisualProgress/box_bg1.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.48vh;
    .stat-head{
        position: absolute;
        top: -1.7vh;
        left: 50%;
        width: 100%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        .stat-head-title{
            font-family: YouSheBiaoTiHei;
            font-size: 1.2vh;
            line-height: 2.03vh;
            color: #fff;
            width: 12.59vh;
            text-align: center;
            background: linear-gradient(270deg, rgba(1, 34, 80, 0) 0%, #193965 50%, rgba(1, 34, 80, 0) 100%);
            backdrop-filter: blur(4px);
            span{
                background: linear-gradient(180deg, #FFFFFF 0%, #B9EFFF 167.05%);
                -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                -webkit-text-fill-color: transparent;/*给文字设置成透明*/
            }
        }
        .arrow{
            width: 1.2vh;
            img{
                width: 100%;
                display: block;
            }
        }
    }
    .num-list{
        display: flex;
        align-items: center;
        column-gap: 0.648vh;
        
        .num-item{
            font-family: 'DIN Pro';
            font-size: 1.85vh;
            line-height: 1.85vh;
            width: 2vh;
            height: 2.59vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(180deg, rgba(0, 156, 255, 0.8) 0%, rgba(0, 26, 73, 0.8) 100%);
            position: relative;
            border-radius: 0.27vh;
            z-index: 1;
            color: #fff;
            &:before{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                @mask-image: linear-gradient(#fff, #fff);
                padding: 0.5px;
                border-radius: 0.27vh;
                background: linear-gradient(180deg, #D6EAFF 0%, #66B4FF 100%);
                mask-image: @mask-image, @mask-image;
                mask-clip: content-box, padding-box;
                mask-composite: exclude;
                -webkit-mask-composite: destination-out;
            }
            span{
                position: relative;
                z-index: 2;
            }
            &.has{
                color: rgba(255, 255, 255, 0.6);
            }
        }
        .unit-item{
            background: linear-gradient(180deg, rgba(43, 119, 255, 0.2) 0%, rgba(0, 26, 73, 0.2) 100%);
            font-family: 'PingFang SC';
            font-size: 1.29vh;
        }
    }
}
.person-list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.48vh;
    padding: 0 1.48vh;
    .person-list-item{
        display: flex;
        align-items: center;
        width: 45%;
        .icon-box{
            width: 3.7vh;
            height: 3.7vh;
            margin-right: 1.48vh;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .right{
            flex: 1;
        }
        .name{
            font-size: 1.29vh;
            line-height: 1.85vh;
            color: rgba(255, 255, 255, 0.6);
        }
        .num{
            font-family: YouSheBiaoTiHei;
            font-size: 1.48vh;
            color: #fff;
        }
    }
}
</style>
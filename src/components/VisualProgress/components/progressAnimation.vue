<template>
  <div>
  <div class="progress-container">
    <div class="progress-swiper">
      <div class="box-bg-top">
        <div class="bg-line"></div>
      </div>
      <div class="box-bg-bot"></div>

      <div class="list" ref="listRef">
        <div class="progress-bar" :style="{ width: s.progressWidth }"></div>
        <div
          class="box"
          v-for="(item, index) in m.taskList"
          v-show="item.point == 1"
          :class="{ on: m.currentIndex >= index, has: index < m.currentIndex }"
          :key="index"
          ref="boxRef"
          :data-index="index"
        >
          <div class="key-node">
            <div @click="chooseAnimPlay(index)" class="rh"></div>
            <div class="info">
              <div class="time">
                <p>{{ formatDate(item.startTime, "yyyy-MM-dd") }}</p>
                <p>~</p>
                <p>{{ formatDate(item.endTime, "yyyy-MM-dd") }}</p>
              </div>
              <div class="name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="btn-arrow prev"></div>
      <div class="btn-arrow next"></div>
    </div>
    <div
      v-show="!m.isGrowthAnimationActivated && m.taskList.length > 0"
      @click="clickPlay"
      class="play"
    >
      <img src="@/assets/images/VisualProgress/icon_play.png" alt="" />
    </div>
    <div v-show="m.isGrowthAnimationActivated" @click="clickPause" class="play">
      <img src="@/assets/images/VisualProgress/icon_play1.png" alt="" />
    </div>
    <div v-show="m.isShowStopFlag" @click="stopAnim" class="play stop">
      <img src="@/assets/images/VisualProgress/icon_stop.png" alt="" />
    </div>
    
  </div>
  <taskPanel ref="taskPanelRef" :list="m.taskPanelList" :curIndex="m.currentIndex" :show="m.isShowStopFlag" />
</div>
</template>

<script>
import {
  getScreenProgressTaskInfo,
  getScreenAllProgressTaskInfo,
} from "@/api/visualProgress";
import moment from "moment";
import taskPanel from './taskPanel.vue';
export default {
  name: "progressAnimation",
  components: {
    taskPanel
  },
  data() {
    return {
      m: {
        taskList: [],
        taskPanelList: [],
        currentIndex: "",
        growthAnimation: [],
        modelId: "",
        isGrowthAnimationActivated: false,
        isShowStopFlag: false,
      },
      viewerGIS: null,
      s: {
        pointArr: [],
        size: {},
        progressWidth: "",
      },
    };
  },
  computed: {
    curSize() {
      if (this.m.currentIndex || this.m.currentIndex === 0) {
        // 当前关键节点
        let curIndex = 0;
        // 前一个关键节点
        let beforeIndex = 0;
        let index = this.s.pointArr.indexOf(this.m.currentIndex);
        if (index >= 0) {
          curIndex = this.s.pointArr[index + 1];
          beforeIndex = this.s.pointArr[index];
        } else {
          for (let i in this.s.pointArr) {
            if (
              this.m.currentIndex < this.s.pointArr[i] &&
              this.m.currentIndex > this.s.pointArr[i - 1] &&
              i > 0
            ) {
              curIndex = this.s.pointArr[i];
              beforeIndex = this.s.pointArr[i - 1];
              break;
            }
          }
        }

        let obj = this.s.size[curIndex];
        if (obj) {
          return {
            ...obj,
            beforeIndex,
          };
        }
      }
      return "";
    },
  },
  created() {
    // 监听右侧面板显示或隐藏
    this.$bus.on("click-toggle-right-panel", (flag) => {
      if (flag && this.m.isGrowthAnimationActivated) {
        this.clickPause();
      }
    });
    this.getTaskData();
  },
  beforeDestroy() {
    this.$bus.off("click-toggle-right-panel");
  },
  methods: {
    stopAnim() {
      this.stopGrowthAnimation(this.m.currentIndex);
      this.m.isShowStopFlag = false;
      this.m.currentIndex = "";
      this.s.progressWidth = "";
      this.componentManager.show({all: true});
    },
    clickPlay() {
      this.$bus.emit("bim-other-toggle-right-panel", false);
      if (!this.viewerGIS) {
        this.m.isShowStopFlag = true;
        this.initBim(window.bimViewerGIS);
      } else {
        if (this.m.currentIndex >= this.m.taskList.length - 1) {
          this.m.currentIndex = 0;
          this.componentManager.hide({all: true});
        }
        if (!this.m.isShowStopFlag) {
          this.m.isShowStopFlag = true;
          this.m.currentIndex = 0;
          // this.componentManager.restoreColor({all: true});
          this.componentManager.hide({all: true});
        }
        this.m.isGrowthAnimationActivated = true;
        this.playGrowthAnimation(this.m.currentIndex);
      }
    },
    // 暂停
    clickPause() {
      this.pauseGrowthAnimation(this.m.currentIndex);
    },
    // 选择时间点播放
    chooseAnimPlay(index) {
      this.clickPause();
      this.componentManager.hide({all: true});
      for (let i = 0; i < index; i++) {
        let nodeList = this.m.taskList[i].nodeList;
        this.componentManager.show({ids: nodeList});
      }
      this.m.currentIndex = index;
      this.m.isGrowthAnimationActivated = true;
      this.playGrowthAnimation(this.m.currentIndex);
    },
    // 总计划任务信息
    getTaskData() {
      let param = {
        customQueryParams: {
          type: 2,
        },
        page: {
          current: 1,
          size: -1,
        },
      };
      getScreenProgressTaskInfo(param).then((res) => {
        if (res.success) {
          let pointArr = [];
          let data = res.data.records.filter(
            (item) => item.integrateList && item.integrateList.length > 0
          );
          let taskList = data.map((item, index) => {
            if (item.point == 1) {
              pointArr.push(index);
            }
            let componentList = [];
            if (item.integrateList && item.integrateList.length > 0) {
              componentList = item.integrateList.map((k) => {
                let arr = k.resourceId.split('.');
                return arr[arr.length - 1];
              });
            }
            item.nodeList = componentList;
            return item;
          });
          console.log("taskList", taskList);
          this.m.taskList = taskList;
          this.getTaskPanelData();

          this.$nextTick(() => {
            this.initSize(pointArr);
          });
        }
      });
    },
    // 总计划任务信息-面板
    getTaskPanelData() {
      let param = {
        customQueryParams: {
          joinModel: true,
        },
        page: {
          current: 1,
          size: 9999,
        },
      };
      getScreenAllProgressTaskInfo(param).then((res) => {
        if (res.success) {
          let data = {};
          (res.data.records || []).forEach((k) => {
            data[k.id] = k;
          });
          let taskPanelList = this.m.taskList.map((item, index) => {
            let obj = data[item.id];
            item.fullPath = obj.fullPath.split("/").slice(1).join("/"); // 去掉计划类型
            item.realStartTime = obj.realStartTime;
            item.realEndTime = obj.realEndTime;
            return item;
          });
          console.log("taskPanelList", taskPanelList);
          this.m.taskPanelList = taskPanelList;
        }
      })
    },
    // 初始化距离
    initSize(pointArr) {
      let width = this.$refs.listRef.offsetWidth;
      let boxArr = this.$refs.boxRef;
      let s = 0;
      let obj = {};
      let len = this.m.taskList.length;
      // 上一个关键节点到起点的宽度
      let offW = 0;
      // 这一段之前的宽度
      let beforeW = 0;
      for (let i in pointArr) {
        let index = pointArr[i];
        let el = boxArr[index];
        // 当前关键节点到起点的宽度
        let curW = el.offsetLeft + el.offsetWidth / 2;
        // s到i 这一段所占总宽的百分比
        let allPer = (100 * (curW - offW)) / width;
        // s到i 这一段每一段平均百分比
        let n = index - s;
        if (n === 0) {
          obj[index] = {
            w: allPer,
            beforeW,
          };
        } else {
          let per = allPer / n;
          obj[index] = {
            w: per,
            beforeW,
            curW,
            offW,
          };
        }
        beforeW = (100 * curW) / width;
        s = index;
        offW = curW;
      }
      // 剩下的距离
      let otherW = 100 - beforeW;
      let otherPer = otherW / (len - s);
      obj[len] = {
        w: otherPer,
        beforeW,
      };
      console.log("size", (obj));
      this.s.size = obj;
      this.s.pointArr = pointArr.concat([len]);
    },
    // 计算进度条
    setProgressWidth(progress) {
      let size = this.curSize;
      if (size) {
        let n = this.m.currentIndex - size.beforeIndex;
        let w = size.beforeW + n * size.w;
        if (progress) {
          w += size.w * progress;
        }
        // console.log(w, progress, this.m.currentIndex, size.beforeIndex)
        this.s.progressWidth = `${w}%`;
      } else {
        this.s.progressWidth = "";
      }
    },
    // 初始化bim
    initBim(viewerGIS) {
      if (!this.viewerGIS) {
        this.viewerGIS = viewerGIS;
        let layerManager = this.viewerGIS.getLayerManager();
        let modelLayer = layerManager.getLayer('layer_10000934136560')
        this.componentManager = modelLayer.getComponentManager()
        console.log(this.componentManager)
        this.m.modelId = modelLayer.modelId;
      }
      if (this.m.growthAnimation.length > 0) {
        this.m.growthAnimation.forEach((item) => {
          item.destroy();
        });
      }
      this.initGrowthAnimation();
    },
    // 生长动画
    initGrowthAnimation() {
      if (!this.viewerGIS || this.m.taskList.length === 0) {
        return;
      }
      this.m.growthAnimation = [];
      this.m.taskList.forEach((item, index) => {
        this.getGrowthAnimation(item, index);
      });
      // this.componentManager.restoreColor({all: true});
      this.componentManager.hide({all: true});

      this.m.currentIndex = 0;
      this.m.isGrowthAnimationActivated = true;
      // console.log('task', this.m.taskList, this.m.growthAnimation)
      this.playGrowthAnimation(this.m.currentIndex);
    },
    playGrowthAnimation(index) {
      // 播放生长动画
      if (index < this.m.growthAnimation.length) {
        this.m.currentIndex = index;
        if (this.m.growthAnimation[index]) {
          let nodeList = this.m.taskList[index].nodeList;
          // console.log('nodeList', nodeList)
          this.componentManager.show({ids: nodeList});
          // this.m.growthAnimation[index].play();
          if (index < this.m.growthAnimation.length - 1) {
            setTimeout(() => {
              this.playGrowthAnimation(index + 1);
            }, 1000);
          } else {
            this.m.isGrowthAnimationActivated = false;
          }
        } else {
          this.m.currentIndex++;
          this.playGrowthAnimation(this.m.currentIndex);
        }
      } else {
        this.m.isGrowthAnimationActivated = false;
      }
      this.setProgressWidth();
    },
    pauseGrowthAnimation(index) {
      if (this.m.growthAnimation[index]) {
        // this.m.growthAnimation[index].pause();
      }
      this.m.isGrowthAnimationActivated = false;
    },
    stopGrowthAnimation(index) {
      if (this.m.growthAnimation[index]) {
        // this.m.growthAnimation[index].stop();
      }
      this.m.isGrowthAnimationActivated = false;
    },
    getGrowthAnimation(item, index) {
      if (item.nodeList.length === 0) {
        this.m.growthAnimation.push(null);
        return;
      }
      console.log('item', item)
      // 构造生长动画配置项
      let growthAnimationConfig = new Glodon.Bimface.Plugins.Animation.GrowthAnimationConfig();
      growthAnimationConfig.viewer = this.viewerGIS;
      // 设置参与生长动画的构件
      growthAnimationConfig.conditions = [
        {
          modelId: this.m.modelId,
          objectIds: item.nodeList,
        },
      ];
      // console.log(growthAnimationConfig)
      // 设置生长动画的时长,单位为毫秒
      // growthAnimationConfig.time = 15000;
      growthAnimationConfig.speed = 2;
      // 设置生长方向
      growthAnimationConfig.direction = { x: 0, y: 0, z: 1 };
      // 构造生长动画对象
      let growthAnimation = new Glodon.Bimface.Plugins.Animation.GrowthAnimation(growthAnimationConfig);
        console.log('growthAnimation', growthAnimation)
      // 设置生长动画的进度监听，当播放完成后更新按钮状态
      growthAnimation.index = index;
      // growthAnimation.onProgressChanged((progress) => {
      //   if (progress == 1) {
      //     let i = growthAnimation.index;
      //     console.log(i, this.m.growthAnimation.length);
      //     if (i < this.m.growthAnimation.length - 1) {
      //       this.playGrowthAnimation(i + 1);
      //     } else {
      //       this.m.isGrowthAnimationActivated = false;
      //     }
      //   } else {
      //     this.setProgressWidth(progress);
      //   }
      // });
      this.m.growthAnimation.push(growthAnimation);
    },
    formatDate(date) {
        if (date) {
            return moment(date).format("YYYY-MM-DD");
        }
        return '';
    },
  },
};
</script>

<style lang="less" scoped>
.progress-container {
  position: absolute;
  bottom: 2.14vh;
  width: 70%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  pointer-events: auto;
  img {
    display: block;
    width: 100%;
  }
  .progress-swiper {
    flex: 1;
    position: relative;
    padding: 0 1.66vh;
    overflow: hidden;
    margin-right: 1.48vh;
    .box-bg-top {
      position: absolute;
      top: 3.2vh;
      left: 0;
      width: 100%;
      height: 0.74vh;
      background: linear-gradient(
          90deg,
          rgba(8, 66, 167, 0) 0.11%,
          #2f416b 16.73%,
          rgba(60, 91, 189, 0.47) 53.09%,
          rgba(47, 65, 107, 0.445108) 75.79%,
          rgba(47, 65, 107, 0) 97.76%
        ),
        linear-gradient(
          180deg,
          rgba(101, 163, 255, 0.2) 0%,
          rgba(101, 163, 255, 0) 100%
        );
    }
    .box-bg-bot {
      position: absolute;
      top: 3.94vh;
      left: 0;
      width: 100%;
      height: 2.4vh;
      background: linear-gradient(
          0deg,
          rgba(0, 0, 0, 0.2) 28.85%,
          rgba(0, 0, 0, 0) 61.54%
        ),
        linear-gradient(
          90deg,
          rgba(8, 66, 167, 0) 0.11%,
          rgba(47, 65, 107, 0.214466) 24.52%,
          rgba(60, 91, 189, 0.47) 49.73%,
          rgba(47, 65, 107, 0.445108) 76.46%,
          rgba(47, 65, 107, 0) 97.76%
        );
    }
    .bg-line {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      height: 0.27vh;
      background: linear-gradient(
        90deg,
        rgba(116, 140, 184, 0) 0%,
        rgba(43, 88, 169, 0.7) 50.04%,
        rgba(248, 250, 252, 0.021489) 100.08%
      );
    }
    .progress-bar {
      position: absolute;
      left: 0;
      top: 3.605vh;
      height: 0.37vh;
      background: rgba(255, 193, 147, 1);
      z-index: 4;
    }
    .list {
      position: relative;
      height: 6.25vh;
      z-index: 5;
      width: 100%;
      overflow: hidden;
      display: flex;
      justify-content: space-around;
      .box {
        width: 1.1vh;
        height: 100%;
        position: relative;
        margin: 0 2.96vh;
        z-index: 5;
        .key-node {
          width: 0.74vh;
          height: 0.74vh;
          position: absolute;
          top: 3.45vh;
          left: 0;
          .rh {
            width: 100%;
            height: 100%;
            background: linear-gradient(
              45deg,
              rgba(130, 180, 255, 0.6) 0%,
              rgba(147, 185, 255, 0.264) 68.75%
            );
            transform: rotate(-45deg);
            position: relative;
            z-index: 3;
            cursor: pointer;
          }
        }
        .info {
          display: none;
          animation: hideAnim 0.3s linear;
          .time,
          .name {
            text-align: center;
            color: #fff;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            z-index: 4;
          }
          .time {
            font-size: 1.11vh;
            line-height: 0.96vh;
            bottom: 1.3vh;
          }
          .name {
            font-size: 1.29vh;
            line-height: 1.85vh;
            bottom: -2vh;
          }
        }
        &.has {
          .key-node {
            .rh {
              background: rgba(238, 151, 88, 1);
            }
          }
        }
        &.on {
          .key-node {
            .rh {
              border: 0.27vh solid rgba(238, 151, 88, 0.99);
              background: #fff;
              box-shadow: 0px 0px 9px 0px rgba(255, 139, 32, 1);
            }
            &:before {
              position: absolute;
              content: "";
              width: 3.88vh;
              height: 0.27vh;
              background: linear-gradient(
                90deg,
                rgba(116, 140, 184, 0) 0%,
                #ee9758 32.07%,
                #ee9758 47.94%,
                #ee9758 64.19%,
                rgba(248, 250, 252, 0) 100.08%
              );
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }
          .info {
            display: block;
            animation: showAnim 0.3s linear;
          }
        }
        &:hover {
          .info {
            display: block;
            animation: showAnim 0.3s linear;
          }
        }
      }
    }
    .btn-arrow {
      position: absolute;
      width: 1.66vh;
      height: 1.66vh;
      top: 2.96vh;
      z-index: 10;
      background: url("~@/assets/images/VisualProgress/icon_arrow.png") center no-repeat;
      background-size: 100%;
      &.prev {
        left: 0;
      }
      &.next {
        right: 0;
        transform: rotate(180deg);
      }
    }
  }
  .play {
    width: 2.96vh;
    height: 2.96vh;
    margin-left: 0.46vh;
    margin-top: 2.3vh;
    cursor: pointer;
  }
  .stop {
    position: absolute;
    top: 0;
    right: -3.2vh;
  }
}
@keyframes showAnim {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes hideAnim {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>

<template>
    <div v-show="showFlag" class="task-panel">
      <div class="task-main">
        <Title title="进度任务"></Title>
        <div class="content">
          <swiper v-if="list && list.length > 0" :options="swiperOptions" ref="mySwiper" class="task-swiper">
            <swiper-slide v-for="(item, index) in list" :key="index" class="task-tr">
              <div class="task-box" :class="{on: curIndex === index}">
                <div class="txt">任务名称：{{ item.fullPath}}</div>
                <div class="txt">计划时间：{{ formatDate(item.startTime) }}~{{ formatDate(item.endTime) }}</div>
                <div class="txt">实际时间：{{ formatDate(item.realStartTime) }}~{{ formatDate(item.realEndTime) }}</div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import Title from '@/components/commonView/title.vue';
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
  import 'swiper/css/swiper.min.css';
  import moment from 'moment';
  export default {
    name: 'taskPanel',
    components: {
        Title,
      Swiper, SwiperSlide
    },
    props: {
      list: { default() { return [] } },
      curIndex: { default: '', type: [String, Number]},
      show: { default: false, type: Boolean}
    },
    data() {
      return {
        swiperOptions: {
          direction: 'vertical',
          slidesPerView: 3,
          loop: false,
          observer: true,
          observeParents: true,
          speed: 600,
          autoHeight: true,
        },
        showFlag: this.show
      }
    },
    computed: {
      swiper() {
        return this.$refs.mySwiper?.$swiper
      }
    },
    watch: {
      curIndex() {
        this.slideTo()
      },
      show(val) {
        this.showFlag = val
      }
    },
    created() {
      // 监听右侧面板显示或隐藏
      this.$bus.on('click-toggle-right-panel', flag => {
        this.showFlag = !flag
      })
    },
    beforeDestroy() {
      this.$bus.off('click-toggle-right-panel')
    },
    methods: {
      slideTo() {
        if (this.swiper && (this.curIndex || this.curIndex === 0)) {
          this.swiper.slideTo(this.curIndex)
        }
      },
      formatDate(date) {
        if (!date) {
          return ''
        }
        return moment(date).format('YYYY-MM-DD')
      }
    }
  }
  </script>
  
  <style lang="less" scoped>
  @w: 20.85vw;
  .task-panel{
    position: fixed;
    top:  ~"calc(4.75rem + 12.96vh - 100vh)";
    right: 1vh;
    z-index: 100;
    background: url('~@/assets/images/modal/modal_bg.png') no-repeat center center;
    background-size: 100% 100%;
    padding: 2vh 0.93vh;
  }
  .task-main{
    background: linear-gradient(265.67deg, rgba(88, 122, 210, 0.459) 2.48%, rgba(115, 116, 149, 0.054) 52.58%);
    width: @w;
    pointer-events: auto;
    .content{
      padding: 0.74vh;
    }
    .task-swiper{
      height: 23vh;
    }
    .task-box{
      font-size: 1.29vh;
      line-height: 1.68vh;
      color: #fff;
      background: rgba(45, 67, 122, 0.9);
      padding: 0.37vh 0;
      .txt{
        padding: 0.2vh 0.74vh;
      }
      &.on{
        background: rgba(48, 105, 238, 1);
      }
    }
  }
  </style>
  
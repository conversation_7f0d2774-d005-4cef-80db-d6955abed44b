<template>
    <div class="project-person-overview">
        <Title title="项目人员概况" desc="PERSON OVERVIEW" style="margin-bottom: 1.48vh;" />
        <div class="person-list">
            <div class="person-list-item" v-for="(item, index) in overviewList" :key="index">
                <div class="icon-box">
                    <img src="@/assets/images/VisualProgress/overview_icon1.png" alt="">
                </div>
                <div class="right">
                    <div class="name">{{ item.name }}</div>
                    <div class="num">{{ item.num }}</div>
                </div>
            </div>
        </div>
        <Title4 title="项目人员数量统计" />
        <barEchart :barData="barData" style="height: 17.1vh;" />
    </div>
</template>
<script>
import Title from "@/components/commonView/title.vue";
import Title4 from "@/components/commonView/title4.vue";
import barEchart from "@/components/echarts/barEcharts/barEchart.vue";
import { getProjectPersonOverview } from "@/api/visualProgress";
export default {
    name: 'project-person-overview',
    components: {
        Title,
        Title4,
        barEchart
    },
    data() {
        return {
            overviewList: [
                { name: '施工单位', num: 0, key: 'constructionUnitCount' },
                { name: '监理单位', num: 0, key: 'supervisionUnitCount' },
                { name: '参建单位', num: 0, key: 'participatingUnitCount' },
            ],
            barData: {
                day: [],
                d1: []
            }
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            getProjectPersonOverview().then(res => {
                this.overviewList.forEach(item => {
                    item.num = res.data[item.key];
                })
                let barData = {
                    day: ['施工单位', '监理单位', '参建单位'],
                    d1: []
                }
                barData.d1[0] = res.data.personnelStatistics.constructionPersonnelCount
                barData.d1[1] = res.data.personnelStatistics.supervisionPersonnelCount
                barData.d1[2] = res.data.personnelStatistics.participatingPersonnelCount
                this.barData = barData;
            })
        }
    }
}
</script>
<style lang="less" scoped>
.person-list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.48vh;
    .person-list-item{
        display: flex;
        align-items: center;
        .icon-box{
            width: 2.96vh;
            height: 2.96vh;
            margin-right: 0.74vh;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .right{
            flex: 1;
        }
        .name{
            font-size: 1.29vh;
            line-height: 1.85vh;
            color: rgba(255, 255, 255, 0.6);
        }
        .num{
            font-family: YouSheBiaoTiHei;
            font-size: 1.48vh;
            color: #fff;
        }
    }
}
</style>
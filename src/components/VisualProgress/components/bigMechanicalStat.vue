<template>
    <div class="big-mechanical-stat">
        <Title title="大型机械设备统计" desc="LARGE MACHINERY STAT" style="margin-bottom: 1.48vh;" />
        <pieEcharts :list="mechanicalTypeList" unit="台" style="margin-bottom: 1.48vh;" />
        <div class="total-main">
            <div class="work-day">
                <div class="name">有侵限风险设备总数</div>
                <div class="num">{{totalData.totalCount}}</div>
            </div>
            <div class="progress-box">
                <div v-for="(item, index) in progressList" :key="index" class="progress-box-bar" :style="{width: item.percentage + '%'}" ></div>
            </div>
            <div class="total-list">
                <div v-for="(item, index) in progressList" :key="index" class="total-list-item">
                    <div class="yuan"></div>
                    <div class="name">{{item.typeName}}</div>
                    <div class="num">{{item.count}}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Title from "@/components/commonView/title.vue";
import pieEcharts from "@/components/echarts/pieEcharts/pieEcharts.vue";
import { getMechanicalTypeStatistics } from "@/api/visualProgress";
export default {
    name: 'big-mechanical-stat',
    components: {
        Title,
        pieEcharts
    },
    data() {
        return {
          mechanicalTypeList: [],
          totalData: {
            totalCount: 0
          },
          progressList: []
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            getMechanicalTypeStatistics().then(res => {
                console.log(res);
                this.mechanicalTypeList = res.data.typeStatistics.map(k => ({
                  name: k.typeName,
                  value: k.count,
                }));
                this.totalData.totalCount = res.data.totalCount;
                this.progressList = res.data.typeStatistics.filter(k => k.typeName != '其他');
            })
        }
    }
}
</script>
<style lang="less" scoped>
.total-main{
    .work-day{
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;
      margin-bottom: 0.37vh;
      .name{
        font-size: 1.29vh;
        line-height: 1.85vh;
      }
      .num{
        font-size: 1.85vh;
        font-family: YouSheBiaoTiHei;
        line-height: 2.407vh;
      }
    }
    .progress-box{
      display: flex;
      align-items: center;
      column-gap: 0.46vh;
      margin-bottom: 0.74vh;
      .progress-box-bar{
        height: 0.74vh;
        position: relative;
        &::before,
        &::after{
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
        
        &::after{
          background: url('~@/assets/images/VisualProgress/box_bg.png') repeat-x left center;
          background-size: auto 100%;
        }
        &:nth-child(1){
          &::before{
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: rgba(0, 77, 235, 1);
          }
        }
        &:nth-child(2){
          &::before{
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: rgba(0, 137, 242, 1);
          }
        }
        &:nth-child(3){
          flex: 1;
          &::before{
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: rgba(0, 200, 255, 1);
          }
          &::after{
          background: url('~@/assets/images/VisualProgress/box_bg2.png') repeat-x left center;
          background-size: auto 100%;
        }
        }
      }
    }
    .total-list{
        display: flex;
        align-items: center;
        column-gap: 0.94vh;
        .total-list-item{
            display: flex;
            align-items: center;
            color: #fff;
            font-size: 1.11vh;
            line-height: 1.57vh;
            flex: 1;
            .yuan{
                width: 0.37vh;
                height: 0.37vh;
                border-radius: 50%;
                margin: 0 0.55vh;
            }
            .name{
                flex: 1;
                padding-left: 0.37vh;
            }
            .num{
                font-family: 'DIN Pro';
                font-size: 1.48vh;
            }
            &:nth-child(1){
                .yuan{
                    background: rgba(0, 77, 235, 1);
                }
            }
            &:nth-child(2){
                .yuan{
                    background: rgba(0, 137, 242, 1);
                }
            }
            &:nth-child(3){
                .yuan{
                    background: rgba(0, 200, 255, 1);
                }
            }    
        }
    }
}
</style>
<template>
  <div class="project-progress">
    <Title title="项目进度" desc="PROJECT PROGRESS" style="margin-bottom: 1.48vh;" />
    <div class="progress-main">
      <div class="echart-main">
        <AEcharts :options="options" height="11.11vh" />
        <div class="echart-info">
          <div class="value">{{progressInfo.totalProgress}}%</div>
          <div class="line"></div>
          <div class="name">总进度</div>
        </div>
      </div>
      <div class="progress-main-right">
        <div class="work-day">
          <div class="name">剩余工期(天)</div>
          <div class="num">{{progressInfo.remainingDays}}</div>
        </div>
        <div class="progress-box">
          <div class="progress-box-bar on" :style="{width: progressInfo.totalProgress + '%'}" ></div>
          <div class="progress-box-bar has"></div>
        </div>
        <div class="time-list">
          <div class="time-list-item">
            <div class="yuan"></div>
            <div class="name">开始时间</div>
            <div class="time">{{progressInfo.startTime}}</div>
          </div>
          <div class="time-list-item">
            <div class="yuan"></div>
            <div class="name">结束时间</div>
            <div class="time">{{progressInfo.endTime}}</div>
          </div>
        </div>
      </div>
    </div>
    <Title4 title="项目施工计划" />
    <ProjectProgressPlan />
  </div>
</template>
<script>
import Title from "@/components/commonView/title.vue";
import Title4 from "@/components/commonView/title4.vue";
import AEcharts from "@/components/commonView/AEcharts.vue";
import ProjectProgressPlan from "./projectProgressPlan.vue";
import { getScreenProgressInfo } from "@/api/visualProgress";
export default {
  name: "project-progress",
  components: {
    Title,
    AEcharts,
    Title4,
    ProjectProgressPlan
  },
  data() {
    return {
      options: {
        angleAxis: {
          max: 100,
          clockwise: false,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          splitLine: { show: false },
        },
        radiusAxis: {
          type: "category",
          data: ["进度"],
          z: 10,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
        },
        polar: {
          radius: ["75%", "100%"],
        },
        series: [
          {
            type: "bar",
            data: [],
            coordinateSystem: "polar",
            barWidth: 6,
            name: "A",
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(0, 81, 255, 1)",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(0, 144, 255, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(114, 190, 255, 1)",
                  },
                ],
                global: false,
              },
              borderWidth: 1,
              borderColor: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(149, 218, 255, 0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(149, 218, 255, 1)",
                  },
                ],
                global: false,
              },
              shadowColor: "rgba(255, 255, 255, 0.34)",
              shadowBlur: 4,
            },
            showBackground: true,
            backgroundStyle: {
              color: "rgba(255, 255, 255, 0.2)",
              shadowColor: "rgba(0, 0, 0, 0.28)",
              shadowBlur: 4,
            },
          },
          {
            type: "bar",
            data: [],
            coordinateSystem: "polar",
            barWidth: 6,
            name: "A",
            itemStyle: {
              color: "rgba(0, 144, 255, 1)",
              shadowColor: "rgba(0, 0, 0, 0.15)",
              shadowBlur: 4,
              opacity: 0.2,
            },
            showBackground: true,
            backgroundStyle: {
              color: "rgba(255, 255, 255, 0.2)",
              shadowColor: "rgba(0, 0, 0, 0.15)",
              shadowBlur: 4,
              opacity: 0.2,
            },
            barGap: "0%",
          },
        ],
      },
      progressInfo: {}
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      getScreenProgressInfo().then(res => {
        console.log(res);
        this.options.series[0].data = [res.data.totalProgress];
        this.options.series[1].data = [res.data.totalProgress];
        this.progressInfo = res.data;
      })
    }
  },
};
</script>
<style lang="less" scoped>
.project-progress {
  display: flex;
  flex-direction: column;
  & > div {
    width: 100%;
  }
}
.progress-main {
  height: 11.11vh;
  display: flex;
  align-items: center;
  column-gap: 1.48vh;
  margin-bottom: 1.48vh;
  .echart-main{
    width: 11.11vh;
    height: 100%;
    position: relative;
    .echart-info{
      position: absolute;
      width: 74%;
      height: 74%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: url('~@/assets/images/VisualProgress/echart_bg.png') no-repeat center;
      background-size: 100%;
      .value{
        font-size: 1.85vh;
        font-family: YouSheBiaoTiHei;
        color: #fff;
        line-height: 2.59vh;
      }
      .line{
        width: 60%;
        height: 0.2px;
        background: #fff;
        margin: 0.185vh 0;
      }
      .name{
        font-size: 1.11vh;
        color: rgba(255, 255, 255, 0.6);
        line-height: 1.57vh;
      }
    }
  }
  .progress-main-right{
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(270deg, rgba(75, 131, 183, 0) 0%, rgba(75, 131, 183, 0.125) 50%, rgba(75, 131, 183, 0) 100%);

    &>div{
      width: 100%;
    }
    .work-day{
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;
      margin-bottom: 0.37vh;
      .name{
        font-size: 1.29vh;
        line-height: 1.85vh;
      }
      .num{
        font-size: 1.85vh;
        font-family: YouSheBiaoTiHei;
        line-height: 2.407vh;
      }
    }
    .progress-box{
      display: flex;
      align-items: center;
      column-gap: 2px;
      margin-bottom: 2.22vh;
      .progress-box-bar{
        height: 0.74vh;
        position: relative;
        &::before,
        &::after{
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
        
        &::after{
          background: url('~@/assets/images/VisualProgress/box_bg.png') repeat-x left center;
          background-size: auto 100%;
        }
        &.on{
          &::before{
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: rgba(0, 77, 235, 1);
          }
        }
        &.has{
          flex: 1;
          &::before{
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: rgba(255, 255, 255, 0.04);
          }
        }
      }
    }
    .time-list{
      position: relative;
      .time-list-item{
        display: flex;
        align-items: center;
        line-height: 1.57vh;
        .yuan{
          width: 0.37vh;
          height: 0.37vh;
          border-radius: 50%;
          margin-right: 0.74vh;
          background: rgba(255, 255, 255, 1);
        }
        .name{
          flex: 1;
          color: rgba(255, 255, 255, 0.6);
          font-size: 1.11vh;
        }
        .time{
          font-size: 1.203vh;
          color: rgba(255, 255, 255, 1);
          font-family: 'DIN Pro';
        }
        &:first-child{
          margin-bottom: 0.74vh;
          .yuan{
            border: 0.11vh solid rgba(2, 85, 255, 1);
            box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
          }
        }
        &:last-child{
          .yuan{
            border: 0.11vh solid rgba(71, 78, 91, 1);
          }
        }
      }
      &:before{
        content: '';
        position: absolute;
        width: 0.4px;
        height: 57%;
        background: linear-gradient(180deg, #0252FF 0%, #999999 56.22%);
        top: 50%;
        left: 0.16vh;
        transform: translateY(-50%);
      }
    }
  }
}
</style>

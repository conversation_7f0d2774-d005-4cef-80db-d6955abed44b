<template>
    <div class="project-info">
        <Title title="项目信息" desc="PROJECT INFO" />
        <div class="project-info-content">
            <ProjectInfoItem title="项目名称" :content="projectInfo.platformProjectName" />
            <ProjectInfoItem title="项目地址" :content="projectInfo.location" :icon="require('@/assets/images/VisualProgress/info_icon2.png')" />
            <ProjectInfoItem title="计划工期" :content="projectPlanTime" :icon="require('@/assets/images/VisualProgress/info_icon3.png')" />
            <ProjectInfoItem title="项目介绍" :content="projectInfo.projectDesc" :icon="require('@/assets/images/VisualProgress/info_icon4.png')" />
        </div>
    </div>
</template>
<script>
import Title from '@/components/commonView/title.vue';
import ProjectInfoItem from './projectInfoItem.vue';
export default {
    name: 'project-info',
    components: {
        Title,
        ProjectInfoItem
    },
    data() {
        return {
        }
    },
    computed: {
        projectInfo() {
            return this.$store.state.user.projectInfo
        },
        projectPlanTime() {
            if (!this.projectInfo.estimateTime || !this.projectInfo.completionTime) {
                return ''
            }
            return this.projectInfo.estimateTime.split(' ')[0] + ' 至 ' + this.projectInfo.completionTime.split(' ')[0]
        }
    },
    methods: {}
}
</script>
<style lang="less" scoped>
.project-info{
    display: flex;
    flex-direction: column;
    &>div{
        width: 100%;
    }
}
.project-info-content{
    display: flex;
    flex-direction: column;
    row-gap: 1.48vh;
    flex: 1;
    overflow: hidden;
    &>div{
        width: 100%;
    }
    .project-info-item:last-child{
        flex: 1;
    }
}
</style>
<template>
    <div class="project-info-item">
        <div class="project-info-item-head">
            <div class="project-info-item-title">
                <div class="icon-box">
                    <img :src="icon" alt="">
                </div>
                <div class="title-box">
                    {{ title }}
                </div>
            </div>
        </div>
        <div class="project-info-item-content">{{ content }}</div>
    </div>
</template>
<script>
export default {
    name: 'project-info-item',
    props: {
        title: {
            type: String,
            default: ''
        },
        icon: {
            type: String,
            default: require('@/assets/images/VisualProgress/info_icon1.png')
        },
        content: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
        }
    },
    methods: {}
}
</script>
<style lang="less" scoped>
.project-info-item{
    background: linear-gradient(90deg, rgba(75, 131, 183, 0) 0.06%, rgba(75, 131, 183, 0.125) 49.14%, rgba(75, 131, 183, 0) 98.23%);
    backdrop-filter: blur(4px);
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
    color: #fff;
    padding-bottom: 0.74vh;
    .project-info-item-head{
        margin-bottom: 1.48vh;
    }
    .project-info-item-title{
        display: flex;
        align-items: center;
        height: 2.407vh;
        position: relative;
        padding-left: 1.48vh;
        z-index: 1;
        width: 12.78vh;
        &::before{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(88.92deg, rgba(255, 154, 43, 0.66) 1.99%, rgba(174, 130, 58, 0) 100%);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
            clip-path: polygon(0 0, 100% 0, calc(100% - 2.407vh) 100%, 0 100%);
            z-index: -1;
        }
        .icon-box{
            width: 1.48vh;
            height: 1.48vh;
            margin-right: 0.74vh;
            img{
                width: 100%;
                display: block;
            }
        }
        .title-box{
            font-size: 1.29vh;
        }
    }
    .project-info-item-content{
        font-size: 1.29vh;
        padding-left: 3.7vh;
    }
}
</style>
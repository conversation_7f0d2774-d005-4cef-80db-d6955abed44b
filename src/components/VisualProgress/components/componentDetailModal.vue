<template>
    <div v-if="isShow" class="component-detail-modal column">
        <div class="close" @click="close">
            <img src="@/assets/images/modal/icon_close.png" alt="">
        </div>
        <!-- 图纸信息 -->
        <div class="paper-info column flex">
            <Title title="图纸信息" desc="PAPER INFO" style="margin-bottom: 1.48vh;" />
            <div v-if="curDrawUrl" class="img-carousel flex" @click="handleDrawClick">
                <img :src="curDrawUrl" alt="">
            </div>
            <div v-else-if="drawList.length > 0" class="draw-box flex">
                <div class="btn" @click="handleDrawClick">
                    <div class="text">查看图纸</div>
                    <div class="icon">
                        <img src="@/assets/images/VisualProgress/icon_arrow1.png" alt="">
                    </div>
                </div>
            </div>
            <div v-else class="no-img flex">
                <img src="@/assets/images/ProjectAI/empty.png" alt="">
                <div class="no-img-text">暂无图纸信息</div>
            </div>
        </div>
        <!-- 施工信息 -->
        <div class="construction-info column flex">
            <Title title="施工信息" desc="CONSTRUCTION INFO" style="margin-bottom: 1.48vh;" />
            <Carousel v-if="imgList.length > 0" v-model="curIndex" class="flex">
                <CarouselItem v-for="(item, index) in imgList" :key="index">
                    <div class="img-carousel" @click="handleImgClick(item, index)">
                        <img :src="item" alt="">
                    </div>
                </CarouselItem>
            </Carousel>
            <div v-else class="no-img flex">
                <img src="@/assets/images/ProjectAI/empty.png" alt="">
                <div class="no-img-text">暂无图片</div>
            </div>
        </div>
        <imgModal ref="imgModalRef" />
        <drawModal ref="drawModalRef" />
    </div>
</template>
<script>
import Title from '@/components/commonView/title.vue';
import { getComponentDetail } from '@/api/visualProgress';
import imgModal from '../../modal/imgModal.vue';
import drawModal from './drawModal.vue';
export default {
    name: 'componentDetailModal',
    components: {
        Title,
        imgModal,
        drawModal
    },
    data() {
        return {
            isShow: false,
            imgList: [],
            drawList: [],
            curDrawUrl: '',
            curIndex: 0,
            viewerGIS: null,
            componentIds: []
        }
    },
    created() {
        // 监听bim点击构件
        this.$bus.on('bim-click-component', (data) => {
            console.log('bim-click-component', data)
            if (!data.id) {
                this.close()
                return
            }
            this.isShow = true
            // if (!this.viewerGIS) {
            //     this.viewerGIS = window.bimViewerGIS;
            //     let layerManager = this.viewerGIS.getLayerManager();
            //     let modelLayer = layerManager.getLayer('layer_10000934136560')
            //     this.componentManager = modelLayer.getComponentManager()
            // }
            this.componentIds = [data.id.split('.')[1]]
            this.getDetail(data.id)
        })
        // 监听右侧面板显示或隐藏
        this.$bus.on('bim-other-toggle-right-panel', (flag) => {
            if (!flag) {
                this.close()
            }
        })
    },
    methods: {
        getDetail(id) {
            this.curDrawUrl = ''
            getComponentDetail(id).then(res => {
                // console.log('res', res)
                this.imgList = res.data.imgs?.split(',') || []
                this.drawingFiles = res.data.drawingFiles?.split(',') || []
                for (let i = 0; i < this.drawingFiles.length; i++) {
                    let url = this.drawingFiles[i]
                    if (url.includes(".png") || url.includes(".jpg") || url.includes(".jpeg")) {
                        this.curDrawUrl = url
                        break
                    }
                }
            })
        },
        overrideColor(progressStatus) {
            if (!progressStatus && progressStatus !== 0) {
                return
            }
            let colorData = {
                0: '#999',
                1: '#0aa5e7',
                2: '#0ab04e'
            }
            let color = new Glodon.Web.Graphics.Color(colorData[progressStatus], 1)
            this.componentManager.overrideColor({ids: this.componentIds}, color)
        },
        handleImgClick(item, index) {
            this.$refs.imgModalRef.show(this.imgList, index)
        },
        handleDrawClick() {
            this.$refs.drawModalRef.show(this.drawingFiles)
        },
        close() {
            this.isShow = false
            // if (this.componentManager && this.componentIds.length > 0) {
            //     this.componentManager.restoreColor({ids: this.componentIds})
            // }
        }
    }
}
</script>
<style scoped lang="less">
.column{
    display: flex;
    flex-direction: column;
    &>div{
        width: 100%;
    }
}
.flex{
    flex: 1;
    overflow: hidden;
}
.component-detail-modal {
    position: absolute;
    top: 0.96vh;
    right: 1vh;
    width: 20.85vw;
    height: 78vh;
    background: url('~@/assets/images/modal/modal_bg.png') no-repeat center center;
    background-size: 100% 100%;
    padding: 2vh 0.93vh;
    pointer-events: auto;
    z-index: 90;
    .close {
        position: absolute;
        top: 0.74vh;
        right: 0.74vh;
        cursor: pointer;
        width: 1.85vh;
        height: 1.85vh;
        color: #fff;
        img {
            width: 100%;
        }
    }
}
.size-info{
    .project-info-item{
        margin-bottom: 1.48vh;
        height: 3.88vh;
        position: relative;
        /deep/.project-info-item-content{
            position: absolute;
            left: 13.78vh;
            top: 50%;
            transform: translateY(-50%);
            padding-left: 0;
        }
    }
}
.paper-info{
    margin-bottom: 1.48vh;
}
.btn{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #fff;
    column-gap: 0.74vh;
    cursor: pointer;
    img{
        width: 1.11vh;
    }
}
.no-img{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{
        width: 21vh;
        margin-bottom: 1.48vh;
    }
    .no-img-text{
        font-size: 1.48vh;
        color: #fff;
    }
}
.img-carousel {
	width: 100%;
	height: 100%;
    cursor: pointer;
	img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}
</style>
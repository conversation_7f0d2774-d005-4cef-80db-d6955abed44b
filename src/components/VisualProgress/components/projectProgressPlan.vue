<template>
    <div class="project-progress-plan" id="project-progress-plan" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
        <div v-for="(item, index) in planList" :key="index" class="box">
            <div class="top-box">
                <div class="num">{{ index + 1 }}</div>
                <div class="name">{{ item.taskName }}</div>
            </div>
            <div class="bot-box">
                <div class="progress-box">
                    <div class="progress-box-bar" :style="{ width: item.progress + '%' }">
                        <div class="yuan"></div>
                    </div>
                </div>
                <div class="num">{{item.progress}}%</div>
            </div>
        </div>
    </div>
</template>
<script>
import { getProjectPlan } from "@/api/visualProgress";
export default {
    name: 'project-progress-plan',
    data() {
        return {
            // 添加轮播控制相关数据
            autoScroll: {
                timer: null,
                speed: 1, // 滚动速度(px)
                isHover: false, // 鼠标是否悬停
                scrollElement: null, // 滚动容器元素
            },
            planList: []
        }
    },
    mounted() {
        this.getData();
        // 初始化自动轮播
        this.$nextTick(() => {
            this.autoScroll.scrollElement = document.getElementById('project-progress-plan');
            this.startAutoScroll();
        });
    },
    destroyed() {
        // 清除定时器
        this.stopAutoScroll();
    },
    methods: {
        getData() {
            getProjectPlan().then(res => {
                this.planList = res.data;
            })
        },
        // 开始自动滚动
        startAutoScroll() {
            if (this.autoScroll.timer) {
                clearInterval(this.autoScroll.timer);
            }
            this.autoScroll.timer = setInterval(() => {
                if (this.autoScroll.isHover) return;
                
                const element = this.autoScroll.scrollElement;
                if (!element) return;

                // 如果滚动到底部，重置到顶部
                if (element.scrollTop >= (element.scrollHeight - element.clientHeight)) {
                    element.scrollTop > 0 && this.resetToTop();
                    // console.log('--------', element.scrollTop)
                } else {
                    element.scrollTop += this.autoScroll.speed;
                }
            }, 50);
        },
        // 重置到顶部
        resetToTop() {
            const element = this.autoScroll.scrollElement;
            if (!element) return;
            element.scrollTop = 0;
            this.stopAutoScroll();
            setTimeout(() => {
                this.startAutoScroll();
            }, 1000);
        },
        // 停止自动滚动
        stopAutoScroll() {
            if (this.autoScroll.timer) {
                clearInterval(this.autoScroll.timer);
                this.autoScroll.timer = null;
            }
        },
        // 处理鼠标进入
        handleMouseEnter() {
            this.autoScroll.isHover = true;
        },
        // 处理鼠标离开
        handleMouseLeave() {
            this.autoScroll.isHover = false;
        },
    }
}
</script>
<style lang="less" scoped>
.project-progress-plan{
    height: 10.8vh;
    overflow: auto;
    .box{
        background: linear-gradient(90deg, rgba(75, 131, 183, 0) 0%, rgba(75, 131, 183, 0.125) 50%, rgba(75, 131, 183, 0) 100%);
        margin-bottom: 0.74vh;
        padding: 0.37vh 2.96vh;
        .top-box{
            display: flex;
            align-items: center;
            margin-bottom: 0.74vh;
            .num{
                font-size: 1.11vh;
                color: rgba(255, 255, 255, 1);
                background: rgba(255, 255, 255, 0.18);
                width: 1.48vh;
                height: 1.48vh;
                line-height: 1.48vh;
                border-radius: 0.37vh;
                text-align: center;
                margin-right: 0.74vh;
            }
            .name{
                flex: 1;
                font-size: 1.203vh;
                color: rgba(255, 255, 255, 1);
                line-height: 1.66vh;
            }
        }
        .bot-box{
            display: flex;
            align-items: center;
            .progress-box{
                flex: 1;
                height: 0.37vh;
                background: rgba(129, 129, 129, 1);
                margin-right: 1.48vh;
                box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5) inset;
                border-radius: 0.37vh;
                position: relative;
                .progress-box-bar{
                    position: absolute;
                    top: 50%;
                    left: 0;
                    height: 0.46vh;
                    transform: translateY(-50%);
                    background: linear-gradient(270deg, #4ACBFA 0%, #2864E3 100%);
                    border-radius: 0.185vh;
                    
                    &::before{
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        @mask-image: linear-gradient(#fff, #fff);
                        padding: 0.5px;
                        border-radius: 0.185vh;
                        background: linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
                        mask-image: @mask-image, @mask-image;
                        mask-clip: content-box, padding-box;
                        mask-composite: exclude;
                        -webkit-mask-composite: destination-out;
                    }
                    .yuan{
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        right: -0.46vh;
                        width: 0.92vh;
                        height: 0.92vh;
                        border-radius: 50%;
                        background: radial-gradient(50% 50% at 50% 50%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
                        box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
                    }
                }
            }
            .num{
                font-size: 1.11vh;
                color: #fff;
                font-family: YouSheBiaoTiHei;
            }
        }
        &:last-child{
            margin-bottom: 0;
        }
    }
}
</style>
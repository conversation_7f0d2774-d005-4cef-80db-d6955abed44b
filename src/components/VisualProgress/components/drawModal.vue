<template>
	<Modal v-model="visible" :footer-hide="true" :mask-closable="false" :closable="false" width="75%" class-name="img-preview-modal">
		
        <div class="draw-head">
            <div class="name">图纸信息</div>
            <div class="close" @click="close">
                <img src="@/assets/images/modal/icon_close.png" alt="">
            </div>
        </div>
        <div class="draw-main">
            <div class="draw-menu">
                <div v-for="(item, index) in list" :class="{active: curIndex === index}" :key="index" @click="handleMenuClick(item, index)" class="draw-menu-item">
                    <div class="name ellipsis-1">{{ item.name }}</div>
                </div>
            </div>
            <div class="draw-content">
                <PreviewFile v-if="curUrl" :fileUrl="curUrl" />
            </div>
        </div>
	</Modal>
</template>
<script>
import PreviewFile from '@/components/commonView/PreviewFile.vue'
export default {
	name: "drawModal",
	components: {
		PreviewFile
	},
	data() {
		return {
			visible: false,
			curIndex: 0,
			list: [],
            curUrl: ''
		}
	},
	methods: {
		show(list) {
			this.visible = true
			this.list = list.map(item => {
				let name = item.split('/').pop()
				return {
					name,
					url: item
				}
			})
			this.curIndex = 0
            this.curUrl = this.list[this.curIndex].url
		},
        handleMenuClick(item, index) {
            this.curIndex = index
            this.curUrl = item.url
        },
		close() {
			this.visible = false
			this.list = []
		}
	}
};
</script>
<style lang="less">
.img-preview-modal{
    .ivu-modal-content{
        background: url('~@/assets/images/modal/modalContent3.png') no-repeat center center;
        background-size: 100% 100%;
    }
    
}
</style>
<style lang="less" scoped>
.draw-head{
    background: linear-gradient(180deg, #004DBA 0%, #0B45AC 100%);
    height: 5.5vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .name{
        font-size: 2.59vh;
        color: #fff;
        text-shadow: 0px 0px 6px #06BDFB;
    }
    .close{
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 1.48vh;
        cursor: pointer;
        width: 2.4vh;
        z-index: 99;
        img{
            width: 100%;
        }
    }
}
.draw-main{
    height: 74.5vh;
    display: flex;
    padding: 2.96vh 2.22vh;
    .draw-menu{
        width: 20.37vh;
        height: 100%;
        margin-right: 1.48vh;
        overflow: auto;
        .draw-menu-item{
            height: 2.59vh;
            display: flex;
            align-items: center;
            padding-left: 1.48vh;
            padding-right: 1.48vh;
            background: url('~@/assets/images/VisualProgress/draw_menu_bg.png') no-repeat center center;
            background-size: 100% 100%;
            color: #fff;
            margin-bottom: 1.48vh;
            cursor: pointer;
            .name{
                font-size: 1.48vh;
                text-shadow: 0px 0px 6px #06BDFB;
            }
            &:hover,
            &.active{
                background-image: url('~@/assets/images/VisualProgress/draw_menu_bg1.png');
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
    .draw-content{
        flex: 1;
        height: 100%;
    }
}

</style>
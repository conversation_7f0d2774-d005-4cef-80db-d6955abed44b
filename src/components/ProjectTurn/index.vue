<template>
  <div class="video-container">
    <video 
      ref="videoPlayer"
      width="100%" 
      height="100%" 
      controls
      autoplay
      muted
      loop
      @loadeddata="onVideoLoaded"
      @canplay="onVideoCanPlay"
    >
      您的浏览器不支持视频播放
    </video>
    
    <!-- 加载状态 -->
    <div v-if="showLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>视频加载中...</p>
    </div>
    
    <!-- 播放按钮 -->
    <div v-if="showPlayButton" class="play-button-overlay" @click="manualPlay">
      <div class="play-button">
        <i class="play-icon">▶</i>
        <span>点击播放</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getMapConfig } from '../../api/index'
export default {
  name: 'ProjectTurn',
  data() {
    return {
      videoLoaded: false,
      videoUrl: '',
      isPlaying: false,
      showPlayButton: false,
      showLoading: false,
      playAttempts: 0,
      maxPlayAttempts: 3,
      checkInterval: null
    }
  },
  mounted() {
    this.initVideo()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }
    
    // 清理视频事件监听器
    if (this.$refs.videoPlayer) {
      const video = this.$refs.videoPlayer
      video.removeEventListener('loadeddata', this.onVideoLoaded)
      video.removeEventListener('canplay', this.onVideoCanPlay)
    }
  },
  watch: {
    videoUrl(newUrl) {
      if (newUrl && this.$refs.videoPlayer) {
        console.log('videoUrl变化，重新设置视频源:', newUrl)
        const video = this.$refs.videoPlayer
        video.src = newUrl
        video.load()
        this.videoLoaded = false // 重置加载状态
        this.showLoading = true // 显示loading状态
        this.showPlayButton = false // 隐藏播放按钮
      }
    }
  },
  methods: {
    initVideo() {
      console.log('开始初始化视频...')
      
      getMapConfig('SCREEN_LOCATION_CONFIG').then(({ data }) => {
        let modeLayer = JSON.parse(data.value)
        console.log('获取到配置:', modeLayer)
        
        // 检查视频URL是否有效
        if (!modeLayer.video) {
          console.error('视频URL为空')
          this.$Message.error('未配置视频地址')
          return
        }
        
        this.videoUrl = modeLayer.video
        console.log('设置视频URL:', this.videoUrl)
        
        // 显示loading状态
        this.showLoading = true
        
        // 等待DOM更新后初始化视频
        this.$nextTick(() => {
          this.setupVideoPlayer()
        })
      }).catch(error => {
        console.error('获取视频配置失败:', error)
        this.$Message.error('获取视频配置失败')
        this.showLoading = false
      })
    },
    
    setupVideoPlayer() {
      if (!this.$refs.videoPlayer) {
        console.error('视频元素不存在')
        return
      }
      
      const video = this.$refs.videoPlayer
      console.log('设置视频播放器...')
      
      // 设置视频属性
      video.muted = true // 静音以支持自动播放
      video.loop = true  // 循环播放
      video.playsInline = true // 支持内联播放
      video.preload = 'auto' // 预加载视频
      
      // 重要：直接设置视频元素的src属性
      if (this.videoUrl) {
        console.log('直接设置视频src:', this.videoUrl)
        video.src = this.videoUrl
        // 强制重新加载视频
        video.load()
      }
      
      // 监听视频事件
      this.setupVideoEvents(video)
      
      // 检查视频是否已经加载完成
      if (video.readyState >= 2) {
        console.log('视频已经加载完成，直接尝试播放')
        this.videoLoaded = true
        this.showLoading = false
        this.attemptAutoplay()
      } else {
        console.log('视频正在加载中，等待加载完成事件...')
        
        // 添加定时检查，以防事件监听器没有正确触发
        let checkCount = 0
        const maxChecks = 10
        this.checkInterval = setInterval(() => {
          checkCount++
        
          if (video.readyState >= 2 && !this.videoLoaded) {
            console.log('定时检查发现视频已加载，设置状态并尝试播放')
            this.videoLoaded = true
            this.showLoading = false
            this.attemptAutoplay()
            clearInterval(this.checkInterval)
            this.checkInterval = null
          } else if (checkCount >= maxChecks) {
            console.log('达到最大检查次数，停止定时检查')
            clearInterval(this.checkInterval)
            this.checkInterval = null
            
            // 明确隐藏loading状态
            this.showLoading = false
            
            // 如果视频仍然没有加载，显示播放按钮
            if (!this.videoLoaded) {
              console.log('视频加载超时，显示播放按钮')
              this.showPlayButton = true
              this.$Message.warning('视频加载超时，请点击播放按钮手动播放')
            }
          }
        }, 500)
      }
    },
    
    setupVideoEvents(video) {
      // 视频加载完成
      video.addEventListener('loadeddata', () => {
        console.log('视频数据加载完成')
        this.videoLoaded = true
        this.showLoading = false
        console.log('videoLoaded状态已设置为true')
        this.attemptAutoplay()
      })
      
      // 视频可以播放
      video.addEventListener('canplay', () => {
        console.log('视频可以播放')
        if (!this.videoLoaded) {
          console.log('设置videoLoaded为true')
          this.videoLoaded = true
          this.showLoading = false
        }
        this.attemptAutoplay()
      })
      
      // 视频元数据加载完成
      video.addEventListener('loadedmetadata', () => {
        console.log('视频元数据加载完成')
        if (!this.videoLoaded) {
          console.log('设置videoLoaded为true')
          this.videoLoaded = true
          this.showLoading = false
        }
      })
      
      // 视频开始播放
      video.addEventListener('play', () => {
        console.log('视频开始播放')
        this.isPlaying = true
        this.showPlayButton = false
        this.showLoading = false
      })
      
      // 视频播放错误
      video.addEventListener('error', (e) => {
        console.error('视频播放错误:', e)
        console.error('错误详情:', video.error)
        this.$Message.error('视频播放失败')
        // 隐藏loading并显示播放按钮让用户手动重试
        this.showLoading = false
        this.showPlayButton = true
      })
      
      // 视频暂停
      video.addEventListener('pause', () => {
        console.log('视频暂停')
        this.isPlaying = false
      })
      
      // 视频结束
      video.addEventListener('ended', () => {
        console.log('视频播放结束')
        // 由于设置了loop，视频会自动重新播放
      })
      
      // 视频加载开始
      video.addEventListener('loadstart', () => {
        console.log('开始加载视频')
        this.videoLoaded = false
        this.showPlayButton = false
        this.showLoading = true
      })
      
      // 视频加载进度
      video.addEventListener('progress', () => {
        console.log('视频加载进度:', video.buffered.length > 0 ? video.buffered.end(0) : 0)
      })
      
      // 视频加载被中止
      video.addEventListener('abort', () => {
        console.log('视频加载被中止')
        this.showLoading = false
        this.showPlayButton = true
      })
      
      // 视频网络错误
      video.addEventListener('stalled', () => {
        console.log('视频加载停滞')
        this.showLoading = false
        this.showPlayButton = true
      })
    },
    
    onVideoLoaded() {
      console.log('视频加载完成')
      this.videoLoaded = true
      this.showLoading = false
      this.attemptAutoplay()
    },
    
    onVideoCanPlay() {
      console.log('视频可以播放')
      if (!this.videoLoaded) {
        this.videoLoaded = true
        this.showLoading = false
      }
      this.attemptAutoplay()
    },
    
    attemptAutoplay() {
      const video = this.$refs.videoPlayer
      console.log('尝试自动播放 - 当前状态:', {
        videoExists: !!video,
        videoLoaded: this.videoLoaded,
        isPlaying: this.isPlaying,
        readyState: video ? video.readyState : 'N/A',
        currentSrc: video ? video.currentSrc : 'N/A'
      })
      
      if (!video) {
        console.log('视频元素不存在，跳过自动播放')
        return
      }
      
      if (!this.videoLoaded) {
        console.log('视频未加载完成，跳过自动播放')
        return
      }
      
      // 如果已经播放中，不需要重复尝试
      if (this.isPlaying) {
        console.log('视频已在播放中')
        return
      }
      
      // 检查播放尝试次数
      if (this.playAttempts >= this.maxPlayAttempts) {
        console.log('已达到最大播放尝试次数，显示播放按钮')
        this.showLoading = false
        this.showPlayButton = true
        return
      }
      
      console.log(`尝试自动播放 (第${this.playAttempts + 1}次)`)
      this.playAttempts++
      
      // 确保视频属性设置正确
      video.muted = true
      video.playsInline = true
      
      // 尝试播放
      const playPromise = video.play()
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('视频自动播放成功')
            this.isPlaying = true
            this.showPlayButton = false
            this.showLoading = false
            this.playAttempts = 0 // 重置尝试次数
          })
          .catch(error => {
            console.log('自动播放失败:', error)
            
            // 如果是用户交互限制，显示播放按钮
            if (error.name === 'NotAllowedError') {
              console.log('浏览器阻止自动播放，需要用户交互')
              this.showLoading = false
              this.showPlayButton = true
            } else {
              // 其他错误，延迟重试
              setTimeout(() => {
                this.attemptAutoplay()
              }, 1000)
            }
          })
      }
    },
    
    // 手动播放方法
    manualPlay() {
      const video = this.$refs.videoPlayer
      if (video) {
        video.play().then(() => {
          console.log('手动播放成功')
          this.isPlaying = true
          this.showPlayButton = false
          this.showLoading = false
        }).catch(error => {
          console.error('手动播放失败:', error)
          this.$Message.error('播放失败')
          this.showLoading = false
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.video-container {
  width: 100%;
  height: calc(100vh - 76px); /* 减去顶部导航栏高度 */
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  video {
    width: 100%;
    height: 100%;
    object-fit: cover; // 保持视频比例并填满容器
    display: block;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }
  
  // 加载状态样式
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 10;
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid #fff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // 播放按钮样式
  .play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    z-index: 15;
    cursor: pointer;

    .play-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: white;
      font-size: 24px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);

      .play-icon {
        margin-bottom: 5px;
      }

      span {
        font-size: 16px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
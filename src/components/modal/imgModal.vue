<template>
	<Modal v-model="visible" :footer-hide="true" :mask-closable="false" :closable="false" width="75%" class-name="img-preview-modal">
		<div class="close" @click="close">
            <img src="@/assets/images/modal/icon_close.png" alt="">
        </div>
        <Carousel v-if="visible" v-model="curIndex" >
        <CarouselItem v-for="(item, index) in list" :key="index">
            <div class="img-carousel">
				<img :src="item" alt="">
			</div>
        </CarouselItem>
		</Carousel>
	</Modal>
</template>
<script>
export default {
	name: "imgModal",
	props: {

	},
	data() {
		return {
			visible: false,
			curIndex: 0,
			list: []
		}
	},
	methods: {
		show(list, index) {
			this.visible = true
			this.list = list
			this.curIndex = index || 0
		},
		close() {
			this.visible = false
			this.list = []
		}
	}
};
</script>
<style lang="less">
.img-preview-modal{
    .ivu-modal-content{
        background: url('~@/assets/images/modal/modalContent3.png') no-repeat center center;
        background-size: 100% 100%;
    }
    .close{
        position: absolute;
        top: 1vh;
        right: 2.48vh;
        cursor: pointer;
        width: 2.4vh;
        z-index: 99;
        img{
            width: 100%;
        }
    }
}
</style>
<style lang="less" scoped>

.img-carousel {
	width: 100%;
	height: 80vh;
	img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}
</style>
<template>
  <div class="Modal">
    <Modal
      :width="610"
      v-model="visibleW610h380"
      :title="title"
      footer-hide
      :mask-closable="false"
    >
      <slot></slot>
      <div slot="close">
        <div class="closeModal" @click="cancel"></div>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  props: ["visibleW610h380", "title"],
  data() {
    return {};
  },
  methods: {
    cancel() {
      this.$emit("update:visibleW610h380", false);
    },
  },
};
</script>

<style lang="less" scoped>
.Modal {
  pointer-events: auto;
  z-index: 999;
}

/deep/ .ivu-modal-mask {
  z-index: 999999 !important;
}

/deep/ .ivu-modal-wrap {
  z-index: 999999 !important;
}

/deep/ .ivu-modal-content {
  background-image: linear-gradient(to bottom, #1d45a2, #051f5a);
  height: calc(380px);
  border-radius: 0;
  color: #fff;
  z-index: 999;
}
/deep/ .ivu-modal-header {
  padding: 0;
  height: 50px;
  background: url(../../assets/images/modal/modalTitle3.png) no-repeat center
    center;
  background-size: 110% 120%;
  background-position-y: -2px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 0;
  .ivu-modal-header-inner {
    text-align: center;
    color: transparent;
    font-family: "YouSheBiaoTiHei";
    // 渐变字体
    background-image: linear-gradient(to bottom, #e2ebfd, #adc6fa);
    background-image: -webkit-linear-gradient(to bottom, #e2ebfd, #adc6fa);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 25px;
  }
}

/deep/ .ivu-modal-body {
  height: calc(100% - 50px);
  background: url(../../assets/images/modal/modalContent3.png) no-repeat center
    center;
  background-size: 100% 100%;
}

.closeModal {
  width: 35px;
  height: 40px;
  background: url(../../assets/images/modal/delete.png) no-repeat center center;
  background-size: 100% 100%;
}
</style>

<template>
  <div v-if="visible" class="simple-hook-video-modal" @click.self="closeModal">
    <div class="popup-content">
      <!-- 头部 -->
      <div class="popup-header">
        <div class="popup-title">{{ deviceData.name || '塔吊' }} - 吊钩可视化</div>
        <span class="popup-close" @click="closeModal">×</span>
      </div>

      <div class="popup-body">
        <!-- 主视频区域和设备列表 -->
        <div class="video-main-container">
          <!-- 左侧设备列表 -->
          <div class="device-list-panel">
            <div class="device-list-header">
              <span class="header-text">视频设备</span>
              <span class="device-count">({{ deviceList.length }}个)</span>
            </div>
            <div class="device-list">
              <div
                v-for="device in deviceList"
                :key="device.id"
                class="device-item"
                :class="{
                  'active': currentDevice && currentDevice.id === device.id
                }"
                @click="switchDevice(device)"
              >
                <!-- <div class="device-icon">📹</div> -->
                <div class="device-info">
                  <div class="device-name">{{ device.name }}</div>
                  <div class="device-location">{{ device.areaPath || '未知位置' }}</div>
                </div>
                <div class="device-status" :class="device.onlineState === 1 ? 'online' : 'offline'">
                  {{ device.onlineState === 1 ? '在线' : '离线' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧视频显示区域 -->
          <div class="video-display-area">
            <div v-if="currentDevice" class="video-container">
              <!-- 视频播放器 -->
              <div class="video-player-wrapper">
                <div class="video-header">
                  <span class="video-title">{{ currentDevice.name }}</span>
                  <span class="video-location">{{ currentDevice.areaPath || '未知位置' }}</span>
                </div>
                <div class="video-content">
                  <div
                    :id="videoPlayerId"
                    class="video-player"
                    v-if="currentDevice && currentDevice.onlineState === 1"
                  ></div>

                  <!-- 设备离线或无设备时的占位符 -->
                  <div v-if="!currentDevice || currentDevice.onlineState !== 1" class="video-placeholder">
                    <div class="placeholder-content">
                      <!-- <div class="camera-icon">📹</div> -->
                      <div class="device-name">{{ currentDevice ? currentDevice.name : '无设备' }}</div>
                      <div class="status-text">{{ currentDevice ? '设备离线' : '请选择设备' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 无设备时的占位符 -->
            <div v-else class="no-device">
              <div class="no-device-content">
                <div class="no-device-icon">📹</div>
                <div class="no-device-text">暂无可用的视频设备</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'SimpleHookVideoModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    deviceData: {
      type: Object,
      default: () => ({})
    },
    videoDeviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      currentDevice: null,
      videoPlayer: null
    }
  },
  computed: {
    // 显示所有设备（包括离线设备）
    deviceList() {
      return this.videoDeviceList || []
    },
    // 视频播放器ID
    videoPlayerId() {
      return `hook-video-player-${this.currentDevice?.id || 'default'}`
    }
  },
  watch: {
    value(newVal) {
      this.visible = newVal
      if (newVal) {
        this.initModal()
      } else {
        this.cleanup()
      }
    },
    visible(newVal) {
      this.$emit('input', newVal)
    },
    videoDeviceList: {
      handler(newList) {
        if (newList && newList.length > 0) {
          // 选择第一个设备（不管在线状态）
          this.currentDevice = newList[0]
          this.$nextTick(() => {
            this.initVideoPlayer()
          })
        } else {
          this.currentDevice = null
        }
      },
      immediate: true
    },
    currentDevice: {
      handler(newDevice, oldDevice) {
        if (newDevice && newDevice !== oldDevice) {
          this.$nextTick(() => {
            this.initVideoPlayer()
          })
        }
      }
    }
  },
  methods: {
    initModal() {
      console.log('初始化简化吊钩可视化弹窗', this.deviceData, this.videoDeviceList)

      // 关闭固定告警面板中的告警（不影响其他弹框）
      if (this.$alert && this.$alert.clearFixed) {
        this.$alert.clearFixed()
      }

      // 选择默认设备：选择第一个设备（不管在线状态）
      if (this.deviceList && this.deviceList.length > 0) {
        this.currentDevice = this.deviceList[0]
        // 等待DOM更新后再初始化视频播放器
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.initVideoPlayer()
          })
        })
      }
    },

    switchDevice(device) {
      console.log('切换视频设备:', device)
      // 清理当前播放器
      this.cleanup()
      this.currentDevice = device
    },

    // 获取视频流URL和AccessToken
    getVideoConfig(device) {
      if (!device) return null;

      let streamUrl = null;
      let accessToken = null;

      // 从deviceAttributeStatusList中获取配置
      if (device.deviceAttributeStatusList && Array.isArray(device.deviceAttributeStatusList)) {
        const ezOpenAttr = device.deviceAttributeStatusList.find(attr => attr.propCode === 'ezopen');
        const tokenAttr = device.deviceAttributeStatusList.find(attr => attr.propCode === 'accessToken');

        if (ezOpenAttr && ezOpenAttr.propValue) {
          streamUrl = ezOpenAttr.propValue;
        }
        if (tokenAttr && tokenAttr.propValue) {
          accessToken = tokenAttr.propValue;
        }
      }

      // 备选：使用设备直接属性
      if (!streamUrl && device.streamUrl) {
        streamUrl = device.streamUrl;
      }
      if (!accessToken && device.accessToken) {
        accessToken = device.accessToken;
      }

      return {
        streamUrl,
        accessToken
      };
    },

    initVideoPlayer() {
      if (!this.currentDevice) {
        console.warn('当前设备为空，无法初始化视频播放器');
        return;
      }

      console.log('初始化EZUIKit视频播放器:', this.currentDevice);

      const videoConfig = this.getVideoConfig(this.currentDevice);
      if (!videoConfig || !videoConfig.streamUrl || !videoConfig.accessToken) {
        console.warn('无法获取视频流地址或AccessToken:', videoConfig);
        return;
      }

      // 等待DOM完全更新
      this.$nextTick(() => {
        const playerId = this.videoPlayerId;
        console.log('查找视频容器ID:', playerId);
        console.log('当前设备:', this.currentDevice);
        console.log('设备在线状态:', this.currentDevice?.onlineState);

        // 等待一小段时间确保DOM完全渲染
        setTimeout(() => {
          const container = document.getElementById(playerId);
          if (!container) {
            console.error('视频容器未找到:', playerId);
            console.log('当前DOM中的所有视频相关元素:', document.querySelectorAll('[id*="hook-video-player"]'));
            console.log('弹窗是否可见:', this.visible);
            return;
          }

          console.log('找到视频容器:', container);
          this.createVideoPlayer(container, playerId);
        }, 100);
      });
    },

    createVideoPlayer(container, playerId) {
      const videoConfig = this.getVideoConfig(this.currentDevice);

      try {
        // 清理之前的播放器
        this.cleanup();

        // 检查是否有EZUIKit
        if (typeof EZUIKit === 'undefined') {
          console.error('EZUIKit 未加载');
          return;
        }

        // 获取容器尺寸
        const containerRect = container.getBoundingClientRect();
        const width = Math.floor(containerRect.width) || 400;
        const height = Math.floor(containerRect.height) || 300;

        console.log(`创建EZUIKit播放器: ${this.currentDevice.name}, 容器尺寸: ${width}x${height}`);

        // 创建EZUIKit播放器
        this.videoPlayer = new EZUIKit.EZUIKitPlayer({
          autoplay: true,
          id: playerId,
          accessToken: videoConfig.accessToken,
          url: videoConfig.streamUrl,
          width: width,
          height: height,
          template: 'standard', // standard-标准版 / simple-简洁版
          // 添加回调函数来监听播放器状态
          openSoundCallBack: data => console.log(`${this.currentDevice.name} 开启声音回调:`, data),
          closeSoundCallBack: data => console.log(`${this.currentDevice.name} 关闭声音回调:`, data),
          startSaveCallBack: data => console.log(`${this.currentDevice.name} 开始录像回调:`, data),
          stopSaveCallBack: data => console.log(`${this.currentDevice.name} 录像回调:`, data),
          capturePictureCallBack: data => console.log(`${this.currentDevice.name} 截图成功回调:`, data),
          fullScreenCallBack: data => console.log(`${this.currentDevice.name} 全屏回调:`, data),
          getOSDTimeCallBack: data => console.log(`${this.currentDevice.name} 获取OSDTime回调:`, data)
        });

        if (!this.videoPlayer) {
          console.error('EZUIKit播放器对象创建失败');
          return;
        }

        console.log('EZUIKit视频播放器初始化成功');

        // 延迟播放
        setTimeout(() => {
          if (this.videoPlayer && typeof this.videoPlayer.play === 'function') {
            try {
              this.videoPlayer.play();
              console.log('视频开始播放');
            } catch (error) {
              console.warn('自动播放失败:', error);
            }
          }
        }, 1000);

      } catch (error) {
        console.error('初始化EZUIKit视频播放器失败:', error);
      }
    },

    closeModal() {
      this.visible = false
    },

    cleanup() {
      // 清理EZUIKit视频播放器资源
      if (this.videoPlayer) {
        try {
          console.log('开始清理EZUIKit播放器...');

          // 检查播放器是否有stop方法
          if (typeof this.videoPlayer.stop === 'function') {
            this.videoPlayer.stop();
            console.log('EZUIKit播放器已停止');
          }

          // 清理可能存在的iframe
          const playerId = `EZUIKitPlayer-${this.videoPlayerId}`;
          const iframe = document.getElementById(playerId);
          if (iframe && iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
            console.log('EZUIKit播放器iframe已清理');
          }

        } catch (error) {
          console.warn('清理EZUIKit视频播放器失败:', error);

          // 强制清理DOM
          try {
            const container = document.getElementById(this.videoPlayerId);
            if (container) {
              container.innerHTML = '';
            }
          } catch (cleanupError) {
            console.error('强制清理播放器DOM失败:', cleanupError);
          }
        }
        this.videoPlayer = null;
      }
    }
  },

  beforeDestroy() {
    this.cleanup()
  }
}
</script>

<style lang="less" scoped>
.simple-hook-video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  width: 900px;
  max-height: 80vh;
  background: url(../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 900px 600px;
  color: #fff;
  font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px 10px;
  position: relative;
  z-index: 10;

  .popup-title {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .popup-close {
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    line-height: 1;
    transition: color 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

    &:hover {
      color: #ff6b6b;
    }
  }
}

.popup-body {
  flex: 1;
  padding: 10px 30px 30px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.video-main-container {
  display: flex;
  gap: 20px;
  height: 400px;
}

.device-list-panel {
  width: 280px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.device-list-header {
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;

  .header-text {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
  }

  .device-count {
    color: #bbb;
    font-size: 12px;
  }
}

.device-list {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &.active {
    background: rgba(74, 144, 226, 0.3);
  }

  .device-icon {
    font-size: 16px;
    margin-right: 12px;
    color: #4a90e2;
  }

  .device-info {
    flex: 1;
    color: #fff;

    .device-name {
      font-size: 13px;
      margin-bottom: 4px;
    }

    .device-location {
      font-size: 11px;
      color: #bbb;
    }
  }

      .device-status {
      font-size: 11px;
      padding: 2px 6px;
      border-radius: 3px;

      &.online {
        background: #27ae60;
        color: #fff;
      }

      &.offline {
        background: #e74c3c;
        color: #fff;
      }
    }
}

.video-display-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .video-container {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;

    .video-player-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .video-header {
        padding: 12px 16px;
        background: rgba(0, 0, 0, 0.7);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .video-title {
          color: #fff;
          font-size: 14px;
          font-weight: bold;
        }

        .video-location {
          color: #bbb;
          font-size: 12px;
        }
      }

      .video-content {
        flex: 1;
        position: relative;
        background: #000;

        .video-player {
          width: 100%;
          height: 100%;
          background: #000;
          position: relative;

          // EZUIKit播放器样式适配 - 确保完全贴合
          /deep/ .ezuikit-container {
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            box-sizing: border-box !important;

            .ezuikit-video-container {
              width: 100% !important;
              height: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              border: none !important;
              box-sizing: border-box !important;
              position: relative !important;

              video {
                width: 100% !important;
                height: 100% !important;
                object-fit: fill !important;
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                box-sizing: border-box !important;
              }
            }

            // 隐藏可能存在的控制栏边距
            .ezuikit-controls {
              margin: 0 !important;
              padding: 0 !important;
            }

            // 重置所有可能的内部元素
            * {
              margin: 0 !important;
              padding: 0 !important;
              box-sizing: border-box !important;
            }

            // 确保播放器本身没有边框和间距
            .ezuikit-player {
              width: 100% !important;
              height: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              border: none !important;
              box-sizing: border-box !important;
            }
          }

          // 兼容原有的video标签样式
          /deep/ video {
            width: 100% !important;
            height: 100% !important;
            object-fit: fill !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            box-sizing: border-box !important;
          }
        }

        .video-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

          .placeholder-content {
            text-align: center;
            color: #fff;

            .camera-icon {
              font-size: 48px;
              margin-bottom: 12px;
              opacity: 0.7;
            }

            .device-name {
              font-size: 16px;
              margin-bottom: 8px;
            }

            .status-text {
              font-size: 14px;
              color: #bbb;
            }
          }
        }
      }
    }
  }



  .no-device {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .no-device-content {
      text-align: center;
      color: #bbb;

      .no-device-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }

      .no-device-text {
        font-size: 16px;
      }
    }
  }
}
</style>

<template>
    <div class="Modal">
        <Modal
            :width="900"
            v-model="visibleW900h486"
            :title="title"
            footer-hide
            :mask-closable="false"
            >
            <slot></slot>
            <div slot="close">
                <div class="closeModal" @click="cancel"></div>
            </div>
        </Modal>
    </div>
</template>

<script>
  export default {
    props: ['visibleW900h486', 'title'],
    data() {
        return {}
    },
    methods: {
        cancel() {
            this.$emit('update:visibleW900h486', false)
        }
    }
  }
</script>

<style lang="less" scoped>
.Modal {
    pointer-events: auto;
}
/deep/ .ivu-modal-content {
    background-image: linear-gradient(to bottom, #1D45A2, #051F5A);
    height: 586px;
    border-radius: 0;
    color: #fff
}
/deep/ .ivu-modal-header {
    padding: 0;
    height: 50px;
    background: url(../../assets/images/modal/modalTitle.png) no-repeat center center;
    background-size: 110% 120%;
    background-position-y: -2px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 0;
    .ivu-modal-header-inner {
        text-align: center;
        color: transparent;
        font-family: 'YouSheBiaoTiHei';
        // 渐变字体
        background-image: linear-gradient(to bottom, #E2EBFD, #ADC6FA);
        background-image: -webkit-linear-gradient(to bottom, #E2EBFD, #ADC6FA);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 25px;
    }
}

/deep/ .ivu-modal-body {
    height: calc(100% - 50px);
    background: url(../../assets/images/modal/modalContent.png) no-repeat center center;
    background-size: 100% 100%;
}

.closeModal {
    width: 35px;
    height: 40px;
    background: url(../../assets/images/modal/delete.png) no-repeat center center;
    background-size: 100% 100%;
}

</style>

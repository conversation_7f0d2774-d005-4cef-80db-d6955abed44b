<template>
  <div class="point" >
    <div>
      <div class="point_drop">
        <div class="point_title" @click="pointInfo(pointData)">{{ pointData.text }}</div>
      </div>
      <div class="weather-info">
        <img :src="imgFormat(rainInfo.icon)" class="weather-icon" />
        <div class="weather-temp">
          {{ rainInfo.temp }}<span class="weather-temp-unit">℃</span>
        </div>
        <span class="weather-wind">{{ numberToChinese(rainInfo.windScale) }}级</span>
      </div>
    </div>
    
    <div class="popup" v-if="isClick">
      <PointPopup :project="pointData" :router="router" :store="store"/>
    </div>
  </div>
</template>

<script>
import { numberToChinese } from '../../../utils/common.js'
import PointPopup from './pointPopup.vue'
import { getWeather } from '@/api/xxindex'
import Bubble from '../Bubble.js';

export default{
  name: 'point',
  data() {
    return {
      isClick: false,
      rainInfo: {},
      pointId: null, // 添加点位唯一标识
      bubbles: null,
    }
  },
  components: {
    PointPopup
  },
  props: {
    pointData: {
      type: Object,
      default: () => {}
    },
    router: {
      type: Object,
      default: () => {}
    },
    store: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    // 生成点位唯一标识
    this.pointId = this.pointData.id || `point_${Date.now()}_${Math.random()}`

    this.getWeather()
  },
  methods: {
    imgFormat(iconCode) {
      if (!iconCode) {
        try {
          return require('@/assets/rainIcon/100.png')
        } catch (e) {
          return ''
        }
      }
      
      try {
        return require(`@/assets/rainIcon/${iconCode}.png`)
      } catch (error) {
        try {
          return require('@/assets/rainIcon/100.png')
        } catch (e) {
          return ''
        }
      }
    },
    pointInfo(pointData) {
      // 然后显示当前popup
      this.isClick = true
      // 触发项目详情面板显示事件
      this.$bus.emit('clickPoint', pointData)
      console.log(pointData, this.store, this.router)

      // 创建小弹窗
      this.bubbles = new Bubble({
        viewer: window.viewer,
        data: {
          ...pointData,
          position: [
            Number(pointData.lon),
            Number(pointData.lan)
          ],
          above: true,
          offset: { offsetX: 10, offsetY: -10 }
        },
        type: 0,
        component: 'PointPopup',
        store: this.store,
        router: this.router
      });

      const closeOnClickOutside = (e) => {
        if (!this.bubbles?.vueContainer?.contains(e.target)) {
          this.bubbles?.windowClose()
          document.removeEventListener('click', closeOnClickOutside)
        }
      }
      setTimeout(() => {
        document.addEventListener('click', closeOnClickOutside)
      }, 0);
    },
    getWeather() {
      // 获取当日风速
      console.log(this.pointData)
      let host = 'np2mtdt4fv.re.qweatherapi.com';
      // 版本 v7
      let v = '/v7'
      let lon = Number(this.pointData.lon);
      let lan = Number(this.pointData.lan);
      // 检查经纬度是否为有效数字，若不是则使用 0 作为默认值
      lon = isNaN(lon) ? 0 : lon;
      lan = isNaN(lan) ? 0 : lan;
      let longitude = lon.toFixed(2) + ',' + lan.toFixed(2);
      getWeather(`https://${host}${v}/weather/now?location=${longitude}`).then(res => {
        console.log(res)
        if (res.code == '200') {
          this.rainInfo = res.now;
        }
      })
    },
    
    /**
     * 将数字转换为中文大写数字（0-9）
     * @param {string|number} num - 输入数字
     * @returns {string} 中文大写数字
     */
    numberToChinese(num) {
      console.log('原始输入:', num, '类型:', typeof num); // 调试日志
      // 1. 将输入统一转换为数字类型
      const parsedNum = typeof num === 'string' ? parseFloat(num) : num;
      
      // 2. 验证数字有效性（支持字符串转数字和纯数字输入）
      if (typeof parsedNum !== 'number' || isNaN(parsedNum) || parsedNum < 0 || parsedNum > 10) {
        console.warn('无效的风力等级:', num);
        return '';
      }
      
      // 3. 数字转中文映射
      const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
      return chineseNumbers[Math.floor(parsedNum)];
    }
  },
  beforeDestroy() {
    this.$bus.off('clickPoint')
  }
}
</script>

<style lang="less" scoped>
.point {
  position: relative;
  width: 250px;
  height: 215.15px;
  z-index: 1000;
  .point_drop {
    width: 100%;
    height: 100%;
    position: absolute;
    background: url(../../../assets/images/map/point_bg.png) no-repeat;
    background-size: 100% 100%;
    
    .point_title {
      font-family: 'PingFang SC';
      font-size: 16px;
      color: #fff;
      position: absolute;
      top: 37px;
      right: 0;
      text-align: left;
      width: calc(100% - 80px);
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  }
  .weather-info {
    position: absolute;
    top: -10px;
    right: 0;
    background: url(../../../assets/images/map/weather_bg.png) no-repeat;
    background-size: 100% 100%;
    width: 131.76px;
    height: 33.81px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    color: #fff;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .weather-icon {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      user-drag: none;
      -webkit-user-drag: none;
      -moz-user-drag: none;
      -ms-user-drag: none;
      pointer-events: none;
    }
    .weather-temp {
      font-size: 22px;
      color: #fff;
      font-weight: bold;
      text-shadow: 1px 1px 2px #000;
      margin-right: 8px;
      .weather-temp-unit {
        font-size: 16px;
      }
    }
    .weather-wind {
      font-size: 18px;
      color: #fff;
      text-shadow: 1px 1px 2px #000;
    }
  }
  .popup {
    position: absolute;
    top: -200px;
    left: -115px;
  }
}
</style>
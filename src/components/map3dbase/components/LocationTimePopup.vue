<template>
  <div class="location-time-popup">
    <div class="popup-header">
      <div class="popup-title">点位信息</div>
      <span class="popup-close" @click="$emit('close')">×</span>
    </div>
    <div class="popup-body">
      <div class="info-item">
        <span class="label">经度：</span>
        <span class="value">{{ longitude }}°</span>
      </div>
      <div class="info-item">
        <span class="label">纬度：</span>
        <span class="value">{{ latitude }}°</span>
      </div>
      <div class="info-item">
        <span class="label">时间：</span>
        <span class="value">{{ currentTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LocationTimePopup',
  props: {
    propsData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    longitude() {
      return this.propsData.longitude || 0
    },
    latitude() {
      return this.propsData.latitude || 0
    },
    currentTime() {
      return this.propsData.time || this.formatCurrentTime()
    }
  },
  methods: {
    formatCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style lang="less" scoped>
.location-time-popup {
  min-width: 260px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'PingFang SC', Arial, sans-serif;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
  }

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;

    .popup-title {
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .popup-close {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      color: #ffffff;
      border-radius: 50%;
      transition: all 0.2s ease;
      opacity: 0.8;

      &:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
      }
    }
  }

  .popup-body {
    padding: 16px;
    position: relative;
    z-index: 1;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.08);

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 14px;
        color: #e6f3ff;
        font-weight: 500;
      }

      .value {
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }
  }

  // 添加三角形箭头
  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #1e3c72;
    z-index: 2;
  }
}
</style> 
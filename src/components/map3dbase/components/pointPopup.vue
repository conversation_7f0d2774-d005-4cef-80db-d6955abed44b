<template>
  <div class="point_Popup">
    <div class="card-header">项目信息</div>
    <div class="card-content">
      <div class="project-title">
        <img src="../../../assets/images/icon/group.png" class="iconFont">
        <span class="title-text">{{ propsData.name }}</span>
      </div>
      <div class="info-row" v-for="item in infoList" :key="item.label">
        <span class="info-label">{{ item.label }}：</span>
        <span class="info-value">{{ propsData[item.value] }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">项目地址：</span>
        <el-button type="primary" size="small" class="enter-btn" @click="clickGetInfo(propsData)">点击进入</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { setStore, setSessionStore, removeSessionStore } from '@/utils/storage';
import { userSelectProject, getLoginUserInfo } from '@/api/project';
export default {
  name: 'pointPopup',
  data() {
    return {
      infoList: [
        { label: '建设单位', value: 'build' },
        { label: '设计单位', value: 'design' },
        { label: '施工单位', value: 'construct' },
        { label: '监理单位', value: 'supervise' }
      ],
    }
  },
  props: {
    propsData: {
      type: Object,
      default: () => {}
    },
    project: {
      type: Object,
      default: () => {}
    },
    router: {
      type: Object,
      default: () => {}
    },
    store: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    console.log('Popup mounted', this.propsData);
    console.log('Store in pointPopup:', this.store);
    console.log('Router in pointPopup:', this.router);
  },
  created() {
    console.log('Popup created');
  },
  beforeDestroy() {
    console.log('Popup beforeDestroy');
  },
  destroyed() {
    console.log('Popup destroyed');
  },
  methods: {
    clickGetInfo(item) {
      console.log(item, this.store, this.router)
      if (!this.store) {
        console.error('❌ Store 未定义！');
        return;
      }
      
      if (!this.router) {
        console.error('❌ Router 未定义！');
        return;
      }
      
      let activeItem = {
        active: true,
        name: "全景感知"
      }
      
      try {
        this.store.commit('menu/setActiveMenu', activeItem)// 切换菜单
        console.log('✅ 菜单切换成功');
      } catch (error) {
        console.error('❌ 菜单切换失败:', error);
      }
      
      console.log('点击进入项目', item);
      this.userSelectProject(item);
    },
    userSelectProject (item) {
      let linkappUser = item.linkappUser
      if (!linkappUser.username) return;

      // 将当前项目名存入缓存
      setStore('projectName', item.platformProjectName);
      setStore('projectId', item.id);

      userSelectProject({ username: linkappUser.username }).then((res) => {
        console.log('用户选择项目返回', res);
          if (res.success) {
            this.loginSuccess(item);
          }
        })
        .finally(() => (this.logining = false));
    },
    loginSuccess (projInfo) {
      getLoginUserInfo().then((res) => {
        console.log('获取用户信息成功', res);
        if (res.success) {
          const data = res.data;
          const authData = res.data.auth;
          let auth = {};
          authData.forEach((item) => {
            auth[item.code] = true;
          });

          // 将用户信息存入缓存
          setStore('auth', JSON.stringify(auth || '{}'));
          setStore('user', JSON.stringify(data.user)); 

          // 打开新窗口，添加userName参数
          const route = this.router.resolve({
            name: 'pointScreen',
            query: {
              projectId: projInfo.id,
              userName: projInfo.linkappUser.username // 添加userName参数
            }
          })
          window.open(route.href, '_blank')
        } 
      });
    }
  }
}
</script>

<style lang="less" scoped>
.point_Popup {
  width: 508px;
  height: 390.13px;
  background: url(../../../assets/images/map/popup_bg.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 10000;

  .card-header {
    position: absolute;
    top: 12px;
    left: 210px;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    letter-spacing: 2px;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 8px #1e90ff44 inset;
  }

  .card-content {
    padding: 18px 28px 0 28px;
    position: absolute;
    top: 40px;
    width: 100%;

    .project-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      color: #fff;
      font-weight: bold;
      margin-bottom: 18px;

      .iconFont {
        width: 24px;
        color: #fff;
        margin-right: 8px;
        filter: drop-shadow(0 0 4px #1e90ff);
      }

      .title-text {
        color: #fff;
        text-shadow: 0 0 6px #1e90ff88;
      }
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .info-label {
        width: 90px;
        color: #b6d6ff;
        font-size: 15px;
        flex-shrink: 0;
      }
      .info-value {
        color: #fff;
        font-size: 15px;
        word-break: break-all;
      }

      .enter-btn {
        margin-left: 0;
        background: linear-gradient(90deg, #1e90ff 60%, #3fa9f5 100%);
        border: none;
        color: #fff;
        font-weight: bold;
        box-shadow: 0 0 8px #1e90ff88;
      }
      .enter-btn:hover {
        background: #3fa9f5;
        color: #fff;
      }
    }
  }
}
</style>
<template>
  <div class="popup-content" :class="popupTypeClass">
    <!-- Loading 界面 -->
    <div v-if="shouldShowLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载设备信息...</div>
    </div>
    
    <!-- 实际内容 -->
    <template v-if="!shouldShowLoading">
      <div class="popup-header">
        <div v-if="showAlertStatus" class="popup-alert">
          <div v-if="!hasAlert" class="alert-badge no-alert">无告警</div>
          <div v-if="hasAlert" class="alert-badge has-alert">有告警</div>
        </div>
        <div class="popup-title">{{ templateData?.name || '设备信息加载失败' }}</div>
        <span class="popup-close" @click="$emit('close')">×</span>
      </div>
    <div class="popup-body">
      <!-- 动态设备信息列表 -->
      <ul class="popup-list" :class="currentTemplate.listClass">
        <li v-for="field in currentTemplate.fields" :key="field.key" v-show="field.show">
          <span class="label">{{ field.label }}</span>
          <span class="value">{{ getFieldValue(field) }}</span>
        </li>
      </ul>
      
      <!-- 动态监测数据网格 -->
      <div v-if="currentTemplate.showDataTable" class="popup-table">
        <div 
          v-for="(row, rowIndex) in currentTemplate.dataRows" 
          :key="rowIndex" 
          class="table-row"
        >
          <div 
            v-for="cell in row" 
            :key="cell.key"
            class="table-cell"
            :class="cell.class"
          >
            <div class="cell-title">{{ cell.title }}</div>
            <div class="cell-value">
              {{ getFieldValue(cell) }}<span class="unit">{{ cell.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- AI识别主机图片展示 -->
      <div v-if="isAIHost && propsData?.aiRecordImg" class="ai-image-container">
        <el-image 
          style="width: 100%; height: 100%"
          :src="propsData.aiRecordImg" 
          :preview-src-list="[propsData.aiRecordImg]">
        </el-image>

      </div>

      <!-- 动态视频容器 -->
      <div v-if="currentTemplate.showVideo" class="video-container">
        <DynamicVideoGrid :cameraList="transformedCameraList" :showInfoBar="false" />
      </div>

      <!-- 动态扩展模块 -->
      <div 
        v-for="module in currentTemplate.extraModules" 
        :key="module.key"
        v-show="module.show"
        :class="module.class"
      >
        <div class="info-header">
          <span class="header-icon">◆</span>
          <span class="header-text">{{ module.title }}</span>
        </div>
        <ul class="popup-list">
          <li v-for="field in module.fields" :key="field.key">
            <span class="label">{{ field.label }}</span>
            <span class="value">{{ getFieldValue(field) }}</span>
          </li>
        </ul>
      </div>
    </div>
    </template>
  </div>
</template>

<script>
import { getDeviceInfo, getMechanicalDetail } from '@/api/index.js'
import DynamicVideoGrid from '@/components/ProjectVideo/components/DynamicVideoGrid.vue'

export default {
  name: 'CustomPopup',
  components: {
    DynamicVideoGrid
  },
  props: {
    propsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      templateData: {},
      loading: true, // 添加loading状态
    }
  },
  watch: {
    // 监控loading状态变化
    loading(newVal, oldVal) {
      console.log(`🔄 loading状态变化: ${oldVal} -> ${newVal}`)
    },
    // 监控templateData变化
    templateData: {
      handler(newVal, oldVal) {
        console.log('📊 templateData变化:', newVal)
        
        // 视频类型使用DynamicVideoGrid组件自动处理
        if (this.isVideoType && newVal && Object.keys(newVal).length > 0) {
          console.log('📹 视频设备数据已更新，DynamicVideoGrid组件将自动处理')
        }
      },
      deep: true
    }
  },
  computed: {
    // 获取设备类型名称
    equipmentTypeName() {
      // 如果是机械设备接口返回的数据（通过判断是否存在type字段）
      if (this.templateData?.type) {
        return '机械';
      }
      
      // 普通设备接口返回的数据，使用deviceTypeName
      return this.templateData?.deviceTypeName || '未知设备';
    },
    
    // 判断是否为机械类设备
    isMachineType() {
      // 如果是机械设备接口返回的数据
      if (this.templateData?.type) {
        const machineTypeMap = {
          '1': '装载机',
          '2': '压路机',
          '3': '旋挖钻',
          '4': '挖掘机',
          '5': '汽车起重机',
          '6': '门式起重机',
          '7': '塔式起重机',
          '8': '随车起重机',
          '9': '洒水车',
          '10': '混凝土泵车',
          '11': '发电机',
          '12': '冲击钻',
          '13': '叉车'
        };
        return !!machineTypeMap[this.templateData.type];
      }
      
      // 普通设备接口返回的数据
      const typeName = this.templateData?.deviceTypeName;
      const machineTypes = ['机械', '机械监测', '吊车监测', '塔吊监测'];
      return machineTypes.includes(typeName);
    },
    
    // 判断是否为视频监控设备
    isVideoType() {
      const typeName = this.equipmentTypeName;
      const videoTypes = ['视频监控'];
      return videoTypes.includes(typeName);
    },
    
    // 判断是否为电力监测设备
    isPowerType() {
      const typeName = this.equipmentTypeName;
      const powerTypes = ['电表', '断路器', '电气火灾'];
      return powerTypes.includes(typeName);
    },
    
    // 判断是否为智能设备
    isSmartType() {
      const typeName = this.equipmentTypeName;
      const smartTypes = ['北斗定位'];
      return smartTypes.includes(typeName);
    },
    
    isHandTerminalType() {
      const typeName = this.equipmentTypeName;
      const handTerminalTypes = ['手持终端'];
      return handTerminalTypes.includes(typeName);
    },
    // 判断是否为通用设备
    isGeneralType() {
      const typeName = this.equipmentTypeName;
      const generalTypes = ['通用设备'];
      return generalTypes.includes(typeName);
    },
    
    // 判断是否为AI识别主机
    isAIHost() {
      return this.equipmentTypeName === 'AI识别主机';
    },
    
    // 计算弹窗类型样式类名
    popupTypeClass() {
      if (this.isMachineType) {
        return 'machine-type';
      } else if (this.isVideoType) {
        return 'video-type';
      } else if (this.isPowerType) {
        return 'power-type';
      } else if (this.isSmartType) {
        return 'smart-type';
      } else if (this.isHandTerminalType) {
        return 'hand-terminal-type';
      } else if (this.isGeneralType) {
        return 'general-type';
      } else if (this.isAIHost) {
        return 'ai-host-type';
      }
      return 'default-type';
    },

    // 动态模板配置
    currentTemplate() {
      const typeName = this.equipmentTypeName;
      
      // 机械类设备模板（机械、吊车监测）
      if (this.isMachineType) {
        return {
          listClass: '',
          showDataTable: true,
          showVideo: false,
          fields: [
            {
              key: 'equipmentName',
              label: typeName === '机械' ? '机械名称：' : '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'equipmentCode',
              label: typeName === '机械' ? '机械编号：' : '设备编号：',
              dataKey: 'code',
              defaultValue: '',
              show: true
            },
            {
              key: 'craneName',
              label: '绑定吊车：',
              dataKey: 'bindMachine',
              defaultValue: '',
              show: typeName === '吊车监测'
            },
            {
              key: 'craneName',
              label: '绑定塔吊：',
              dataKey: 'bindMachine',
              defaultValue: '',
              show: typeName === '塔吊监测'
            },
            {
              key: 'maintainer',
              label: '维护人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: typeName === '机械'
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: typeName === '吊车监测'|| typeName === '塔吊监测'
            },
            {
              key: 'contactPhone',
              label: typeName === '机械' ? '维护人联系方式：' : '负责人联系方式：',
              dataKey: 'responsiblePersonTel',
              defaultValue: '',
              show: true
            },
            {
              key: 'maintenanceDate',
              label: '维保日期：',
              dataKey: 'repairTime',
              defaultValue: '',
              show: typeName === '机械'
            }
          ],
          dataRows: this.getMachineryDataRows(),
          extraModules: [
            {
              key: 'manufacturer',
              title: '厂家信息',
              class: 'manufacturer-info',
              show: typeName === '机械',
              fields: [
                {
                  key: 'manufacturerName',
                  label: '厂家名称：',
                  dataKey: 'manufacturer',
                  defaultValue: ''
                },
                {
                  key: 'manufacturerPhone',
                  label: '厂家联系方式：',
                  dataKey: 'manufacturerTel',
                  defaultValue: ''
                }
              ]
            }
          ]
        };
      }
      
      // 视频监控设备模板
      if (this.isVideoType) {
        return {
          listClass: '',
          showDataTable: false,
          showVideo: true,
          fields: [
            {
              key: 'equipmentName',
              label: '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: true
            },
          ],
          dataRows: [],
          extraModules: []
        };
      }
      
      // 电力监测设备模板（电表、断路器、电气火灾）
      if (this.isPowerType) {
        return {
          listClass: 'simple',
          showDataTable: false,
          showVideo: false,
          fields: [
            {
              key: 'equipmentName',
              label: '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'equipmentCode',
              label: '设备编号：',
              dataKey: 'code',
              defaultValue: '',
              show: true
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: true
            },
            {
              key: 'contactPhone',
              label: '负责人电话：',
              dataKey: 'responsiblePersonTel',
              defaultValue: '',
              show: true
            }
          ],
          dataRows: [],
          extraModules: []
        };
      }
      
      // 智能设备模板（北斗定位）
      if (this.isSmartType) {
        return {
          listClass: '',
          showDataTable: false,
          showVideo: false,
          fields: [
            {
              key: 'equipmentName',
              label: '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'equipmentCode',
              label: '设备编号：',
              dataKey: 'code',
              defaultValue: '',
              show: true
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: true
            },
            {
              key: 'x',
              label: 'x：',
              dataKey: 'x',
              defaultValue: '',
              show: true,
              isMonitoringData: true
            },
            {
              key: 'y',
              label: 'y：',
              dataKey: 'y',
              defaultValue: '',
              show: true,
              isMonitoringData: true
            }
          ],
          dataRows: [],
          extraModules: []
        };
      }
      
      // 手持终端模板
      if (this.isHandTerminalType) {
        return {
          listClass: '',
          showDataTable: false,
          showVideo: false,
          fields: [
            {
              key: 'equipmentName',
              label: '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'equipmentCode',
              label: '设备编号：',
              dataKey: 'code',
              defaultValue: '',
              show: true
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: true
            },
            {
              key: 'contactPhone',
              label: '负责人联系方式：',
              dataKey: 'responsiblePersonTel',
              defaultValue: '',
              show: true
            },
            {
              key: 'lng',
              label: 'X:',
              dataKey: 'lng',
              defaultValue: '',
              show: true,
              isMonitoringData: true
            },
            {
              key: 'lat',
              label: 'y:',
              dataKey: 'lat',
              defaultValue: '',
              show: true,
              isMonitoringData: true
            }
          ],
          dataRows: [],
          extraModules: []
        };
      }
      // 通用设备模板
      if (this.isGeneralType) {
        return {
          listClass: '',
          showDataTable: false,
          showVideo: false,
          fields: [
            {
              key: 'equipmentName',
              label: '设备名称：',
              dataKey: 'name',
              defaultValue: '',
              show: true
            },
            {
              key: 'equipmentCode',
              label: '设备编号：',
              dataKey: 'code',
              defaultValue: '',
              show: true
            },
            {
              key: 'supplierContact',
              label: '供应商负责人：',
              dataKey: 'responsiblePerson',
              defaultValue: '',
              show: true
            },
            {
              key: 'contactPhone',
              label: '负责人联系方式：',
              dataKey: 'responsiblePersonTel',
              defaultValue: '',
              show: true
            }
          ],
          dataRows: [],
          extraModules: []
        };
      }
      
      // 默认模板
      return {
        listClass: '',
        showDataTable: false,
        showVideo: false,
        fields: [
          {
            key: 'equipmentName',
            label: '设备名称：',
            dataKey: 'name',
            defaultValue: '',
            show: true
          },
          {
            key: 'equipmentCode',
            label: '设备编号：',
            dataKey: 'code',
            defaultValue: '',
            show: true
          },
          {
            key: 'supplierContact',
            label: '供应商负责人：',
            dataKey: 'responsiblePerson',
            defaultValue: '',
            show: true
          },
          {
            key: 'contactPhone',
            label: '负责人联系方式：',
            dataKey: 'responsiblePersonTel',
            defaultValue: '',
            show: true
          }
        ],
        dataRows: [],
        extraModules: [],
      };
    },
    
    // 判断是否有告警（可以根据实际业务逻辑修改）
    hasAlert() {
      // 根据设备状态判断告警 (0:正常; 1:告警)
      if (this.templateData?.status === 1) {
        return true;
      }
      
      // 根据监测数据判断是否告警
      const weight = parseFloat(this.getMonitoringValue('weight')) || 0;
      const windSpeed = parseFloat(this.getMonitoringValue('windSpeed')) || 0;
      const forceArm = parseFloat(this.getMonitoringValue('forceArm')) || 0;
      
      // 告警阈值判断
      const hasWeightAlert = weight > 8;
      const hasWindSpeedAlert = windSpeed > 6;
      const hasForceArmAlert = forceArm > 5;
      
      // 调试信息
      if (hasWeightAlert || hasWindSpeedAlert || hasForceArmAlert) {
        console.log('检测到告警:', {
          weight: weight,
          windSpeed: windSpeed,
          forceArm: forceArm,
          hasWeightAlert,
          hasWindSpeedAlert,
          hasForceArmAlert
        });
      }
      
      return hasWeightAlert || hasWindSpeedAlert || hasForceArmAlert;
    },

    // 计算是否应该显示加载状态
    shouldShowLoading() {
      const result = this.loading && !this.templateData?.name;
      console.log('🤔 shouldShowLoading计算:', {
        loading: this.loading,
        hasTemplateName: !!this.templateData?.name,
        result: result
      });
      return result;
    },

    // 转换设备属性状态列表为DynamicVideoGrid期望的摄像头列表格式
    transformedCameraList() {
      if (!this.isVideoType) {
        console.log('📹 不是视频类型，返回空摄像头列表');
        return [];
      }

      // 获取设备属性状态列表
      const attributeList = this.templateData?.deviceAttributeStatusList || 
                           this.propsData?.deviceAttributeStatusList || 
                           [];

      if (!Array.isArray(attributeList) || attributeList.length === 0) {
        console.log('📹 设备属性状态列表为空，返回空摄像头列表');
        return [];
      }

      console.log('📹 开始转换设备属性状态列表:', attributeList);

      // 创建字段查找助手函数
      const findAttributeValue = (fieldNames) => {
        for (const fieldName of fieldNames) {
          const attribute = attributeList.find(attr => 
            attr.propName === fieldName || attr.propCode === fieldName
          );
          if (attribute?.propValue) {
            return attribute.propValue.trim();
          }
        }
        return null;
      };

      // 提取视频相关属性
      const streamUrl = findAttributeValue([
        '播放地址', 'playUrl', 'videoUrl', 'streamUrl', 'rtspUrl', 'hlsUrl'
      ]);

      const accessToken = findAttributeValue([
        'accessToken', 'token', 'authToken', 'videoToken', 'ezToken'
      ]);

      const onlineStateValue = findAttributeValue([
        'onlineState', 'online', 'status', 'deviceStatus', 'state'
      ]);

      // 解析在线状态（默认为1-在线）
      let onlineState = 1;
      if (onlineStateValue !== null) {
        const parsedState = parseInt(onlineStateValue);
        onlineState = isNaN(parsedState) ? 1 : parsedState;
      }

      // 验证视频流地址格式
      const isValidVideoUrl = (url) => {
        if (!url || typeof url !== 'string') return false;
        const validFormats = [
          /^https?:\/\/.+\.(mp4|avi|mov|wmv|flv|webm|mkv)(\?.*)?$/i,
          /^https?:\/\/.+\.m3u8(\?.*)?$/i,
          /^rtsp:\/\/.+/i,
          /^rtmp:\/\/.+/i,
          /^ezopen:\/\/.+/i,
          /^https?:\/\/.+\/live(\?.*)?$/i,
          /^https?:\/\/.+\/stream(\?.*)?$/i
        ];
        return validFormats.some(format => format.test(url));
      };

      // 检查是否有有效的视频流
      const hasValidStream = !!(streamUrl && accessToken && isValidVideoUrl(streamUrl));

      // 获取设备基本信息
      const deviceName = this.templateData?.name || 
                        this.propsData?.equipmentName || 
                        this.propsData?.name || 
                        '未知设备';

      const areaPath = this.templateData?.site || 
                      this.propsData?.site || 
                      this.templateData?.areaPath || 
                      this.propsData?.areaPath || 
                      '未知区域';

      console.log('📹 提取的视频信息:', {
        deviceName,
        streamUrl: streamUrl ? `${streamUrl.substring(0, 50)}...` : null,
        accessToken: accessToken ? `${accessToken.substring(0, 10)}...` : null,
        onlineState,
        hasValidStream,
        areaPath
      });

      // 如果没有视频相关信息，返回空列表
      if (!streamUrl && !accessToken) {
        console.log('📹 未找到视频流地址或AccessToken，返回空列表');
        return [];
      }

      // 构造摄像头对象
      const cameraObject = {
        deviceName: deviceName,
        onlineState: onlineState,
        accessToken: accessToken,
        streamUrl: streamUrl,
        areaPath: areaPath,
        hasValidStream: hasValidStream,
        equipmentId: this.propsData?.equipmentId || this.propsData?.id
      };

      console.log('📹 构造的摄像头对象:', cameraObject);

      // 返回包含单个摄像头的数组
      return [cameraObject];
    },

    showAlertStatus() {
      // 需要显示告警状态的设备类型列表
      const alertDeviceTypes = [
        'AI识别主机',
        '既有线监测',
        '转体桥监测',
        '高支模监测',
        '深基坑监测',
        '温湿度传感器',
        '施工温控监测',
        '电气火灾',
        '混凝士温控',
        '无线烟感传感器',
        '断路器',
        '电表'
      ];
      
      const deviceType = this.templateData?.deviceTypeName || '';

      return alertDeviceTypes.some(type => deviceType.includes(type)) && !this.isMachineType;
    },
  },

  methods: {
    // 风速单位转换：km/h 转 m/s
    convertWindSpeed(kmhValue) {
      if (kmhValue === null || kmhValue === undefined || kmhValue === '') {
        return 0;
      }
      // km/h 转 m/s：除以 3.6
      return Math.round((parseFloat(kmhValue) / 3.6) * 10) / 10; // 保留1位小数
    },
    
    // 获取字段值
    getFieldValue(field) {
      if (!field || !field.dataKey) {
        return field?.defaultValue || '';
      }
      
      let value = '';
      
      // 如果是监测数据字段，从设备属性状态列表中获取
      if (this.isMonitoringData(field.dataKey)) {
        value = this.getMonitoringValue(field.dataKey) || field.defaultValue || '';
      }
      // 如果是机械字段，使用机械数据获取方法
      else if (this.isMachineryData(field.dataKey)) {
        value = this.getMachineryValue(field.dataKey) || field.defaultValue || '';
      }
      else {
        // 标准字段映射处理
        const fieldMappings = {
          'name': ['equipmentName', 'name'],
          'code': ['equipmentCode', 'code'],
          'responsiblePerson': ['responsiblePerson'],
          'responsiblePersonTel': ['responsiblePersonTel']
        };
        
        const mappingKeys = fieldMappings[field.dataKey] || [field.dataKey];
        
        // 按优先级查找字段值
        for (const key of mappingKeys) {
          if (this.templateData?.[key] !== undefined && this.templateData[key] !== null && this.templateData[key] !== '') {
            value = this.templateData[key];
            break;
          }
        }
        
        // 如果还没找到值，使用默认值
        if (!value) {
          value = field.defaultValue || '';
        }
      }
      
      // // 如果是风速字段，需要进行单位转换（km/h -> m/s）
      // if (field.dataKey === 'windSpeed' && value && value !== '') {
      //   value = this.convertWindSpeed(value);
      // }     
      
      return value;
    },

    // 判断是否为监测数据字段
    isMonitoringData(dataKey) {
      const monitoringFields = [
        'weight', 'forceArm', 'windSpeed', 'rotation', 
        'elevationAngle', 'torque', 'horizontalAngleX', 
        'horizontalAngleY', 'tiltAngle', 'horizontalAngleX2',
        'range', 'torquePercentage', 'slide', 'horizontal', 'vertical', 'depth', 'height',
        'bigArmLen', 'x', 'y', 'lng', 'lat'  // 添加lng和lat字段
      ];
      return monitoringFields.includes(dataKey);
    },

    // 判断是否为机械数据字段
    isMachineryData(dataKey) {
      const machineryFields = [
        'edzl', 'cdrl', 'xzgd', 'xzjl', 'fdjgl', 'zjzl', 'zdyql', 'zxjd',
        'gzzl', 'zdplzf', 'nykd', 'xzsd', 'glzjkd', 'ppnl',
        'zkjz', 'zdkjzd', 'dltnj', 'zjtsl', 'dllx', 'wdqxj',
        'zdwjsd', 'zdwjgd', 'zdwjbj', 'yyxyl', 'hzsd',
        'zdqzl', 'bc', 'zdhzbj', 'ztkj', 'qsgd', 'yyxylx', 'dbjg', 'dccznl',
        'kd', 'edqzl', 'yxsd', 'gdxh', 'zljg', 'czfs',
        'zdzl', 'dbc', 'xbc', 'zdqzlj', 'tjgd', 'gzfd', 'jcczl', 'tbg', 'tmg',
        'qsnl', 'xssd',
        'sxrl', 'pskd', 'ptsl', 'sbl', 'dczznl', 'sby',
        'bjcd', 'blbj', 'bsl', 'ssyl',
        'edgl', 'scdy', 'rllx', 'yxrl', 'zysp', 'lxyxsj',
        'zkzj', 'cjpl', 'srgl', 'zs', 'jtlx', 'zl',
        'edzh', 'mjqj', 'zwbj', 'hccd', 'hckd'
      ];
      return machineryFields.includes(dataKey);
    },

    // 前端字段名到JSON文件identifier的映射关系
    getFieldMapping() {
      return {
        'weight': 'weight',              // 主钩吊重
        'forceArm': 'arm_force',         // 力臂
        'windSpeed': 'wind_speed',       // 风速
        'rotation': 'rotation',          // 回转角度
        'elevationAngle': 'pitch',       // 俯仰角
        'torque': 'moment',              // 主钩力矩
        'horizontalAngleX': 'level_x',   // 水平度X
        'horizontalAngleY': 'level_y',   // 水平度Y
        'tiltAngle': 'inclination',      // 倾斜度
        'horizontalAngleX2': 'level_x',   // 如果需要显示第二个水平角度X，也映射到level_x
        'bigArmLen': 'big_arm_len',      // 大臂长度
        'torquePercentage': 'torque_percentage', // 力矩比
        'x': 'longitude',                        // 北斗定位x坐标
        'y': 'latitude',                         // 北斗定位y坐标
        'lng': 'lng',                           // 手持终端经度
        'lat': 'lat'                            // 手持终端纬度
      };
    },

    // 从设备属性状态列表中获取监测值
    getMonitoringValue(frontendKey) {
      if (!this.templateData?.deviceAttributeStatusList) {
        return '';
      }
      
      // 获取字段映射关系
      const fieldMapping = this.getFieldMapping();
      // 将前端字段名映射到JSON中的identifier
      const actualPropCode = fieldMapping[frontendKey] || frontendKey;
      
      const attribute = this.templateData.deviceAttributeStatusList.find(
        attr => attr.propCode === actualPropCode
      );
      return attribute?.propValue || '';
    },

    // 获取机械类型ID
    getMachineryType() {
      // 优先使用type字段作为机械类型标识
      const type = this.templateData?.type
      
      // 如果有type字段，直接返回
      if (type) {
        return String(type);
      }
      
      // 兼容旧的类型字段
      return type || '';
    },

    // 获取机械类型名称
    getMachineryTypeName() {
      const typeId = this.templateData?.type;
      const machineTypeMap = {
        '1': '装载机',
        '2': '压路机',
        '3': '旋挖钻',
        '4': '挖掘机',
        '5': '汽车起重机',
        '6': '门式起重机',
        '7': '塔式起重机',
        '8': '随车起重机',
        '9': '洒水车',
        '10': '混凝土泵车',
        '11': '发电机',
        '12': '冲击钻',
        '13': '叉车'
      };
      return machineTypeMap[typeId] || '未知机械';
    },

    // 根据机械类型动态生成dataRows
    getMachineryDataRows() {
      const typeId = this.getMachineryType();
      
      // 如果是吊车监测，返回默认的吊车监测数据行
      if (this.equipmentTypeName === '吊车监测') {
        return [
          [
            { key: 'weight', title: '吊重', dataKey: 'weight', defaultValue: '', unit: 't' },
            { key: 'windSpeed', title: '风速', dataKey: 'windSpeed', defaultValue: '', unit: 'm/s' },
            { key: 'elevationAngle', title: '俯仰角', dataKey: 'elevationAngle', defaultValue: '', unit: '°' },
          ],
          [
            { key: 'torque', title: '力矩', dataKey: 'torque', defaultValue: '', unit: 'N·m' },
            { key: 'bigArmLen', title: '大臂长度', dataKey: 'bigArmLen', defaultValue: '', unit: 'm', class: 'single' },
            { key: 'rotation', title: '回转角', dataKey: 'rotation', defaultValue: '', unit: '°' },
          ],
          [
            { key: 'forceArm', title: '力臂', dataKey: 'forceArm', defaultValue: '', unit: 'm' },
            { key: 'horizontalAngleX', title: '水平角度X', dataKey: 'horizontalAngleX', defaultValue: '', unit: '°' },
            { key: 'horizontalAngleY', title: '水平角度Y', dataKey: 'horizontalAngleY', defaultValue: '', unit: '°' },
          ],
          [
            { key: 'tiltAngle', title: '倾斜度', dataKey: 'tiltAngle', defaultValue: '', unit: '°' }
          ]
        ];
      } else if (this.equipmentTypeName === '塔吊监测') {
        return [
          [
            { key: 'weight', title: '吊重', dataKey: 'weight', defaultValue: '', unit: 't' },
            { key: 'windSpeed', title: '风速', dataKey: 'windSpeed', defaultValue: '', unit: 'm/s' },
            { key: 'range', title: '变幅', dataKey: 'range', defaultValue: '', unit: 'm' },
          ],
          [
            { key: 'rotation', title: '回转角度', dataKey: 'rotation', defaultValue: '', unit: '°' },
            { key: 'slide', title: '溜钩距离', dataKey: 'slide', defaultValue: '', unit: 'm', class: 'single' },
            { key: 'torquePercentage', title: '力矩比', dataKey: 'torquePercentage', defaultValue: '', unit: '%' },
          ],
          [
            { key: 'horizontal', title: '水平角度', dataKey: 'horizontal', defaultValue: '', unit: '°' },
            { key: 'vertical', title: '垂直角度', dataKey: 'vertical', defaultValue: '', unit: '°' },
            { key: 'depth', title: '吊钩下降深度', dataKey: 'depth', defaultValue: '', unit: 'm' },
          ],
          [
            { key: 'height', title: '吊钩高度', dataKey: 'height', defaultValue: '', unit: 'm' }
          ]
        ];
      }

      // 根据机械类型返回对应的数据行
      const machineTypeFields = {
        // 装载机
        '1': [
          [
            { key: 'edzl', title: '额定载重量', dataKey: 'edzl', defaultValue: '', unit: '' },
            { key: 'cdrl', title: '铲斗容量', dataKey: 'cdrl', defaultValue: '', unit: '' },
            { key: 'xzgd', title: '卸载高度', dataKey: 'xzgd', defaultValue: '', unit: '' }
          ],
          [
            { key: 'xzjl', title: '卸载距离', dataKey: 'xzjl', defaultValue: '', unit: '' },
            { key: 'zdyql', title: '最大牵引力', dataKey: 'zdyql', defaultValue: '', unit: '' },
            { key: 'zxjd', title: '转向角度', dataKey: 'zxjd', defaultValue: '', unit: '' }
          ]
        ],
        // 压路机
        '2': [
          [
            { key: 'gzzl', title: '工作质量', dataKey: 'gzzl', defaultValue: '', unit: '' },
            { key: 'zdplzf', title: '振动频率/振幅', dataKey: 'zdplzf', defaultValue: '', unit: '' },
            { key: 'nykd', title: '碾压宽度', dataKey: 'nykd', defaultValue: '', unit: '' }
          ],
          [
            { key: 'xzsd', title: '行走速度', dataKey: 'xzsd', defaultValue: '', unit: '' },
            { key: 'fdjgl', title: '发动机功率', dataKey: 'fdjgl', defaultValue: '', unit: '' },
            { key: 'ppnl', title: '爬坡能力', dataKey: 'ppnl', defaultValue: '', unit: '' }
          ]
        ],
        // 旋挖钻
        '3': [
          [
            { key: 'zkjz', title: '钻孔直径', dataKey: 'zkjz', defaultValue: '', unit: '' },
            { key: 'zdkjzd', title: '最大钻孔深度', dataKey: 'zdkjzd', defaultValue: '', unit: '' },
            { key: 'dltnj', title: '动力头扭矩', dataKey: 'dltnj', defaultValue: '', unit: '' }
          ],
          [
            { key: 'zjtsl', title: '主卷扬提升力', dataKey: 'zjtsl', defaultValue: '', unit: '' },
            { key: 'dllx', title: '动力类型', dataKey: 'dllx', defaultValue: '', unit: '' },
            { key: 'zjysc', title: '整机运输尺寸', dataKey: 'zjysc', defaultValue: '', unit: '' }
          ]
        ],
        // 挖掘机
        '4': [
          [
            { key: 'zjzl', title: '整机重量', dataKey: 'zjzl', defaultValue: '', unit: '' },
            { key: 'zdwjsd', title: '最大挖掘深度', dataKey: 'zdwjsd', defaultValue: '', unit: '' },
            { key: 'zdwjgd', title: '最大挖掘高度', dataKey: 'zdwjgd', defaultValue: '', unit: '' }
          ],
          [
            { key: 'zdwjbj', title: '最大挖掘半径', dataKey: 'zdwjbj', defaultValue: '', unit: '' },
            { key: 'yyxyl', title: '液压系统压力', dataKey: 'yyxyl', defaultValue: '', unit: '' },
            { key: 'hzsd', title: '回转速度', dataKey: 'hzsd', defaultValue: '', unit: '' }
          ]
        ],
        // 汽车起重机
        '5': [
          [
            { key: 'zdqzl', title: '最大起重量', dataKey: 'zdqzl', defaultValue: '', unit: '' },
            { key: 'bc', title: '臂长', dataKey: 'bc', defaultValue: '', unit: '' },
            { key: 'zdhzbj', title: '最大回转半径', dataKey: 'zdhzbj', defaultValue: '', unit: '' }
          ],
          [
            { key: 'ztkj', title: '支腿跨距', dataKey: 'ztkj', defaultValue: '', unit: '' },
            { key: 'qsgd', title: '起升高度', dataKey: 'qsgd', defaultValue: '', unit: '' },
            { key: 'yyxylx', title: '液压系统类型', dataKey: 'yyxylx', defaultValue: '', unit: '' }
          ],
          [
            { key: 'dbjg', title: '吊臂结构', dataKey: 'dbjg', defaultValue: '', unit: '' },
            { key: 'dccznl', title: '底盘承载能力', dataKey: 'dccznl', defaultValue: '', unit: '' }
          ]
        ],
        // 门式起重机
        '6': [
          [
            { key: 'kd', title: '跨度', dataKey: 'kd', defaultValue: '', unit: '' },
            { key: 'qsgd', title: '起升高度', dataKey: 'qsgd', defaultValue: '', unit: '' },
            { key: 'edqzl', title: '额定起重量', dataKey: 'edqzl', defaultValue: '', unit: '' }
          ],
          [
            { key: 'yxsd', title: '运行速度', dataKey: 'yxsd', defaultValue: '', unit: '' },
            { key: 'gdxh', title: '轨道型号', dataKey: 'gdxh', defaultValue: '', unit: '' },
            { key: 'czfs', title: '操作方式', dataKey: 'czfs', defaultValue: '', unit: '' }
          ]
        ],
        // 塔式起重机
        '7': [
          [
            { key: 'zdzl', title: '最大重量', dataKey: 'zdzl', defaultValue: '', unit: '' },
            { key: 'dbc', title: '大臂长', dataKey: 'dbc', defaultValue: '', unit: '' },
            { key: 'xbc', title: '小臂长', dataKey: 'xbc', defaultValue: '', unit: '' }
          ],
          [
            { key: 'zdqzlj', title: '最大起重力矩', dataKey: 'zdqzlj', defaultValue: '', unit: '' },
            { key: 'tjgd', title: '塔基高度', dataKey: 'tjgd', defaultValue: '', unit: '' },
            { key: 'tbg', title: '塔臂高', dataKey: 'tbg', defaultValue: '', unit: '' }
          ]
        ],
        // 随车起重机
        '8': [
          [
            { key: 'zdqzl', title: '最大起重量', dataKey: 'zdqzl', defaultValue: '', unit: '' },
            { key: 'bc', title: '臂长', dataKey: 'bc', defaultValue: '', unit: '' },
            { key: 'qsnl', title: '起升能力', dataKey: 'qsnl', defaultValue: '', unit: '' }
          ],
          [
            { key: 'zdhzbj', title: '最大回转半径', dataKey: 'zdhzbj', defaultValue: '', unit: '' },
            { key: 'yyxylx', title: '液压系统类型', dataKey: 'yyxylx', defaultValue: '', unit: '' },
            { key: 'dbjg', title: '吊臂结构', dataKey: 'dbjg', defaultValue: '', unit: '' }
          ]
        ],
        // 洒水车
        '9': [
          [
            { key: 'sxrl', title: '水箱容量', dataKey: 'sxrl', defaultValue: '', unit: '' },
            { key: 'pskd', title: '喷洒宽度', dataKey: 'pskd', defaultValue: '', unit: '' },
            { key: 'ptsl', title: '喷头数量', dataKey: 'ptsl', defaultValue: '', unit: '' }
          ],
          [
            { key: 'sbl', title: '水泵流量', dataKey: 'sbl', defaultValue: '', unit: '' },
            { key: 'dczznl', title: '底盘载重能力', dataKey: 'dczznl', defaultValue: '', unit: '' },
            { key: 'sby', title: '水泵压力', dataKey: 'sby', defaultValue: '', unit: '' }
          ]
        ],
        // 混凝土泵车
        '10': [
          [
            { key: 'bjcd', title: '臂架长度', dataKey: 'bjcd', defaultValue: '', unit: '' },
            { key: 'blbj', title: '布料半径', dataKey: 'blbj', defaultValue: '', unit: '' },
            { key: 'bsl', title: '泵送量', dataKey: 'bsl', defaultValue: '', unit: '' }
          ],
          [
            { key: 'dccznl', title: '底盘承载能力', dataKey: 'dccznl', defaultValue: '', unit: '' },
            { key: 'ssyl', title: '输送压力', dataKey: 'ssyl', defaultValue: '', unit: '' },
            { key: 'yyxyl', title: '液压系统压力', dataKey: 'yyxyl', defaultValue: '', unit: '' }
          ]
        ],
        // 发电机
        '11': [
          [
            { key: 'edgl', title: '整机重量', dataKey: 'edgl', defaultValue: '', unit: '' },
            { key: 'scdy', title: '最大牵引力', dataKey: 'scdy', defaultValue: '', unit: '' },
            { key: 'rllx', title: '铲斗容量', dataKey: 'rllx', defaultValue: '', unit: '' }
          ],
          [
            { key: 'yxrl', title: '发动机功率', dataKey: 'yxrl', defaultValue: '', unit: '' },
            { key: 'zysp', title: '卸载高度', dataKey: 'zysp', defaultValue: '', unit: '' },
            { key: 'lxyxsj', title: '转速差', dataKey: 'lxyxsj', defaultValue: '', unit: '' }
          ]
        ],
        // 冲击钻
        '12': [
          [
            { key: 'zl', title: '整机重量', dataKey: 'zl', defaultValue: '', unit: '' },
            { key: 'jtlx', title: '最大牵引力', dataKey: 'jtlx', defaultValue: '', unit: '' },
            { key: 'zs', title: '铲斗容量', dataKey: 'zs', defaultValue: '', unit: '' }
          ],
          [
            { key: 'srgl', title: '发动机功率', dataKey: 'srgl', defaultValue: '', unit: '' },
            { key: 'cjpl', title: '卸载高度', dataKey: 'cjpl', defaultValue: '', unit: '' },
            { key: 'zkzj', title: '转速差', dataKey: 'zkzj', defaultValue: '', unit: '' }
          ]
        ],
        // 叉车
        '13': [
          [
            { key: 'xssd', title: '整机重量', dataKey: 'xssd', defaultValue: '', unit: '' },
            { key: 'hckd', title: '最大牵引力', dataKey: 'hckd', defaultValue: '', unit: '' },
            { key: 'hccd', title: '铲斗容量', dataKey: 'hccd', defaultValue: '', unit: '' }
          ],
          [
            { key: 'dllx', title: '发动机功率', dataKey: 'dllx', defaultValue: '', unit: '' },
            { key: 'zwbj', title: '卸载高度', dataKey: 'zwbj', defaultValue: '', unit: '' },
            { key: 'mjqj', title: '转速差', dataKey: 'mjqj', defaultValue: '', unit: '' }
          ]
        ]
      };

      // 如果没有找到机械类型ID或配置，返回通用机械字段
      if (!typeId || !machineTypeFields[typeId]) {
        return [
          [
            { key: 'zdqzl', title: '最大起重量', dataKey: 'zdqzl', defaultValue: '', unit: '' },
            { key: 'bc', title: '臂长', dataKey: 'bc', defaultValue: '', unit: '' },
            { key: 'qsgd', title: '起升高度', dataKey: 'qsgd', defaultValue: '', unit: '' }
          ],
          [
            { key: 'zjzl', title: '整机重量', dataKey: 'zjzl', defaultValue: '', unit: '' },
            { key: 'fdjgl', title: '发动机功率', dataKey: 'fdjgl', defaultValue: '', unit: '' },
            { key: 'xssd', title: '行驶速度', dataKey: 'xssd', defaultValue: '', unit: '' }
          ]
        ];
      }

      return machineTypeFields[typeId];
    },

    // 将字段数组转换为dataRows格式（每行3个字段）
    generateDataRowsFromFields(fields) {
      const rows = [];
      const fieldsPerRow = 3;
      
      for (let i = 0; i < fields.length; i += fieldsPerRow) {
        const row = [];
        for (let j = 0; j < fieldsPerRow && (i + j) < fields.length; j++) {
          const field = fields[i + j];
          row.push({
            key: field.prop,
            title: field.label,
            dataKey: field.prop,
            defaultValue: '',
            unit: field.unit,
            class: (j === fieldsPerRow - 1 && row.length === 1) ? 'single' : undefined
          });
        }
        
        // 如果最后一行只有一个字段，添加single类
        if (row.length === 1) {
          row[0].class = 'single';
        }
        
        rows.push(row);
      }
      
      return rows;
    },

    // 重写getMonitoringValue方法以支持机械字段
    getMachineryValue(propCode) {
      // 首先尝试从设备属性状态列表中获取
      if (this.templateData?.deviceAttributeStatusList) {
        const attribute = this.templateData.deviceAttributeStatusList.find(
          attr => attr.propCode === propCode
        );
        if (attribute?.propValue) {
          return attribute.propValue;
        }
      }
      
      // 然后尝试从模板数据的直接属性中获取
      if (this.templateData?.[propCode]) {
        return this.templateData[propCode];
      }
      
      // 最后尝试从原始数据中获取
      if (this.propsData?.[propCode]) {
        return this.propsData[propCode];
      }
      
      return '';
    },

    // 获取设备信息
    getDeviceInfo() {
      this.loading = true
      console.log('🔄 开始获取设备信息...')
      console.log('设备类型:', this.propsData.equipmentTypeName)
      console.log('设备完整数据:', this.propsData)
      
      // 获取设备ID，优先级：equipmentId > id > equipmentCode
      const deviceId = this.propsData.equipmentId || this.propsData.id || this.propsData.equipmentCode
      console.log('设备ID:', deviceId)
      
      // 如果没有设备ID，直接使用传入的数据
      if (!deviceId) {
        console.log('🎯 没有设备ID，直接使用传入数据')
        this.templateData = this.propsData
        this.loading = false
        return
      }
      // 调用普通设备接口
      console.log('📡 调用普通设备接口，ID:', deviceId)
      getDeviceInfo({id: deviceId}).then((res) => {
        console.log('普通设备信息:', res)
        if (res.code === 'success') {
          // 检查返回的数据是否为undefined或null
          if (res.data && res.data[0] !== undefined && res.data[0] !== null) {
            console.log('设备详情:', res.data[0])
            this.templateData = res.data[0]
            
            // 如果是视频类型，初始化播放器
            if (this.isVideoType) {
              console.log('🎬 普通设备获取完成，准备初始化视频播放器')
              this.$nextTick(() => {
                setTimeout(() => {
                  this.initVideoPlayer()
                }, 1000)
              })
            }
          } else {
            // 当普通设备接口没有数据时，尝试调用机械设备接口
            console.log('⚠️ 普通设备接口无数据，尝试调用机械设备接口')
            this.requestMechanicalDetail(deviceId);
          }
        } else {
          // 当普通设备接口失败时，尝试调用机械设备接口
          console.log('⚠️ 普通设备接口失败，尝试调用机械设备接口')
          this.requestMechanicalDetail(deviceId);
        }
      }).catch((error) => {
        console.error('获取普通设备信息出错:', error)
        // 当普通设备接口报错时，尝试调用机械设备接口
        console.log('⚠️ 普通设备接口报错，尝试调用机械设备接口')
        this.requestMechanicalDetail(deviceId);
      })
      
    },

    // 请求机械设备详情的方法
    requestMechanicalDetail(deviceId) {
      console.log('📡 调用机械设备接口，ID:', deviceId)
      
      getMechanicalDetail({id: deviceId}).then((res) => {
        console.log('✅ 机械设备API响应:', res)
        if (res.code === 'success') {
          if (res.data) {
            console.log('📋 机械设备详情数据:', res.data)
            
            // 处理specificalInfo字段
            if (res.data.specificalInfo) {
              try {
                // 尝试解析specificalInfo为JSON对象
                const specificalData = typeof res.data.specificalInfo === 'string' 
                  ? JSON.parse(res.data.specificalInfo)
                  : res.data.specificalInfo;
                
                console.log('解析后的specificalInfo数据:', specificalData);
                
                // 将解析后的数据合并到templateData中
                this.templateData = {
                  ...res.data,
                  ...specificalData
                };
                
                console.log('🎯 合并specificalInfo后的templateData:', this.templateData);
              } catch (error) {
                console.error('解析specificalInfo失败:', error);
                this.templateData = res.data;
              }
            } else {
              this.templateData = res.data;
            }
            
            console.log('🎯 最终的templateData:', this.templateData)
            
            // 使用安全方法设置loading为false
            this.safeSetLoadingFalse()
            
            // 如果是视频类型，初始化播放器
            if (this.isVideoType) {
              console.log('🎬 机械设备获取完成，准备初始化视频播放器')
              this.$nextTick(() => {
                setTimeout(() => {
                  this.initVideoPlayer()
                }, 1000)
              })
            }
          } else {
            console.warn('⚠️ 两个接口都未返回数据')
            this.$emit('close')
            this.showCustomError('设备数据不存在或已被删除（已尝试所有可用接口）')
          }
        } else {
          console.error('❌ 两个接口都请求失败')
          this.$emit('close')
          this.showCustomError('获取设备信息失败（已尝试所有可用接口）: ' + (res.message || '未知错误'))
        }
      }).catch((error) => {
        console.error('💥 两个接口都出错')
        this.$emit('close')
        this.showCustomError('网络请求失败（已尝试所有可用接口），请检查网络连接')
      }).finally(() => {
        console.log('🏁 所有API调用完成，finally块中设置loading为false')
        this.loading = false
      })
    },

    // 显示自定义错误弹窗
    showCustomError(message) {
      // 显示自定义错误弹窗
      this.$alert.error({
        title: '数据加载失败',
        message: message,
        time: new Date().toLocaleString(),
        duration: 5000,
        rightMargin: 10,
      })
    },

    // 强制停止加载状态（调试用）
    forceStopLoading() {
      console.log('🚨 强制停止loading状态')
      this.loading = false
      this.$forceUpdate()
    },

    // 安全地设置loading状态为false
    safeSetLoadingFalse() {
      console.log('🛡️ 安全设置loading为false')
      
      // 方法1：直接设置
      this.loading = false
      
      // 方法2：使用$set确保响应式
      this.$set(this, 'loading', false)
      
      // 方法3：使用setTimeout延迟设置
      setTimeout(() => {
        this.loading = false
        this.$forceUpdate()
        console.log('⏰ setTimeout设置loading为false完成')
      }, 0)
      
      // 方法4：使用$nextTick
      this.$nextTick(() => {
        this.loading = false
        this.$forceUpdate()
        console.log('🔄 $nextTick设置loading为false完成')
      })
      
      // 方法5：延迟更长时间确保状态更新
      setTimeout(() => {
        this.loading = false
        this.$forceUpdate()
        console.log('🕐 1秒后强制设置loading为false')
      }, 1000)
    },

    // 紧急修复：直接显示内容
    emergencyShowContent() {
      console.log('🚨 紧急修复：强制显示内容')
      this.loading = false
      this.$set(this, 'loading', false)
      this.$forceUpdate()
      
      // 如果还是不行，直接操作DOM
      setTimeout(() => {
        const loadingEl = document.querySelector('.loading-container')
        const contentEl = document.querySelector('.popup-header')
        if (loadingEl) {
          loadingEl.style.display = 'none'
          console.log('🎯 直接隐藏loading元素')
        }
        if (contentEl) {
          contentEl.style.display = 'flex'
          console.log('🎯 直接显示内容元素')
        }
      }, 100)
    },
    
  },

  mounted() {
    // 获取设备ID
    const deviceId = this.propsData.equipmentId || this.propsData.id || this.propsData.equipmentCode;
    
    if (!deviceId) {
      console.warn('⚠️ 没有设备ID，使用原始数据');
      this.templateData = this.propsData;
      this.loading = false;
      return;
    }

    // 调用普通设备接口
    console.log('📡 调用普通设备接口，ID:', deviceId);
    getDeviceInfo({id: deviceId}).then((res) => {
      if (res.code === 'success' && res.data && res.data[0]) {
        this.templateData = res.data[0];
      } else {
        // 普通设备接口无数据，尝试调用机械设备接口
        this.requestMechanicalDetail(deviceId);
      }
    }).catch(() => {
      // 普通设备接口失败，尝试调用机械设备接口
      this.requestMechanicalDetail(deviceId);
    });
  },

  beforeDestroy() {
    console.log('🗑️ CustomPopup beforeDestroy');
  }
}
</script>

<style lang='less' scoped>
.popup-content {
  width: 420px;
  min-height: 350px; /* 增加最小高度，确保头部不被挤压 */
  max-height: 85vh; /* 限制最大高度，防止超出屏幕 */
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 420px 500px; /* 固定背景图片尺寸，保持头部位置不变 */
  color: #fff;
  font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* Loading 容器样式 */
.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  box-sizing: border-box;
}

/* Loading 动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4a9eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Loading 文字 */
.loading-text {
  color: #b8d4ff;
  font-size: 14px;
  text-align: center;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0; /* 防止header被压缩 */
  padding: 10px 20px 8px 20px;
  // min-height: 60px; /* 确保头部有足够的固定高度 */
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  z-index: 10;
  box-sizing: border-box;
  position: relative;
}

.popup-alert {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 80px; /* 确保有足够的空间 */
}

.alert-badge {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  white-space: nowrap;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-badge.has-alert {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  animation: alertPulse 2s infinite;
}

.alert-badge.no-alert {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  opacity: 0.9;
}

@keyframes alertPulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.4);
  }
}

.popup-title {
  flex: 1;
  text-align: center;
  color: #fff;
  margin: 0 15px; /* 给标题左右留一些间距 */
  font-size: 16px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  font-weight: bold;
}

.popup-close {
  font-size: 24px;
  cursor: pointer;
  font-weight: normal;
  opacity: 0.9;
  min-width: 32px; /* 与左侧对称 */
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  
  &:hover {
    opacity: 1;
    color: #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
  }
}

.popup-body {
  flex: 1; /* 占据剩余空间，但不挤压头部 */
  padding: 8px 20px 20px 20px;
  overflow-y: auto; /* 内容溢出时显示滚动条 */
  overflow-x: hidden;
  box-sizing: border-box;
  min-height: 0; /* 确保flex子项能够正常缩放 */
  
  /* 自定义滚动条样式 - 仅Webkit浏览器 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1); /* Firefox */
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.popup-list {
  list-style: none;
  padding: 0;
  margin: 0 0 12px 0;
  flex-shrink: 0; /* 防止列表被压缩 */
  
  li {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    min-height: 32px;
    box-sizing: border-box;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      width: 140px;
      color: #b8d4ff;
      font-size: 13px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      line-height: 1.4;
      
      &::before {
        content: "◆";
        margin-right: 6px;
        color: #4a9eff;
        font-size: 12px;
      }
    }
    
    .value {
      flex: 1;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-align: right;
      line-height: 1.4;
      word-break: break-all; /* 防止长文本溢出 */
    }
  }
}

.popup-table {
  width: 100%;
  margin-top: 16px;
  flex-shrink: 0; /* 防止表格被压缩 */
  
  .table-row {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .table-cell {
      flex: 1;
      background: rgba(15,35,85,0.6);
      border-radius: 6px;
      margin-right: 8px;
      padding: 10px 6px;
      text-align: center;
      border: 1px solid rgba(255,255,255,0.1);
      min-height: 55px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      box-sizing: border-box;
      
      &:last-child {
        margin-right: 0;
      }
      
      &.single {
        flex: 0 0 calc(33.33% - 5px);
      }
      
      .cell-title {
        font-size: 12px;
        color: #b8d4ff;
        margin-bottom: 3px;
        font-weight: normal;
        line-height: 1.2;
      }
      
      .cell-value {
        font-size: 22px;
        font-weight: bold;
        color: #fff;
        line-height: 1.1;
        
        .unit {
          font-size: 13px;
          font-weight: normal;
          margin-left: 2px;
          color: #b8d4ff;
        }
      }
    }
  }
}

/* 厂家信息样式 */
.manufacturer-info {
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
  flex-shrink: 0;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #b8d4ff;
  font-size: 15px;
  font-weight: bold;
  line-height: 1.4;
}

.header-icon {
  color: #4a9eff;
  margin-right: 6px;
  font-size: 12px;
}

.header-text {
  letter-spacing: 0.5px;
}

/* 视频容器样式 */
.video-container {
  margin-top: 20px;
  width: 100%;
  height: 400px; /* 从350px增加到400px */
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  position: relative;
  
  // EZUIKit播放器样式适配 - 确保完全贴合
  /deep/ .ezuikit-container {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    
    .ezuikit-video-container {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      box-sizing: border-box !important;
      position: relative !important;
      border-radius: 8px !important;
      
      video {
        width: 100% !important;
        height: 100% !important;
        object-fit: fill !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        display: block !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        border-radius: 8px !important;
      }
    }
    
    .loading-container {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 8px !important;
      
      .loading-item {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 8px !important;
      }
    }
    
    // 隐藏可能存在的控制栏边距
    .ezuikit-controls {
      margin: 0 !important;
      padding: 0 !important;
    }
    
    // 重置所有可能的内部元素
    * {
      margin: 0 !important;
      padding: 0 !important;
      box-sizing: border-box !important;
    }
    
    // 确保播放器本身没有边框和间距
    .ezuikit-player {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      box-sizing: border-box !important;
      border-radius: 8px !important;
    }
  }
  
  // 兼容原有的video标签样式
  /deep/ video {
    width: 100% !important;
    height: 100% !important;
    object-fit: fill !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    display: block !important;
    border-radius: 8px !important;
  }
}

/* 视频加载中样式 */
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: 2;
}

.loading-content {
  text-align: center;
  color: #b8d4ff;
}

.loading-content .loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #4a9eff;
  border-radius: 50%;
  animation: videoLoadingSpin 1s linear infinite;
  margin: 0 auto 12px auto;
}

@keyframes videoLoadingSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 设备离线样式 */
.offline-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  z-index: 3;
}

.offline-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1;
}

.offline-content {
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  padding: 16px 20px;
  border-radius: 8px;
  border: 2px solid #ff4757;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  max-width: 250px;
}

.offline-icon {
  font-size: 28px;
  margin-bottom: 8px;
  color: #ff4757;
}

.offline-content p {
  font-size: 13px;
  margin: 0;
  color: #ff4757;
  font-weight: 500;
  line-height: 1.4;
}

/* 手动播放覆盖层样式 */
.manual-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  z-index: 4;
  cursor: pointer;
}

.manual-play-content {
  text-align: center;
  color: #fff;
}

.play-button {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px auto;
  background: linear-gradient(135deg, #4a9eff, #1890ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(74, 158, 255, 0.4);
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(74, 158, 255, 0.6);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.play-button .play-icon {
  font-size: 24px;
  color: #fff;
  margin-left: 3px; /* 视觉上居中 */
}

.manual-play-content p {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

.manual-play-content small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* 视频占位符样式 */
.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(15, 35, 85, 0.8), rgba(30, 60, 130, 0.6));
  border-radius: 8px;
  z-index: 1;
}

.placeholder-content {
  text-align: center;
  color: #b8d4ff;
}

.play-icon {
  font-size: 48px;
  color: #4a9eff;
  margin-bottom: 10px;
  opacity: 0.8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

.placeholder-content .error-message {
  text-align: center;
  margin: 10px 0;
}

.placeholder-content .error-message p {
  margin: 0 0 5px 0;
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

.placeholder-content .error-message p:last-child {
  margin-bottom: 0;
}

/* 调试按钮样式 */
.debug-buttons {
  margin-top: 15px;
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.debug-btn {
  padding: 4px 8px;
  background: rgba(74, 158, 255, 0.2);
  border: 1px solid #4a9eff;
  border-radius: 3px;
  color: #4a9eff;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  min-width: 60px;
  text-align: center;
  flex: 1;
  
  &:hover {
    background: rgba(74, 158, 255, 0.3);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* 简化列表样式（电力设备等） */
.popup-list.simple {
  margin-bottom: 0;
}

.popup-list.simple li {
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 36px;
}

.popup-list.simple li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.popup-list.simple .label {
  width: 160px;
  font-size: 14px;
}

.popup-list.simple .value {
  font-size: 15px;
}

/* 根据设备类型调整弹窗样式 */
.popup-content.video-type {
  min-height: auto;
  width: 650px; /* 摄像头设备弹窗宽度设为650px */
  height: 570px; /* 摄像头设备弹窗高度设为500px */
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 650px 550px; /* 调整背景图片尺寸适应新的容器尺寸 */

  .popup-header {
    padding: 0 20px;

    .popup-title {
      min-height: 50px
    }
  }
}

.popup-content.power-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg2.png) no-repeat top center;
  background-size: 420px 400px; /* 固定背景图片尺寸，保持头部位置不变 */
}

.popup-content.smart-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg2.png) no-repeat top center;
  background-size: 420px 400px; /* 固定背景图片尺寸，保持头部位置不变 */
}

.popup-content.hand-terminal-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg2.png) no-repeat top center;
  background-size: 420px 400px; /* 固定背景图片尺寸，保持头部位置不变 */
}

.popup-content.general-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 420px 100%; /* 固定背景图片尺寸，保持头部位置不变 */
}

.popup-content.machine-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 420px 100%; /* 机械类设备使用更大的背景尺寸 */
}

.popup-content.default-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg2.png) no-repeat top center;
  background-size: 420px 400px; /* 固定背景图片尺寸，保持头部位置不变 */
}

.popup-content.ai-host-type {
  min-height: auto;
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 420px 600px; /* 固定背景图片尺寸，保持头部位置不变 */
}

/* 内容较少的设备类型，头部需要更多上边距 */
.power-type {
  .popup-header {
    padding: 4px 20px 0 20px; /* 增加顶部padding，确保头部在正确位置 */
    min-height: 20px;
  }
}

.smart-type {
  .popup-header {
    padding: 4px 20px 0 20px; /* 增加顶部padding，确保头部在正确位置 */
    min-height: 20px;
  }
}

.default-type {
  .popup-header {
    padding: 4px 20px 0 20px; /* 增加顶部padding，确保头部在正确位置 */
    min-height: 20px;
  }
}

.general-type {
  .popup-header {
    padding: 4px 20px 0 20px; /* 通用设备稍微少一点padding */
    min-height: 20px;
  }
}

/* 响应式调整 */
@media (max-width: 480px) {
  .video-container {
    height: 150px;
  }
  
  .popup-content {
    width: 350px;
    max-height: 70vh;
  }
  
  .popup-header {
    padding: 0 16px 0 16px;
    font-size: 17px;
    min-height: 55px;
  }
  
  .popup-body {
    padding: 8px 16px 16px 16px;
  }
  
  .popup-table .table-row {
    flex-direction: column;
  }
  
  .popup-table .table-cell {
    margin-right: 0;
    margin-bottom: 6px;
    min-height: 45px;
  }
  
  .popup-list li .label {
    width: 120px;
    font-size: 12px;
  }
  
  .popup-list li .value {
    font-size: 13px;
  }

  .ai-image-container {
    height: 180px;
  }
}

/* AI识别主机图片容器样式 */
.ai-image-container {
  margin: 15px 0;
  width: 100%;
  height: 240px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  :deep(.ivu-image) {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
}

.ai-capture-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}
</style>
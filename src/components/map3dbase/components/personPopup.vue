<template>
    <div class="popup-content">
      <!-- Loading 界面 -->
      <div v-if="shouldShowLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载信息...</div>
      </div>
      
      <!-- 实际内容 -->
      <template v-if="!shouldShowLoading">
        <div class="popup-header">
          <div class="popup-title">{{ title || '加载失败' }}</div>
          <span class="popup-close" @click="closePopup">×</span>
        </div>
      <div class="popup-body">
        <!-- 时间轨迹列表 -->
         <div v-if="isPerson" class="track-main">
            <div class="track-header">
                <div class="track-item">
                    <div class="track-item-index">序号</div>
                    <div class="track-item-time">轨迹时间</div>
                </div>
            </div>
            <div class="track-list" v-if="trackList.length > 0">
                <div class="track-item" v-for="(item, index) in trackList" :class="{'on': currentTrack.workRecordId === item.workRecordId}" :key="item.workRecordId" @click="getPersonTrackDetail(item)">
                    <div class="track-item-index">{{ index + 1 }}</div>
                    <div class="track-item-time">{{ item.startTime }}~{{ item.endTime }}</div>
                </div>
            </div>
            <div class="no-data" v-else>暂无数据</div>
         </div>

      </div>
      </template>
    </div>
  </template>
  
  <script>
  import { getPersonTrackList, getPersonTrackDetail } from '@/api/index.js'
  import moment from 'moment'
  export default {
    name: 'PersonPopup',
    components: {
    },
    props: {
      propsData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        templateData: {},
        loading: true, // 添加loading状态
        title: '',
        trackList: [],
        currentTrack: {}
      }
    },
    computed: {
        // 计算是否应该显示加载状态
        shouldShowLoading() {
            const result = this.loading && !this.templateData?.name;
            console.log('🤔 shouldShowLoading计算:', {
                loading: this.loading,
                hasTemplateName: !!this.templateData?.name,
                result: result
            });
            return result;
        },
        isPerson() {
            return this.propsData.personType === 'person'
        }
    },
    watch: {
      // 监控loading状态变化
      loading(newVal, oldVal) {
        console.log(`🔄 loading状态变化: ${oldVal} -> ${newVal}`)
      },
    },
  
    methods: {
      closePopup() {
        this.$bus.emit('togglePersonTrajectory', false, [])
        this.$emit('close')
      },
        getPersonTrackDetail(item) {
            this.currentTrack = item
            getPersonTrackDetail({
                id: item.workRecordId
            }).then((res) => {
                console.log('轨迹详情:', res)
                let list = res.data.locationDataList.reverse()
                this.$bus.emit('togglePersonTrajectory', true, list)
            })
        },
      // 获取轨迹时间信息
      getPersonTrackList() {
        this.loading = true
        console.log('人员完整数据:', this.propsData)
        this.title = this.propsData.person.realName
        let startTime = moment(new Date()).startOf('day').format('YYYY-MM-DD HH:mm:ss')
        let endTime = moment(new Date()).endOf('day').format('YYYY-MM-DD HH:mm:ss')
        getPersonTrackList({
            page: {size: -1, current: 1},
            customQueryParams: {
                personId: this.propsData.person.id,
                startTime,
                endTime
            }
        }).then((res) => {
          console.log('轨迹信息:', res)
          if (res.code === 'success') {
            this.trackList = res.data.records
          }
          this.loading = false
        }).catch((error) => {
            this.loading = false
            this.showCustomError(error.message)
        })
        
      },
  
      // 显示自定义错误弹窗
      showCustomError(message) {
        // 显示自定义错误弹窗
        this.$alert.error({
          title: '数据加载失败',
          message: message,
          time: new Date().toLocaleString(),
          duration: 5000,
          rightMargin: 10,
        })
      },
  
      // 强制停止加载状态（调试用）
      forceStopLoading() {
        console.log('🚨 强制停止loading状态')
        this.loading = false
        this.$forceUpdate()
      },
  
      // 安全地设置loading状态为false
      safeSetLoadingFalse() {
        console.log('🛡️ 安全设置loading为false')
        
        // 方法1：直接设置
        this.loading = false
        
        // 方法2：使用$set确保响应式
        this.$set(this, 'loading', false)
        
        // 方法3：使用setTimeout延迟设置
        setTimeout(() => {
          this.loading = false
          this.$forceUpdate()
          console.log('⏰ setTimeout设置loading为false完成')
        }, 0)
        
        // 方法4：使用$nextTick
        this.$nextTick(() => {
          this.loading = false
          this.$forceUpdate()
          console.log('🔄 $nextTick设置loading为false完成')
        })
        
        // 方法5：延迟更长时间确保状态更新
        setTimeout(() => {
          this.loading = false
          this.$forceUpdate()
          console.log('🕐 1秒后强制设置loading为false')
        }, 1000)
      },
  
      // 紧急修复：直接显示内容
      emergencyShowContent() {
        console.log('🚨 紧急修复：强制显示内容')
        this.loading = false
        this.$set(this, 'loading', false)
        this.$forceUpdate()
        
        // 如果还是不行，直接操作DOM
        setTimeout(() => {
          const loadingEl = document.querySelector('.loading-container')
          const contentEl = document.querySelector('.popup-header')
          if (loadingEl) {
            loadingEl.style.display = 'none'
            console.log('🎯 直接隐藏loading元素')
          }
          if (contentEl) {
            contentEl.style.display = 'flex'
            console.log('🎯 直接显示内容元素')
          }
        }, 100)
      },
      
    },
  
    mounted() {
      this.getPersonTrackList()
    },
  
    beforeDestroy() {
      console.log('PersonPopup beforeDestroy');
    }
  }
  </script>
  
  <style lang='less' scoped>
  .popup-content {
    width: 420px;
    min-height: 350px; /* 增加最小高度，确保头部不被挤压 */
    max-height: 85vh; /* 限制最大高度，防止超出屏幕 */
    background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
    background-size: 420px 500px; /* 固定背景图片尺寸，保持头部位置不变 */
    color: #fff;
    font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }
  
  /* Loading 容器样式 */
  .loading-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    box-sizing: border-box;
  }
  
  /* Loading 动画 */
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #4a9eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Loading 文字 */
  .loading-text {
    color: #b8d4ff;
    font-size: 14px;
    text-align: center;
    opacity: 0.9;
    letter-spacing: 0.5px;
  }
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0; /* 防止header被压缩 */
    padding: 10px 20px 8px 20px;
    // min-height: 60px; /* 确保头部有足够的固定高度 */
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 1px;
    z-index: 10;
    box-sizing: border-box;
    position: relative;
  }
  
  .popup-title {
    flex: 1;
    text-align: center;
    color: #fff;
    margin: 0 15px; /* 给标题左右留一些间距 */
    font-size: 16px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    font-weight: bold;
  }
  
  .popup-close {
    font-size: 24px;
    cursor: pointer;
    font-weight: normal;
    opacity: 0.9;
    min-width: 32px; /* 与左侧对称 */
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    
    &:hover {
      opacity: 1;
      color: #ff6b6b;
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  .popup-body {
    flex: 1; /* 占据剩余空间，但不挤压头部 */
    padding: 8px 20px 20px 20px;
    overflow-y: auto; /* 内容溢出时显示滚动条 */
    overflow-x: hidden;
    box-sizing: border-box;
    min-height: 0; /* 确保flex子项能够正常缩放 */
    
    /* 自定义滚动条样式 - 仅Webkit浏览器 */
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1); /* Firefox */
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
      transition: background 0.2s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }
  .no-data{
    text-align: center;
    font-size: 1.48vh;
    color: rgba(190, 226, 255, 1);
    margin-top: 1.48vh;
  }
  .track-main{
    .track-item{
        display: flex;
        align-items: center;
        font-size: 1.48vh;
        color: #fff;
        padding: 0.37vh 0.74vh;
        height: 3.33vh;
        justify-content: space-between;
        .track-item-index{
            width: 4vh;
        }
    }
    .track-header{
        .track-item{
            color: rgba(190, 226, 255, 1);
            font-weight: bold;
        }
    }
    .track-list{
        .track-item{
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
            margin-bottom: 0.37vh;
            cursor: pointer;
            &.on{
            background: linear-gradient(90deg, rgba(218, 28, 35, 0) 0%, rgba(218, 28, 35, 0.3) 50%, rgba(218, 28, 35, 0) 100%);

            }
        }
    }
  }
  
  /* 响应式调整 */
  @media (max-width: 480px) {
    
    
    .popup-content {
      width: 350px;
      max-height: 70vh;
    }
    
    .popup-header {
      padding: 0 16px 0 16px;
      font-size: 17px;
      min-height: 55px;
    }
    
    .popup-body {
      padding: 8px 16px 16px 16px;
    }
  }
  
  </style>
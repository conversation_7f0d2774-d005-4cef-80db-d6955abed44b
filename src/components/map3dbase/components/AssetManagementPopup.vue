<template>
  <div class="popup">
    <div class="title">
      <span class="text"> {{ format(data.name) }} </span>
      <i class="close" @click="closePointPopup">
        <img src="/img/zhts/close.png" alt="" />
      </i>
    </div>
    <div class="content" :style="`flex-direction: ${getDisplayFields.length === 2 ? 'row' : 'column'}`">
      <template v-for="(item, index) in getDisplayFields" >
        <el-row :key="index" v-if="!Array.isArray(item)" type="flex" :gutter="20" style="margin-bottom: 5px">
          <el-col :span="leftSpan">
            <div class="leftText">{{ item.label }}</div>
          </el-col>
          <el-col :span="rightSpan">
            <div class="rightText">
              <span>{{ format(data[item.key]) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row class="item" :key="item" v-else type="flex" :gutter="10">
          <template v-for="(subItem, subIndex) in item">
            <!-- 为了避免 :key 报错，当 subItem.key 不存在时，使用索引作为备用 key -->
            <el-col :key="subIndex" :span="20">
              <div style="margin-bottom: 5px">
                <el-row v-if="subItem.isImage && data[subItem.key].length > 0">
                  <el-col :span="12">
                    <img :src="format(data[subItem.key][0])" alt="" style="width: 100%; height: 50px;">
                  </el-col>
                </el-row>
                <el-row v-else type="flex" :gutter="20" style="align-items: center;">
                  <el-col :span="10" style="display: flex; align-items: center;">
                    <div class="leftText" style="white-space: nowrap; line-height: 1.5;">{{ subItem.label }}</div>
                  </el-col>
                  <el-col :span="10" style="display: flex; align-items: center;">
                    <div class="rightText" style="white-space: nowrap; line-height: 1.5;">
                      <span>{{ format(data[subItem.key]) }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </template>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      listData: [],
      leftSpan: 6,
      rightSpan: 14,
    };
  },
  props: {
    data: Object,
    close: Function,
  },
  computed: {
    getDisplayFields() {
      const layerType = this.data.layerType;
      switch (layerType) {
        case 'FC':
          return [
            { label: '具体位置', key: 'location' },
            { label: '建筑面积', key: 'area' },
            { label: '实用面积', key: 'actualArea' },
            { label: '结构', key: 'structure' },
            { label: '用途', key: 'useTo' },
            { label: '评估', key: 'assess' }
          ];
        case 'TD':
          return [
            { label: '具体位置', key: 'location' },
            { label: '面积', key: 'area' },
            { label: '编号', key: 'code' },
            { label: '权属', key: 'layerType' },
            { label: '用途', key: 'useTo' }
          ];
        case 'LY':
          return [
            { label: '数量', key: 'number' },
            { label: '具体位置', key: 'location' },
            { label: '占用面积', key: 'area' },
            { label: '植物种类', key: 'type' },
            { label: '生长状况', key: 'growStatus' }
          ];
        default:
          return [
            [
              { isImage: true, key: 'pictures' },
              { label: '安置时间', key: 'arrangeTime' },
              { label: '联系人', key: 'contacts' },
              { label: '联系电话', key: 'phoneNumber' }
            ],
            [
              { label: '设施名称', key: 'name' },
              { label: '具体位置', key: 'location' },
              { label: '设施编号', key: 'code' },
              { label: '设施种类', key: 'type' },
              { label: '设施型号', key: 'model' },
              { label: '数量', key: 'number' }
            ]
          ];
      }
    }
  },
  mounted() {
    console.log(this.data, "数据");
  },
  methods: {
    closePointPopup() {
      this.close();
    },
    format(str) {
      if (str === undefined || str === null || str.length === 0) {
        return "";  
      } else {
        return str;
      }
    },
  },
};
</script>
<style>
#popup-container0 {
  position: absolute;
  z-index: 999999999;
  /* width: 464px;
  height: 300px; */
  left: 0;
  top: 0;
  /* pointer-events: none !important; */
}
</style>
<style lang='less' scoped>
.popup {
  width: 100%;
  height: 100%;
  // background: url(/img/zhts/point-model.png) no-repeat;
  background: url(/img/zhts/tkbg.jpg) no-repeat;
  background-size: 100% 100%;
  position: relative;

  .title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 30px;
    // height: 65px;
    .text {
      font-size: 16px;
      color: #fff;
      font-family: syst-Light;
      padding-left: 20px;
    }

    .close {
      cursor: pointer;
      padding-right: 20px;
      img {
        width: 13px;
      }
    }
  }

  .content {
    // height: calc(100% - 20px);
    overflow: auto;
    padding: 10px 20px;
    display: flex;

    .content-item {
      display: flex;
      align-items: center;
      height: 40px;

      .left-icon {
        img {
          width: 15px;
          vertical-align: middle;
        }
      }

      .right-text {
        padding-left: 10px;
        display: flex;
        align-items: center;

        .title {
          font-size: 14px;
          color: #699fa8;
          font-family: syst-Light;
          line-height: 30px;
        }

        .value {
          color: #fff;
          font-size: 18px;
          font-family: syst-Regular;
          span {
            font-size: 14px;
          }
        }

        .value_active {
          color: rgb(221, 125, 15);
        }
      }
    }

    .leftText {
      font-size: 12px;
      color: #4F96A3;
    }
    .rightText {
      font-size: 12px;
      color: #fff;
    }
    
    .item {
      width: 100%;
      flex: 1;
      flex-direction: column;
      /deep/ .el-col-12 {
        width: 100%;
      }
    }
  }

  .content::-webkit-scrollbar {
    display: none;
  }
}
</style>
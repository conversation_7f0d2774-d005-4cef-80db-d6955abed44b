<template>
  <div>
    <Carousel class="carousel" dots="none" arrow="never" :autoplay-speed="3000">
      <CarouselItem v-for="(item, index) in dataInfo" :key="index">
        <div class="padcontent">
          <div class="realData">
            <div class="title">
              <div class="online">
                <span>{{ item.name }}</span>
                <img
                  :src="
                    item.onlineState == 1
                      ? require('@/assets/images/icon/online.png')
                      : require('@/assets/images/icon/offline.png')
                  "
                />
              </div>
              <div class="online time">
                <span>{{
                  item.craneBusinessRecord &&
                  item.craneBusinessRecord.createTime != null
                    ? item.craneBusinessRecord.createTime
                    : "-"
                }}</span>
                <span
                  v-if="!propsData.isTowerCrane"
                  class="photo"
                  @click="showPic(item)"
                  >机械五定图</span
                >
              </div>
            </div>

            <div class="peopleDetail">
              <div class="profile">
                <img
                  :src="
                    item.driverList ? item.driverList[0].profilePict : empty
                  "
                />
              </div>
              <div class="profileInfo">
                <div class="info">
                  <span class="titles"> 司机姓名： </span>
                  <span class="text">
                    {{ item.driverList ? item.driverList[0].realName : "-" }}
                  </span>
                </div>
                <div class="info">
                  <span class="titles"> 电话号码： </span>
                  <span class="text">
                    {{ item.driverList ? item.driverList[0].linkPhone : "-" }}
                  </span>
                </div>
                <div class="info">
                  <span class="titles"> 司机证书： </span>
                  <div class="text" @click="search(item)">查看</div>
                </div>
              </div>
            </div>
            <div v-if="propsData.isTowerCrane" class="information">
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmWeight) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">吊重</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.weight != null
                      ? item.craneBusinessRecord.weight
                      : "-"
                  }}<sub>t</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmWindSpeed) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">风速</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.windSpeed != null
                      ? item.craneBusinessRecord.windSpeed
                      : "-"
                  }}<sub>m/s</sub>
                </div>
              </div>
              <div class="item">
                <div class="top">变幅</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.range != null
                      ? item.craneBusinessRecord.range
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div class="item">
                <div class="top">回转角度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.rotation != null
                      ? item.craneBusinessRecord.rotation
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmSlide) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">溜钩距离</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.slide != null
                      ? item.craneBusinessRecord.slide
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmTorquePercentage) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">力矩比</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.torquePercentage != null
                      ? item.craneBusinessRecord.torquePercentage
                      : "-"
                  }}<sub>%</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmHorizontal) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">水平角度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.horizontal != null
                      ? item.craneBusinessRecord.horizontal
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmVertical) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">垂直角度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.vertical != null
                      ? item.craneBusinessRecord.vertical
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
              <div class="item">
                <div class="top">吊钩下降深度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.depth != null
                      ? item.craneBusinessRecord.depth
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div class="item">
                <div class="top">吊钩高度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.height != null
                      ? item.craneBusinessRecord.height
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
            </div>

            <div v-else class="information">
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmWind) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">吊重</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.weight != null
                      ? item.craneBusinessRecord.weight
                      : "-"
                  }}<sub>t</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmWindSpeed) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">风速</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.windSpeed != null
                      ? item.craneBusinessRecord.windSpeed
                      : "-"
                  }}<sub>m/s</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmPitch) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">俯仰角</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.pitch != null
                      ? item.craneBusinessRecord.pitch
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmMoment) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">力矩</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.moment != null
                      ? item.craneBusinessRecord.moment
                      : "-"
                  }}<sub>KM·M</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmBigArmLen) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">大臂长度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.bigArmLen != null
                      ? item.craneBusinessRecord.bigArmLen
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmRotation) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">回转角</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.rotation != null
                      ? item.craneBusinessRecord.rotation
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmArmForce) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">力臂</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.armForce != null
                      ? item.craneBusinessRecord.armForce
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmLevelX) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">水平度X</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.levelX != null
                      ? item.craneBusinessRecord.levelX
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmLevelY) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">水平度Y</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.levelY != null
                      ? item.craneBusinessRecord.levelY
                      : "-"
                  }}<sub>m</sub>
                </div>
              </div>
              <div
                :class="
                  'item ' +
                  (((item.craneBusinessRecord &&
                    item.craneBusinessRecord.alarmInclination) ||
                    0) == 1
                    ? 'active'
                    : '')
                "
              >
                <div class="top">倾斜度</div>
                <div class="bottom">
                  {{
                    item.craneBusinessRecord &&
                    item.craneBusinessRecord.inclination != null
                      ? item.craneBusinessRecord.inclination
                      : "-"
                  }}<sub>°</sub>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CarouselItem>
      <ModalW610H380 :visibleW610h380.sync="isShow" title="司机证书">
        <el-carousel indicator-position="none" arrow="hover">
          <el-carousel-item
            v-for="(item, index) in currentCertList"
            :key="index"
          >
            <div class="cardContent">
              <div class="card">
                <Empty
                  title="暂无图片"
                  v-if="!item.specialAuthBookContent"
                ></Empty>
                <img :src="item.specialAuthBookContent" v-else />
              </div>
              <div class="content">
                <div class="beforeTime" v-if="false">
                  <div>证书已过期</div>
                </div>
                <!-- <div class="contentItem">
                                <span>证书名称：</span>
                                <span>这里是一个证书的名称</span>
                                <div class="line"></div>
                            </div>
                            <div class="contentItem">
                                <span>证书编号：</span>
                                <span>这里是一个证书的编号</span>
                                <div class="line"></div>
                            </div> -->
                <div class="contentItem">
                  <span>证书类型：</span>
                  <span>{{ initDict(item.specialAuthBookType) }}</span>
                  <div class="line"></div>
                </div>
                <!-- <div class="contentItem">
                                <span>证书等级：</span>
                                <span>这里是一个证书的等级</span>
                                <div class="line"></div>
                            </div> -->
                <div class="contentItem">
                  <span>证书有效起始日期：</span>
                  <span>{{ item.specialAuthBookDate.split("~")[0] }}</span>
                  <div class="line"></div>
                </div>
                <div class="contentItem">
                  <span>证书有效截止：</span>
                  <span>{{ item.specialAuthBookDate.split("~")[1] }}</span>
                  <div class="line"></div>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </ModalW610H380>
      <ModalW610H380 :visibleW610h380.sync="picShow" title="机械五定图">
        <div class="picContent">
          <span>
            {{ picData.name }}
          </span>
          <span> 时间：{{ picData.fiveChartTime }} </span>
          <span> 作业内容： {{ picData.fiveChartContent }} </span>
          <div class="pic">
            <el-image
              :src="item"
              v-for="(item, index) in picData.fiveChartImg"
              :key="index"
              :preview-src-list="picData.fiveChartImg"
            />
          </div>
        </div>
      </ModalW610H380>
    </Carousel>
  </div>
</template>

<script>
import { getTowerCraneMechanical } from "@/api/index";
import { getListPage, getDict } from "@/api/xxindex";
import ModalW610H380 from "@/components/modal/w610h380.vue";
export default {
  name: "dcCarousel",
  data() {
    return {
      dataInfo: [],
      picShow: false,
      picData: {},
      isShow: false,
      currentCertList: [],
      PlainShow: false,
      planValueList: [],
    };
  },
  components: {
    ModalW610H380,
  },
  props: {
    propsData: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    this.initRequest();
  },
  methods: {
    async initRequest() {
      if (this.propsData.isTowerCrane) {
        const res = await getTowerCraneMechanical();
        console.log(res, "getTowerCraneMechanical");
        if (res.code === "success") {
          this.dataInfo = res.data.filter(
            (item) => item.id === this.propsData.id
          );
          console.log(this.dataInfo, 1423423);
        }
      } else {
        let data = {
          customQueryParams: {
            alarmState: null,
            codeOrName: "",
            onlineState: null,
            workPointId: null,
          },
          page: {
            current: 1,
            size: 9999,
          },
        };
        getListPage(data).then((res) => {
          if (res.code === "success") {
            this.dataInfo = res.data.records.filter(
              (item) => item.id === this.propsData.id
            );
            console.log(this.dataInfo, 1423423);
          }
        });
      }
    },
    showPic(item) {
      console.log(item);
      this.picShow = true;
      this.picData = {
        fiveChartContent: item.fiveChartContent,
        fiveChartImg: item.fiveChartImg,
        fiveChartTime: item.fiveChartTime,
        workPointName: item.workPointName,
        name: item.name,
      };
    },
    search(item) {
      // 查看
      try {
        if (
          !item.driverList ||
          !item.driverList[0] ||
          !item.driverList[0].specialAuthBookJson
        ) {
          this.$Message.warning("暂无证书信息");
          return;
        }
        const specialAuthBookJson = item.driverList[0].specialAuthBookJson;
        let certList = [];

        if (typeof specialAuthBookJson === "string") {
          certList = JSON.parse(specialAuthBookJson);
        } else {
          certList = specialAuthBookJson;
        }

        // 确保是数组格式
        if (!Array.isArray(certList)) {
          certList = [certList];
        }
        getDict("special_book_type").then((res) => {
          if (res.code == "success") {
            this.Dict = res.data;
            this.currentCertList = certList;
            this.isShow = true;
            console.log(this.currentCertList);
          }
        });
      } catch (error) {
        console.error("解析证书数据失败:", error);
        this.$Message.error("证书数据格式错误");
      }
    },
    initDict(item) {
      let data = this.Dict.filter((i) => i.itemValue == item);
      return data[0].itemText;
    },
    convertWindSpeed(kmhValue) {
      if (kmhValue === null || kmhValue === undefined || kmhValue === "") {
        return 0;
      }
      // km/h 转 m/s：除以 3.6
      return Math.round((parseFloat(kmhValue) / 3.6) * 10) / 10; // 保留1位小数
    },
  },
};
</script>

<style lang="less" scoped>
.carousel {
  width: 464px;
  height: 300px;
  z-index: 1;
}
.popup-close {
  font-size: 24px;
  cursor: pointer;
  font-weight: normal;
  opacity: 0.9;
  min-width: 32px; /* 与左侧对称 */
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    color: #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
  }
}

.picContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;

  .pic {
    height: calc(100% - 90px);
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    overflow-y: auto;
  }
}

/deep/ .side-panel {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  );
  height: 100%;
  gap: 0px;
}

.side-panel-right {
  background: linear-gradient(
    to left,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
  gap: 0px;
}

/deep/ .ivu-timeline-item-head {
  width: 20px;
  height: 20px;
  background: url(../../../assets/images/ProjectAnti/point.png) no-repeat center
    center;
  background-size: 100% auto;
  border: none;
  position: absolute;
  left: 0px;
  top: -2px;
}

/deep/ .ivu-timeline-item-tail {
  position: absolute;
  left: 10px;
  top: 5px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

/deep/ .ivu-timeline-item {
  padding: 0;
}

.cardContent {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  gap: 10px;

  .card {
    width: 35%;
    height: 100%;
    background: url(../../../assets/images/ProjectCrane/border.png) no-repeat
      center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 99%;
      height: 100%;
      object-fit: contain;
    }
  }

  .content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .beforeTime {
      width: 100%;
      height: 40px;
      color: #fff;
      background: url(../../../assets/images/ProjectCrane/titleBack.png)
        no-repeat center center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .beforeTime::before {
      content: "";
      display: inline-block;
      width: 25px;
      height: 25px;
      background: url(../../../assets/images/ProjectCrane/errorIcon.png)
        no-repeat center center;
      background-size: contain;
      background-position-y: 2px;
      margin-right: 5px;
      margin-left: 2px;
    }

    .contentItem {
      width: 100%;
      height: 12.5%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      margin-bottom: 10px;
      padding-bottom: 10px;

      span:first-child {
        color: rgba(255, 255, 255, 0.5);
      }

      span:first-child::before {
        content: "";
        display: inline-block;
        vertical-align: middle;
        background-color: #fff;
        width: 5px;
        height: 5px;
        transform: rotate(45deg);
        margin-right: 7px;
      }

      span:last-child {
        color: #fff;
        font-weight: bold;
      }

      .line {
        width: 100%;
        height: 2px;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(255, 255, 255, 0.3) 50%,
          transparent 100%
        );
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .contentItem:last-child {
      .line {
        background: transparent !important;
      }
    }
  }
}

.timeConetent {
  width: 100%;
  height: 75px;
  display: flex;
  flex-direction: column;
  background: url(../../../assets/images/ProjectAnti/warn.png) no-repeat center
    center;
  background-size: 100% 100%;
  color: #fff;

  .title {
    height: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 5px;
    height: 35px;
    align-items: center;

    span {
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }

    // span:last-child {
    //     width: 68px;
    //     height: 80%;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     color: #fff;
    //     background: url(../../assets/images/ProjectAnti/Button.png) no-repeat center center;
    //     background-size: 100% 100%;
    //     cursor: pointer;
    // }
  }

  .content {
    height: calc(75px - 35px);
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    div {
      width: 100%;
      text-align: left;
    }

    div::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      margin: 0 8px;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.floorFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom, transparent, #1d2d47);

  .floorItem {
    width: 48%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .echartsContents {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0 10px;

      .echarts {
        width: 100%;
        height: calc(100% - 30px - 40px);
      }
    }

    .warnNum {
      width: 100%;
      height: 88%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5%;

      .left {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .item {
          width: 100%;
          height: 48%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 4px;

          .icon {
            width: 100%;
            height: 30px;
            background-image: linear-gradient(
              to right,
              transparent 1%,
              #102f97 10%,
              transparent 100%
            );
            display: flex;
            align-items: center;
            color: #fff;
          }

          .icon::before {
            content: "";
            width: 20px;
            height: 20px;
            background: url(../../../assets/images/ProjectCrane/iconLeft.png)
              no-repeat center center;
            background-size: contain;
            background-position-y: 3px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
          }

          .value {
            width: 80px;
            height: calc(100% - 30px - 10px);
            background: url(../../../assets/images/ProjectCrane/warnNumBack.png)
              no-repeat center center;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 29px;
            color: #fff;
            padding-bottom: 25px;
            box-sizing: border-box;
          }
        }
      }

      .right {
        flex: 1;
        height: 100%;

        .timeLine {
          width: 100%;
          height: 100%;
          overflow-y: auto;

          .time {
            color: #fff;
          }
        }
      }
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../../assets/images/ProjectMap/nav-bg.png)
            no-repeat center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }

  .right-content {
    width: 408px;
    position: absolute;
    right: 0;
    top: 0;
    height: calc(100%);
    flex: 1;
  }

  .floor-content {
    width: calc(100% - 408px - 408px - 40px);
    position: absolute;
    height: 35%;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../../assets/images/icon/s_title.png) no-repeat center
    center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -5px;
}

.padcontent {
  width: 100%;
  box-sizing: border-box;
  background-color: #1e2f4a;
}

.myButton {
  width: 62px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center center;
  // background-size: 100% 100%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  border-radius: 3px;
}

.myButton.active {
  background: url(../../../assets/images/ProjectAI/activeBack.png) no-repeat
    center center;
  background-size: 100% 100%;
}

.realData {
  width: 100%;
  height: calc(100vh - 22vh - 10vh - 100px);
  background: url(../../../assets/images/ProjectCrane/h617Card.png) no-repeat
    center center;
  background-size: 100% 100%;
  padding: 0px 8px;
  box-sizing: border-box;

  .title {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: column;

    .online {
      width: 100%;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;

      span:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        background: url(../../../assets/images/ProjectCrane/icon.png) no-repeat
          center center;
        background-size: contain;
      }

      span:last-child {
        cursor: pointer;
        // color: #184099
      }

      img {
        width: 42px;
        height: auto;
        margin-top: 5px;
      }
    }

    .time {
      span:before {
        background: url(../../../assets/images/icon/time.png) no-repeat center
          center !important;
        background-size: contain !important;
      }
    }
  }

  .worktime {
    width: 100%;
    height: 18%;
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 10px;

    .worktime-item {
      width: 47%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon {
        width: 50px;
        height: 50px;
        background: url(../../../assets/images/ProjectCrane/leftIcon.png)
          no-repeat center center;
        background-size: contain;
      }

      .times {
        height: 100%;
        width: calc(100% - 40px - 10%);
        display: flex;
        flex-direction: column;
        gap: 5px;

        .text {
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
        }

        .hours {
          width: 100%;
          height: calc(100% - 30px);
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(26, 44, 76, 0.9),
            transparent
          );
          text-shadow: 0 0 10px #20549b;
          color: #fff;
          font-size: 29px;
          font-weight: bold;

          sub {
            margin-left: 5px;
            margin-bottom: 2px;
            font-size: 16px;
          }
        }
      }
    }
  }

  .peopleDetail {
    width: 100%;
    height: 16vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 10px;
    gap: 10px;

    .profile {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .profileInfo {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .info {
        width: 100%;
        height: calc(100% / 4);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .titles {
          color: rgba(255, 255, 255, 0.5);
          white-space: nowrap;
        }

        .text {
          color: #fff;
          white-space: nowrap;
        }

        div.text {
          width: 62px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: url(../../../assets/images/ProjectAI/activeBack.png)
            no-repeat center center;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }
    }
  }

  .information {
    width: 100%;
    height: calc(100% - 16vh - 18%);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: auto;

    .item {
      width: 30%;
      height: 70px;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .top {
        width: 100%;
        height: 45%;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(13, 41, 89, 0.563),
          transparent
        );
        display: flex;
        align-items: center;
        padding-left: 10px;
        color: #fff;
      }

      .bottom {
        width: 100%;
        height: 55%;
        background: url(../../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }

    .item.active {
      .top {
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(179, 55, 55, 0.612),
          transparent
        ) !important;
      }

      .bottom {
        background: url(../../../assets/images/ProjectAnti/red.png) no-repeat
          center center !important;
        background-size: 100% 100% !important;
      }
    }
  }
}

.information2 {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-y: auto;

  .item {
    width: 31%;
    height: 70px;
    display: flex;
    flex-direction: column;
    gap: 5px;

    .top {
      width: 100%;
      height: 45%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(13, 41, 89, 0.563),
        transparent
      );
      display: flex;
      align-items: center;
      padding-left: 10px;
    }

    .bottom {
      width: 100%;
      height: 55%;
      background: url(../../../assets/images/ProjectAnti/blue.png) no-repeat
        center center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      box-sizing: border-box;
      padding-left: 15px;

      sub {
        margin-left: 5px;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .item.active {
    .top {
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(179, 55, 55, 0.612),
        transparent
      ) !important;
    }

    .bottom {
      background: url(../../../assets/images/ProjectAnti/red.png) no-repeat
        center center !important;
      background-size: 100% 100% !important;
    }
  }
}

.statistics {
  width: 100%;
  height: 22vh;
  background: url(../../../assets/images/ProjectCrane/h201Card.png) no-repeat
    center center;
  background-size: 100% 105%;
  background-position-y: 5%;

  .title {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 5px;
  }

  .title::before {
    content: "";
    background: url(../../../assets/images/ProjectCrane/icon.png) no-repeat
      center center;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
  }

  .totalNum {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 8px;

    .num {
      width: 48%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 8px;

      div:first-child {
        color: transparent;
        // 渐变字体
        background-image: linear-gradient(to right, #adc6fa, #e2ebfd);
        background-image: -webkit-linear-gradient(to right, #adc6fa, #e2ebfd);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 18px;
      }

      div:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../../../assets/images/icon/work.png) no-repeat center
          center;
        background-size: contain;
        vertical-align: middle;
        display: inline-block;
        text-align: center;
      }

      div:last-child {
        width: 70px;
        height: 65px;
        font-size: 25px;
        color: #fff;
        background: url(../../../assets/images/icon/cardIcon.png) no-repeat
          center center;
        background-size: 80% auto;
        display: flex;
        justify-content: center;
        display: inline-block;
        vertical-align: top;
        text-align: center;

        span {
          font-size: 15px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  .time {
    width: 100%;
    height: calc(100% - 38px - 45%);
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;

    .item {
      width: 32%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .top {
        width: 100%;
        height: 35%;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(38, 82, 132, 0.4),
          transparent
        );
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hour {
        width: 100%;
        height: calc(100% - 55%);
        background: url(../../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

.echartsContent {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-direction: column;
  margin-top: -10px;
  margin-bottom: 15px;

  .echarts {
    width: 100%;
    height: calc(100% - 20%);
  }
}

.ehcharts-title {
  height: 20%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/ .ivu-select {
    width: 40%;
  }

  /deep/.ivu-select-selection {
    background-image: linear-gradient(to bottom, #223d71, #1d3051);
    border: 2px solid #2e5bbf;
    height: 30px;
    color: #fff;
  }

  /deep/ .ivu-select-placeholder {
    color: #fff;
  }

  /deep/ .ivu-select-arrow {
    color: #fff;
  }

  /deep/ .ivu-select-item {
    background-image: linear-gradient(
      to bottom,
      rgba(34, 61, 113, 3),
      rgba(29, 48, 81, 0.9)
    );
    color: #fff;
  }

  /deep/ .ivu-select-dropdown {
    padding: 0;
  }

  .btn {
    display: flex;
    gap: 10px;
  }
}

.monitorContent {
  height: calc(100vh - 25vh - 35vh - 10vh);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .monitor {
    height: 70px;
    width: 100%;
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    flex-direction: column;
    background: url(../../../assets/images/ProjectCrane/titleBack.png) no-repeat
      center center;
    background-size: 100% 100%;

    .text {
      color: #fff;
    }

    .text::before {
      content: "";
      width: 30px;
      height: 30px;
      display: inline-block;
      vertical-align: middle;
      background: url(../../../assets/images/ProjectCrane/errorIcon.png)
        no-repeat center center;
      background-size: contain;
    }
  }

  .wind {
    width: 100%;
    height: 40px;
    background: url(../../../assets/images/ProjectCrane/titleBlueBack.png)
      no-repeat center center;
    background-size: 100% 100%;
    display: flex;

    .windNum {
      width: 110px;
      height: 100%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(12, 52, 98, 0.9),
        transparent
      );
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .windNum::before {
      content: "";
      width: 20px;
      height: 20px;
      background: url(../../../assets/images/ProjectCrane/windIcon.png)
        no-repeat center center;
      background-size: contain;
    }

    .num {
      width: 110px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      background: url(../../../assets/images/ProjectAnti/blue.png) no-repeat
        center center;
      background-size: 100% 100%;
    }
  }

  .echarts {
    width: 100%;
    height: calc(100% - 60px);
  }
}

// /deep/ .el-image-viewer__wrapper {
//   z-index: 999999999999 !important;
// }
</style>

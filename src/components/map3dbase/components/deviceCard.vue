<template>
  <div
    class="device-card"
    :style="{ position: 'absolute', left: `${left}px`, top: `${top}px`, zIndex: 1000 }"
    v-show="visible" >
    <div class="device-icon-container">
      <div class="radar-pulse"></div>
      <div class="radar-line"></div>
      <img src="@/assets/images/map/Crane_icon.png" alt="设备图标" class="device-icon">
      <!-- 指向箭头 -->
      <div 
        v-if="showArrow" 
        class="direction-arrow" 
        :style="arrowStyle">
        <div class="arrow-line"></div>
        <div class="arrow-head"></div>
        <div class="arrow-label">{{ arrowTarget }}</div>
      </div>
    </div>
    <div class="card-content">
      <div class="card-content-deviceName">
        <span class="name">{{ deviceName }}</span>
        <span class="status" :class="statusClass">{{ deviceStatus }}</span>
        <span class="status" :class="subStatusClass">{{ subDeviceStatus }}</span>
      </div>
      <div class="card-content-workData">
        <div class="card-content-workData-left">
          <div class="card-content-workData-item">
            安全限界<span>{{ workData.safeLimit }}<sub>m</sub></span>
          </div>
        </div>
        <div class="card-content-workData-right">
          <div class="card-content-workData-item">
            作业半径:<span>{{ workData.workRadius }}<sub>m</sub></span>
          </div>
          <div class="card-content-workData-item">
            侵覆半径<span>{{ workData.invasionRadius }}<sub>m</sub></span>
          </div>
        </div>
      </div>
      <!-- 其他内容、按钮等 -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    deviceName: String,
    deviceStatus: String,
    subDeviceStatus: String,
    position: Array, // [lng, lat]
    workData: Object,
    // 箭头配置
    showArrow: {
      type: Boolean,
      default: false
    },
    arrowDirection: {
      type: Number,
      default: 45 // 箭头角度（度）
    },
    arrowLength: {
      type: Number,
      default: 100 // 箭头长度（px）
    },
    arrowTarget: {
      type: String,
      default: '作业区域' // 箭头指向的目标名称
    },
    // 目标位置坐标，用于自动计算方向和长度
    targetPosition: {
      type: Array,
      default: null // [lng, lat] 目标位置坐标
    }
  },
  data() {
    return {
      left: 0,
      top: 0,
      visible: true,
      scale: 1,
      dynamicArrowLength: 100, // 动态箭头长度
      calculatedDirection: 0, // 计算得出的箭头方向
    }
  },
  computed: {
    statusClass() {
      return this.deviceStatus === '在线' ? 'online' : 'offline'
    },
    subStatusClass() {
      return this.subDeviceStatus === '作业' ? 'online' : 'subOffline'
    },
    arrowStyle() {
      const finalDirection = this.targetPosition ? this.calculatedDirection : this.arrowDirection;
      
      return {
        '--arrow-length': `${this.dynamicArrowLength || this.arrowLength}px`,
        '--arrow-direction': `${finalDirection}deg`,
        left: '50%',
        top: '50%',
        transform: `rotate(${finalDirection}deg)`,
        transformOrigin: '0 0'
      }
    }
  },
  mounted() {
    console.log('showArrow:', this.showArrow)
    console.log('arrowDirection:', this.arrowDirection)
    console.log('arrowLength:', this.arrowLength)
    setTimeout(() => {
      this.updatePosition()
      if (window.viewer && window.viewer.scene) {
        // 监听地图渲染事件，实时刷新
        window.viewer.scene.postRender.addEventListener(this.updatePosition)
      } else {
        console.log('viewer或scene未初始化')
      }
    }, 1000);
  },
  beforeDestroy() {
    if (window.viewer && window.viewer.scene) {
      window.viewer.scene.postRender.removeEventListener(this.updatePosition)
    }
  },
  methods: {
    updatePosition() {
      if (!window.viewer || !this.position) return
      const cartesian = Cesium.Cartesian3.fromDegrees(this.position[0], this.position[1], 20) // 20米高
      const windowCoord = Cesium.SceneTransforms.wgs84ToWindowCoordinates(window.viewer.scene, cartesian)
      if (windowCoord) {
        this.left = windowCoord.x
        this.top = windowCoord.y
        const cameraHeight = window.viewer.camera.positionCartographic.height
        this.scale = 10000 / cameraHeight
        this.visible = true
        
        // 根据相机高度自动调整箭头长度
        this.updateArrowLength()
      } else {
        this.visible = false
      }
    },
    
    updateArrowLength() {
      if (!window.viewer || !this.showArrow) return
      
      // 如果有目标位置，基于实际距离计算
      if (this.targetPosition && this.position) {
        this.calculateArrowToTarget()
      } else {
        // 基于相机高度计算箭头长度
        const cameraHeight = window.viewer.camera.positionCartographic.height
        const baseLength = this.arrowLength
        // 增加缩放因子，让箭头更长
        const scaleFactor = Math.max(1, Math.min(5, cameraHeight / 5000))
        this.dynamicArrowLength = baseLength * scaleFactor
      }
    },
    
    calculateArrowToTarget() {
      if (!window.viewer || !this.position || !this.targetPosition) return
      
      try {
        const deviceCartesian = Cesium.Cartesian3.fromDegrees(this.position[0], this.position[1], 20)
        const deviceScreen = Cesium.SceneTransforms.wgs84ToWindowCoordinates(window.viewer.scene, deviceCartesian)
        
        const targetCartesian = Cesium.Cartesian3.fromDegrees(this.targetPosition[0], this.targetPosition[1], 20)
        const targetScreen = Cesium.SceneTransforms.wgs84ToWindowCoordinates(window.viewer.scene, targetCartesian)
        
        if (deviceScreen && targetScreen) {
          const deltaX = targetScreen.x - deviceScreen.x
          const deltaY = targetScreen.y - deviceScreen.y
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
          
          let angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI
          this.calculatedDirection = angle
          this.dynamicArrowLength = Math.max(20, distance)
        }
      } catch (error) {
        console.warn('计算箭头方向时出错:', error)
        this.calculatedDirection = this.arrowDirection
        this.dynamicArrowLength = this.arrowLength
      }
    }
  }
}
</script>

<style lang="less" scoped>
.device-card {
  color: #fff;
  border-radius: 100%;
  padding: 8px 16px;
  min-width: 254.09px;
  min-height: 164px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;

  .device-icon-container {
    min-width: 240px;
    min-height: 240px;
    background: rgba(108, 248, 255, 0.24);;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid #6CF8FF;
    overflow: visible;

    .radar-pulse {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      border: 2px solid #6CF8FF;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: radar-pulse 2s ease-out infinite;
      z-index: 0;
      clip-path: circle(70px at center);
    }

    .radar-line {
      position: absolute;
      top: 0%;
      left: 50%;
      width: 3px;
      height: 120px; /* 扫描半径限制在内部圆形区域 */
      background: linear-gradient(to bottom, #6CF8FF, transparent);
      transform-origin: center bottom;
      transform: translate(-50%, 0%) rotate(0deg);
      animation: radar-spin 4s linear infinite;
      z-index: 1;
      border-radius: 2px;
      box-shadow: 0 0 8px rgba(108, 248, 255, 0.6);
    }

    .device-icon {
      position: relative;
      z-index: 5;
    }

    .direction-arrow {
      position: absolute;
      z-index: 4;

      .arrow-line {
        width: var(--arrow-length, 100px);
        height: 2px;
        background: #FFFFFF;
        position: relative;
        box-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
      }
      
      .arrow-head {
        position: absolute;
        right: -8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid #FFFFFF;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.4));
      }
      
      .arrow-label {
        position: absolute;
        right: -10px;
        top: -25px;
        color: #FFFFFF;
        font-size: 11px;
        font-family: 'HuXiaoBo_KuHei';
        white-space: nowrap;
        background: rgba(0, 0, 0, 0.6);
        padding: 2px 6px;
        border-radius: 3px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transform: rotate(calc(-1 * var(--arrow-direction, 0deg)));
        transform-origin: left center;
      }
    }

    &::after {
      content: '';
      position: absolute;
      left: -30px;
      top: -30px;
      width: calc(100% + 60px);
      height: calc(100% + 60px);
      border-radius: 50%;
      pointer-events: none;
      z-index: 3;
      /* 使用SVG背景实现虚线圆形边框 */
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='48' fill='none' stroke='%236CF8FF' stroke-width='2' stroke-dasharray='10 15' /%3E%3C/svg%3E") no-repeat center;
      background-size: 100% 100%;
    }
  }

  .card-content {
    
    .card-content-deviceName {
      position: absolute;
      left: 50%;
      top: -120px;
      transform: translateX(-50%);
      width: max-content;
      min-height: 36px;
      background: url(../../../assets/images/map/Crane-label.png) no-repeat;
      background-size: 100%;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      padding: 0 32px;
      font-size: 30px;
      color: #fff;
      font-weight: bold;
      gap: 18px; // 新增，内容间距

      .name {
        margin-right: 0; // 用gap后可去掉
      }
      .status {
        margin-right: 0;
        font-size: 24px;
        font-family: 'Arial', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        font-weight: bold;
        border-radius: 6px;
        padding: 0 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.10);
        text-shadow: 0 1px 2px rgba(0,0,0,0.15);
        transition: background 0.2s;
        cursor: default;
      }
    }
    
    .card-content-workData {
      position: absolute;
      left: -20%;
      top: -60px;
      width: 120%;
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
      
      .card-content-workData-left {
        position: absolute;
        left: -160px;
        top: 25px;
      }
      
      .card-content-workData-right {
        position: absolute;
        right: -280px;
        bottom: -240px;
      }

      .card-content-workData-left,
      .card-content-workData-right {
        .card-content-workData-item {
          display: flex;
          align-items: center;
          padding: 0 24px;
          min-height: 40px;
          border-radius: 8px;
          font-size: 26px;
          font-family: 'Arial', 'PingFang SC', 'Microsoft YaHei', sans-serif;
          font-weight: 400;
          color: #fff;
          background: linear-gradient(90deg, #001044 0%, #0073FF 70%, rgba(0,115,255,0) 100%);
          box-shadow: 0 2px 8px rgba(0,0,0,0.10);
          position: relative;
          margin-bottom: 10px;
          

          span {
            margin-left: 18px;
            letter-spacing: 2px;
            font-weight: 400;
            letter-spacing: 1px;
            
            sub {
              font-size: 16px;
              margin-left: 2px;
              vertical-align: baseline;
            }
          }
        }
      }
    }

    .status {
      font-size: 12px;
      color: #FFFFFF;
      font-family: 'HuXiaoBo_KuHei';
    }

    .online {
      background: url(../../../assets/images/map/Crane-zx.png) no-repeat;
      background-size: 100% 100%;
    }

    .offline {
      background: url(../../../assets/images/map/Crane-lx.png) no-repeat;
      background-size: 100% 100%;
    }
    
    .subOffline {
      background: url(../../../assets/images/map/Crane-tj.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}

@keyframes radar-spin {
  from {
    transform: translate(-50%, 0%) rotate(0deg);
  }
  to {
    transform: translate(-50%, 0%) rotate(360deg);
  }
}

@keyframes radar-pulse {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
    border-width: 2px;
  }
  50% {
    width: 100px;
    height: 100px;
    opacity: 0.5;
    border-width: 1px;
  }
  100% {
    width: 140px;
    height: 140px;
    opacity: 0;
    border-width: 0;
  }
}
</style>

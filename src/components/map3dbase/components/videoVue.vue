<template>
  <div class="main">
    <div id="videoaa">
      <video
        :id="id"
        class="video-js"
        ref="videoPlayer"
        loop
        muted
        autoplay
      ></video>
    </div>
  </div>
</template>

<script>
//1.引入对应的视频插件以支持m3u8
import "video.js/dist/video-js.css";
import videojs from "video.js";

var player = null;
export default {
  data() {
    return {};
  },
  props: ["id", "videoUrl"],
  mounted() {},
  methods: {
    getExtension(url) {
      if (url == undefined) {
        return "video/mp4";
      }
      var urlParts = url.split("/");
      var filename = urlParts[urlParts.length - 1];
      var filenameParts = filename.split(".");
      var extension = filenameParts[filenameParts.length - 1];
      if (extension.toLowerCase() === "mp4") {
        return "video/mp4";
      } else if (extension.toLowerCase() === "m3u8") {
        return "application/x-mpegURL";
      } else if (extension.toLowerCase() === "WebM") {
        return "video/webm";
      } else if (extension.toLowerCase() === "Ogg") {
        return "video/ogg";
      } else if (extension.toLowerCase() === "mov") {
        return "video/quicktime";
      } else if (extension.toLowerCase() === "flv") {
        return "video/x-flv";
      }
    },
    open(id, url) {
      //传递url为空时候不生成视频
      if (!url) {
        return;
      }
      player = videojs(id, {
        controls: true,
        autoplay: true,
        loop: false,
        preload: "auto",
        sources: [
          {
            src: url,
            type: this.getExtension(url),
          },
        ],
      });
      setTimeout(() => {
        player.play();
      }, 500);
    },
  },
  watch: {
    videoUrl: {
      handler(newVal, oldVal) {
        let that = this;
        let url = "";
        url = `${that.videoUrl}`;
        if (!newVal && !oldVal) {
          //一直没有视频时候操作
        } else if (newVal && !oldVal) {
          //从没有视频到有视频
          console.log("创建视频");
          this.$nextTick(() => {
            let div2 = document.getElementById(that.id);
            if (!div2) {
              //没有视频容器就增加一个
              let div = document.getElementById("videoaa");
              div.innerHTML = `<video :id="${that.id}" class="video-js" style="width: 100%;height: 100%; background: rgb(28, 28, 28);" ref="videoPlayer" loop autoplay></video>`;
            }
            if (document.getElementById(that.id)) {
              this.open(that.id, url);
            } else {
              console.error("Video element not found");
            }
          });
        } else if (!newVal && oldVal) {
          //从有视频到没视频
          console.log("销毁视频");
          if (player) {
            player.dispose();
          }
        } else if (newVal != oldVal) {
          //切换视频
          console.log("切换视频");
          player.src(url);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    if (player) {
      console.log("组件销毁销毁视频");
      player.dispose();
    }
  },
};
</script>

<style lang='less' scoped>
.main {
  position: relative;
  width: 100%;
  height: 100%;
}
#videoaa {
  width: 100%;
  height: 100%;
}
.video-js {
  width: 100%;
  height: 100%;
  background: rgb(28, 28, 28);
}
</style>
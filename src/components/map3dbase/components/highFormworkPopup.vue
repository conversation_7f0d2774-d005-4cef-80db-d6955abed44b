<template>
  <div class="popup-content">
    <!-- Loading 界面 -->
    <div v-if="shouldShowLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载信息...</div>
    </div>

    <!-- 实际内容 -->
    <template v-if="!shouldShowLoading">
      <div class="popup-header">
        <div v-if="showAlertStatus" class="popup-alert">
          <div v-if="!hasAlert" class="alert-badge no-alert">无告警</div>
          <div v-if="hasAlert" class="alert-badge has-alert">有告警</div>
        </div>
        <div class="popup-title">{{ templateData?.name || "加载失败" }}</div>
        <span class="popup-close" @click="closePopup">×</span>
      </div>
      <div class="popup-body">
        <!-- 动态设备信息列表 -->
        <ul class="popup-list">
          <li v-for="field in fields" :key="field.key" v-show="field.show">
            <span class="label">{{ field.label }}</span>
            <span v-if="field.dataKey === 'lastPushTime'" class="value">{{ templateData?.record?.createTime }}</span>
            <span v-else-if="field.dataKey === 'responsiblePerson'" class="value">{{ templateData?.responsiblePerson }} ({{ templateData?.responsiblePersonTel }})</span>
            <span v-else class="value">{{ templateData[field.dataKey] }}</span>
          </li>
        </ul>
        <div v-if="attrList.length > 0" class="attr-list">
          <div class="attr-item" v-for="item in attrList" :class="{'alarm': item.alarmFlag}" :key="item.key">
            <div class="attr-label">{{ item.label }}</div>
            <div class="attr-value"><span>{{ item.value }}</span>{{ item.unit }}</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { getHighDeviceByNowRecord } from "@/api/highFormworkMonitoring";

export default {
  name: "PersonPopup",
  components: {},
  props: {
    propsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      templateData: {},
      loading: true, // 添加loading状态
      title: "",
      fields: [
        {
          key: "equipmentName",
          label: "设备名称：",
          dataKey: "name",
          defaultValue: "",
          show: true,
        },
        {
          key: "equipmentCode",
          label: "设备编号：",
          dataKey: "code",
          defaultValue: "",
          show: true,
        },
        {
          key: "highDeviceType",
          label: "监测设备类型：",
          dataKey: "deviceUnitType",
          defaultValue: "",
          show: true,
        },
        {
          key: "supplierContact",
          label: "供应商负责人：",
          dataKey: "responsiblePerson",
          defaultValue: "",
          show: true,
        },
        {
          key: "lastPushTime",
          label: "更新时间",
          dataKey: "lastPushTime",
          defaultValue: "",
          show: true,
          isMonitoringData: true,
        },
      ],
      attrList: []
    };
  },
  computed: {
    // 计算是否应该显示加载状态
    shouldShowLoading() {
      const result = this.loading && !this.templateData?.name;
      console.log("🤔 shouldShowLoading计算:", {
        loading: this.loading,
        hasTemplateName: !!this.templateData?.name,
        result: result,
      });
      return result;
    },
    // 判断是否有告警（可以根据实际业务逻辑修改）
    hasAlert() {
      // 根据设备状态判断告警 (0:正常; 1:告警)
      if (this.templateData?.record?.alarmStatus == 1) {
        return true;
      }
      
      return false;
    },
  },
  watch: {
    // 监控loading状态变化
    loading(newVal, oldVal) {
      console.log(`🔄 loading状态变化: ${oldVal} -> ${newVal}`);
    },
  },

  methods: {
    closePopup() {
      this.$bus.emit("togglePersonTrajectory", false, []);
      this.$emit("close");
    },
    getDeviceInfo() {
        getHighDeviceByNowRecord(this.propsData.equipmentCode).then((res) => {
            this.templateData = res.data
            let attrList = []
            switch(this.templateData.deviceUnitType){
                case '水平位移':
                    attrList = [
                        {label: '水平位移', value: this.templateData.record.horizontalDisplacement, key: 'horizontalDisplacement', unit: 'mm'},
                    ]
                    break
                case '竖向沉降':
                    attrList = [
                        {label: '沉降值', value: this.templateData.record.sedimentation, key: 'sedimentation', unit: 'mm'},
                    ]
                    break
                case '倾斜监测':
                    attrList = [
                        {label: 'X轴倾斜', value: this.templateData.record.angleX, key: 'angleX', unit: '°'},
                        {label: 'Y轴倾斜', value: this.templateData.record.angleY, key: 'angleY', unit: '°'},
                    ]
                    break
                case '荷重压力':
                    attrList = [
                        {label: '荷重压力', value: this.templateData.record.pressure, key: 'pressure', unit: 'KN'},
                    ]
                    break
                default:
                    break
            }
            attrList.push({label: '电量', value: this.templateData.record.erg, key: 'erg', unit: '%'})
            let alarm = this.templateData.record.alarmFix
            if (alarm && alarm.length > 0) {
                attrList.forEach(item => {
                    if (alarm.includes(item.key)) {
                        item.alarmFlag = true
                    }
                })
            }
            this.attrList = attrList
        console.log('高支模设备信息:', res)
      })
    },

    // 显示自定义错误弹窗
    showCustomError(message) {
      // 显示自定义错误弹窗
      this.$alert.error({
        title: "数据加载失败",
        message: message,
        time: new Date().toLocaleString(),
        duration: 5000,
        rightMargin: 10,
      });
    },

    // 强制停止加载状态（调试用）
    forceStopLoading() {
      console.log("🚨 强制停止loading状态");
      this.loading = false;
      this.$forceUpdate();
    },

    // 安全地设置loading状态为false
    safeSetLoadingFalse() {
      console.log("🛡️ 安全设置loading为false");

      // 方法1：直接设置
      this.loading = false;

      // 方法2：使用$set确保响应式
      this.$set(this, "loading", false);

      // 方法3：使用setTimeout延迟设置
      setTimeout(() => {
        this.loading = false;
        this.$forceUpdate();
        console.log("⏰ setTimeout设置loading为false完成");
      }, 0);

      // 方法4：使用$nextTick
      this.$nextTick(() => {
        this.loading = false;
        this.$forceUpdate();
        console.log("🔄 $nextTick设置loading为false完成");
      });

      // 方法5：延迟更长时间确保状态更新
      setTimeout(() => {
        this.loading = false;
        this.$forceUpdate();
        console.log("🕐 1秒后强制设置loading为false");
      }, 1000);
    },

    // 紧急修复：直接显示内容
    emergencyShowContent() {
      console.log("🚨 紧急修复：强制显示内容");
      this.loading = false;
      this.$set(this, "loading", false);
      this.$forceUpdate();

      // 如果还是不行，直接操作DOM
      setTimeout(() => {
        const loadingEl = document.querySelector(".loading-container");
        const contentEl = document.querySelector(".popup-header");
        if (loadingEl) {
          loadingEl.style.display = "none";
          console.log("🎯 直接隐藏loading元素");
        }
        if (contentEl) {
          contentEl.style.display = "flex";
          console.log("🎯 直接显示内容元素");
        }
      }, 100);
    },
  },

  mounted() {
    this.getDeviceInfo();
  },

  beforeDestroy() {
    console.log("PersonPopup beforeDestroy");
  },
};
</script>

<style lang="less" scoped>
.popup-content {
  width: 420px;
  min-height: 350px; /* 增加最小高度，确保头部不被挤压 */
  max-height: 85vh; /* 限制最大高度，防止超出屏幕 */
  background: url(../../../assets/images/map/pointBg.png) no-repeat top center;
  background-size: 420px 500px; /* 固定背景图片尺寸，保持头部位置不变 */
  color: #fff;
  font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* Loading 容器样式 */
.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  box-sizing: border-box;
}

/* Loading 动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4a9eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading 文字 */
.loading-text {
  color: #b8d4ff;
  font-size: 14px;
  text-align: center;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0; /* 防止header被压缩 */
  padding: 10px 20px 8px 20px;
  // min-height: 60px; /* 确保头部有足够的固定高度 */
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  z-index: 10;
  box-sizing: border-box;
  position: relative;
}

.popup-title {
  flex: 1;
  text-align: center;
  color: #fff;
  margin: 0 15px; /* 给标题左右留一些间距 */
  font-size: 16px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  font-weight: bold;
}

.popup-close {
  font-size: 24px;
  cursor: pointer;
  font-weight: normal;
  opacity: 0.9;
  min-width: 32px; /* 与左侧对称 */
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    color: #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
  }
}

.popup-body {
  flex: 1; /* 占据剩余空间，但不挤压头部 */
  padding: 8px 20px 20px 20px;
  overflow-y: auto; /* 内容溢出时显示滚动条 */
  overflow-x: hidden;
  box-sizing: border-box;
  min-height: 0; /* 确保flex子项能够正常缩放 */

  /* 自定义滚动条样式 - 仅Webkit浏览器 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1); /* Firefox */

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
.attr-list{
    display: flex;
    flex-wrap: wrap;
    column-gap: 0.74vh;
    row-gap: 1.48vh;
    .attr-item{
        width: ~"calc(33.33% - 0.74vh)";
        .attr-label{
            color: #fff;
            font-size: 1.29vh;
            background: linear-gradient(90deg, rgba(0, 81, 255, 0) 0%, rgba(13, 125, 230, 0.48) 25%, rgba(0, 68, 213, 0) 100%);
            padding: 0 1.48vh;
            height: 2.59vh;
            line-height: 2.59vh;
            margin-bottom: 0.37vh;
        }
        .attr-value{
            padding: 0 1.48vh;
            display: flex;
            align-items: center;
            height: 2.77vh;
            background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(75, 131, 183, 0.36) 76.63%, rgba(75, 131, 183, 0) 100%);
            line-height: 2.4vh;
            color: rgba(255, 255, 255, 0.5);
            font-size: 1.29vh;
            span{
                text-shadow: 0px 0px 4px rgba(0, 132, 255, 1);
                font-size: 2.22vh;
                color: #fff;
                font-family: 'DIN Pro';
            }
        }
        &.alarm{
            .attr-label{
                background: linear-gradient(90deg, rgba(162, 0, 2, 0) 0%, rgba(162, 0, 2, 0.4) 25%, rgba(162, 0, 2, 0) 100%);
            }
            .attr-value{
                background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(255, 0, 0, 0.3) 76.63%, rgba(255, 0, 0, 0) 100%);
            }
        }
    }
}
.no-data {
  text-align: center;
  font-size: 1.48vh;
  color: rgba(190, 226, 255, 1);
  margin-top: 1.48vh;
}
.popup-list {
  list-style: none;
  padding: 0;
  margin: 0 0 12px 0;
  flex-shrink: 0; /* 防止列表被压缩 */

  li {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 32px;
    box-sizing: border-box;

    &:last-child {
      border-bottom: none;
    }

    .label {
      width: 140px;
      color: #b8d4ff;
      font-size: 13px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      line-height: 1.4;

      &::before {
        content: "◆";
        margin-right: 6px;
        color: #4a9eff;
        font-size: 12px;
      }
    }

    .value {
      flex: 1;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-align: right;
      line-height: 1.4;
      word-break: break-all; /* 防止长文本溢出 */
    }
  }
}
.popup-alert {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 80px; /* 确保有足够的空间 */
}

.alert-badge {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  white-space: nowrap;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-badge.has-alert {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  animation: alertPulse 2s infinite;
}

.alert-badge.no-alert {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  opacity: 0.9;
}

@keyframes alertPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.4);
  }
}
/* 响应式调整 */
@media (max-width: 480px) {
  .popup-content {
    width: 350px;
    max-height: 70vh;
  }

  .popup-header {
    padding: 0 16px 0 16px;
    font-size: 17px;
    min-height: 55px;
  }

  .popup-body {
    padding: 8px 16px 16px 16px;
  }
}
</style>

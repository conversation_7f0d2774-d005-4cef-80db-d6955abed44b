import Vue from 'vue';
// import Popup from './components/popup.vue';
// import Popup2 from './components/popup2.vue';
import AssetManagementPopup from './components/AssetManagementPopup.vue';
import CustomPopup from './components/CustomPopup.vue';
import LocationTimePopup from './components/LocationTimePopup.vue';
import DcCarousel from './components/dcCarousel.vue';
import alert from '../common/alert.vue'
import PointPopup from './components/pointPopup.vue'
import PersonPopup from './components/personPopup.vue'
import cameraViewManager from '@/utils/CameraViewManager.js'; // 导入相机视角管理器
import HighFormworkPopup from './components/highFormworkPopup.vue'

export default class Bubble {
  static currentInstance = null;

  constructor(val) {
    console.log(val, 'val')
    // 关闭上一个实例
    if (Bubble.currentInstance && typeof Bubble.currentInstance.windowClose === 'function') {
      Bubble.currentInstance.windowClose();
    }
    Bubble.currentInstance = this;
    console.log('Bubble构造函数参数:', val)
    this.viewer = val.viewer;
    this.isClosing = false; // 添加关闭状态标记
    this.data = val.data || {}; // 保存data，便于后续判断above

    // 处理position参数，确保是Cartesian3格式
    if (val.data && val.data.position) {
      if (Array.isArray(val.data.position)) {
        // 如果是数组 [longitude, latitude, height]
        this.position = Cesium.Cartesian3.fromDegrees(
          val.data.position[0],
          val.data.position[1],
          val.data.position[2] || 0
        );
      } else if (val.data.position.x !== undefined && val.data.position.y !== undefined && val.data.position.z !== undefined) {
        // 如果已经是Cartesian3格式
        this.position = val.data.position;
      } else if (val.data.position.longitude !== undefined && val.data.position.latitude !== undefined) {
        // 如果是经纬度对象格式
        this.position = Cesium.Cartesian3.fromDegrees(
          val.data.position.longitude,
          val.data.position.latitude,
          val.data.position.height || 0
        );
      } else {
        console.error('无法识别的position格式:', val.data.position);
        this.position = null;
      }
    } else {
      console.error('缺少position参数');
      this.position = null;
    }

    console.log('处理后的position:', this.position);

    this.vmInstance = null;
    this.vueContainer = null;
    this.elWidth = null;
    this.elHeight = null;
    this.offset = val.data.offset ? val.data.offset : {
      offsetX: 0,
      offsetY: 0,
    };
    this.store = val.store;
    this.router = val.router

    // 动态注册组件
    // Vue.component('Popup', Popup);
    // Vue.component('Popup2', Popup2);
    Vue.component('AssetManagementPopup', AssetManagementPopup)
    Vue.component('CustomPopup', CustomPopup);
    Vue.component('LocationTimePopup', LocationTimePopup);
    Vue.component('DcCarousel', DcCarousel);
    Vue.component('alert', alert)
    Vue.component('PointPopup', PointPopup)
    Vue.component('PersonPopup', PersonPopup)
    Vue.component('HighFormworkPopup', HighFormworkPopup)

    console.log(this.store, this.router)

    // 选择 Popup 组件
    const Component = val.component
    // 创建 Vue 实例并渲染组件
    this.vmInstance = new Vue({
      store: this.store,
      router: this.router,
      render: h => h(Component, {
        props: {
          propsData: val.data,
          store: this.store,
          router: this.router,
        },
        on: {
          close: () => {
            console.log('🔔 弹窗组件触发close事件')
            this.windowClose()
          }
        }
      }),
      methods: {
        closeEvent() {
          this.windowClose();
        },
      },
    });
    // 创建一个容器用于装载弹框
    this.vueContainer = document.createElement('div');
    this.vueContainer.id = `popup-container${val.type}`;
    this.vueContainer.className = 'bubble-content'; // 添加类名，让alert组件能检测到
    this.vueContainer.style.zIndex = '999999'; // 设置最高层级
    this.vueContainer.style.position = 'absolute';
    let div = document.createElement('div');
    div.style.zIndex = '999999'; // 设置子元素也使用最高层级
    // 将弹窗添加到body，确保层级最高
    document.body.appendChild(this.vueContainer);
    this.vueContainer.appendChild(div);
    // 挂载 Vue 实例
    this.vmInstance.$mount(div);

    // 等待Vue实例挂载完成后再初始化位置
    this.vmInstance.$nextTick(() => {
      this.initFirstPos();
      this.addPostRender();
    });
  }

  addPostRender() {
    this.viewer.scene.postRender.addEventListener(this.postRender, this);
  }

  postRender() {
    try {
      if (!this.vueContainer || !this.vueContainer.style) return;

      // 确保position是Cartesian3格式
      if (!this.position || !Cesium.defined(this.position)) {
        console.warn('弹窗位置未定义');
        return;
      }

      // 使用正确的坐标转换方法
      const windowPosition = this.viewer.scene.cartesianToCanvasCoordinates(this.position);

      // 检查坐标转换是否成功
      if (!windowPosition || !Cesium.defined(windowPosition)) {
        this.vueContainer.style.display = 'none';
        console.warn('坐标转换失败:', this.position);
        return;
      }

    // 更新弹窗尺寸（防止内容动态变化）
    this.updateSize();

    // 获取画布尺寸
    const canvasWidth = this.viewer.scene.canvas.width || 800;
    const canvasHeight = this.viewer.scene.canvas.height || 600;

    // 获取Cesium容器的位置，因为弹窗现在添加到body，需要加上容器的offset
    const cesiumContainer = this.viewer.cesiumWidget.container;
    const containerRect = cesiumContainer.getBoundingClientRect();

    // 计算弹窗位置（相对于Cesium容器）
    const showAbove = this.offset.above || (this.data && this.data.above);

    // 确保尺寸有效 - 为alert组件使用特殊尺寸
    const isAlertComponent = this.data && this.data.bubbleId && this.data.bubbleId.includes('alert');
    let elWidth = this.elWidth || 420;
    let elHeight = this.elHeight || 500;

    // alert组件的特殊尺寸
    if (isAlertComponent) {
      elWidth = 280; // alert组件的固定宽度
      elHeight = 100; // alert组件的固定高度
    }

    // 计算动态偏移量，根据地图缩放级别调整
    const dynamicOffset = this.calculateDynamicOffset();

    // 水平居中
    let popupX = windowPosition.x - elWidth / 2 + this.offset.offsetX + dynamicOffset.x;
    let popupY;
    if (showAbove) {
      // 弹窗底边对点位（上方）
      popupY = windowPosition.y - elHeight + this.offset.offsetY + dynamicOffset.y;
    } else {
      // 弹窗顶边对点位（下方）
      popupY = windowPosition.y + this.offset.offsetY + dynamicOffset.y;
    }

    // 边界检查 - 防止弹窗超出屏幕
    if (popupX < 10) popupX = 10;
    if (popupX + elWidth > canvasWidth - 10) popupX = canvasWidth - elWidth - 10;
    if (popupY + elHeight > canvasHeight - 10) popupY = windowPosition.y - elHeight - 20 + this.offset.offsetY + dynamicOffset.y;
    if (popupY < 10) popupY = 10;

    // 由于弹窗现在在body下，需加上Cesium容器的offset
    this.vueContainer.style.left = `${Math.round(popupX + containerRect.left)}px`;
    this.vueContainer.style.top = `${Math.round(popupY + containerRect.top)}px`;
    this.vueContainer.style.zIndex = '999999';
    this.vueContainer.style.pointerEvents = 'auto';

    // 可见性判断
    const camerPosition = this.viewer.camera.position;
    let height = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
    height += this.viewer.scene.globe.ellipsoid.maximumRadius;

    const isVisible = !(Cesium.Cartesian3.distance(camerPosition, this.position) > height) &&
                     this.viewer.camera.positionCartographic.height < 50000000 &&
                     windowPosition.x >= -elWidth && windowPosition.x <= canvasWidth + elWidth &&
                     windowPosition.y >= -elHeight && windowPosition.y <= canvasHeight + elHeight;

    this.vueContainer.style.display = isVisible ? 'block' : 'none';
    } catch (error) {
      console.error('弹窗定位过程中发生错误:', error);
      // 发生错误时隐藏弹窗
      if (this.vueContainer && this.vueContainer.style) {
        this.vueContainer.style.display = 'none';
      }
    }
  }

  /**
   * 计算动态偏移量，根据地图缩放级别调整
   */
  calculateDynamicOffset() {
    try {
      // 获取当前相机高度（缩放级别）
      const cameraHeight = this.viewer.camera.positionCartographic.height;

      // 获取当前缩放级别
      const zoomLevel = cameraViewManager.getCurrentZoomLevel();

      // 基础偏移量
      const baseOffsetX = this.offset.offsetX || 0;
      const baseOffsetY = this.offset.offsetY || 0;

      // 根据缩放级别计算动态偏移
      let dynamicOffsetX = 0;
      let dynamicOffsetY = 0;

      // 缩放级别对应的偏移调整
      if (zoomLevel <= 10) {
        // 高缩放级别（近距离），减少偏移
        dynamicOffsetX = baseOffsetX * 0.5;
        dynamicOffsetY = baseOffsetY * 0.5;
      } else if (zoomLevel <= 15) {
        // 中等缩放级别，使用基础偏移
        dynamicOffsetX = baseOffsetX;
        dynamicOffsetY = baseOffsetY;
      } else {
        // 低缩放级别（远距离），增加偏移
        dynamicOffsetX = baseOffsetX * 1.5;
        dynamicOffsetY = baseOffsetY * 1.5;
      }

      // 对于alert组件，使用特殊的偏移计算
      const isAlertComponent = this.data && this.data.bubbleId && this.data.bubbleId.includes('alert');

      if (isAlertComponent) {
        // alert组件的特殊偏移逻辑
        // 使用更精确的缩放级别计算
        const scaleFactor = Math.pow(2, 15 - zoomLevel); // 以15级为基准
        const adjustedScaleFactor = Math.max(0.1, Math.min(3, scaleFactor)); // 限制缩放因子范围

        dynamicOffsetX = 170 * adjustedScaleFactor;
        dynamicOffsetY = -160 * adjustedScaleFactor;

        // console.log('🎯 alert组件动态偏移计算:', {
        //   zoomLevel: zoomLevel,
        //   scaleFactor: scaleFactor,
        //   adjustedScaleFactor: adjustedScaleFactor,
        //   dynamicOffsetX: dynamicOffsetX,
        //   dynamicOffsetY: dynamicOffsetY
        // });
      } else {
        // 其他组件的偏移计算
        const scaleFactor = Math.pow(2, 15 - zoomLevel); // 以15级为基准
        const adjustedScaleFactor = Math.max(0.1, Math.min(3, scaleFactor)); // 限制缩放因子范围

        dynamicOffsetX = baseOffsetX * adjustedScaleFactor;
        dynamicOffsetY = baseOffsetY * adjustedScaleFactor;
      }

      return {
        x: Math.round(dynamicOffsetX),
        y: Math.round(dynamicOffsetY),
        zoomLevel: zoomLevel,
        cameraHeight: cameraHeight
      };

    } catch (error) {
      console.warn('计算动态偏移量失败:', error);
      return { x: 0, y: 0, zoomLevel: 0, cameraHeight: 0 };
    }
  }

  //初始化位置
  initFirstPos() {
    // 设置初始样式
    this.vueContainer.style.position = 'absolute';
    this.vueContainer.style.zIndex = '999999';
    this.vueContainer.style.pointerEvents = 'auto';

    // 使用Vue实例的$nextTick来确保组件渲染完成
    this.vmInstance.$nextTick(() => {
      this.updateSize();
    });

    // 额外的延迟确保尺寸获取正确
    setTimeout(() => {
      this.updateSize();
    }, 150);
  }

  // 更新弹窗尺寸
  updateSize() {
    if (!this.vueContainer) return;

    try {
      // 检查是否是alert组件
      const isAlertComponent = this.data && this.data.bubbleId && this.data.bubbleId.includes('alert');

      // 如果是alert组件，使用固定尺寸
      if (isAlertComponent) {
        this.elWidth = 280;
        this.elHeight = 100;
        // console.log('🎯 alert组件使用固定尺寸:', { width: this.elWidth, height: this.elHeight });
        return;
      }

      // 强制重新计算布局
      this.vueContainer.style.visibility = 'hidden';
      this.vueContainer.style.display = 'block';

      // 获取实际渲染的宽高
      let newWidth = this.vueContainer.offsetWidth || this.vueContainer.clientWidth;
      let newHeight = this.vueContainer.offsetHeight || this.vueContainer.clientHeight;

      // 如果offsetWidth为0，尝试从子元素获取
      if (newWidth <= 0 && this.vueContainer.children.length > 0) {
        const firstChild = this.vueContainer.children[0];
        newWidth = firstChild.offsetWidth || firstChild.clientWidth;
        newHeight = firstChild.offsetHeight || firstChild.clientHeight;
      }

      // 如果还是0，从computedStyle获取
      if (newWidth <= 0 || newHeight <= 0) {
        const computedStyle = window.getComputedStyle(this.vueContainer);
        const width = computedStyle.width;
        const height = computedStyle.height;

        if (width && width.includes('px')) newWidth = Number(width.split("px")[0]);
        if (height && height.includes('px')) newHeight = Number(height.split("px")[0]);
      }

      // 恢复可见性
      this.vueContainer.style.visibility = 'visible';

      // 只有当尺寸有效时才更新
      if (newWidth > 0 && newHeight > 0) {
        this.elWidth = newWidth;
        this.elHeight = newHeight;
        // console.log('弹窗尺寸更新:', { width: this.elWidth, height: this.elHeight });
      }

      // 如果仍然获取不到尺寸，使用默认值
      if (!this.elWidth || this.elWidth <= 0) this.elWidth = 420; // CustomPopup的默认宽度
      if (!this.elHeight || this.elHeight <= 0) this.elHeight = 500; // CustomPopup的默认高度

    } catch (error) {
      console.warn('获取弹窗尺寸失败:', error);
      // 使用默认尺寸
      this.elWidth = this.elWidth || 420;
      this.elHeight = this.elHeight || 500;
    }
  }

  windowClose() {
    if (this.isClosing || !this.vmInstance) return; // 如果正在关闭则直接返回

    this.isClosing = true; // 设置关闭状态

    try {
      // 移除DOM元素
      if (this.vueContainer) {
        this.vueContainer.remove();
      }

      // 移除事件监听器
      if (this.viewer && this.viewer.scene) {
        this.viewer.scene.postRender.removeEventListener(this.postRender, this);
      }

      // 最后才触发关闭事件
      this.vmInstance.$emit('close');

      // 销毁Vue实例
      this.vmInstance.$destroy();

      // 清理引用
      this.vmInstance = null;
      this.vueContainer = null;
    } catch (error) {
      console.warn('关闭弹窗时发生错误:', error);
    }
  }
}

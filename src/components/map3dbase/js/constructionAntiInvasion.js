// 施工防侵限
import { getCranePersonList, getCranePersonAreaList } from '@/api/index';
import { createImageByIndex, createImageByTime, createImageByAreaName} from './customCanvasLabel';
import personVideoPlay from './personVideo';
import { findLocation } from './utils';
// 人员轨迹点id前缀
const PERSON_TRAJECTORY_POINT_ID_PREFIX = 'personTrajectoryPoint'
// 人员电子围栏id前缀
const PERSON_ELECTRONIC_FENCE_ID_PREFIX = 'personElectronicFence'
// 人员点位id前缀
const PERSON_IMAGE_POINT_ID_PREFIX = 'personImagePoint'
export default class ConstructionAntiInvasion {
  constructor(vm) {
    this.vm = vm;
    this.iconObj = {
      person: {},
      area: {}
    }
  }
  async init() {
    console.log('施工防侵限初始化');
    this.setupTrajectoryEventListener();
    await this.setupPersonDataTimer();
    
  }
  /**
   * 设置人员数据定时器
   */
  async setupPersonDataTimer() {
    const updateFun = async () => {
      await this.loadCranePersonArea()
      await this.loadCranePersonList();
    }
    if (this.personDataTimer) {
      clearInterval(this.personDataTimer)
    }
    updateFun()
    this.personDataTimer = setInterval(() => {
      updateFun()
    }, 10000)
  }
  clear() {
    if (this.personDataTimer) {
      clearInterval(this.personDataTimer)
    }
    console.log('清除人员数据')
    try {
      this.vm.$bus.off('togglePersonTrajectory');
      let elArr = document.querySelectorAll('.person-label-3d')
      elArr.forEach(item => {
        item.remove()
      })
      this.clearScreenSpaceEventHandler()
    } catch (error) {
      console.error('清除人员数据时发生错误:', error)
    }
  }
  // 鼠标滑过事件
  screenSpaceEventHandler() {
    let label = null
    window.viewer.screenSpaceEventHandler.setInputAction((movement) => {
        if (label) {
            label._children.forEach(item => {
                item.show = false
            })
        }
        const pickedObject = window.viewer.scene.pick(movement.endPosition);
        // 判断是否是轨迹点
        if (Cesium.defined(pickedObject) && pickedObject.id && typeof pickedObject.id.id === 'string' && pickedObject.id.id.includes('personTrajectoryPoint')) {
            console.log('鼠标滑过事件', pickedObject)
            pickedObject.id._children.forEach(item => {
                item.show = true
            })
            label = pickedObject.id
        } else {
            label = null
        }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }
  // 清除鼠标滑过事件
  clearScreenSpaceEventHandler() {
    window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }
  /**
     * 设置人员轨迹显示事件监听器
     */
  setupTrajectoryEventListener() {
    this.vm.$bus.off('togglePersonTrajectory');
    this.vm.$bus.on('togglePersonTrajectory', (isShow, val) => {
      console.log('🎯 togglePersonTrajectory 事件被触发，数据:', isShow);
      if (isShow) {
        // 显示轨迹
        this.togglePersonImagePointsAndCraneData(false)
        // 轨迹调用方法
        let data = val.map(i => {
          return {
            id: i.id,
            lng: Number(i.locationLng),
            lat: Number(i.locationLat),
            time: i.locationTime
          };
        });
        this.addPersonTrajectoryPoints(data, {
          showSequence: true,
          autoRemove: true
        });
        this.screenSpaceEventHandler();
      } else {
        this.togglePersonImagePointsAndCraneData(true)
        this.clearScreenSpaceEventHandler()
        this.clearPersonTrajectoryPoints()
      }
    });
    
    console.log('📝 已设置 showPersonTrajectory 事件监听器');
  }
  /**人员轨迹显示时，隐藏人员图片点位及吊车数据 
   * 人员轨迹隐藏时，重新显示人员图片点位及吊车数据 
   * @param {boolean} isShow 是否显示
  */
  togglePersonImagePointsAndCraneData(isShow) {
    let elArr = document.querySelectorAll('.person-label-3d')
    elArr.forEach(item => {
      if (isShow) {
        item.style.display = 'block'
      } else {
        item.style.display = 'none'
      }
    })
    let radArr = document.querySelectorAll('.radar-label')
    radArr.forEach(item => {
      if (isShow) {
        item.style.display = 'block'
      } else {
        item.style.display = 'none'
      }
    })
    window.viewer.entities.values.forEach(item => {
      if (item.id && typeof item.id === 'string' &&
         (item.id.startsWith(`${PERSON_IMAGE_POINT_ID_PREFIX}_`) || 
          item.id.startsWith('videoMonitoring_') 
        )) {
        item.show = isShow
      }
    })
  }
  // 清除轨迹点
  clearPersonTrajectoryPoints() {
    this.vm.removeMapElement(window.viewer, `${PERSON_TRAJECTORY_POINT_ID_PREFIX}_`)
  }
  /**
     * 添加轨迹点标记（canvas合成图片，彻底防闪烁）
     */
  async addPersonTrajectoryPoints(trajectoryPoints = [], options = {}) {
    const {
      showSequence = true,      // 是否显示序号
      autoRemove = true,        // 是否自动清除之前的轨迹点
      idPrefix = PERSON_TRAJECTORY_POINT_ID_PREFIX // ID前缀
    } = options

    try {
      // 自动清除之前的轨迹点
      if (autoRemove) {
        this.clearPersonTrajectoryPoints()
      }

      if (!Array.isArray(trajectoryPoints) || trajectoryPoints.length === 0) {
        console.warn('轨迹点数据为空或格式不正确')
        return
      }

      for (let index = 0; index < trajectoryPoints.length; index++) {
        const point = trajectoryPoints[index]
        const {
          id,
          lng,
          lat,
          time,
          status = 'normal'
        } = point

        if (!lng || !lat || !time) {
          console.warn(`轨迹点数据不完整:`, point)
          continue
        }

        // 背景图片
        let backgroundImage, timeBgImage
        
        if (status === 'alarm' || status === 'warning') {
          backgroundImage = require('../../../assets/images/map/label_icon_bg2.png')
          timeBgImage = require('../../../assets/images/map/label_box_bg2.png')
        } else {
          backgroundImage = require('../../../assets/images/map/label_icon_bg1.png')
          timeBgImage = require('../../../assets/images/map/label_box_bg1.png')
        }

        // 序号
        const sequenceNumber = showSequence ? (index + 1) : ''
        // 右侧时间
        const displayTime = time

        // 合成图片（序号+六芒星+时间）
        const img = await createImageByIndex(sequenceNumber, backgroundImage, true, trajectoryPoints.length)
        const timeImg = await createImageByTime(displayTime, timeBgImage)
        let iconId = `${idPrefix}_icon_${id || index}`
        let boxId = `${idPrefix}_box_${id || index}`
        let iconEntity = window.viewer.entities.getById(iconId)
        let boxEntity = window.viewer.entities.getById(boxId)
        if (iconEntity) {
          iconEntity.billboard.image = img.url
          boxEntity.billboard.image = timeImg.url
        } else {
          let icon = window.viewer.entities.add({
            id: iconId,
            name: `人员轨迹点${sequenceNumber}_标签`,
            position: Cesium.Cartesian3.fromDegrees(lng, lat, 2),
            billboard: {
              image: img.url,
              scale: 1.0,
              sizeInMeters: false,
              horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
              verticalOrigin: Cesium.VerticalOrigin.LEFT,
              pixelOffset: new Cesium.Cartesian2(-15, -20),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
              heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,
              eyeOffset: new Cesium.Cartesian3(0, 0, 30),
              scaleByDistance: undefined,
              translucencyByDistance: undefined,
              pixelOffsetScaleByDistance: undefined,
              width: img.width,
              height: img.height
            },
          })
          let box = window.viewer.entities.add({
              id: boxId,
              show: false,
              name: `人员轨迹点${sequenceNumber}_时间`,
              position: Cesium.Cartesian3.fromDegrees(lng, lat, 2),
              parent: icon,
              billboard: {
                image: timeImg.url,
                scale: 1.0,
                sizeInMeters: false,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.LEFT,
                pixelOffset: new Cesium.Cartesian2(-2, -20),
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,
                eyeOffset: new Cesium.Cartesian3(0, 0, 30),
                scaleByDistance: undefined,
                translucencyByDistance: undefined,
                pixelOffsetScaleByDistance: undefined,
                width: timeImg.width,
                height: timeImg.height
              },
            })
        }
      }

      // 添加连接轨迹点的蓝色线段
      if (trajectoryPoints.length > 1) {
        const linePositions = trajectoryPoints.map(point =>
          Cesium.Cartesian3.fromDegrees(point.lng, point.lat, 1)
        )

        window.viewer.entities.add({
          id: `${idPrefix}_line`,
          name: '轨迹连接线',
          polyline: {
            positions: linePositions,
            width: 3,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.fromCssColorString('#4FC2FF'),
              dashLength: 16.0
            }),
            clampToGround: true,
            followSurface: true
          }
        })
      }

      console.log(`成功添加 ${trajectoryPoints.length} 个轨迹点和连接线`)

    } catch (error) {
      console.error('添加轨迹点时发生错误:', error)
    }
  }
  /**
   * 加载人员电子围栏列表
   */
  async loadCranePersonArea() {
    const { data: { records: personArea } } = await getCranePersonAreaList({
      page: {
        current: 1,
        size: -1
      },
      customQueryParams: {}
    });
    let list = personArea.filter(k => k.enabledFlag === 1 && k.location)
    console.log('人员电子围栏列表:', list);
    try {
      for (let i = 0; i < list.length; i++) {
        let item = list[i]
        let lineArr = JSON.parse(item.location);
        // 获取围栏多边形
        let polygonId = `${PERSON_ELECTRONIC_FENCE_ID_PREFIX}_Polygon_${item.id || i}`
        let polygonEntity = window.viewer.entities.getById(polygonId)
        // 获取围栏线
        let lineId = `${PERSON_ELECTRONIC_FENCE_ID_PREFIX}_Line_${item.id || i}`
        let lineEntity = window.viewer.entities.getById(lineId)
        // 获取围栏标签图片
        let areaNameImg = {}
        if (!this.iconObj.area[item.regionName]) {
          this.iconObj.area[item.regionName] = {}
        }
        if (item.alarmFlag) {
          if (this.iconObj.area[item.regionName].alarm) {
            areaNameImg = this.iconObj.area[item.regionName].alarm
          } else {
            areaNameImg = await createImageByAreaName(item.regionName, item.alarmFlag)
            this.iconObj.area[item.regionName].alarm = areaNameImg
          }
        } else {
          if (this.iconObj.area[item.regionName].normal) {
            areaNameImg = this.iconObj.area[item.regionName].normal
          } else {
            areaNameImg = await createImageByAreaName(item.regionName, item.alarmFlag)
            this.iconObj.area[item.regionName].normal = areaNameImg
          }
        }
        let areaNameId = `${PERSON_ELECTRONIC_FENCE_ID_PREFIX}_Name_${item.id || i}`
        let areaNameEntity = window.viewer.entities.getById(areaNameId)
        // 获取围栏标签位置
        let pos = findLocation(lineArr)
        if (polygonEntity && lineEntity && areaNameEntity) {
          let pcolor = item.alarmFlag ? '#C40003' : '#0053FF'
          polygonEntity.polygon.positions = lineArr
          polygonEntity.polygon.material = Cesium.Color.fromCssColorString(pcolor).withAlpha(0.25)
          let positions = [...lineArr, lineArr[0], lineArr[1]]
          let lcolor = item.alarmFlag ? '#C40003' : '#4FC2FF'
          lineEntity.polyline.positions = Cesium.Cartesian3.fromDegreesArray(positions.flat ? positions.flat() : positions)
          lineEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString(lcolor),
            dashLength: 16
          })
          areaNameEntity.billboard.image = areaNameImg.url
          areaNameEntity.billboard.rotation = pos.rotation
        } else {
          if (polygonEntity) {
            polygonEntity.remove()
          }
          if (lineEntity) {
            lineEntity.remove()
          }
          if (areaNameEntity) {
            areaNameEntity.remove()
          }
          this.vm.addMapElement(window.viewer, {
            id: polygonId,
            type: 'polygon',
            positions: lineArr,
            color: item.alarmFlag ? '#C40003' : '#0053FF'
          });
          this.vm.addMapElement(window.viewer, {
            id: lineId,
            type: 'line',
            positions: [...lineArr, lineArr[0], lineArr[1]],
            color: item.alarmFlag ? '#C40003' : '#4FC2FF',
            width: 3,
            dashPattern: 255
          });
          // 在左下角斜放区域名称标签
          const entity = window.viewer.entities.add({
            id: areaNameId,
            // deviceType: item.data.equipmentTypeId, // 添加设备类型标识
            position: Cesium.Cartesian3.fromDegrees(pos.lng, pos.lat, 0),
            billboard: {
              image: areaNameImg.url,
              scale: 1.0,
              heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
              pixelOffset: new Cesium.Cartesian2(-15, 0),
              rotation: pos.rotation,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
              distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 10000),
            },
          });
        }
      }
    } catch (error) {
      console.error('加载人员电子围栏列表时发生错误:', error)
    }
  }
  /**
     * 加载吊车安全监测-人员列表点位
     */
  async loadCranePersonList() {
    const { data: personList } = await getCranePersonList();

    const points = personList
      .filter(item => item.latestLocationData && item.latestLocationData.locationLng !== null && item.latestLocationData.locationLat !== null)
      .map(item => ({
        id: item.person.id,
        lng: Number(item.latestLocationData.locationLng),
        lat: Number(item.latestLocationData.locationLat),
        name: item.person.realName,
        data: {
          ...item,
          status: item.limitAlarmFlag ? 1 : '',
          onlineState: item.device.onlineState
        }
      }));
      console.log('吊车安全监测-人员列表:', points);
    this.addImagePoints3(points);
  }
  // 批量添加人员图片点位，支持名称和点击弹窗,不支持随机弹窗
  async addImagePoints3(points, onClick) {
    console.log(`开始添加 ${points.length} 个图片点位`);
    let successCount = 0;

    // 创建坐标映射，用于记录相同坐标的点位数量
    const coordMap = new Map();
    points.forEach(item => {
      const coordKey = `${item.lng},${item.lat}`;
      coordMap.set(coordKey, (coordMap.get(coordKey) || 0) + 1);
    });

    // 记录每个坐标已经使用的高度偏移次数
    const coordOffsetCount = new Map();
    const equipmentTypeName = '司机行为识别'
    for (let index = 0; index < points.length; index++) {
      const item = points[index];
      try {
        console.log(item)
        // 检查坐标数据有效性
        if (!item.lng || !item.lat || !item.data) {
          console.warn(`点位数据无效，跳过:`, item);
          continue;
        }
        const coordKey = `${item.lng},${item.lat}`;
        const totalPoints = coordMap.get(coordKey);
        const currentOffset = coordOffsetCount.get(coordKey) || 0;

        // 计算高度偏移
        let heightOffset = 0;
        if (totalPoints > 1) {
          // 如果有多个点位在同一坐标，设置不同的高度
          heightOffset = currentOffset * 5; // 每个点位高度差5米
          coordOffsetCount.set(coordKey, currentOffset + 1);
        }
        let image = null
        if (!this.iconObj.person[item.name]) {
          this.iconObj.person[item.name] = {}
        }
        if (item.data.onlineState === 0) {
          // 离线
          if (this.iconObj.person[item.name].offline) {
            image = this.iconObj.person[item.name].offline
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 0)
            this.iconObj.person[item.name].offline = image
          }
        } else if (item.data.status === 1) {
          // 告警
          if (this.iconObj.person[item.name].alarm) {
            image = this.iconObj.person[item.name].alarm
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 1)
            this.iconObj.person[item.name].alarm = image
          }
        } else {
          // 正常
          if (this.iconObj.person[item.name].normal) {
            image = this.iconObj.person[item.name].normal
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 2)
            this.iconObj.person[item.name].normal = image
          }
        }
        // 获取人员图片点位
        let id = `${PERSON_IMAGE_POINT_ID_PREFIX}_${item.id}`
        let entity = window.viewer.entities.getById(id)
        // 获取告警标签
        let htmlId = id + '_label'
        let pos = Cesium.Cartesian3.fromDegrees(item.lng, item.lat, heightOffset)
        if (entity) {
          entity.billboard.image = image
          entity.position = pos
          this.getPersonLabelHtml(item.data, htmlId)
        } else {
          // 1. 添加billboard
          entity = window.viewer.entities.add({
            id: id,
            position: pos,
            billboard: {
              image: image,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
              scale: 1.0,
              heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
              alignedAxis: Cesium.Cartesian3.ZERO,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
          });
          entity._isCustomMapPoint = true;
          // 2. 绑定点击事件
          entity._customData = item.data;
          // 添加人员上的按钮，和告警信息
          let personLabelEL = this.getPersonLabelHtml(item.data, htmlId)
          let base3dmap = document.getElementById('base3dmap')
          if (base3dmap) {
            base3dmap.appendChild(personLabelEL)
          }
          console.log('entity position', entity.position)
          function updateLabelPosition() {
            // 计算正东方向的标签位置
            let position = entity.position.getValue(new Cesium.JulianDate()); // 获取当前位置的 Cartesian3 坐标
            let cartographic = Cesium.Cartographic.fromCartesian(position); // 将 Cartesian3 转换为 Cartographic
            let longitude = Cesium.Math.toDegrees(cartographic.longitude); // 经度转换为度
            let latitude = Cesium.Math.toDegrees(cartographic.latitude); 
            const cartesian = Cesium.Cartesian3.fromDegrees(longitude, latitude, heightOffset)
            const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
            if (Cesium.defined(canvasPosition)) {
              personLabelEL.style.left = `${canvasPosition.x}px`
              personLabelEL.style.bottom = `${window.viewer.scene.canvas.clientHeight - canvasPosition.y + 50}px`
            }
          }
          window.viewer.scene.postRender.addEventListener(updateLabelPosition)
          updateLabelPosition()
        }
        successCount++;

      } catch (error) {
        console.error(`添加点位 ${index} 时发生错误:`, error, item);
      }
    }
    console.log(`成功添加 ${successCount} 个图片点位，失败 ${points.length - successCount} 个`);
  }
  getPersonLabelHtml(data, id) {
    let pop = '<div class="person-content-3d">'
    // 告警信息
    if (data.status === 1) {
      pop += '<div class="person-alarm-3d">'
      pop += `<div class="person-alarm-item-3d">${data.newlestLimitAlarm.createTime}</div>`
      pop += `<div class="person-alarm-item-3d">${data.person.realName}</div>`
      pop += `<div class="person-alarm-item-3d">${data.newlestLimitAlarm.bigScreenAlarmContent}</div>`
      pop += '</div>'
    }
    // 2个按钮
    // 创建按钮容器
    const buttonContainer = document.createElement('div')
    buttonContainer.className = 'person-button-3d'

    // 创建轨迹按钮
    const trackButton = document.createElement('div')
    trackButton.className = 'person-button-item-3d'
    trackButton.textContent = '查看轨迹'
    trackButton.addEventListener('click', () => {
        console.log('查看轨迹', data)
        this.vm.createBubble(
            {
              ...data,
              position: [
                Number(data.latestLocationData.locationLng),
                Number(data.latestLocationData.locationLat)
              ],
              personType: 'person'
            },
            0,
            'PersonPopup'
          )
    })

    // 创建视频按钮
    const videoButton = document.createElement('div')
    videoButton.className = 'person-button-item-3d'
    videoButton.textContent = '实时视频'
    videoButton.addEventListener('click', async () => {
        console.log('实时视频', data)
        await personVideoPlay(data.device.code)
    })

    buttonContainer.appendChild(trackButton)
    buttonContainer.appendChild(videoButton)
    let html = document.getElementById(id)
    if (!html){
      html = document.createElement('div')
      html.className = 'person-label-3d'
      html.id = id
    }
    html.innerHTML = pop
    html.appendChild(buttonContainer)
    return html
  }
}

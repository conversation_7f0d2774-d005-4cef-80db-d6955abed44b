import { getMapConfig } from '@/api/index'

// 默认配置，作为备用
const defaultConfig = {
    userName: 'whwt02',
    password: 'WaHuTTmk#)40&oP$.*'
}

export default async function personVideoPlay (deviceCode = '') {
    if (!deviceCode) {
        return
    }
    
    try {
        // 从后台获取配置
        const res = await getMapConfig('SCREEN_LOCATION_CONFIG')
        let config = JSON.parse(res.data.value)
        
        // 获取taidingConfig配置
        let userName = defaultConfig.userName
        let password = defaultConfig.password
        
        if (config.taidingConfig) {
            userName = config.taidingConfig.userName || defaultConfig.userName
            password = config.taidingConfig.password || defaultConfig.password
        }
        console.log("config.taidingConfig.userName, password", userName, password)
        var js = {};
        js.user = userName;
        js.pwd = password;
        js.server = 'rw.tideen.com';
        js.port = 5000;
        js.action = 3;
        js.target = deviceCode;
        js.params1 = '';
        js.params2 = '';
        var jsonstring = JSON.stringify(js);
        jsonstring = escape(jsonstring);
        let videoUrl = "tideen://" + jsonstring;
        window.open(videoUrl, '_blank')
    } catch (error) {
        console.error('获取视频配置失败:', error)
        // 如果获取配置失败，使用默认配置
        var js = {};
        js.user = defaultConfig.userName;
        js.pwd = defaultConfig.password;
        js.server = 'rw.tideen.com';
        js.port = 5000;
        js.action = 3;
        js.target = deviceCode;
        js.params1 = '';
        js.params2 = '';
        var jsonstring = JSON.stringify(js);
        jsonstring = escape(jsonstring);
        let videoUrl = "tideen://" + jsonstring;
        window.open(videoUrl, '_blank')
    }
}
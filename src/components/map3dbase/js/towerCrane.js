// 施工防侵限
import { getCranePersonList, getCranePersonAreaList } from '@/api/index';
import { createImageByIndex, createImageByTime, createImageByAreaName } from './customCanvasLabel';
import personVideoPlay from './personVideo';
import { findLocation } from './utils';
export default class TowerCraneConstructor {
  constructor(vm) {
    this.vm = vm;
  }
  async init() {
    console.log('塔吊安全监测');
    this.vm.setupTrajectoryEventListener();
  }

  /**
  * 处理塔吊数据
  */
  processTowerCraneData(item, index) {
    try {
      const lng = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLng) || 0);
      const lat = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLat) || 0);

      console.log('设备坐标:', { lng, lat });

      if (lng === 0 || lat === 0 || isNaN(lng) || isNaN(lat)) {
        console.warn('跳过坐标无效的设备:', item.equipmentLocation?.equipmentName || '未知设备');
        return null;
      }

      let key = (item.equipmentLocation && item.equipmentLocation.equipmentId) || item.id || `mechanical_${index}`;
      const mechanicalId = item.id || item.mechanicalId || key;

      // 安全获取北斗设备航向角
      let beidouHeading = 0;
      try {
        if (item.beidouDevice && item.beidouDevice.deviceAttributeStatusList) {
          const headingAttr = item.beidouDevice.deviceAttributeStatusList.find(i => i.propCode === 'heading');
          if (headingAttr && headingAttr.propValue) {
            beidouHeading = Number(headingAttr.propValue) || 0;
          }
        }
      } catch (error) {
        console.warn('获取北斗航向角失败:', error);
      }

      const specificalInfo = JSON.parse(item.specificalInfo)
      return {
        // ================ 实时数据 ================
        weight: item.craneBusinessRecord.weight || 0, // 吊重
        windSpeed: item.craneBusinessRecord.windSpeed || 0, // 风速
        range: item.craneBusinessRecord.range || 0, // 变幅
        rotation: item.craneBusinessRecord.rotation || 0, // 回转角度
        heading: beidouHeading,
        slide: item.craneBusinessRecord.slide || 0, // 溜钩距离
        torquePercentage: item.craneBusinessRecord.torquePercentage || 0, // 力矩比
        horizontal: item.craneBusinessRecord.horizontal || 0, // 水平角度
        vertical: item.craneBusinessRecord.vertical || 0, // 垂直角度
        depth: item.craneBusinessRecord.depth || 0, // 吊钩下降深度
        height: item.craneBusinessRecord.height || 0, // 吊钩高度
        // ================ 实时数据 ================

        name: item.name || '未知设备',
        id: key,
        alarmType: item.alarmType,
        coordinates: [lng, lat],
        targetPositions: item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0
          ? item.verticalProtectionAreaList.map(area => ({
            middlePoint: area.middlePoint || null,
            distance: area.distance || 0,
          })).filter(item => item.middlePoint !== null)
          : [],
        distance: (item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0 && item.verticalProtectionAreaList[0].distance) || 0,
        onlineState: (item.device && item.device.onlineState) || 0,
        workState: item.beidouDevice?.onlineState || 0,
        alarmFlag: item.alarmFlag || false,
        limitAlarmFlag: item.alarmFlag || false,
        newlestLimitAlarm: item.newlestAlarm || {},
        deviceHeight: specificalInfo.tjgd,// 塔机高度
        dashRadius: item.dashRadius, // 虚线半径
        longArm: specificalInfo.dbc, // 大臂长
        shortArm: specificalInfo.xbc, // 小臂长
        phi: item.craneBusinessRecord.phi, // 航向角
        isTowerCrane: true,
        videoList: item.videoDeviceList || [],
      };
    } catch (error) {
      console.error('处理吊车数据时发生错误:', error);
      return null;
    }
  }
}
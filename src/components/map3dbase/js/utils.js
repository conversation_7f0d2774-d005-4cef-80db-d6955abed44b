/**
 * 找到多边形左下角的2个坐标
 * @param {Array} coordinates 多边形坐标
 * @returns {
 * min: [], // 最小坐标
 * left: [], // 最小坐标左边相邻的坐标
 * }
 */
export function findLocation(coordinates) {
  if (coordinates.length === 0) {
    return null;
  }
  
  // 计算区域中心点
  let sumLng = 0, sumLat = 0, pointCount = 0;
  for (let i = 0; i < coordinates.length; i++) {
    sumLng += coordinates[i][0];
    sumLat += coordinates[i][1];
    pointCount++;
  }
  const centerLng = sumLng / pointCount;
  const centerLat = sumLat / pointCount;

  // 找到最左下角的点
  let min = findBottomLeft(coordinates);
  
  // 找到左侧两个点
  let leftPoints = coordinates
    .filter(coord => coord[0] <= centerLng)
    .sort((a, b) => {
      // 按照到最左下角点的距离排序
      let distA = Math.sqrt(Math.pow(a[0] - min[0], 2) + Math.pow(a[1] - min[1], 2));
      let distB = Math.sqrt(Math.pow(b[0] - min[0], 2) + Math.pow(b[1] - min[1], 2));
      return distA - distB;
    });

  let left1 = leftPoints[0];
  let left2 = leftPoints[1] || leftPoints[0];

  // 计算两点之间的方位角
  let y = Math.sin(left2[0] - left1[0]) * Math.cos(left2[1]);
  let x = Math.cos(left1[1]) * Math.sin(left2[1]) -
          Math.sin(left1[1]) * Math.cos(left2[1]) * Math.cos(left2[0] - left1[0]);
  let brng = Math.atan2(y, x);
  let rotation = -(brng + Math.PI / 2)
  // 计算标签位置（取两点的中点）
  let lng = (left1[0] + left2[0]) / 2;
  let lat = (left1[1] + left2[1]) / 2;

  return {
    lng,
    lat,
    rotation
  };
}

/**
 * 找到多边形左下角坐标
 * @param {Array} coordinates 多边形坐标
 * @returns {Array} 左下角坐标
 */
function findBottomLeft(coordinates, min) {
  if (coordinates.length === 0) {
    return null; // 如果没有坐标点，返回null
  }
  if (min) {
    let index = coordinates.findIndex(
      (coord) => coord[0] === min[0] && coord[1] === min[1]
    );
    if (index >= 0) {
      coordinates.splice(index, 1);
    }
  }

  let minLon = coordinates[0][0]; // 初始化最小经度为第一个点的经度
  let minLat = coordinates[0][1]; // 初始化最小纬度为第一个点的纬度

  // 遍历所有坐标点
  coordinates.forEach((coord) => {
    const lon = coord[0]; // 当前点的经度
    const lat = coord[1]; // 当前点的纬度

    // 更新最小经度和最小纬度
    if (lon < minLon) {
      minLon = lon;
    }
    if (lat < minLat) {
      minLat = lat;
    }
  });

  return [minLon, minLat]; // 返回左下角坐标
}

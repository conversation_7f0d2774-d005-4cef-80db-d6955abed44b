// canvas生成的标签图片
/**
 * canvas合成六芒星+序号
 * @param {number} num 序号
 * @param {string} bgImgUrl 背景图片
 * @param {boolean} isStartText 是否显示起点和终点的文字
 * @param {number} len 轨迹点数量
 * @returns {Promise<string>} 返回合成后的图片
 */
export function createImageByIndex(num, bgImgUrl, isStartText = true, len) {
  return new Promise((resolve) => {
    const img = new window.Image();
    img.src = bgImgUrl;
    img.onload = () => {
      const width = img.width;
      const height = img.height;

      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext("2d");

      // 1. 绘制整个背景图（六芒星+蓝色条）
      ctx.drawImage(img, 0, 0, width, height);

      // 2. 序号，居中在六芒星内
      ctx.save();
      let txt = num;
      ctx.font = "36px PingFang SC";
      if (isStartText) {
        if (num === 1) {
          txt = "起点";
          ctx.font = "18px PingFang SC";
        } else if (num === len) {
          txt = "终点";
          ctx.font = "18px PingFang SC";
        }
      }
      ctx.fillStyle = "#fff";
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.shadowColor = "rgba(0,0,0,0.25)";
      ctx.shadowBlur = 4;
      ctx.fillText(txt, width / 2, height / 2.3);
      ctx.restore();
      resolve({
        url: canvas.toDataURL(),
        width: parseInt(width / 2),
        height: parseInt(height / 2),
      });
    };
    img.onerror = () => {
      resolve({
        url: bgImgUrl,
        width: parseInt(img.width / 2),
        height: parseInt(img.height / 2),
      });
    };
  });
}

/**
 * canvas合成六芒星后面的时间
 * @param {string} time 时间
 * @param {string} bgImgUrl 背景图片
 * @returns {Promise<string>} 返回合成后的图片
 */
export function createImageByTime(time, bgImgUrl) {
  return new Promise((resolve) => {
    const img = new window.Image();
    img.src = bgImgUrl;
    img.onload = () => {
      const width = img.width;
      const height = img.height;

      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext("2d");

      // 1. 绘制整个背景图（六芒星+蓝色条）
      ctx.drawImage(img, 0, 0, width, height);

      // 2. 序号，居中在六芒星内
      ctx.save();
      ctx.font = "26px PingFang SC";
      ctx.fillStyle = "#fff";
      ctx.textAlign = "left";
      ctx.textBaseline = "middle";
      ctx.shadowColor = "rgba(0, 132, 255, 0.8)";
      ctx.shadowBlur = 4;
      ctx.fillText(time, 48, height / 2.6);
      ctx.restore();
      resolve({
        url: canvas.toDataURL(),
        width: parseInt(width / 2),
        height: parseInt(height / 2),
      });
    };
    img.onerror = () => {
      resolve({
        url: bgImgUrl,
        width: parseInt(img.width / 2),
        height: parseInt(img.height / 2),
      });
    };
  });
}

/**
 * canvas合成区域名称标签
 * @param {string} name 区域名称
 * @param {string} status 状态 1: 报警 0: 正常
 * @returns {Promise<string>} 返回合成后的图片
 */
export function createImageByAreaName(name, status) {
  let bgImgUrl = ''
  if (status == 1 || status === true) {
    bgImgUrl = require('@/assets/images/map/area_label_alarm.png')
  } else {
    bgImgUrl = require('@/assets/images/map/area_label.png')
  }
  return new Promise((resolve) => {
    const img = new window.Image();
    img.src = bgImgUrl;
    img.onload = () => {
      const width = img.width;
      const height = img.height;

      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext("2d");

      // 1. 绘制整个背景图（六芒星+蓝色条）
      ctx.drawImage(img, 0, 0, width, height);

      // 2. 序号，居中在六芒星内
      ctx.save();
      ctx.font = "16px PingFang SC";
      ctx.fillStyle = "#fff";
      ctx.textAlign = "left";
      ctx.textBaseline = "middle";
      ctx.shadowColor = "rgba(0, 132, 255, 0.8)";
      ctx.shadowBlur = 4;
      ctx.fillText(name, 42, height / 2);
      ctx.restore();
      resolve({
        url: canvas.toDataURL(),
        width: parseInt(width),
        height: parseInt(height),
      });
    };
    img.onerror = () => {
      resolve({
        url: bgImgUrl,
        width: parseInt(img.width),
        height: parseInt(img.height),
      });
    };
  });
}
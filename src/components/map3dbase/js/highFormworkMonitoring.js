// 高支模安全监控
import { getHighFormworkMonitoringPoints } from '@/api/highFormworkMonitoring'
// 高支模点位id前缀
const HIGH_FORMWORK_POINT_ID_PREFIX = 'highFormworkPoint'
export default class HighFormworkMonitoring {
  constructor(vm) {
    this.vm = vm
    this.highFormworkIcon = {}
  }
  init() {
    console.log('高支模安全监控初始化')
    this.vm.craneManager.loadVideoMonitoringPoints()
    this.setupDataTimer()
  }
  clear() {
    console.log('高支模安全监控清理')
    if (this.dataTimer) {
        clearInterval(this.dataTimer)
    }
    try {
      let elArr = document.querySelectorAll('.highFormwork-label-3d')
      elArr.forEach(item => {
        item.remove()
      })
    } catch (error) {
      console.error('清除高支模数据时发生错误:', error)
    }
  }
  // 数据定时器
  async setupDataTimer() {
    const updateFun = async () => {
      await this.loadHighFormworkPoints()
    }
    if (this.dataTimer) {
      clearInterval(this.dataTimer)
    }
    updateFun()
    this.dataTimer = setInterval(() => {
      updateFun()
    }, 8000)
  }
  /**
   * 加载高支模点位
   */
  async loadHighFormworkPoints() {
    const { data: highFormworkList } = await getHighFormworkMonitoringPoints()
    
    const points = highFormworkList.filter(item => item.equipmentLng !== null && item.equipmentLat !== null).map(item => ({
      id: item.equipmentId,
      lng: Number(item.equipmentLng),
      lat: Number(item.equipmentLat),
      name: item.equipmentName,
      imageType: '高支模监测',
      data: {...item, status: item.isAlarm}
    }))
    console.log('高支模安全监控点位', points)
    let onClick = (data, entity) => {
        console.log('🖱️ 点位点击回调被触发:', data);

        // 停止随机弹窗
        this.vm.stopRandomPopup();
        // 标记用户手动关闭弹窗，禁止后续自动弹窗
        this._userClosedPopup = true;

        // 创建用户点击的弹窗
        console.log('📱 开始创建用户点击的弹窗...');
       
        this.vm.createBubble(
            {
              ...data,
              position: [
                Number(data.equipmentLng),
                Number(data.equipmentLat)
              ],
              personType: 'highFormwork'
            },
            0,
            'HighFormworkPopup'
          )
      }
    this.addImagePoints3(points, onClick)
  }
  // 批量添加高支模点位，支持名称和点击弹窗,不支持随机弹窗
  async addImagePoints3(points, onClick) {
    console.log(`开始添加 ${points.length} 个图片点位`);
    let successCount = 0;

    // 创建坐标映射，用于记录相同坐标的点位数量
    const coordMap = new Map();
    points.forEach(item => {
      const coordKey = `${item.lng},${item.lat}`;
      coordMap.set(coordKey, (coordMap.get(coordKey) || 0) + 1);
    });

    // 记录每个坐标已经使用的高度偏移次数
    const coordOffsetCount = new Map();
    const equipmentTypeName = '高支模监测'
    for (let index = 0; index < points.length; index++) {
      const item = points[index];
      try {
        console.log(item)
        // 检查坐标数据有效性
        if (!item.lng || !item.lat || !item.data) {
          console.warn(`点位数据无效，跳过:`, item);
          continue;
        }
        const coordKey = `${item.lng},${item.lat}`;
        const totalPoints = coordMap.get(coordKey);
        const currentOffset = coordOffsetCount.get(coordKey) || 0;

        // 计算高度偏移
        let heightOffset = 0;
        if (totalPoints > 1) {
          // 如果有多个点位在同一坐标，设置不同的高度
          heightOffset = currentOffset * 5; // 每个点位高度差5米
          coordOffsetCount.set(coordKey, currentOffset + 1);
        }
        let image = null
        if (!this.highFormworkIcon[item.name]) {
          this.highFormworkIcon[item.name] = {}
        }
        if (item.data.onlineState === 0) {
          // 离线
          if (this.highFormworkIcon[item.name].offline) {
            image = this.highFormworkIcon[item.name].offline
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 0)
            this.highFormworkIcon[item.name].offline = image
          }
        } else if (item.data.status === 1) {
          // 告警
          if (this.highFormworkIcon[item.name].alarm) {
            image = this.highFormworkIcon[item.name].alarm
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 1)
            this.highFormworkIcon[item.name].alarm = image
          }
        } else {
          // 正常
          if (this.highFormworkIcon[item.name].normal) {
            image = this.highFormworkIcon[item.name].normal
          } else {
            image = await this.vm.getEquipmentIcon(equipmentTypeName, item.name, 2)
            this.highFormworkIcon[item.name].normal = image
          }
        }
        // 获取人员图片点位
        let id = `${HIGH_FORMWORK_POINT_ID_PREFIX}_${item.id}`
        let entity = window.viewer.entities.getById(id)
        // 获取告警标签
        let htmlId = id + '_label'
        let pos = Cesium.Cartesian3.fromDegrees(item.lng, item.lat, heightOffset)
        if (entity) {
          entity.billboard.image = image
          entity.position = pos
        } else {
          // 1. 添加billboard
          entity = window.viewer.entities.add({
            id: id,
            position: pos,
            billboard: {
              image: image,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
              scale: 1.0,
              heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
              alignedAxis: Cesium.Cartesian3.ZERO,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
          });
          entity._isCustomMapPoint = true;
          // 2. 绑定点击事件
          entity._customData = item.data;
          entity._customCallback = onClick
          
        }
        this.getHighFormworkLabelHtml(item.data, htmlId, entity, heightOffset)
        successCount++;

      } catch (error) {
        console.error(`添加高支模点位 ${index} 时发生错误:`, error, item);
      }
    }
    console.log(`成功添加 ${successCount} 个高支模点位，失败 ${points.length - successCount} 个`);
  }
  getHighFormworkLabelHtml(data, id, entity, heightOffset) {
    let html = document.getElementById(id)

    function updateLabelPosition() {
        // 计算正东方向的标签位置
        let position = entity.position.getValue(new Cesium.JulianDate()); // 获取当前位置的 Cartesian3 坐标
        let cartographic = Cesium.Cartographic.fromCartesian(position); // 将 Cartesian3 转换为 Cartographic
        let longitude = Cesium.Math.toDegrees(cartographic.longitude); // 经度转换为度
        let latitude = Cesium.Math.toDegrees(cartographic.latitude); 
        const cartesian = Cesium.Cartesian3.fromDegrees(longitude, latitude, heightOffset)
        const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
        if (Cesium.defined(canvasPosition)) {
            html.style.left = `${canvasPosition.x}px`
            html.style.bottom = `${window.viewer.scene.canvas.clientHeight - canvasPosition.y + 50}px`
        }
    }
    // 创建删除按钮
    const deleteButton = document.createElement('div')
    deleteButton.innerHTML = '<i class="ivu-icon ivu-icon-md-close"></i>'
    deleteButton.className = 'highFormwork-close'
    deleteButton.addEventListener('click', async () => {
        console.log('删除', data)
        html.remove()
    })

    let pop = ''
    // 告警信息
    if (data.status === 1 && data.onlineState === 1) {
      pop += '<div class="highFormwork-alarm-3d">'
      pop += `<div class="highFormwork-alarm-title-3d"><div class="img"></div>${data.equipmentName}</div>`
      pop += `<div class="highFormwork-alarm-item-3d">`
      if (data.alarmHighName || data.alarmHighItemName) {
        pop += `<div class="txt">${data.alarmHighName} ${data.alarmHighItemName}</div>`
      }
      pop += `<div class="txt">${data.alarmContent}</div>`
      pop += `<div class="txt">${data.alarmTime}</div>`
      pop += '</div>'
      pop += '</div>'
      if (!html){
        
        html = document.createElement('div')
        html.className = 'highFormwork-label-3d'
        html.id = id
        let base3dmap = document.getElementById('base3dmap')
        if (base3dmap) {
        base3dmap.appendChild(html)
        }
      }
      html.innerHTML = pop
      html.appendChild(deleteButton)
          window.viewer.scene.postRender.addEventListener(updateLabelPosition)
          updateLabelPosition()
    } else {
        if (html) {
            html.remove()
            window.viewer.scene.postRender.removeEventListener(updateLabelPosition)
        }
    }
  }
}
// 群塔防碰撞
import {
  getTowerCraneMechanical,
  getCraneVerticalProtection
} from '@/api/index'
import { listPage } from '@/api/xxindex'
import { findLocation } from './utils';
import { videoList } from '../../../api/xxindex';
export default class CollisionPreventionConstructor {
  constructor(vm) {
    this.vm = vm;

  }
  async init() {
    console.log('群塔防碰撞');
    /**
    * 设置轨迹显示事件监听器
    */
    this.vm.setupTrajectoryEventListener();
  }

  // 设置视角
  async loadView() {
    const { data } = await getTowerCraneMechanical();
    this.setView(data[0].verticalProtectionAreaList, "verticalProtection")
  }

  // 加载垂直防护数据
  async loadVerticalProtectionData() {

    const { data: verticalProtection } = await getCraneVerticalProtection({
      page: {
        current: -1,
        size: 9999,
      },
      customQueryParams: {
        keyword: ""
      },
    });

    console.log(verticalProtection, 'getTowerCraneMechanical')

    verticalProtection.records.forEach((item, index) => {
      if (item.enabledFlag === 1) {
        this.drawVerticalProtectionArea(item, index);
      }
    });
  }

  /**
   * 绘制垂直防护区域
   */
  drawVerticalProtectionArea(item, index) {
    try {
      console.log('verticalProtection item.location:', item.location);
      let lineArr = JSON.parse(item.location);
      console.log('解析后的lineArr:', lineArr);

      // 验证数据格式
      if (!Array.isArray(lineArr) || lineArr.length < 3) {
        console.warn('verticalProtection lineArr格式无效:', lineArr);
        return;
      }

      // 定义实体ID
      const polygonId = `verticalProtectionPolygon_${item.id || index}`;
      const lineId = `verticalProtectionLine_${item.id || index}`;

      // 添加多边形
      this.vm.addMapElement(window.viewer, {
        id: polygonId,
        type: 'polygon',
        positions: lineArr,
        color: item.alarmFlag ? '#C40003' : '#4FC2FF'
      });

      // 为线条创建闭合路径
      let linePositions = [...lineArr, lineArr[0]];
      this.vm.addMapElement(window.viewer, {
        id: lineId,
        type: 'line',
        positions: linePositions,
        width: 1,
        color: item.alarmFlag ? '#C40003' : '#4FC2FF',
        dashPattern: true,
        dashLength: 6.0
      });

      // 添加标签
      this.addVerticalProtectionLabel(item, index, lineArr);
    } catch (error) {
      console.error('处理verticalProtection时发生错误:', error);
      console.error('item.location:', item.location);
    }
  }

  /**
   * 添加垂直防护区域标签
   */
  addVerticalProtectionLabel(item, index, lineArr) {
    // 计算中心点位置
    let centerLng, centerLat;
    if (item.centerLon && item.centerLat) {
      centerLng = item.centerLon;
      centerLat = item.centerLat;
    } else {
      const firstPoint = lineArr[0];
      if (Array.isArray(firstPoint) && firstPoint.length >= 2) {
        centerLng = firstPoint[0];
        centerLat = firstPoint[1];
      } else {
        centerLng = lineArr[0];
        centerLat = lineArr[1];
      }
    }

    // 定义标签ID
    const labelId = `verticalProtectionLabel_${item.id || index}`;
    // 添加以图片为背景的、大小固定的文字标签
    window.viewer.entities.add({
      id: labelId,
      name: item.regionName,
      position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat),
      billboard: {
        image: item.alarmFlag ? require('../../../assets/images/map/logo_alarm.png') : require('../../../assets/images/map/logo.png'),
        scale: 1.0,
        sizeInMeters: false,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      label: {
        text: item.regionName,
        font: '14px "PingFang SC"',
        fillColor: Cesium.Color.fromCssColorString('#ffffff'),
        style: Cesium.LabelStyle.FILL,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(-20, -2.5),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        scale: 1.0,
        scaleByDistance: undefined,
        translucencyByDistance: undefined
      }
    });
  }
  clear() {
    this.vm.craneManager.clearCraneEntities();
    // this.vm.removeDynamicEntities();
    this.vm.clearTrajectoryPoints();
    this.vm.removeDynamicMapElements();
    this.vm.cleanupEventListeners();
  }

  // 定时查询吊塔数据
  async setupTowerCraneDataTimer() {
    const fetchAndRedrawCraneData = async () => {
      try {
        console.log('🔄 开始获取和重绘塔吊数据...');

        // 在获取新数据前，先清理所有旧的塔吊实体
        console.log('🧹 清理旧的塔吊实体...');
        try {
          this.clear()
          // this.vm.craneManager.clearCraneEntities(); // 不传参数，清理塔吊实体
          // this.vm.removePoints(); // 不传参数，清理塔吊实体
        } catch (error) {
          console.warn('清理旧塔吊实体时发生错误:', error);
        }
        this.vm.craneManager.loadVerticalProtectionData();
        this.loadVerticalProtectionData()

        let { data: mechanical } = await getTowerCraneMechanical();
        console.log('获取到的塔吊数据:', mechanical);


        if (!mechanical || !Array.isArray(mechanical)) {
          console.warn('塔吊数据格式不正确:', mechanical);
          return;
        }

        // 检查Cesium viewer是否可用
        if (!window.viewer || window.viewer.isDestroyed()) {
          console.error('Cesium viewer 不可用，跳过塔吊数据处理');
          return;
        }

        const processCranePromises = mechanical.map(async (item, index) => {
          try {
            console.log(`处理第 ${index + 1} 个塔吊数据:`, item);

            if (!item.craneBusinessRecord || !item.equipmentLocation) {
              console.warn('跳过不完整的塔吊数据:', item);
              return;
            }

            const processedData = this.processTowerCraneData(item, index);

            if (!processedData) {
              console.warn('塔吊数据处理失败，跳过创建雷达图');
              return;
            }

            if (!window.viewer || window.viewer.isDestroyed()) {
              console.error('Cesium viewer 不可用，跳过雷达图创建');
              return;
            }

            console.log(processedData, 'processedData')
            // 为每个塔吊创建雷达图，包含错误处理
            await this.createRadarX(processedData);
            // console.log(`✅ 塔吊 ${processedData.id} 雷达图创建成功`);

          } catch (error) {
            console.error(`❌ 处理塔吊数据失败 (设备 ${item.id || index}):`, error);
            // 继续处理其他塔吊，不因为单个失败而中断
          }
        });

        // 等待所有塔吊处理完成
        await Promise.allSettled(processCranePromises);
        console.log('🎉 所有塔吊数据处理完成');

      } catch (error) {
        console.error('❌ 处理塔吊数据时发生错误:', error);
        // 定时器会继续运行，下次再试
      }
    };

    // 清理可能存在的旧定时器
    if (this.vm.towerCraneDataTimer) {
      console.log('🧹 清理旧的塔吊数据定时器');
      clearInterval(this.vm.towerCraneDataTimer);
      this.vm.towerCraneDataTimer = null;
    }

    // 立即执行一次获取数据和重绘
    await fetchAndRedrawCraneData();

    // 设置定时器，每10秒执行一次获取数据和重绘
    this.vm.towerCraneDataTimer = setInterval(fetchAndRedrawCraneData, 10 * 1000);
    console.log('⏰ 塔吊数据定时器已启动，间隔10秒');
  }

  // 飞行到俯视视角
  async setView(arr, key) {
    const hasSavedView = this.vm.checkSavedViewExists();
    if (!hasSavedView && arr.length > 0) {
      // 转换点位数据格式以适配calculateOptimalFlyPosition方法
      let formattedPoints = []
      switch (key) {
        case "verticalProtection":
          formattedPoints = [{
            lon: arr[0].middlePoint[0],
            lan: arr[0].middlePoint[1],
          }]
          break;
      }

      console.log(formattedPoints, 'formattedPoints')
      // const formattedPoints = points.map(item => ({
      //   lon: item.lng,
      //   lan: item.lat,
      //   id: item.id,
      //   name: item.name
      // }));

      const flyToPosition = this.vm.calculateOptimalFlyPosition(formattedPoints);
      console.log('🛸 addImagePoints2计算出的最佳视角:', flyToPosition);

      // 使用平滑的飞行动画
      window.viewer.camera.flyTo({
        destination: flyToPosition.destination,
        duration: flyToPosition.duration || 2.5,
        complete: () => {
          console.log('✅ addImagePoints2地图视角调整完成，所有项目点位已可见');
        }
      });
    } else if (hasSavedView) {
      console.log('🔒 存在保存视角，跳过视角调整，保持当前视角');
    }
  }

  /**
 * 处理塔吊数据
 */
  processTowerCraneData(item, index) {
    try {
      const lng = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLng) || 0);
      const lat = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLat) || 0);

      console.log('设备坐标:', { lng, lat });

      if (lng === 0 || lat === 0 || isNaN(lng) || isNaN(lat)) {
        console.warn('跳过坐标无效的设备:', item.equipmentLocation?.equipmentName || '未知设备');
        return null;
      }

      let key = (item.equipmentLocation && item.equipmentLocation.equipmentId) || item.id || `mechanical_${index}`;
      const mechanicalId = item.id || item.mechanicalId || key;

      // 安全获取北斗设备航向角
      let beidouHeading = 0;
      try {
        if (item.beidouDevice && item.beidouDevice.deviceAttributeStatusList) {
          const headingAttr = item.beidouDevice.deviceAttributeStatusList.find(i => i.propCode === 'heading');
          if (headingAttr && headingAttr.propValue) {
            beidouHeading = Number(headingAttr.propValue) || 0;
          }
        }
      } catch (error) {
        console.warn('获取北斗航向角失败:', error);
      }

      const specificalInfo = JSON.parse(item.specificalInfo)
      return {
        // ================ 实时数据 ================
        weight: item.craneBusinessRecord.weight || 0, // 吊重
        windSpeed: item.craneBusinessRecord.windSpeed || 0, // 风速
        range: item.craneBusinessRecord.range || 0, // 变幅
        rotation: item.craneBusinessRecord.rotation || 0, // 回转角度
        heading: beidouHeading,
        slide: item.craneBusinessRecord.slide || 0, // 溜钩距离
        torquePercentage: item.craneBusinessRecord.torquePercentage || 0, // 力矩比
        horizontal: item.craneBusinessRecord.horizontal || 0, // 水平角度
        vertical: item.craneBusinessRecord.vertical || 0, // 垂直角度
        depth: item.craneBusinessRecord.depth || 0, // 吊钩下降深度
        height: item.craneBusinessRecord.height || 0, // 吊钩高度
        // ================ 实时数据 ================

        name: item.name || '未知设备',
        id: key,
        alarmType: item.alarmType,
        coordinates: [lng, lat],
        targetPositions: item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0
          ? item.verticalProtectionAreaList.map(area => ({
            middlePoint: area.middlePoint || null,
            distance: area.distance || 0,
          })).filter(item => item.middlePoint !== null)
          : [],
        distance: (item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0 && item.verticalProtectionAreaList[0].distance) || 0,
        onlineState: (item.device && item.device.onlineState) || 0,
        workState: item.beidouDevice?.onlineState || 0,
        alarmFlag: item.alarmFlag || false,
        limitAlarmFlag: item.alarmFlag || false,
        newlestLimitAlarm: item.newlestAlarm || {},
        deviceHeight: specificalInfo.tjgd,// 塔机高度
        dashRadius: item.dashRadius, // 虚线半径
        longArm: specificalInfo.dbc, // 大臂长
        shortArm: specificalInfo.xbc, // 小臂长
        phi: item.craneBusinessRecord.phi, // 航向角
        isTowerCrane: true,
        videoList: item.videoDeviceList || [],
      };
    } catch (error) {
      console.error('处理吊车数据时发生错误:', error);
      return null;
    }
  }

  // 用cesium地图编写吊车圆心方法
  async createRadarX(deviceData) {
    console.log('createRadarX', deviceData)

    // 1. 基本验证
    if (!window.viewer) {
      console.error('Cesium viewer 未初始化')
      return
    }

    if (!deviceData?.coordinates?.length) {
      console.error('无效的坐标数据')
      return
    }

    // 2. 清理之前绘制的指定吊车的圆心和相关实体（避免重复）
    const baseId = deviceData.id || 'default';
    try {
      // this.vm.craneManager.clearCraneEntities();
      // this.vm.removePoints(); // 不传参数，清理塔吊实体

      // 清理可能残留的相机事件监听器
      if (window.viewer && window.viewer.camera) {
        // 移除可能与该吊车相关的事件监听器
        // 注意：这里不能直接清理所有监听器，因为可能影响其他功能
        // 主要是为了避免监听器累积
        const cameraEvents = window.viewer.camera.changed._listeners;
        if (cameraEvents && cameraEvents.length > 50) {
          console.warn(`⚠️ 检测到相机事件监听器过多 (${cameraEvents.length} 个)，可能存在内存泄漏`);
        }
      }

      // 清理场景渲染事件监听器
      if (window.viewer && window.viewer.scene) {
        const sceneEvents = window.viewer.scene.postRender._listeners;
        if (sceneEvents && sceneEvents.length > 50) {
          console.warn(`⚠️ 检测到场景渲染事件监听器过多 (${sceneEvents.length} 个)，可能存在内存泄漏`);
        }
      }

    } catch (error) {
      console.warn('清理吊车实体时发生错误:', error);
    }

    const [lng, lat] = deviceData.coordinates
    console.log('原始坐标:', { lng, lat })

    // 检查 viewer 状态
    console.log('Viewer 状态:', {
      isDestroyed: window.viewer.isDestroyed(),
      scene: {
        mode: window.viewer.scene.mode,
        globe: window.viewer.scene.globe.show
      }
    })

    try {
      // 确保时钟启用
      window.viewer.clock.shouldAnimate = true;
      window.viewer.scene.globe.enableLighting = false;

      const radarCenter = Cesium.Cartesian3.fromDegrees(lng, lat, 0)

      // 吊车图标
      const craneLabelBg = require('@/assets/images/map/Crane-label4.png')
      const craneLabelBg2 = require('@/assets/images/map/Crane-label3.png')
      const label_box_bg2 = require('@/assets/images/map/label_box_bg2.png')
      const craneZx = require('@/assets/images/map/Crane-zx1.png')
      const craneZy = require('@/assets/images/map/Crane-zy.png')
      const craneLx = require('@/assets/images/map/Crane-lx1.png')
      const craneTj = require('@/assets/images/map/Crane-tj1.png')

      // "@/assets"

      // 半径参数
      const workRadius = Number(deviceData.range) || 0; // 实心半径
      const shortArm = Number(deviceData.shortArm) || 0; // 小臂长
      const longArm = Number(deviceData.longArm) || 0; // 大臂长
      const overturnRadius = Number(deviceData.dashRadius) || 0;

      // 1. 添加中心点图标
      try {
        let image = deviceData.onlineState === 0 ? require('@/assets/images/map/Crane_icon_lx.png') :
          deviceData.limitAlarmFlag ? require('@/assets/images/map/Crane_icon_alarm.png')
            : require('@/assets/images/map/Crane_icon.png')
        const iconEntity = window.viewer.entities.add({
          id: `crane_icon_${baseId}`,
          position: radarCenter,
          billboard: {
            image: image,
            scale: 1,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            show: true
          },
          // 添加吊车数据，用于点击时弹出弹窗
          _craneData: deviceData,
          _isCrane: true
        });
        console.log('中心图标创建完成:', iconEntity);
      } catch (error) {
        console.error('创建中心图标失败:', error);
      }

      // 2. 添加基础圆
      try {
        let material
        if (deviceData.onlineState === 0) {
          material = Cesium.Color.fromCssColorString('#909090').withAlpha(0.9)
        } else {
          if (deviceData.limitAlarmFlag) {
            // 仅在alarmType为如下内容时显示为红色
            const arr = ["50", "51", "52"]
            if (arr.includes(deviceData.newlestLimitAlarm.alarmType)) {
              material = Cesium.Color.fromCssColorString('#C40003').withAlpha(0.4)
            } else {
              material = Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4)
            }
          } else {
            material = Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4)
          }
        }

        const circleEntity = window.viewer.entities.add({
          id: `crane_circle_${baseId}`,
          position: radarCenter,
          polygon: {
            hierarchy: new Cesium.CallbackProperty(function () {
              const positions = [];
              const segments = 360;
              for (let i = 0; i <= segments; i++) {
                const angle = (i / segments) * 2 * Math.PI;
                const x = workRadius * Math.cos(angle);
                const y = workRadius * Math.sin(angle);
                const latCorrection = Math.cos(Cesium.Math.toRadians(lat));
                positions.push(
                  Cesium.Cartesian3.fromDegrees(
                    lng + (x / 111000) / latCorrection,
                    lat + (y / 111000),
                    1
                  )
                );
              }
              return new Cesium.PolygonHierarchy(positions);
            }, false),
            material,
            outline: true,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            perPositionHeight: false,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            classificationType: Cesium.ClassificationType.TERRAIN,
            show: true
          }
        });
        console.log('基础圆创建完成:', circleEntity);
      } catch (error) {
        console.error('创建基础圆失败:', error);
      }

      // 3. 添加目标位置箭头、线段和安全限界标签（在基础圆之上）
      if (deviceData.targetPositions && Array.isArray(deviceData.targetPositions) && deviceData.targetPositions.length > 0) {
        for (const [index, target] of deviceData.targetPositions.entries()) {
          if (target.middlePoint && Array.isArray(target.middlePoint) && target.middlePoint.length >= 2) {
            try {
              const [startLng, startLat] = deviceData.coordinates;
              const [endLng, endLat] = target.middlePoint;

              // 计算线条角度
              const dx = endLng - startLng;
              const dy = endLat - startLat;
              // SVG箭头默认朝上，rotation需减去Math.PI/2
              const angle = Math.atan2(dy, dx);

              // 获取线条颜色
              //  const lineColor = deviceData.workState === 0
              //    ? Cesium.Color.fromCssColorString('#909090').withAlpha(0.8)
              //    : deviceData.limitAlarmFlag
              //      ? Cesium.Color.fromCssColorString('#C40003').withAlpha(0.8)
              //      : Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.8);
              const lineColor = Cesium.Color.WHITE;

              // 添加贴地目标线段 - 使用唯一ID避免重复
              const targetLineId = `${deviceData.id}_target_line_${index}`;
              console.log(`准备创建目标线段，ID: ${targetLineId}`);

              // 检查是否已存在同ID的实体
              const existingEntity = window.viewer.entities.getById(targetLineId);
              if (existingEntity) {
                console.warn(`目标线段 ${targetLineId} 已存在，先移除`);
                window.viewer.entities.remove(existingEntity);
              }

              window.viewer.entities.add({
                id: targetLineId,
                polyline: {
                  positions: [
                    Cesium.Cartesian3.fromDegrees(startLng, startLat, 0),
                    Cesium.Cartesian3.fromDegrees(endLng, endLat, 0)
                  ],
                  width: 2,
                  material: lineColor,
                  clampToGround: true,
                  classificationType: Cesium.ClassificationType.TERRAIN,
                  zIndex: 9999 // 设置为最高层级
                }
              });

              function getArrowTrianglePoints(endLng, endLat, angle, length, width) {
                // 箭头尖端与终点重合，tip点高度3米，底边点高度1.5米，扩大显示效果
                const tip = [
                  endLng,
                  endLat,
                  3
                ];
                const leftAngle = angle + Math.PI - Math.PI / 6;
                const rightAngle = angle + Math.PI + Math.PI / 6;
                const left = [
                  endLng + Math.cos(leftAngle) * length,
                  endLat + Math.sin(leftAngle) * length,
                  1.5
                ];
                const right = [
                  endLng + Math.cos(rightAngle) * length,
                  endLat + Math.sin(rightAngle) * length,
                  1.5
                ];
                const bottom = [
                  endLng,
                  endLat,
                  1.5
                ];
                return [tip, left, right, bottom];
              }
              const trianglePoints = getArrowTrianglePoints(endLng, endLat, angle, 0.00003, 0.0001);
              window.viewer.entities.add({
                id: `${deviceData.id}_target_arrow_${index}`,
                polygon: {
                  hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights([
                    ...trianglePoints[0], ...trianglePoints[1], ...trianglePoints[2]
                  ]),
                  material: lineColor,
                  heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                  zIndex: 9999 // 设置为最高层级
                }
              });
              // 添加安全限界标签
              const midPointLng = (startLng + endLng) / 2;
              const midPointLat = (startLat + endLat) / 2;

              const labelText = `${target.distance.toFixed(2)}`;

              const labelDiv = document.createElement('div')
              labelDiv.className = 'radar-label radar-label-east'
              labelDiv.id = `radar-label-safety-${deviceData.id}-${index}`
              labelDiv.innerHTML = `
                    <div class="radar-label-content" style="
                      background-image: url('${craneLabelBg2}');
                      background-size: 100% 100%;
                      background-repeat: no-repeat;
                      background-position: center center;
                      padding: 4px 4px;
                      display: flex;
                      align-items: center;
                      height: 33px;
                    ">
                      <div class="radar-label-content-name" 
                        style="color: #fff;
                        font-family: 'HuXiaoBo_KuHei';">
                        <span style="font-size: 14px;">距离安全限界：</span>
                        <span style="font-size: 20px;">${labelText}<span style="font-size: 12px;">m</span></span>
                      </div>
                    </div>
                  `
              labelDiv.style.position = 'absolute'
              labelDiv.style.pointerEvents = 'auto'
              labelDiv.style.transform = 'translate(-50%, -100%)'
              const container = document.getElementById('base3dmap')
              if (container) {
                container.appendChild(labelDiv)
              }

              function updateLabelPosition() {
                // 计算正东方向的标签位置
                const cartesian = Cesium.Cartesian3.fromDegrees(midPointLng, midPointLat, 0)
                const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
                if (Cesium.defined(canvasPosition)) {
                  labelDiv.style.left = `${canvasPosition.x + 75}px`
                  labelDiv.style.top = `${canvasPosition.y}px`
                  labelDiv.style.display = 'block'
                } else {
                  labelDiv.style.display = 'none'
                }
              }

              window.viewer.camera.changed.addEventListener(updateLabelPosition)
              window.viewer.scene.postRender.addEventListener(updateLabelPosition)
              updateLabelPosition()
              console.log(`目标线段创建成功: ${targetLineId}`);
            } catch (error) {
              console.error(`创建目标线段 ${index} 失败:`, error);
            }
          }
        }
      }

      // 4.1 创建固定扫描线（基于大臂长、航向角）
      try {
        // 获取航向角，默认为0（正北方向）
        const headingAngle = Number(deviceData.phi) || 0;
        console.log(headingAngle, '航向角123123123')
        // 将航向角转换为弧度，顺时针为正，正北为0度
        // 注意：数学坐标系中，0度是正东方向，而航向角0度是正北方向
        // 所以需要转换：航向角度数 -> 数学角度 = 90° - 航向角度数
        //  const mathAngleInDegrees = 90 - headingAngle;
        const mathAngleInDegrees = 90 - headingAngle;
        const angleInRadians = Cesium.Math.toRadians(mathAngleInDegrees);

        // 计算扫描线终点坐标
        const x = longArm * Math.cos(angleInRadians);
        const y = longArm * Math.sin(angleInRadians);
        const scanLineLatCorrection = Math.cos(Cesium.Math.toRadians(lat));

        const scanLineEndPos = Cesium.Cartesian3.fromDegrees(
          lng + (x / 111000) / scanLineLatCorrection,
          lat + (y / 111000),
          0
        );

        const pointDistance = workRadius; // 10米
        const px = pointDistance * Math.cos(angleInRadians);
        const py = pointDistance * Math.sin(angleInRadians);
        const pointLatCorrection = Math.cos(Cesium.Math.toRadians(lat));
        const pointPosition = Cesium.Cartesian3.fromDegrees(
          lng + (px / 111000) / pointLatCorrection,
          lat + (py / 111000),
          0
        );
        // 添加圆点（吊钩）
        window.viewer.entities.add({
          id: `crane_scanning_point_${baseId}`,
          position: pointPosition,
          point: {
            pixelSize: 10,
            color: Cesium.Color.fromCssColorString('#ffffff').withAlpha(1),
            // color: Cesium.Color.RED.withAlpha(1),
            outlineColor: Cesium.Color.fromCssColorString('#195a8f').withAlpha(1),
            // outlineColor: Cesium.Color.WHITE,
            outlineWidth: 6,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        });

        // 添加固定扫描线
        const scanningLine = window.viewer.entities.add({
          id: `crane_scanning_line_${baseId}1`,
          polyline: {
            positions: [radarCenter, scanLineEndPos],
            width: 4,
            material: deviceData.onlineState === 0 ?
              new Cesium.ColorMaterialProperty({
                color: Cesium.Color.fromCssColorString('#909090').withAlpha(1),
                dashLength: 16.0
              }) : deviceData.limitAlarmFlag ?
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#C40003').withAlpha(1),
                  dashLength: 16.0
                }) :
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(1),
                  dashLength: 16.0
                }),
            clampToGround: true,
            classificationType: Cesium.ClassificationType.BOTH
          }
        });

        console.log(`扫描线创建完成，航向角: ${headingAngle}°, 数学角度: ${mathAngleInDegrees}°`);
      } catch (error) {
        console.error('创建扫描线失败:', error);
      }

      // 4.2 创建固定扫描线（基于小臂长、航向角）
      try {
        // 获取航向角，默认为180（正南方向）
        const headingAngleShort = (Number(deviceData.phi) + 180) || 180;
        console.log(headingAngleShort, '航向角123123123')
        // 将航向角转换为弧度，顺时针为正，正北为0度
        // 注意：数学坐标系中，0度是正东方向，而航向角0度是正北方向
        // 所以需要转换：航向角度数 -> 数学角度 = 90° - 航向角度数
        //  const mathAngleInDegrees = 90 - headingAngle;
        const mathAngleInDegrees = 90 - headingAngleShort;
        const angleInRadians = Cesium.Math.toRadians(mathAngleInDegrees);

        // 计算扫描线终点坐标
        const x = shortArm * Math.cos(angleInRadians);
        const y = shortArm * Math.sin(angleInRadians);
        const scanLineLatCorrection = Math.cos(Cesium.Math.toRadians(lat));

        const scanLineEndPos = Cesium.Cartesian3.fromDegrees(
          lng + (x / 111000) / scanLineLatCorrection,
          lat + (y / 111000),
          0
        );

        // 添加固定扫描线
        const scanningLine = window.viewer.entities.add({
          id: `crane_scanning_line_${baseId}2`,
          polyline: {
            positions: [radarCenter, scanLineEndPos],
            width: 4,
            material: deviceData.onlineState === 0 ?
              new Cesium.ColorMaterialProperty({
                color: Cesium.Color.fromCssColorString('#909090').withAlpha(1),
                dashLength: 16.0
              }) : deviceData.limitAlarmFlag ?
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#C40003').withAlpha(1),
                  dashLength: 16.0
                }) :
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(1),
                  dashLength: 16.0
                }),
            clampToGround: true,
            classificationType: Cesium.ClassificationType.BOTH
          }
        });

        console.log(`扫描线创建完成，航向角: ${headingAngleShort}°, 数学角度: ${mathAngleInDegrees}°`);
      } catch (error) {
        console.error('创建扫描线失败:', error);
      }

      // 添加外圈虚线圆
      try {
        const radius = overturnRadius;
        const segments = 60;
        const dashLength = 8;
        const gapLength = 4;
        const latCorrection = Math.cos(Cesium.Math.toRadians(lat));
        for (let i = 0; i < segments; i++) {
          const startAngle = (i / segments) * 2 * Math.PI;
          const endAngle = ((i + dashLength / (dashLength + gapLength)) / segments) * 2 * Math.PI;
          const startX = radius * Math.cos(startAngle);
          const startY = radius * Math.sin(startAngle);
          const endX = radius * Math.cos(endAngle);
          const endY = radius * Math.sin(endAngle);

          const startPos = Cesium.Cartesian3.fromDegrees(
            lng + (startX / 111000) / latCorrection,
            lat + (startY / 111000),
            0
          );
          const endPos = Cesium.Cartesian3.fromDegrees(
            lng + (endX / 111000) / latCorrection,
            lat + (endY / 111000),
            0
          );

          let material
          if (deviceData.onlineState === 0) {
            material = Cesium.Color.fromCssColorString('#909090').withAlpha(0.9)
          } else {
            if (deviceData.limitAlarmFlag) {
              // 仅在alarmType为如下内容时显示为红色
              const arr = ["50", "51", "52"]
              if (arr.includes(deviceData.newlestLimitAlarm.alarmType)) {
                material = Cesium.Color.fromCssColorString('#C40003').withAlpha(0.4)
              } else {
                material = Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4)
              }

            } else {
              material = Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4)
            }
          }

          window.viewer.entities.add({
            id: `crane_dashed_line_${baseId}_${i}`,
            polyline: {
              positions: [startPos, endPos],
              width: 3,
              // material: Cesium.Color.fromCssColorString('#C40003').withAlpha(0.4),
              material,
              clampToGround: true,
              classificationType: Cesium.ClassificationType.BOTH
            }
          });
        }
        console.log('虚线圆创建完成');
      } catch (error) {
        console.error('创建虚线圆失败:', error);
      }

      // 3. 添加塔吊标签
      try {
        const craneZx = require('@/assets/images/map/Crane-zx1.png')
        const craneZy = require('@/assets/images/map/Crane-zy.png')
        const craneLx = require('@/assets/images/map/Crane-lx1.png')
        const craneTj = require('@/assets/images/map/Crane-tj1.png')
        const northLat = lat + (overturnRadius / 111000);
        const eastLng = lng + (overturnRadius / (111000 * Math.cos(Cesium.Math.toRadians(lat))));

        // 根据在线状态选择状态图标
        const statusIcon = deviceData.onlineState === 1 ? craneZx : craneLx;
        const statusText = deviceData.onlineState === 1 ? '在线' : '离线';

        console.log(`创建塔吊标签: ${deviceData.name}, 在线状态: ${deviceData.onlineState}, 图标: ${statusIcon}`);

        const labelDiv = document.createElement('div')
        labelDiv.className = 'radar-label radar-label-north'
        labelDiv.id = `radar-label-${baseId}`
        labelDiv.innerHTML = `
             <div class="radar-label-content" style="
               background-image: url('${craneLabelBg}');
               background-size: 100% 100%;
               background-repeat: no-repeat;
               background-position: center center;
               padding: 4px 4px;
               display: flex;
               align-items: center;
               justify-content: space-between;
               height: 28px;
               opacity: 1;
               visibility: visible;
             ">
               <div class="radar-label-content-name" style="color: #fff; font-size: 14px; font-family: 'HuXiaoBo_KuHei';margin-right: 16px;margin-left: 4px;">${deviceData.name}</div>
               <img src="${statusIcon}" style="width: auto; height: auto; margin-right: 4px;" alt="${statusText}" title="${statusText}">
               <div class="person-button-3d" style="display: flex; align-items: center; column-gap: 0.74vh;">
                 <div class="person-button-item-3d hook-video-btn" style="
                   background: linear-gradient(180deg, rgba(0, 229, 255, 0.5) 0%, rgba(0, 126, 116, 0.5) 50.96%, rgba(0, 62, 69, 0.5) 100%);
                   border: 1px solid rgba(121, 242, 255, 1);
                   border-radius: 0.18vh;
                   font-size: 1.29vh;
                   padding: 0.37vh 0.74vh;
                   line-height: 1.66vh;
                   color: #fff;
                   text-shadow: 0 0 0.74vh rgba(0, 0, 0, 0.25);
                   cursor: pointer;
                  
                 ">吊钩可视化</div>
               </div>
               </div>
             </div>
           `
        labelDiv.style.position = 'absolute'
        labelDiv.style.pointerEvents = 'auto'
        labelDiv.style.transform = 'translate(-50%, -100%)'
        labelDiv.style.zIndex = '1000'
        labelDiv.style.display = 'block'
        labelDiv.style.opacity = '1'
        labelDiv.style.visibility = 'visible'

        const container = document.getElementById('base3dmap')
        if (container) {
          container.appendChild(labelDiv)
          console.log(`塔吊标签已添加到容器: ${deviceData.name}, 在线状态: ${deviceData.onlineState}`)
        } else {
          console.error('未找到base3dmap容器，无法添加塔吊标签')
        }

        function updateLabelPosition() {
          const cartesian = Cesium.Cartesian3.fromDegrees(lng, northLat, 0)
          const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
          if (Cesium.defined(canvasPosition)) {
            labelDiv.style.left = `${canvasPosition.x + 160}px`
            labelDiv.style.top = `${canvasPosition.y}px`
            labelDiv.style.display = 'block'
            labelDiv.style.visibility = 'visible'
            labelDiv.style.opacity = '1'
          } else {
            labelDiv.style.display = 'none'
          }
        }

        window.viewer.camera.changed.addEventListener(updateLabelPosition)
        window.viewer.scene.postRender.addEventListener(updateLabelPosition)
        updateLabelPosition()

        // 为吊钩可视化按钮添加点击事件
        const hookVideoBtn = labelDiv.querySelector('.hook-video-btn')
        if (hookVideoBtn) {
          hookVideoBtn.addEventListener('click', (e) => {
            e.stopPropagation()
            console.log('点击吊钩可视化按钮:', deviceData)
            // 调用openHookVideoModal方法
            if (this.vm && typeof this.vm.openHookVideoModal === 'function') {
              this.vm.openHookVideoModal(deviceData)
            } else {
              console.error('openHookVideoModal方法不存在')
            }
          })
        }

        console.log('主标签创建完成');
      } catch (error) {
        console.error('创建主标签失败:', error);
      }

      // 添加告警标签
      try {
        const northLat = lat + (overturnRadius / 111000);
        const eastLng = lng + (overturnRadius / (111000 * Math.cos(Cesium.Math.toRadians(lat))));

        // 根据在线状态选择状态图标
        const labelDiv = document.createElement('div')
        labelDiv.className = 'radar-label radar-label-north'
        labelDiv.id = `radar-label-${baseId}443`
        labelDiv.innerHTML = `
             <div class="radar-label-content" style="
                display: block !important;
       
                background: linear-gradient(90deg, rgba(84, 0, 1, 0.75) 0%, rgba(68, 0, 0, 0.75) 100%);
               padding: 10px;
               border-radius: 6px;
               opacity: 0.8 !important;
               visibility: visible;
             ">
               <div class="radar-label-content-name" style="color: #fff; font-size: 14px; font-family: 'HuXiaoBo_KuHei';margin-right: 16px;margin-left: 4px;">${deviceData.newlestLimitAlarm.createTime || "0000-00-00 00:00:00"}</div>
               <div class="radar-label-content-name" style="color: #fff; font-size: 14px; font-family: 'HuXiaoBo_KuHei';margin-right: 16px;margin-left: 4px;">${deviceData.newlestLimitAlarm.alarmTypeText || '群塔防撞告警'}</div>
               <div class="radar-label-content-name" style="color: #fff; font-size: 14px; font-family: 'HuXiaoBo_KuHei';margin-right: 16px;margin-left: 4px;">·${deviceData.newlestLimitAlarm.bigScreenAlarmContent}</div>
             </div>
           `
        labelDiv.style.position = 'absolute'
        labelDiv.style.pointerEvents = 'auto'
        labelDiv.style.transform = 'translate(-50%, -100%)'
        labelDiv.style.zIndex = '1000'
        labelDiv.style.display = 'block'
        labelDiv.style.opacity = '1'
        labelDiv.style.visibility = 'visible'

        if (deviceData.limitAlarmFlag) {
          const container = document.getElementById('base3dmap')
          if (container) {
            container.appendChild(labelDiv)
            console.log(`告警标签已添加到容器: ${deviceData.name}`)
          } else {
            console.error('未找到base3dmap容器，无法添加告警标签')
          }

          function updateLabelPosition() {
            const cartesian = Cesium.Cartesian3.fromDegrees(lng, northLat, 0)
            const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
            if (Cesium.defined(canvasPosition)) {
              labelDiv.style.left = `${canvasPosition.x + 160}px`
              labelDiv.style.top = `${canvasPosition.y - 30}px`
              labelDiv.style.display = 'block'
              labelDiv.style.visibility = 'visible'
              labelDiv.style.opacity = '1'
            } else {
              labelDiv.style.display = 'none'
            }
          }

          window.viewer.camera.changed.addEventListener(updateLabelPosition)
          window.viewer.scene.postRender.addEventListener(updateLabelPosition)
          updateLabelPosition()

          console.log('告警创建完成');
        }
      } catch (error) {
        console.error('创建告警失败:', error);
      }

      // 添加半径标签
      try {
        // 圆外标签参数 - 统一位置上下排列，锚点在右侧
        const labels = [
          {
            id: 'radius',
            text: '塔机高度：',
            value: `${deviceData.deviceHeight}`,
            angle: 90, // 正东方向（右侧）
            radius: Number(deviceData.range) || 40,
          },
          {
            id: 'overturn',
            text: '工作半径：',
            value: `${deviceData.range}`,
            angle: 90, // 正东方向（右侧）
            radius: Number(deviceData.range) || 50,
          }
        ];

        // 计算圆外点经纬度（直接用半径，不加margin）
        function getLabelPosition(centerLng, centerLat, radius, angle) {
          const earthRadius = 6378137;
          const rad = angle * Math.PI / 180;
          const dist = radius;
          const dLat = (dist * Math.cos(rad)) / earthRadius;
          const dLng = (dist * Math.sin(rad)) / (earthRadius * Math.cos(centerLat * Math.PI / 180));
          const lat = centerLat + dLat * 180 / Math.PI;
          const lng = centerLng + dLng * 180 / Math.PI;
          return { lng, lat };
        }

        // 只用第一个标签的半径作为所有标签的锚点
        function getAnchorCanvasPosition() {
          const anchor = labels[0];
          const { lng: labelLng, lat: labelLat } = getLabelPosition(
            lng, lat, anchor.radius, anchor.angle
          );
          const anchorCartesian = Cesium.Cartesian3.fromDegrees(labelLng, labelLat, 0);
          return window.viewer.scene.cartesianToCanvasCoordinates(anchorCartesian);
        }

        function updateAllLabelPositions() {
          const baseCanvasPosition = getAnchorCanvasPosition();
          if (baseCanvasPosition && Cesium.defined(baseCanvasPosition)) {
            labels.forEach((labelInfo, index) => {
              const labelDiv = document.getElementById(`radar-label-${labelInfo.id}-${baseId}`);
              if (labelDiv) {
                const pixelGap = 8;
                const labelHeight = labelDiv.offsetHeight || 33;
                labelDiv.style.left = `${baseCanvasPosition.x}px`;
                labelDiv.style.top = `${baseCanvasPosition.y + index * (labelHeight + pixelGap)}px`;
                labelDiv.style.display = 'block';
              }
            });
          } else {
            labels.forEach((labelInfo) => {
              const labelDiv = document.getElementById(`radar-label-${labelInfo.id}-${baseId}`);
              if (labelDiv) {
                labelDiv.style.display = 'none';
              }
            });
          }
        }

        labels.forEach((labelInfo, i) => {
          try {
            const labelDiv = document.createElement('div')
            labelDiv.className = 'radar-label radar-label-east'
            labelDiv.id = `radar-label-${labelInfo.id}-${baseId}`
            labelDiv.innerHTML = `
                 <div class=\"radar-label-content\" style=\"
                   background-image: url('${craneLabelBg2}');
                   background-size: 100% 100%;
                   background-repeat: no-repeat;
                   background-position: center center;
                   padding: 4px 4px;
                   display: flex;
                   align-item: center;
                   height: 33px;
                 \">
                   <div class="radar-label-content-name" style="display: flex; align-items: center; justify-content: space-between;color: #fff; font-family: 'HuXiaoBo_KuHei';">
                     <span style="font-size: 14px;">${labelInfo.text}</span>
                     <span style="font-size: 20px;">${labelInfo.value}<span style="font-size: 12px;">m</span></span>
                   </div>
                 </div>
               `
            labelDiv.style.position = 'absolute'
            labelDiv.style.pointerEvents = 'auto'
            labelDiv.style.transform = 'translate(0%, -100%)'
            const container = document.getElementById('base3dmap')
            if (container) {
              container.appendChild(labelDiv)
            }
            console.log(`半径标签 ${labelInfo.id} 创建完成`);
          } catch (error) {
            console.error(`创建半径标签 ${labelInfo.id} 失败:`, error);
          }
        })

        window.viewer.camera.changed.addEventListener(updateAllLabelPositions)
        window.viewer.scene.postRender.addEventListener(updateAllLabelPositions)
        updateAllLabelPositions()
        console.log('所有半径标签创建完成');
      } catch (error) {
        console.error('创建半径标签失败:', error);
      }

      if (deviceData.limitAlarmFlag) {
        // 先清理该设备之前的弹窗
        const deviceId = deviceData.id || deviceData.mechanicalId;
        if (deviceId && this.vm.bubbles) {
          // 如果存在旧的弹窗，先移除
          this.vm.bubbles.windowClose();
        }

        return
        // 创建新的弹窗
        this.vm.createBubble(
          {
            ...deviceData,
            position: [
              Number(deviceData.coordinates[0]),
              Number(deviceData.coordinates[1]),
              0
            ],
            above: true,
            offset: {
              offsetX: 170,
              offsetY: -160
            },
            // 添加唯一标识，便于后续清理
            bubbleId: `alert_${deviceId || Date.now()}`
          },
          0,
          'alert'
        );
      }

      console.log(`🎉 吊车 ${baseId} 雷达图创建完成`);

    } catch (error) {
      console.error(`❌ 创建吊车雷达图失败 (${baseId}):`, error)
      throw error; // 重新抛出错误，让调用者处理
    }
  }
}
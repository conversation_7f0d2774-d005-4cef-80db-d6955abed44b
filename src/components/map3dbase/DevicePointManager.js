import Bubble from './Bubble.js'

/**
 * 设备点位管理器
 * 负责管理地图上的设备图标、标签和相关功能
 */
export default class DevicePointManager {
  constructor(vm) {
    this.vm = vm
    this.imagePointsData = []
    this._userClosedPopup = false
    this.bubbles = null
  }

  /**
   * 创建DOM设备图标的公共函数
   * @param {Object} item - 设备数据
   * @param {string} id - 设备ID
   * @param {number} heightOffset - 高度偏移
   * @returns {Object} 返回图标元素和清理函数
   */
  async createDOMDeviceIcon(item, id, heightOffset) {
    try {
      console.log(`创建设备图标和标签: ${item.name}`);
      
      // 加载吊车标签所需的背景图片
      const craneLabelBg = require('@/assets/images/map/Crane-label4.png')
      
      // 获取设备图标
      let imageResult = null
      if (item.data.onlineState === 0) {
        // 离线
        imageResult = await this.vm.getEquipmentIcon(item.data.equipmentTypeName, item.name, 0, false)
      } else if (item.data.status === 1 || item.data.isAlarm == 1) {
        // 告警
        imageResult = await this.vm.getEquipmentIcon(item.data.equipmentTypeName, item.name, 1, false)
      } else {
        imageResult = await this.vm.getEquipmentIcon(item.data.equipmentTypeName, item.name, undefined, false)
      }

      // 创建设备图标元素（支持背景和图标叠加）
      const iconDiv = document.createElement('div')
      iconDiv.className = 'device-icon'
      iconDiv.id = `device-icon-${id}`
      
      // 检查是否为复合图标（包含背景和图标）
      if (imageResult && imageResult.isComposite) {
        iconDiv.innerHTML = `
          <div class="device-icon-container" style="
            position: relative;
            width: 170px;
            height: 51px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            overflow: visible;
          ">
            <img src="${imageResult.baseBg}" class="device-icon-bg" style="
              position: absolute;
              top: 0;
              left: 0;
              width: 152px;
              height: 51px;
              display: block;
              z-index: 1;
            " alt="背景" />
            <img src="${imageResult.iconImg}" class="device-icon-overlay" style="
              position: absolute;
              top: 43%;
              left: 15px;
              transform: translateY(-50%);
              width: 20px;
              height: 20px;
              display: block;
              z-index: 2;
            " alt="${item.name}" />
            <div class="device-title" style="
              position: absolute;
              top: 40%;
              left: 45px;
              transform: translateY(-50%);
              color: #ffffff;
              font-size: 16px;
              font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
              font-weight: 360;
              white-space: nowrap;
              text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
              z-index: 3;
              pointer-events: none;
              overflow: visible;
              max-width: none;
              width: auto;
            ">${item.name}</div>
          </div>
        `
      } else {
        // 兼容旧格式（直接返回图片URL）
        iconDiv.innerHTML = `
          <img src="${imageResult}" style="
            width: 152px;
            height: 51px;
            cursor: pointer;
            display: block;
          " alt="${item.name}" />
        `
      }
      iconDiv.style.position = 'absolute'
      iconDiv.style.pointerEvents = 'auto'
      iconDiv.style.transform = 'translate(0%, -45%)'
      iconDiv.style.zIndex = '999'
      iconDiv.style.overflow = 'visible'

      const container = document.getElementById('base3dmap')
      if (container) {
        container.appendChild(iconDiv)
        console.log(`设备图标已添加到容器: ${item.name}`)
      } else {
        console.error('未找到base3dmap容器，无法添加设备图标')
        return null
      }

      // 更新图标位置的函数
      function updatePosition() {
        const cartesian = Cesium.Cartesian3.fromDegrees(item.lng, item.lat, heightOffset)
        const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
        if (Cesium.defined(canvasPosition)) {
          // 参考Billboard的pixelOffset偏移方式，添加像素偏移
          // 图标在容器内位置：left: 15px, width: 20px，所以图标中心在25px
          const pixelOffsetX = -25; // 向左偏移25像素，让设备图标的中心对齐点位
          const pixelOffsetY = -25;   // 垂直向上偏移25像素
          
          // 更新图标位置（在设备实际位置+像素偏移）
          iconDiv.style.left = `${canvasPosition.x + pixelOffsetX}px`
          iconDiv.style.top = `${canvasPosition.y + pixelOffsetY}px`
          iconDiv.style.display = 'block'
          iconDiv.style.visibility = 'visible'
          iconDiv.style.opacity = '1'
        } else {
          iconDiv.style.display = 'none'
        }
      }

      // 监听相机变化和场景渲染事件
      window.viewer.camera.changed.addEventListener(updatePosition)
      window.viewer.scene.postRender.addEventListener(updatePosition)
      updatePosition()

      // 添加DOM元素自动清理机制
      let cleanupCheckCount = 0;
      const cleanupCheck = () => {
        // 降低检查频率，每60帧检查一次（约1秒检查一次）
        cleanupCheckCount++;
        if (cleanupCheckCount < 60) return;
        cleanupCheckCount = 0;
        
        // 检查对应的entity是否还存在
        const entity = window.viewer.entities.getById(id);
        if (!entity) {
          // 如果entity不存在，清理DOM元素
          if (iconDiv && iconDiv.parentNode) {
            iconDiv.parentNode.removeChild(iconDiv);
            console.log(`🧹 自动清理孤立的设备图标DOM: ${iconDiv.id}`);
          }
          // 移除事件监听器
          window.viewer.camera.changed.removeEventListener(updatePosition);
          window.viewer.scene.postRender.removeEventListener(updatePosition);
          window.viewer.scene.postRender.removeEventListener(cleanupCheck);
        }
      };
      
      // 监听场景渲染，定期检查DOM清理（低频率）
      window.viewer.scene.postRender.addEventListener(cleanupCheck);

      return {
        iconDiv,
        updatePosition,
        cleanup: () => {
          if (iconDiv && iconDiv.parentNode) {
            iconDiv.parentNode.removeChild(iconDiv);
          }
          window.viewer.camera.changed.removeEventListener(updatePosition);
          window.viewer.scene.postRender.removeEventListener(updatePosition);
          window.viewer.scene.postRender.removeEventListener(cleanupCheck);
        }
      }
    } catch (error) {
      console.error('创建设备图标失败:', error);
      return null
    }
  }

  /**
   * 添加图片点位，支持随机弹窗
   * @param {Array} points - 点位数组
   * @param {Function} onClick - 点击回调函数
   */
  async addImagePoints(points, onClick) {
    console.log(`开始添加 ${points.length} 个图片点位`);
    let successCount = 0;

    // 存储点位数据
    this.imagePointsData = points;

    // 创建坐标映射，用于记录相同坐标的点位数量
    const coordMap = new Map();
    points.forEach(item => {
      const coordKey = `${item.lng},${item.lat}`;
      coordMap.set(coordKey, (coordMap.get(coordKey) || 0) + 1);
    });

    // 记录每个坐标已经使用的高度偏移次数
    const coordOffsetCount = new Map();

    // 使用 for...of 循环替代 forEach，确保异步操作正确执行
    for (let index = 0; index < points.length; index++) {
      const item = points[index];
      try {
        // 检查坐标数据有效性
        if (!item.lng || !item.lat || !item.data || !item.data.equipmentTypeName) {
          console.warn(`点位数据无效，跳过:`, item);
          continue;
        }

        const coordKey = `${item.lng},${item.lat}`;
        const totalPoints = coordMap.get(coordKey);
        const currentOffset = coordOffsetCount.get(coordKey) || 0;

        // 计算高度偏移
        let heightOffset = 0;
        if (totalPoints > 1) {
          // 如果有多个点位在同一坐标，设置不同的高度
          heightOffset = currentOffset * 5; // 每个点位高度差5米
          coordOffsetCount.set(coordKey, currentOffset + 1);
        }

        // 1. 添加不可见的点用于定位和点击事件
        // 确保ID有效，如果item.id为null则生成一个
        let entityId = item.id || `imagePoint_${index}_${Date.now()}`;
        const entity = window.viewer.entities.add({
          id: entityId,
          deviceType: item.data.equipmentTypeId, // 添加设备类型标识
          position: Cesium.Cartesian3.fromDegrees(item.lng, item.lat, heightOffset),
          point: {
            pixelSize: 1, // 设为1像素，几乎不可见
            color: Cesium.Color.TRANSPARENT, // 透明色
            outlineColor: Cesium.Color.TRANSPARENT,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.CENTER
          }
        });
        entity._isCustomMapPoint = true;

        // 2. 绑定点击事件
        entity._customData = item.data;
        entity._customCallback = onClick || ((data, entity) => {
          console.log('🖱️ 点位点击回调被触发:', data);

          // 停止随机弹窗
          this.vm.stopRandomPopup();
          // 标记用户手动关闭弹窗，禁止后续自动弹窗
          this._userClosedPopup = true;

          // 创建用户点击的弹窗
          console.log('📱 开始创建用户点击的弹窗...');
          const bubbleInstance = new Bubble({
            viewer: window.viewer,
            data: { ...data, position: [Number(data.equipmentLng), Number(data.equipmentLat)] },
            type: 0,
            component: 'CustomPopup'
          });

          // 保存弹窗实例
          this.bubbles = bubbleInstance;

          // 设置弹窗关闭事件
          if (this.bubbles && this.bubbles.vmInstance) {
            this.bubbles.vmInstance.$on('close', () => {
              // 执行原始关闭
              this.bubbles.windowClose();
              this.bubbles = null;
              // 标记用户手动关闭弹窗，禁止后续自动弹窗
              this._userClosedPopup = true;
              // 不再自动弹窗
            });
          }
        })

        // 3. 使用公共函数创建DOM图标
        const domIconResult = await this.createDOMDeviceIcon(item, entityId, heightOffset);
        if (domIconResult) {
          // 为DOM图标添加点击事件
          domIconResult.iconDiv.addEventListener('click', (e) => {
            e.stopPropagation()
            console.log('点击设备图标:', item.data)
            // 触发设备点击回调
            if (entity._customCallback) {
              entity._customCallback(item.data, entity)
            }
          })
        }

        successCount++;
      } catch (error) {
        console.error(`添加点位 ${index} 时发生错误:`, error, item);
      }
    };

    console.log(`成功添加 ${successCount} 个图片点位，失败 ${points.length - successCount} 个`);

    // 4. 注册点击事件（先清理再注册，避免重复）
    this.vm.setupImagePointClickHandler();

    // 5. 启动随机弹窗（只有在有有效数据且用户未手动关闭时才启动）
    if (successCount > 0 && this.imagePointsData.length > 0) {
      if (!this._userClosedPopup) {
        console.log('📱 计划8秒后启动随机弹窗...');
        setTimeout(() => {
          // 再次检查用户是否手动关闭了弹窗
          if (!this._userClosedPopup) {
            console.log('✅ 开始启动随机弹窗');
            this.vm.startRandomPopup();
          } else {
            console.log('❌ 用户已手动关闭弹窗，取消启动随机弹窗');
          }
        }, 8000); // 8秒后开始随机弹窗
      } else {
        console.log('❌ 用户已手动关闭弹窗，跳过启动随机弹窗');
      }
    } else {
      console.log('❌ 无有效数据，清理并停止随机弹窗');
      this.imagePointsData = [];
      this.vm.stopRandomPopup();
    }
  }

  /**
   * 批量添加图片点位，支持名称和点击弹窗，不支持随机弹窗
   * @param {Array} points - 点位数组
   * @param {Function} onClick - 点击回调函数
   * @param {string} idPrefix - 点位id前缀，用于区分点位类型
   * @param {boolean} isNoFly - 是否跳过飞行动画
   */
  async addImagePoints2(points, onClick, idPrefix, isNoFly) {
    console.log(`开始添加 ${points.length} 个图片点位`);
    let successCount = 0;

    // 检查是否存在保存的视角，如果存在则跳过flyto操作
    const hasSavedView = this.vm.checkSavedViewExists();
    if (hasSavedView) {
      console.log('🔒 检测到存在保存的视角，跳过flyto操作，仅添加图片点位');
    }

    // 创建坐标映射，用于记录相同坐标的点位数量
    const coordMap = new Map();
    points.forEach(item => {
      const coordKey = `${item.lng},${item.lat}`;
      coordMap.set(coordKey, (coordMap.get(coordKey) || 0) + 1);
    });

    // 记录每个坐标已经使用的高度偏移次数
    const coordOffsetCount = new Map();

    for (let index = 0; index < points.length; index++) {
      const item = points[index];
      try {
        console.log(item)
        // 检查坐标数据有效性
        if (!item.lng || !item.lat || !item.data || !item.data.equipmentTypeName) {
          console.warn(`点位数据无效，跳过:`, item);
          continue;
        }

        const coordKey = `${item.lng},${item.lat}`;
        const totalPoints = coordMap.get(coordKey);
        const currentOffset = coordOffsetCount.get(coordKey) || 0;

        // 计算高度偏移
        let heightOffset = 0;
        if (totalPoints > 1) {
          // 如果有多个点位在同一坐标，设置不同的高度
          heightOffset = currentOffset * 5; // 每个点位高度差5米
          coordOffsetCount.set(coordKey, currentOffset + 1);
        }

        // 1. 仅添加一个不可见的点用于定位和点击事件（不显示canvas图标）
        let id = idPrefix ? `${idPrefix}${item.id}` : item.id
        const entity = window.viewer.entities.add({
          id: id,
          deviceType: item.data.equipmentTypeId, // 添加设备类型标识
          position: Cesium.Cartesian3.fromDegrees(item.lng, item.lat, heightOffset),
          point: {
            pixelSize: 1, // 设为1像素，几乎不可见
            color: Cesium.Color.TRANSPARENT, // 透明色
            outlineColor: Cesium.Color.TRANSPARENT,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.CENTER
          }
        });
        entity._isCustomMapPoint = true;

        // 2. 绑定点击事件
        entity._customData = item.data;
        entity._customCallback = onClick || ((data, entity) => {
          console.log('默认点击回调:', data)
          this.vm.createBubble(
            {
              ...data,
              position: [
                Number(data.equipmentLng),
                Number(data.equipmentLat)
              ]
            },
            0,
            'CustomPopup'
          )
        })

        // 3. 使用公共函数创建DOM图标
        const domIconResult = await this.createDOMDeviceIcon(item, id, heightOffset);
        if (domIconResult) {
          // 为DOM图标添加点击事件
          domIconResult.iconDiv.addEventListener('click', (e) => {
            e.stopPropagation()
            console.log('点击设备图标:', item.data)
            // 触发设备点击回调
            if (entity._customCallback) {
              entity._customCallback(item.data, entity)
            }
          })
        }

        successCount++;

      } catch (error) {
        console.error(`添加点位 ${index} 时发生错误:`, error, item);
      }
    }

    console.log(`成功添加 ${successCount} 个图片点位，失败 ${points.length - successCount} 个`);

    if (isNoFly) return

    // 只有在没有保存视角时才执行flyto操作
    if (!hasSavedView && successCount > 0 && points.length > 0) {
      // 转换点位数据格式以适配calculateOptimalFlyPosition方法
      const formattedPoints = points.map(item => ({
        lon: item.lng,
        lan: item.lat,
        id: item.id,
        name: item.name
      }));

      const flyToPosition = this.vm.calculateOptimalFlyPosition(formattedPoints);
      console.log('🛸 addImagePoints2计算出的最佳视角:', flyToPosition);

      // 使用平滑的飞行动画
      window.viewer.camera.flyTo({
        destination: flyToPosition.destination,
        duration: flyToPosition.duration || 2.5,
        complete: () => {
          console.log('✅ addImagePoints2地图视角调整完成，所有项目点位已可见');
        }
      });
    } else if (hasSavedView) {
      console.log('🔒 存在保存视角，跳过视角调整，保持当前视角');
    }

    // 注册点击事件（先清理再注册，避免重复）
    this.vm.setupImagePointClickHandler();
  }

  /**
   * 停止随机弹窗
   */
  stopRandomPopup() {
    if (this.vm.stopRandomPopup) {
      this.vm.stopRandomPopup();
    }
  }

  /**
   * 清理所有点位数据
   */
  clearImagePointsData() {
    this.imagePointsData = [];
    this._userClosedPopup = false;
    this.bubbles = null;
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearImagePointsData();
    this.vm = null;
  }
} 
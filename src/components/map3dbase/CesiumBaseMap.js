/*
 *3d地图容器组件
 * @module components/map3dbase/CesiumBaseMap
 * @param {Array} [center]    - 地图默认中心坐标
 * @param {Number} [zoom]     - 地图缩放级别
 */
import Vue from 'vue';
import Point from './components/point'
import DeviceCard from './components/deviceCard'
import CustomPopup from './components/CustomPopup.vue'
import SimpleHookVideoModal from '../modal/SimpleHookVideoModal.vue'

import {
  getMapConfig,
  getCraneArea,
  getCraneVerticalProtection,
  getMechanical,
  getTowerCraneMechanical,
  getAiSafePoint,
  getMechanicalRefDevice,
  getUseElectricDevices,
} from '@/api/index'
import { listPage } from '@/api/xxindex'

window.mapLayer = null;
window.layer = null;
window.modelTile = [];

window.entity = null; //图层
window.modeCurrentId = '';
window.modeCurrentHight = null;
import Bubble from './Bubble.js';
import ConstructionAntiInvasion from './js/constructionAntiInvasion.js';
import CraneManager from './CraneManager.js'; // 导入吊车管理器
import TowerCraneConstructor from './js/towerCrane.js';
import CollisionPrevention from './js/collisionPrevention.js';
import cameraViewManager from '@/utils/CameraViewManager.js'; // 导入相机视角管理器
import ConstructionAreaManager from './ConstructionAreaManager.js'; // 导入施工区域管理器
import CivilAirDefense from './js/civilAirDefense.js'; // 导入人防（穿透式）
import DevicePointManager from './DevicePointManager.js'; // 导入设备点位管理器
import HighFormworkMonitoring from './js/highFormworkMonitoring.js'; // 导入高支模安全监控

var getClickPositions;
var drawHandler = null;
var updatePosition = null;
import { EventBus } from "@/utils/event-bus.js";
export default {
  name: '',
  components: { CustomPopup, SimpleHookVideoModal, Point, DeviceCard },
  events: {},
  props: {},
  data() {
    return {
      leave: '13',
      selectTreeLabel: null,
      bubbles: null,
      showDeviceCard: false,
      mapInitialized: false, // 地图是否已完全初始化

      // 设备图标映射
      deviceImageUrl: {},
      imagePointsData: [], // 存储图片点位数据
      autoCloseTimer: null, // 自动关闭定时器
      currentPopupData: null, // 当前弹窗数据
      equipmentTypes: null, // 存储设备类型数据
      mapViewLogTimer: null, // 地图视图日志防抖定时器
      _userClosedPopup: false, // 用户手动关闭弹窗标志
      currentScene: null, // 当前场景状态：'model'、'high'、'ai'、'useElectric' 等
      currentPageName: 'pointScreen', // 当前页面名称，用于保存和恢复视角
      base: {
        constructionAntiInvasion: null, // 施工防侵限
        civilAirDefense: null, // 人防（穿透式）
        highFormworkMonitoring: null, // 高支模安全监控
      },
      radarDataCache: null,
      craneManager: null, // 吊车管理器实例
      towerCrane: null, // 塔吊安全监测管理器实例
      collisionPrevention: null, // 群塔防碰撞管理器实例
      constructionAreaManager: null, // 高支模管理器实例
      _hasRestoredView: false, // 标记是否已恢复视角
      towerCraneDataTimer: null,

      // 吊钩可视化弹窗相关
      showHookVideoModal: false,
      currentHookDevice: null,
      hookVideoDeviceList: []
    };
  },
  // components: {
  //   Point,
  //   DeviceCard,
  // },
  created() {
    console.log('🔧 CesiumBaseMap created - 组件创建');
  },
  mounted() {
    console.log('🔧 CesiumBaseMap mounted - 组件开始挂载');
    this.base.constructionAntiInvasion = new ConstructionAntiInvasion(this);
    this.base.civilAirDefense = new CivilAirDefense(this);
    this.base.highFormworkMonitoring = new HighFormworkMonitoring(this);
    this.init3dMap();
    this.setupEventListeners();
    this.initializeMapViewLogging();
    this.craneManager = new CraneManager(this); // 初始化吊车管理器
    this.towerCrane = new TowerCraneConstructor(this); // 初始化吊车管理器
    this.collisionPrevention = new CollisionPrevention(this); // 初始化群塔防碰撞管理器
    this.constructionAreaManager = new ConstructionAreaManager(this); // 初始化高支模管理器
    this.devicePointManager = new DevicePointManager(this); // 初始化设备点位管理器

    // 延迟确保地图完全初始化后，处理可能在初始化期间发送的事件
    setTimeout(() => {
      console.log('🚀 地图组件初始化完成，准备接收事件');
      this.mapInitialized = true;
    }, 1000);
  },

  beforeDestroy() {
    EventBus.$off('showVideoList') // 避免内存泄漏
  },
  methods: {
    /*
     *@description: 初始创建三维球,以及实例化相应对象
     *@author: qqjuanqq
     *@date: 2023-07-24
     *@version: V1.0.0
     */
    init3dMap() {
      let that = this;
      if (window.EarthMap != null) {
        window.EarthMap = null;
      }
      window.current3dModels = [];
      window.EarthMap = EarthView;
      window.polyline = new EarthView.PolylineGeometry(); //线图层
      window.EarthMap.createEarth('base3dmap', {
        navigation: true,
        statusBar: true,
        allowPrint: true,
        selectionIndicator: false,
        enableCompass: true, // 启用指北针
        // 指南针外环
        enableCompassOuterRing: true,
        // 添加基础配置
        scene3DOnly: true, // 仅使用3D模式
        shouldAnimate: true, // 启用动画
        baseLayerPicker: false, // 禁用底图选择器
        fullscreenButton: false, // 禁用全屏按钮
        geocoder: false, // 禁用地理编码器
        homeButton: false, // 禁用Home按钮
        infoBox: false, // 禁用信息框
        sceneModePicker: false, // 禁用场景模式选择器
        timeline: false, // 禁用时间线
        navigationHelpButton: false // 禁用帮助按钮
        // terrainProvider: new Cesium.EllipsoidTerrainProvider() // 使用默认地形
      });


      //初始创建全局的对象
      //创建模型对象
      window.EarthMap.model = new EarthView.Model();
      // 服务图层
      window.EarthMap.layer = new EarthView.Layer();
      //标注对象
      window.EarthMap.entity = new EarthView.Entity(); //图层

      window.EarthMap.pointMark = new EarthView.PlaceMark(); //标注图层
      window.EarthMap.terrainProvider = new EarthView.Terrain();

      //参数设置
      window.EarthMap.getEarth().scene.sun.show = false; //在Cesium1.6(不确定)之后的版本会显示太阳和月亮，不关闭会影响展示
      window.EarthMap.getEarth().scene.moon.show = false;
      window.EarthMap.getEarth().scene.skyBox.show = false; //关闭天空盒，否则会显示天空颜色
      window.EarthMap.getEarth().scene.globe.enableLighting = false;
      window.EarthMap.getEarth().shadows = false;

      // 实例化粒子效果
      window.flameParticle = new EarthView.FlameParticle();

      window.viewer = window.EarthMap.getEarth();
      window.thirdPartyLayer = new window.EarthMap.Layer();
      window.viewer.scene.globe.depthTestAgainstTerrain = false;

      // // 创建指北针小部件并将其添加到地图
      // window.viewer.extend(Cesium.viewerCesiumNavigationMixin, {
      //   defaultResetView: Cesium.Cartographic.fromDegrees(0, 0, 15000000),
      //   enableCompass: true, // 启用指北针
      //   enableZoomControls: true, // 启用缩放
      // });

      // 加载地形
      if (globalConfig.map.isAddmodel) {
        that.addmodel();
      }
      //是否加载天地图底图
      if (globalConfig.map.isLoadBaseMap) that.addTiandituBaseLayer();

      // 加载边界线
      // that.addCoverPolygon(fgArea); //遮罩，边界墙

      window.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );
      // 解决抗锯齿的问题
      if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
        window.viewer.resolutionScale = window.devicePixelRatio;
      }
      window.viewer.scene.fxaa = true;
      window.viewer.scene.postProcessStages.fxaa.enabled = true;

      var supportsImageRenderingPixelated =
        window.viewer.cesiumWidget._supportsImageRenderingPixelated;
      if (supportsImageRenderingPixelated) {
        var vtxf_dpr = window.devicePixelRatio;
        while (vtxf_dpr >= 2.0) {
          vtxf_dpr /= 2.0;
        }
        window.viewer.resolutionScale = vtxf_dpr;
      }
      this.leave = this.getZoom()
      window.viewer.camera.moveEnd.addEventListener(() => {
        this.leave = this.getZoom()
        // 添加地图操作日志
        this.logMapViewInfo()
      })
      window.viewer.camera.changed.addEventListener(this.SetBiLie)
      document.addEventListener('keydown', function (e) {
        if (e.key == 'r') {
          // 检查是否存在保存的视角，如果存在则跳过重置操作
          if (that.checkSavedViewExists()) {
            console.log('🔒 检测到存在保存的视角，跳过键盘重置视角操作');
            return;
          }

          if (globalConfig.map && globalConfig.map.initPosition) {
            EarthView.lookAt(globalConfig.map.initPosition);
          }
        }
      });
      this.addClickEvent();

      // 记录地图初始化后的视图信息
      setTimeout(() => {
        console.log('📍 地图初始化完成，记录初始视图:');
        this.logMapViewInfo();
      }, 1000);

    },

    //删除
    deleteDraw() {
      window.drawAreaCollection.entities.removeAll();
      // /移除点击事件
      if (drawHandler) {
        drawHandler.destroy();
      }
      // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      // //移除鼠标移动事件
      // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      // //移除鼠标右击事件
      // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      let body = document.querySelector("body")
      body.style.cursor = "default"
    },
    //绘制范围区域
    drawArea(type) {
      this.deleteDraw();
      if (type === "delete") return
      let body = document.querySelector("body")
      body.style.cursor = "crosshair"
      let line = undefined; //全局线对象
      let polygon = undefined; //全局线对象
      let point = undefined; //全局点对象
      let linearr = []; //线的坐标存储
      let viewer = window.viewer
      //添加事件点击添加绘制点
      drawHandler = new Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      );
      drawHandler.setInputAction((clickEvent) => {
        //获取世界坐标点
        var pick = viewer.camera.getPickRay(clickEvent.position);
        var cartesian = viewer.scene.globe.pick(pick, viewer.scene);
        //如果世界坐标点存在
        if (cartesian) {
          if (linearr.length === 0) {
            //第一次添加,加两个值
            linearr.push(cartesian); //起点
            linearr.push(cartesian); //鼠标点
          } else if (linearr.length === 2) {
            linearr.push(cartesian); //鼠标点
            linearr.push(linearr[0]); //起点
          } else {
            linearr.push(cartesian); //鼠标点
            let tmp = linearr[linearr.length - 2] //添加一个鼠标点,然后交换顺序,让起点到最后
            linearr[linearr.length - 2] = cartesian;
            linearr[linearr.length - 1] = tmp;
          }
          //添加一个线对象
          if (!line) {
            line = window.drawAreaCollection.entities.add({
              polyline: {
                positions: new Cesium.CallbackProperty(function () {
                  return linearr;
                }, false),
                width: 3,
                material: Cesium.Color.fromCssColorString("rgb(106,248,206)"),
                clampToGround: true,
              },
            });
          }
          if (!polygon && linearr.length > 2) {
            polygon = window.drawAreaCollection.entities.add({
              polygon: {
                hierarchy: new Cesium.CallbackProperty(function () {
                  return new Cesium.PolygonHierarchy(linearr.slice(0, linearr.length - 1));
                }, false),
                outline: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                material: new Cesium.Color.fromCssColorString('rgb(67,214,218)').withAlpha(0.3),
                outlineWidth: 2,
                fill: true,
              }
            });
          }
          if (!point) {
            point = window.drawAreaCollection.entities.add({
              position: new Cesium.CallbackProperty(function () {
                return linearr.length > 2 ? linearr[linearr.length - 2] : linearr[linearr.length - 1];
              }, false),
              label: {
                // 文本。支持显式换行符" \ n"
                text: new Cesium.CallbackProperty(function () {
                  return linearr.length > 2 ? "右键完成绘制" : "需要三个点才可完成绘制";
                }, false),
                // 字体样式，以CSS语法指定字体
                font: '14px Source Han Sans CN',
                // 字体颜色
                fillColor: Cesium.Color.WHITE,
                // 背景颜色
                backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
                // 是否显示背景颜色
                showBackground: true,
                // 字体边框
                outline: true,
                // 字体边框颜色
                outlineColor: Cesium.Color.WHITE,
                // 字体边框尺寸
                outlineWidth: 10,
                // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
                scale: 1.0,
                // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                // 相对于坐标的水平位置
                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                // 相对于坐标的水平位置
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
                pixelOffset: new Cesium.Cartesian2(10, 0),
                eyeOffset: new Cesium.Cartesian3(0, 0, -50),
                // 是否显示
                show: true
              }
            });
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      //鼠标移动事件
      drawHandler.setInputAction((movement) => {
        var pick = viewer.camera.getPickRay(movement.endPosition);
        var cartesian = viewer.scene.globe.pick(pick, viewer.scene);
        if (cartesian) {
          if (line) {
            //考虑在鼠标移动的一瞬间,linearr应该增加一个坐标点,当再次移动时,该坐标点应该更换
            if (linearr.length > 2) {
              linearr[linearr.length - 2] = cartesian;
            } else {
              linearr[linearr.length - 1] = cartesian;
            }

          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      //右键结束事件
      drawHandler.setInputAction((movement) => {
        //执行双击操作
        const a = (linearr) => {
          //笛卡尔点位数组转查询cql点位
          let list = [];
          let tmp = "";
          linearr.forEach((e) => {
            let position = this.transformCartesianToWGS84(e)
            list.push(position);
          })
          list.forEach((e) => {
            let {
              lng,
              lat,
              alt
            } = e;
            tmp += `${lng} ${lat},`
          })
          return tmp.substr(0, tmp.length - 1);
        }
        if (linearr.length < 3) {
          return this.$Message.info("需要三个点才可以完成绘制");
        }
        // /移除点击事件
        // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        // //移除鼠标移动事件
        // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        // //移除鼠标右击事件
        // window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        if (drawHandler) {
          drawHandler.destroy();
        }
        //移除提示
        point && window.drawAreaCollection.entities.remove(point)
        let body = document.querySelector("body")
        body.style.cursor = "default"
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    },
    //绘制属性表需要高亮的图层
    drawHightColorLayer(value) {
      const drawArea = (coordinates, index) => {
        coordinates.forEach((e) => {
          e.forEach(m => {
            let list = [];
            m.forEach((item) => {
              item.forEach((o) => {
                list.push(o)
              })
            })
            window.drawTablGeometryeCollection.entities.add({
              id: index,
              polygon: {
                // 获取指定属性（positions，holes（图形内需要挖空的区域））
                hierarchy: {
                  positions: Cesium.Cartesian3.fromDegreesArray(list),
                },
                // 边框
                outline: true,
                // 边框颜色
                outlineColor: Cesium.Color.BLUE,
                // 边框尺寸
                outlineWidth: 20,
                // 填充的颜色，withAlpha透明度
                material: Cesium.Color.BLUE.withAlpha(0.3),
                // 是否被提供的材质填充
                fill: true,
                // 恒定高度
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, //设置HeightReference高度参考类型为CLAMP_TO_GROUND贴地类型
                // 显示在距相机的距离处的属性，多少区间内是可以显示的
                // 是否显示
                show: true,
                // 顺序,仅当`clampToGround`为true并且支持地形上的折线时才有效。
                zIndex: 10
              },
              polyline: {
                positions: Cesium.Cartesian3.fromDegreesArray(list),
                // 宽度
                width: 3,
                // 线的颜色
                clampToGround: true,
                material: Cesium.Color.BLUE,
                zIndex: 10,
                // 显示在距相机的距离处的属性，多少区间内是可以显示的
                // 是否显示
                show: true
              }
            })
          })

        })
      }
      let {
        geometry,
        index
      } = value;
      let {
        coordinates,
        type
      } = geometry;
      switch (type) {
        case "MultiPolygon":
          drawArea(coordinates, index)
          break;

        default:
          break;
      }
    },
    //飞行
    flyToPosition(value) {
      // 检查是否存在保存的视角，如果存在则跳过flyto操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过flyToPosition操作');
        return;
      }

      window.EarthView.lookAt(value);
    },

    //点击获取点位
    clickGetPosition() {
      let list = [];
      let viewer = window.viewer;
      viewer.screenSpaceEventHandler.setInputAction((clickEvent) => {
        var pick = viewer.camera.getPickRay(clickEvent.position);
        if (
          pick &&
          pick.id
        ) {
          console.log(pick.id);
        }
        var cartesian = viewer.scene.globe.pick(pick, viewer.scene);
        list.push(cartesian)
        getClickPositions = list;
        console.log(this.transformCartesianToWGS84(cartesian));
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    // 创建弹框
    createBubble(data, type = 0, component) {
      console.log('📱 createBubble: 开始创建弹窗', { type, component, hasExisting: !!this.bubbles })

      // 强制关闭现有弹窗（更彻底的清理）
      if (this.bubbles) {
        console.log('🔄 createBubble: 关闭现有弹窗')
        try {
          this.bubbles.windowClose();
        } catch (error) {
          console.warn('⚠️ createBubble: 关闭弹窗时发生错误:', error)
        }
        this.bubbles = null;
      }

      console.log('📱 createBubble: 创建新弹窗实例...')
      try {
        this.bubbles = new Bubble({
          viewer: window.viewer,
          data,
          type,
          component,
          store: this.$store,
          router: this.$router
        });

        // 验证弹窗是否成功创建
        if (this.bubbles) {
          console.log('✅ createBubble: 弹窗创建成功', {
            hasBubbles: !!this.bubbles,
            hasWindowClose: !!(this.bubbles && this.bubbles.windowClose),
            hasContainer: !!(this.bubbles && this.bubbles.container)
          })
        } else {
          console.error('❌ createBubble: 弹窗对象为空')
        }
      } catch (error) {
        console.error('❌ createBubble: 创建弹窗时发生错误:', error)
        this.bubbles = null
      }
    },

    /**
     * 启动图片点位随机弹窗系统
     */
    startImagePointsRandomPopup() {
      // 清除之前的定时器
      this.stopImagePointsRandomPopup()

      // 如果没有点位数据，不启动定时器
      if (!this.imagePointsData || this.imagePointsData.length === 0) {
        console.log('❌ 没有图片点位数据，不启动随机弹窗')
        return
      }

      console.log(`🚀 启动随机弹窗系统，点位数据: ${this.imagePointsData.length} 个`)

      // 预选第一个弹窗数据
      this.selectNextPopupData()

      // 立即显示第一个弹窗
      this.showScheduledPopup()

      console.log('✅ 随机弹窗系统已启动')
    },

    /**
     * 停止图片点位随机弹窗系统
     */
    stopImagePointsRandomPopup() {
      try {
        console.log('🛑 停止随机弹窗系统...')

        // 清除主定时器
        if (this.imagePointsRandomTimer) {
          clearInterval(this.imagePointsRandomTimer)
          this.imagePointsRandomTimer = null
        }

        // 清除调度定时器
        if (this.popupScheduleTimer) {
          clearTimeout(this.popupScheduleTimer)
          this.popupScheduleTimer = null
        }

        // 清除自动关闭定时器
        if (this.autoCloseTimer) {
          clearTimeout(this.autoCloseTimer)
          this.autoCloseTimer = null
        }

        // 重置所有状态
        this.isRandomPopupPaused = false
        this.currentPopupData = null
        this.nextPopupData = null
        this.isCreatingPopup = false

        // 关闭当前弹窗（如果存在）
        if (this.bubbles) {
          try {
            this.bubbles.windowClose()
            this.bubbles = null
          } catch (error) {
            console.warn('⚠️ 关闭弹窗时发生错误:', error)
            this.bubbles = null
          }
        }

        console.log('✅ 随机弹窗系统已停止')

      } catch (error) {
        console.error('❌ 停止随机弹窗系统时发生错误:', error)
        // 强制重置所有状态
        this.imagePointsRandomTimer = null
        this.popupScheduleTimer = null
        this.autoCloseTimer = null
        this.bubbles = null
        this.isRandomPopupPaused = false
        this.currentPopupData = null
        this.nextPopupData = null
        this.isCreatingPopup = false
      }
    },

    /**
     * 选择下一个弹窗数据（避免连续重复）
     */
    selectNextPopupData() {
      if (!this.imagePointsData || this.imagePointsData.length === 0) {
        console.warn('❌ 没有点位数据可选择')
        this.nextPopupData = null
        return
      }

      let selectedData

      // 如果只有一个点位，直接选择
      if (this.imagePointsData.length === 1) {
        selectedData = this.imagePointsData[0]
      } else {
        // 多个点位时，避免连续相同
        let attempts = 0
        do {
          const randomIndex = Math.floor(Math.random() * this.imagePointsData.length)
          selectedData = this.imagePointsData[randomIndex]
          attempts++
        } while (
          this.currentPopupData &&
          selectedData.id === this.currentPopupData.id &&
          attempts < 10 // 最多尝试10次，避免死循环
        )
      }

      this.nextPopupData = selectedData
      // console.log(`🎯 已选择下一个弹窗: ${selectedData.name} (ID: ${selectedData.id})`)
    },

    /**
 * 显示预定的弹窗（防重复创建）
 */
    showScheduledPopup() {
      // 检查创建锁，防止重复创建
      if (this.isCreatingPopup) {
        console.log('🔒 弹窗正在创建中，跳过重复请求')
        return
      }

      if (this.isRandomPopupPaused) {
        console.log('⏸️ 随机弹窗已暂停，跳过显示')
        return
      }

      if (!this.nextPopupData) {
        console.warn('❌ 没有预定的弹窗数据')
        return
      }

      // 设置创建锁
      this.isCreatingPopup = true

      try {
        console.log(`📱 显示预定弹窗: ${this.nextPopupData.name}`)

        // 先清理现有弹窗和定时器
        this.forceCleanupPopup()

        // 记录当前弹窗数据
        this.currentPopupData = this.nextPopupData

        // 创建弹窗
        this.createBubble(
          {
            ...this.currentPopupData.data,
            position: [
              Number(this.currentPopupData.data.equipmentLng),
              Number(this.currentPopupData.data.equipmentLat)
            ]
          },
          0,
          'CustomPopup'
        )

        // 验证弹窗是否成功创建
        if (this.bubbles) {
          console.log('✅ 预定弹窗创建成功')

          // 设置弹窗关闭监听器
          this.setupScheduledPopupCloseListener()

          // 5秒后自动关闭并继续下一个
          this.autoCloseTimer = setTimeout(() => {
            if (this.bubbles && !this.isRandomPopupPaused) {
              console.log('⏰ 弹窗显示5秒，自动关闭并继续下一个')
              try {
                this.bubbles.windowClose()
                this.bubbles = null
                this.currentPopupData = null
                this.autoCloseTimer = null

                // 准备并显示下一个弹窗（连续显示模式）
                this.selectNextPopupData()
                setTimeout(() => {
                  this.isCreatingPopup = false // 释放创建锁
                  if (!this.isRandomPopupPaused) {
                    this.showScheduledPopup()
                  }
                }, 200) // 稍等一下再显示下一个

              } catch (error) {
                console.warn('⚠️ 自动关闭弹窗时发生错误:', error)
                this.bubbles = null
                this.currentPopupData = null
                this.autoCloseTimer = null
                // 出错时也要继续下一个
                this.selectNextPopupData()
                setTimeout(() => {
                  this.isCreatingPopup = false // 释放创建锁
                  if (!this.isRandomPopupPaused) {
                    this.showScheduledPopup()
                  }
                }, 1000) // 出错后延迟更长时间
              }
            } else {
              this.autoCloseTimer = null
              this.isCreatingPopup = false // 释放创建锁
            }
          }, 5000) // 5秒后自动关闭

        } else {
          console.error('❌ 预定弹窗创建失败')
          this.isCreatingPopup = false // 释放创建锁
        }

      } catch (error) {
        console.error('❌ 显示预定弹窗时发生错误:', error)
        this.isCreatingPopup = false // 释放创建锁
      }
    },

    /**
     * 调度下一个弹窗（5秒后）
     */
    scheduleNextPopup() {
      // 清除之前的调度定时器
      if (this.popupScheduleTimer) {
        clearTimeout(this.popupScheduleTimer)
        this.popupScheduleTimer = null
      }

      console.log('⏰ 调度下一个弹窗（5秒后）')
      console.log(`📊 当前状态 - 暂停: ${this.isRandomPopupPaused}, 点位数量: ${this.imagePointsData.length}`)

      this.popupScheduleTimer = setTimeout(() => {
        console.log(`🔍 5秒后检查状态 - 暂停: ${this.isRandomPopupPaused}, 点位数量: ${this.imagePointsData.length}`)

        if (!this.isRandomPopupPaused && this.imagePointsData.length > 0) {
          console.log('✅ 条件满足，开始调度下一个随机弹窗')
          this.selectNextPopupData()
          this.showScheduledPopup()
        } else {
          console.log('❌ 条件不满足，跳过随机弹窗调度')
          if (this.isRandomPopupPaused) {
            console.log('   原因：随机弹窗处于暂停状态')
          }
          if (this.imagePointsData.length === 0) {
            console.log('   原因：没有点位数据')
          }
        }
      }, 5000)
    },

    /**
     * 强制清理弹窗和定时器（直接清理，不触发监听器）
     */
    forceCleanupPopup() {
      console.log('🧹 开始强制清理弹窗...')

      // 清除自动关闭定时器
      if (this.autoCloseTimer) {
        clearTimeout(this.autoCloseTimer)
        this.autoCloseTimer = null
        console.log('🧹 清除自动关闭定时器')
      }

      // 直接清理弹窗DOM和引用（不触发关闭监听器）
      if (this.bubbles) {
        console.log('🧹 清理现有弹窗对象')
        try {
          // 先尝试正常关闭，但不触发我们的监听器
          const bubbleInstance = this.bubbles

          // 清除我们设置的监听器
          if (bubbleInstance.originalWindowClose) {
            bubbleInstance.windowClose = bubbleInstance.originalWindowClose
          }

          // 直接移除DOM元素
          if (bubbleInstance.container && bubbleInstance.container.parentNode) {
            bubbleInstance.container.parentNode.removeChild(bubbleInstance.container)
            console.log('🧹 移除弹窗DOM元素')
          }
        } catch (error) {
          console.warn('⚠️ 清理弹窗DOM时发生错误:', error)
        }

        // 直接清除引用，不调用 windowClose()
        this.bubbles = null
        console.log('🧹 清除弹窗引用')
      }

      console.log('🧹 强制清理完成')
    },

    // 清理指定设备的弹窗
    cleanupDevicePopup(deviceId) {
      if (this.bubbles && this.bubbles.bubbleId && this.bubbles.bubbleId.includes(deviceId)) {
        this.bubbles.windowClose();
        this.bubbles = null;
        console.log(`🧹 清理设备弹窗: ${deviceId}`);
      }
    },

    /**
     * 设置预定弹窗的关闭监听器
     */
    setupScheduledPopupCloseListener() {
      if (!this.bubbles) return

      setTimeout(() => {
        if (this.bubbles && this.bubbles.windowClose) {
          // 保存原始关闭方法
          const originalClose = this.bubbles.windowClose.bind(this.bubbles)

          // 设置自定义关闭方法
          this.bubbles.windowClose = () => {
            // 调用原始关闭方法
            originalClose()

            // 清理所有相关状态
            this.bubbles = null
            this.currentPopupData = null
            this.isCreatingPopup = false // 释放创建锁

            // 清除自动关闭定时器
            if (this.autoCloseTimer) {
              clearTimeout(this.autoCloseTimer)
              this.autoCloseTimer = null
            }

            console.log('🖱️ 用户手动关闭随机弹窗，彻底停止自动弹窗系统')

            // 标记用户手动关闭弹窗，禁止后续自动弹窗
            this._userClosedPopup = true

            // 用户手动关闭，彻底停止自动弹窗系统
            this.stopRandomPopup()
          }
        }
      }, 100)
    },

    /**
     * 设置用户点击弹窗的关闭监听器（轮询确保可靠性）
     */
    setupUserClickPopupCloseListener() {
      let attempts = 0
      const maxAttempts = 20 // 最多尝试20次（2秒）

      const setupListener = () => {
        attempts++
        if (this.bubbles && this.bubbles.windowClose && typeof this.bubbles.windowClose === 'function') {
          // 防止重复绑定
          if (this.bubbles._userClickListenerSet) return true;
          this.bubbles._userClickListenerSet = true;

          const originalClose = this.bubbles.originalWindowClose || this.bubbles.windowClose.bind(this.bubbles)
          this.bubbles.originalWindowClose = originalClose

          this.bubbles.windowClose = () => {
            // 先执行原始关闭
            originalClose()

            // 清理状态和定时器
            this.bubbles = null
            this.currentPopupData = null
            this.isCreatingPopup = false

            // 清理所有定时器
            if (this.autoCloseTimer) {
              clearTimeout(this.autoCloseTimer)
              this.autoCloseTimer = null
            }
            if (this.popupScheduleTimer) {
              clearTimeout(this.popupScheduleTimer)
              this.popupScheduleTimer = null
            }

            // 确保重置暂停状态
            this.isRandomPopupPaused = false

            // 延迟一小段时间后重启随机弹窗系统
            setTimeout(() => {
              if (!this.isRandomPopupPaused && this.imagePointsData.length > 0) {
                console.log('🔄 点位弹窗关闭，重启随机弹窗系统')
                this.selectNextPopupData()
                this.showScheduledPopup()
              }
            }, 100)
          }
          return true
        } else {
          if (attempts < maxAttempts) {
            setTimeout(setupListener, 100)
          } else {
            // 备用方案：直接重置状态并重启随机弹窗
            this.currentPopupData = null
            this.isCreatingPopup = false
            this.isRandomPopupPaused = false

            // 延迟一小段时间后重启随机弹窗
            setTimeout(() => {
              if (!this.isRandomPopupPaused && this.imagePointsData.length > 0) {
                this.selectNextPopupData()
                this.showScheduledPopup()
              }
            }, 100)
          }
          return false
        }
      }
      setupListener()
    },

    /**
     * 检查当前地图上是否有活跃的弹窗
     * @returns {boolean} 如果有弹窗正在显示则返回true，否则返回false
     */
    checkIfPopupIsActive() {
      try {
        let hasActivePopup = false

        // 方法1: 检查 bubbles 对象
        if (this.bubbles && typeof this.bubbles.windowClose === 'function') {
          console.log('🔍 检测到 bubbles 对象存在')

          // 检查 bubbles 对象的容器是否真实存在并可见
          if (this.bubbles.container) {
            const container = this.bubbles.container

            // 检查容器是否在DOM中
            if (document.contains(container)) {
              // 检查容器的可见性
              const style = window.getComputedStyle(container)
              if (style.display !== 'none' && style.visibility !== 'hidden' &&
                parseFloat(style.opacity) > 0) {
                console.log('🔍 bubbles 容器存在且可见')
                hasActivePopup = true
              } else {
                console.log('🔍 bubbles 容器存在但不可见')
                // 清理无效的 bubbles 对象
                this.bubbles = null
              }
            } else {
              console.log('🔍 bubbles 容器不在DOM中')
              // 清理无效的 bubbles 对象
              this.bubbles = null
            }
          } else {
            console.log('🔍 bubbles 对象没有容器')
            // 清理无效的 bubbles 对象
            this.bubbles = null
          }
        }

        // 方法2: 检查DOM中是否有明显的弹窗元素
        if (!hasActivePopup) {
          const popupSelectors = [
            '.bubble-content',
            '.popup-container',
            '.device-popup',
            '.cesium-infoBox-visible',
            '[class*="bubble"]',
            '[class*="popup"]'
          ]

          for (const selector of popupSelectors) {
            const elements = document.querySelectorAll(selector)
            for (const element of elements) {
              if (element.offsetParent !== null) { // 元素可见
                console.log(`🔍 检测到可见弹窗元素: ${selector}`)
                hasActivePopup = true
                break
              }
            }
            if (hasActivePopup) break
          }
        }

        // 方法3: 检查Cesium内置的InfoBox
        if (!hasActivePopup && window.viewer && window.viewer.infoBox && window.viewer.infoBox.viewModel) {
          if (window.viewer.infoBox.viewModel.showInfo) {
            console.log('🔍 检测到 Cesium InfoBox 活跃')
            hasActivePopup = true
          }
        }

        if (hasActivePopup) {
          console.log('🔍 最终结果: 检测到活跃弹窗')
        } else {
          console.log('🔍 最终结果: 未检测到活跃弹窗')
        }

        return hasActivePopup

      } catch (error) {
        console.warn('⚠️ 检查弹窗状态时发生错误:', error)
        // 出错时保守地返回false，允许继续显示弹窗
        return false
      }
    },

    // 清理点击事件监听器
    clearClickEventHandlers() {
      try {
        // 清理全局图片点位点击事件监听器
        if (window._imagePointClickHandler) {
          window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
          window._imagePointClickHandler = null;
          console.log('已清理图片点位点击事件监听器');
        }

        // 清理弹窗
        if (this.bubbles) {
          this.bubbles.windowClose();
          this.bubbles = null;
        }

      } catch (error) {
        console.warn('清理点击事件监听器时发生错误:', error);
      }
    },

    // 设置图片点位点击事件处理器
    setupImagePointClickHandler() {
      try {
        // 先清理之前的事件监听器
        if (window._imagePointClickHandler) {
          window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        }

        // 创建新的点击事件处理器
        window._imagePointClickHandler = (movement) => {
          const picked = window.viewer.scene.pick(movement.position);
          console.log('点击检测:', picked); // 调试信息

          // 检查是否点击到了吊车
          if (picked && picked.id && picked.id._isCrane && picked.id._craneData) {
            console.log('点击到吊车:', picked.id._craneData); // 调试信息
            this.handleCraneClick(picked.id._craneData, picked.id);
            return; // 阻止事件继续冒泡
          }

          // 检查是否点击到了图片点位
          if (picked && picked.id && picked.id._customData && picked.id._customCallback) {
            console.log('触发图片点位点击回调:', picked.id._customData); // 调试信息
            picked.id._customCallback(picked.id._customData, picked.id);
            return; // 阻止事件继续冒泡
          }

          // 如果没有点击到图片点位，执行原有的地图点击逻辑
          this.handleMapClick(picked);
        };

        // 重新注册点击事件
        window.viewer.screenSpaceEventHandler.setInputAction(
          window._imagePointClickHandler,
          Cesium.ScreenSpaceEventType.LEFT_CLICK
        );

        console.log('图片点位点击事件处理器设置完成');

      } catch (error) {
        console.error('设置图片点位点击事件处理器时发生错误:', error);
      }
    },

    // 处理吊车点击事件
    handleCraneClick(craneData, entity) {
      console.log('🚛 处理吊车点击事件:', craneData);

      // 验证数据完整性
      if (!craneData) {
        console.error('❌ 吊车数据为空');
        return;
      }

      if (!craneData.coordinates || !Array.isArray(craneData.coordinates) || craneData.coordinates.length < 2) {
        console.error('❌ 吊车坐标数据无效:', craneData.coordinates);
        return;
      }

      // 确保坐标是数字类型
      const lng = Number(craneData.coordinates[0]);
      const lat = Number(craneData.coordinates[1]);

      if (isNaN(lng) || isNaN(lat)) {
        console.error('❌ 吊车坐标转换失败:', craneData.coordinates);
        return;
      }

      console.log('✅ 吊车坐标验证通过:', { lng, lat });

      // 创建弹窗数据
      const bubbleData = {
        ...craneData,
        position: [lng, lat, 0], // 添加高度参数
        coordinates: [lng, lat], // 保持原有格式
        // 确保数据完整性
        name: craneData.name || '未知吊车',
        id: craneData.id || 'unknown',
        // 添加必要的属性
        data: craneData, // 将原始数据作为data属性传递
        propsData: craneData, // 兼容组件props
        // 确保吊车数据包含必要字段
        craneBusinessRecord: craneData.craneBusinessRecord || {},
        driverList: craneData.driverList || [],
        workState: craneData.workState || 0,
        onlineState: craneData.onlineState || 0,
        limitAlarmFlag: craneData.limitAlarmFlag || false,
        heading: craneData.heading || 0
      };

      console.log('📱 准备创建吊车弹窗:', bubbleData);

      // 验证Bubble构造函数参数
      const bubbleParams = {
        viewer: window.viewer,
        data: bubbleData,
        type: 0,
        component: 'DcCarousel',
        store: this.$store,
        router: this.$router
      };

      console.log('🔧 Bubble构造函数参数:', bubbleParams);

      // 创建弹窗
      this.createBubble(
        bubbleData,
        0,
        'DcCarousel'
      );
    },

    // 处理地图点击事件（原addClickEvent中的逻辑）
    handleMapClick(pick) {
      console.log(pick)
      if (pick && pick.id) {
        if (pick.id._name === '洪涝') {
          let data = {
            position: pick.id.position._value,
            offset: {
              offsetX: 0,
              offsetY: pick.id.billboard.height._value,
            },
            data: pick.id
          }
          this.createBubble(data);
        } else if (typeof pick.id == "string" && pick.id.split("#")[0] == "洪涝") {
          let data = {
            position: pick.primitive._actualPosition,
            offset: {
              offsetX: 0,
              offsetY: pick.primitive.height,
            },
            data: pick.primitive
          }
          this.createBubble(data);
        } else {
          // 发送事件关闭所有point的popup
          this.$bus.emit('closeAllPointPopups')
          // 发送点击地图事件，隐藏项目详情面板
          this.$bus.emit('clickMap')
          if (this.bubbles) {
            this.bubbles.windowClose();
            this.bubbles = null;
          }
        }
      } else {
        // 发送事件关闭所有point的popup
        this.$bus.emit('closeAllPointPopups')
        // 发送点击地图事件，隐藏项目详情面板
        this.$bus.emit('clickMap')
        if (this.bubbles) {
          this.bubbles.windowClose();
          this.bubbles = null;
        }
      }
    },
    //添加地图点击事件（基础版本，主要用于非图片点位场景）
    addClickEvent() {
      let viewer = window.viewer;
      //添加事件点击添加绘制点
      viewer.screenSpaceEventHandler.setInputAction((clickEvent) => {
        let pick = viewer.scene.pick(clickEvent.position);
        this.handleMapClick(pick);
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    /*
     *@description: 监听地图缩放级别
     *@author: luxiaolin
     *@date: 2024-10-21
     *@version: V1.0.0
     */
    getZoom() {
      let viewer = window.viewer;
      let h = viewer.camera.positionCartographic.height
      if (h <= 100) {
        return 19
      } else if (h <= 300) {
        return 18
      } else if (h <= 660) {
        return 17
      } else if (h <= 1300) {
        return 16
      } else if (h <= 2600) {
        return 15
      } else if (h <= 6400) {
        return 14
      } else if (h <= 13200) {
        return 13
      } else if (h <= 26000) {
        return 12
      } else if (h <= 67985) {
        return 11
      } else if (h <= 139780) {
        return 10
      } else if (h <= 250600) {
        return 9
      } else if (h <= 380000) {
        return 8
      } else if (h <= 640000) {
        return 7
      } else if (h <= 1280000) {
        return 6
      } else if (h <= 2600000) {
        return 5
      } else if (h <= 6100000) {
        return 4
      } else if (h <= 11900000) {
        return 3
      } else {
        return 2
      }
    },

    orglocation() {
      // 检查是否存在保存的视角，如果存在则跳过重置到原始位置的操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过orglocation操作');
        return;
      }

      if (globalConfig.map && globalConfig.map.initPosition) {
        EarthView.lookAt(globalConfig.map.initPosition);
      }
    },


    bigMapLayer(layerName) {
      this.zoomByBound(true);
    },
    smallMapLayer(layerName) {
      this.zoomByBound(false);
    },

    zoomByBound(flag) {
      // 检查是否存在保存的视角，如果存在则跳过缩放操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过zoomByBound操作');
        return;
      }

      const center = this.pickCenter()
      let viewer = window.viewer;
      var height = viewer.camera.positionCartographic.height;
      const camera = viewer.camera;
      // var boundingSph = new Cesium.BoundingSphere(Cesium.Cartesian3.fromDegrees(center.lon, center.lat, 1000), height);
      var boundingSph = new Cesium.BoundingSphere(Cesium.Cartesian3.fromDegrees(center.lon, center.lat, 0), height);
      var moveRate = 0;
      if (flag) {
        moveRate = 0.5
      } else {
        moveRate = 2.5
      }
      var zoomParams = {
        duration: 0.8,
        offset: new Cesium.HeadingPitchRange(camera.heading, camera.pitch, height * moveRate)
      }
      camera.flyToBoundingSphere(boundingSph, zoomParams);
    },

    pickCenter() {
      let viewer = window.viewer;
      var ellipsoid = viewer.camera.pickEllipsoid(new Cesium.Cartesian2(
        viewer.canvas.clientWidth / 2,
        viewer.canvas.clientHeight / 2));
      var curPosition = Cesium.Ellipsoid.WGS84.cartesianToCartographic(ellipsoid);
      var lon = curPosition.longitude * 180 / Math.PI;
      var lat = curPosition.latitude * 180 / Math.PI;
      return {
        lon: lon,
        lat: lat
      };
    },

    /*
     *@description: 添加地形
     *@author: qqjuanqq
     *@date: 2023-09-26
     *@version: V1.0.0
     */
    addmodel() {
      // window.EarthMap.terrainProvider.remove(); //删除地形
      window.EarthMap.terrainProvider.addTerrain(globalConfig.map.terrainUrl); //添加地形
    },

    /*
     *@description: 加载天地图底图
     *@author: qqjuanqq
     *@date: 2023-07-24
     *@version: V1.0.0
     */
    addTiandituBaseLayer() {
      window.EarthMap.layer.addTiandituLayer('天地图影像', {
        mapStyle: EarthView.TiandituMapsStyle.IMG_W,
        tk: globalConfig.map.tdtkey,
        maximumLevel: 18,
      });

      // 天地图注记图层配置
      const annotationLayerConfig = {
        mapStyle: EarthView.TiandituMapsStyle.CIA_W,
        tk: globalConfig.map.tdtkey,
        maximumLevel: 18,
      };
    },

    /*
     *@description:循环添加模型
     *@author: qqjuanqq
     *@date: 2023-07-24
     *@version: V1.0.0
     */
    addModelLayer() {
      getMapConfig('SCREEN_LOCATION_CONFIG').then(({ data }) => {
        let modeLayer = JSON.parse(data.value)
        console.log(modeLayer)
        let location = typeof modeLayer.location === 'string' ? modeLayer.location.split(',') : modeLayer.location
        switch (modeLayer.type) {
          case 'layer.photography':
            this.createModelLayer(modeLayer)
            break;
          case 'layer.geoserver':
            this.createGeoserverLayer(modeLayer)
            break;
          case 'layer.wmtsgeoserver':
            this.createWmtsGeoserverLayer(modeLayer)
            break;
          default:
            break;
        }
        // 处理施工区域数据
        if (modeLayer.shigongArea) {
          console.log('施工区域数据:', modeLayer.shigongArea)
          this.constructionAreaManager.drawConstructionAreasFromConfig(modeLayer.shigongArea)
        }

        // 检查是否存在保存的视角，如果存在则跳过flyto操作
        if (this.checkSavedViewExists()) {
          console.log('🔒 检测到存在保存的视角，跳过飞行到高精度地图的操作');
          return;
        }

        // 飞行到高精度地图附近
        window.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            Number(location[0]),
            Number(location[1]),
            300
          ),
        })
      })
    },

    /*
     *@description:添加模型图层
     *@author: qqjuanqq
     *@date: 2023-07-24
     *@version: V1.0.0
     */
    createModelLayer(config) {
      //三维模型数据
      window.EarthMap.model.remove3Dtiles(config.id);
      let tileSet = window.EarthMap.model.add3DTiles(
        config.url,
        config.id,
        (tmp_tileSet) => {
          window.EarthMap.model.adjuest3DtilesVerticalPosition(
            tmp_tileSet,
            config.height
          );
          let obj = {
            id: config.id,
            url: config.url,
            name: config.title,
            type: config.type,
            tileset: tmp_tileSet
          }
          window.current3dModels.push(obj);
        }
      );
      tileSet.id = config.id;
      tileSet.show = true;
    },

    createGeoserverLayer(item) {
      let imageryLayer = window.thirdPartyLayer.addLayer4WMS(item.id, {
        url: item.url,
        layers: item.layername,
        parameters: {
          service: 'WMS',
          format: 'image/png',
          version: '1.1.0',
          request: 'GetMap',
          transparent: true,
        },
      });

      let obj = {
        id: item.id,
        url: item.url,
        name: item.title,
        type: item.type,
        tileset: imageryLayer
      }
      window.current3dModels.push(obj);

    },

    createWmtsGeoserverLayer(item) {
      console.log(item)
      //切片地图
      let imageryLayer = window.thirdPartyLayer.addEGISServerWMTS(item.id, {
        url: item.url,
        layer: item.layername,
        minimumLevel: 1,
        maximumLevel: 22,
      });

      // 设置图层亮度 - 在图层创建后单独设置
      if (imageryLayer) {
        const brightness = item.brightness !== undefined ? item.brightness : 0.8;
        const saturation = item.saturation !== undefined ? item.saturation : 1.5;

        // 立即设置亮度和饱和度
        imageryLayer.brightness = brightness;
        imageryLayer.saturation = saturation;

        // 如果图层有readyPromise，等待加载完成后再次确保设置正确
        if (imageryLayer.readyPromise) {
          imageryLayer.readyPromise.then(() => {
            imageryLayer.brightness = brightness;
            imageryLayer.saturation = saturation;
            console.log(`🎨 WMTS图层参数设置 - 亮度: ${brightness}, 饱和度: ${saturation}`);
          }).catch(error => {
            console.warn('设置WMTS图层参数时出错:', error);
          });
        } else {
          console.log(`🎨 WMTS图层参数设置 - 亮度: ${brightness}, 饱和度: ${saturation}`);
        }
      }

      let obj = {
        id: item.id,
        url: item.url,
        name: item.title,
        type: item.type,
        tileset: imageryLayer,
        brightness: item.brightness || 0.7,
      }
      window.current3dModels.push(obj);
    },
    SetBiLie() {
      var lat
      var lon
      // 获取当前视图的中心点经纬度
      let viewer = window.viewer;
      var canvas = viewer.scene.canvas;
      var width = canvas.clientWidth;
      var height = canvas.clientHeight;
      var center = new Cesium.Cartesian2(width / 2.0, height / 2.0);

      // 将屏幕坐标转换为世界坐标
      var ray = viewer.camera.getPickRay(center);
      var worldCoordinates = viewer.scene.globe.pick(ray, viewer.scene);

      // 如果世界坐标不为undefined，则转换为地理坐标
      if (Cesium.defined(worldCoordinates)) {
        var cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(worldCoordinates);
        lat = Cesium.Math.toDegrees(cartographic.latitude);
        lon = Cesium.Math.toDegrees(cartographic.longitude);
      }
      // 获取视高
      // var height = viewer.camera.positionCartographic.height;


      // 获取海拔高度
      var ellipsoid = viewer.scene.globe.ellipsoid;
      var cart = viewer.camera.positionWC;
      var cartographic = ellipsoid.cartesianToCartographic(cart);
      var altitude = ellipsoid.cartographicToCartesian(cartographic).z;
      // console.log(cartographic, altitude, 888888888888888)
      // that.currentHeight = altitude;
      var newHeight = cartographic.height;


      // 假设你已经有了一个Cesium.Viewer实例叫做viewer

      // 获取相机
      var camera = viewer.scene.camera;

      // 获取俯仰角（Pitch），返回值是弧度
      var pitch = camera.pitch;

      // 获取方位角（Yaw），返回值是弧度
      var yaw = camera.heading;

      // 如果需要转换为角度，可以使用Cesium.Math.toDegrees函数
      var pitchDegrees = Cesium.Math.toDegrees(pitch);
      var yawDegrees = Cesium.Math.toDegrees(yaw);
      var viewHeight = viewer.camera.positionCartographic.height;
      // that.viewHeight = height;
      const ViewpointData = {
        longitude: lon,
        latitude: lat,
        currentHeight: newHeight,
        currentDirection: yawDegrees,
        currentPitch: pitchDegrees,
        viewHeight: viewHeight
      }
      // console.log(ViewpointData, 999999999999999)
      localStorage.setItem('ViewpointData', JSON.stringify(ViewpointData))

    },


    /*
     *@description: 在地图上加载遮罩效果
     *@author: luxiaolin
     *@date: 2024-10-21
     *@version: V1.0.0
     */
    addCoverPolygon(geojson) {
      let arr = [];
      geojson.features[0].geometry.coordinates[0][0].forEach((item) => {
        arr.push(item[0]);
        arr.push(item[1]);
      });
      let polygonEntity = new Cesium.Entity({
        polygon: {
          hierarchy: {
            // 设置遮罩范围
            positions: Cesium.Cartesian3.fromDegreesArray([
              60, 0, 60, 90, 160, 90, 160, 0,
            ]),
            // 挖空的部分
            holes: [{
              positions: Cesium.Cartesian3.fromDegreesArray(arr),
            },],
          },
          material: Cesium.Color.fromCssColorString('#0c4a98').withAlpha(0),
        },

        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(arr),
          width: 4,
          // material: new Cesium.Spriteline1MaterialProperty(2000, '/img/page-img/spriteline1.png')
          material: Cesium.Color.fromCssColorString('#19f1ff').withAlpha(0.7)
        },
      });
      window.viewer.entities.add(polygonEntity);
    },

    /*
     *@description: 飞到指定点的代码逻辑，可参考
     *@author: luxiaolin
     *@date: 2024-10-21
     *@version: V1.0.0
     */
    flyToDestination(option) {
      // 检查是否存在保存的视角，如果存在则跳过flyto操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过flyToDestination操作');
        return;
      }

      window.EarthView.lookAt(option);
    },

    //坐标转换,屏幕坐标转笛卡尔
    getCatesian3FromPX(px) {
      if (window.viewer && px) {
        let picks = window.viewer.scene.drillPick(px);
        let cartesian = null;
        let isOn3dtiles = false;
        let isOnTerrain = false;
        // drillPick
        for (let i in picks) {
          let pick = picks[i];

          if (
            (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature) ||
            (pick && pick.primitive instanceof Cesium.Cesium3DTileset) ||
            (pick && pick.primitive instanceof Cesium.Model)
          ) {
            //模型上拾取
            isOn3dtiles = true;
          }
          // 3dtilset
          if (isOn3dtiles) {
            window.viewer.scene.pick(px); // pick
            cartesian = window.viewer.scene.pickPosition(px);
            if (cartesian) {
              let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
              if (cartographic.height < 0) cartographic.height = 0;
              let lon = Cesium.Math.toDegrees(cartographic.longitude),
                lat = Cesium.Math.toDegrees(cartographic.latitude),
                height = cartographic.height;
              cartesian = this.transformWGS84ToCartesian({
                lng: lon,
                lat: lat,
                alt: height,
              });
            }
          }
        }
        // 地形
        let boolTerrain = window.viewer.sceneterrainProvider instanceof Cesium.EllipsoidTerrainProvider;
        // Terrain
        if (!isOn3dtiles && !boolTerrain) {
          var ray = window.viewer.scene.camera.getPickRay(px);
          if (!ray) return null;
          cartesian = window.viewer.scene.globe.pick(ray, window.viewer.scene);
          isOnTerrain = true;
        }
        // 地球
        if (!isOn3dtiles && !isOnTerrain && boolTerrain) {
          cartesian = this.viewer.scene.camera.pickEllipsoid(px, window.viewer.value.scene.globe.ellipsoid);
        }

        if (cartesian) {
          let position = this.transformCartesianToWGS84(cartesian);

          if (position.alt < 0) {
            cartesian = this.transformWGS84ToCartesian(position);
          }
          return cartesian;
        }
        return false;
      }
    },
    /***
     * 坐标转换 笛卡尔转84
     *
     * @param {Object} Cartesian3 三维位置坐标
     *
     * @return {Object} {lng,lat,alt} 地理坐标
     */
    transformCartesianToWGS84(cartesian) {
      if (window.viewer && cartesian) {
        let ellipsoid = Cesium.Ellipsoid.WGS84;
        let cartographic = ellipsoid.cartesianToCartographic(cartesian);

        return {
          lng: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          alt: cartographic.height,
        };
      }
    },
    /***
     * 坐标转换 84转笛卡尔
     *
     * @param {Object} {lng,lat,alt} 地理坐标
     *
     * @return {Object} Cartesian3 三维位置坐标
     */
    transformWGS84ToCartesian(position, alt) {
      if (window.viewer) {
        return position ?
          Cesium.Cartesian3.fromDegrees(position.lng || position.lng, position.lat, (position.alt = alt || position.alt), Cesium.Ellipsoid.WGS84) :
          Cesium.Cartesian3.ZERO;
      }
    },
    //坐标转换,屏幕坐标转84
    transformPXToWGS84(px) {
      return this.transformCartesianToWGS84(this.getCatesian3FromPX(px))
    },

    // 添加项目自定义点位即弹窗
    addPoints(arr) {
      console.log('addPoints---', arr);
      arr.forEach(item => {
        window.viewer.entities.removeById(item.id);

        const entity = window.viewer.entities.add({
          id: item.id,
          position: Cesium.Cartesian3.fromDegrees(item.lon, item.lan, 0),
          point: {
            pixelSize: 5,
            color: Cesium.Color.TRANSPARENT
          }
        })

        updatePosition = () => {
          const position = entity.position.getValue(window.viewer.clock.currentTime)
          if (position) {
            const cartesian2 = window.viewer.scene.cartesianToCanvasCoordinates(position)
            if (cartesian2 && entity.customHtml && entity.customHtml.element) {
              const canvas = window.viewer.scene.canvas
              const rect = canvas.getBoundingClientRect()
              entity.customHtml.element.style.left = `${cartesian2.x}px`
              entity.customHtml.element.style.top = `${cartesian2.y}px`
            }
          }
        }

        const div = document.createElement('div')
        div.className = 'custom-html'
        div.style.position = 'absolute'
        div.style.pointerEvents = 'auto'

        let PointComponent
        if (Point && typeof Point === 'object') {
          PointComponent = new Vue({
            ...Point,
            propsData: {
              pointData: item,
              router: this.$router,
              store: this.$store,
            }
          }).$mount()
          div.appendChild(PointComponent.$el)
        }

        div.addEventListener('click', (e) => {
          e.stopPropagation()
          if (PointComponent && PointComponent.$emit) {
            PointComponent.$emit('click', item)
          }
        })

        window.viewer.container.appendChild(div)

        entity.customHtml = {
          element: div,
          update: updatePosition
        }

        updatePosition()

        window.viewer.scene.postRender.addEventListener(updatePosition)
      })

      // 检查是否存在保存的视角，如果存在则跳过flyto操作
      if (!this.checkSavedViewExists()) {
        // 计算最合适的飞行点位和高度
        const flyToPosition = this.calculateOptimalFlyPosition(arr)
        window.viewer.camera.flyTo({
          destination: flyToPosition.destination,
          duration: flyToPosition.duration || 2.0
        })
      }
    },

    /**
     * 计算最合适的飞行点位和高度
     * @param {Array} points - 点位数组，每项包含 lon, lan 坐标
     * @returns {Object} 包含 destination 和 duration 的飞行参数
     */
    calculateOptimalFlyPosition(points) {
      if (!points || points.length === 0) {
        console.warn('点位数组为空，使用默认视角')
        return {
          destination: Cesium.Cartesian3.fromDegrees(114.1492223, 29.80303966, 1000),
          duration: 2.0
        }
      }

      // 单个点位的情况
      if (points.length === 1) {
        const point = points[0]
        return {
          destination: Cesium.Cartesian3.fromDegrees(
            Number(point.lon),
            Number(point.lan),
            800 // 单个点位使用较低高度
          ),
          duration: 2.0
        }
      }

      // 多个点位的情况 - 计算边界框
      let minLon = Number(points[0].lon)
      let maxLon = Number(points[0].lon)
      let minLat = Number(points[0].lan)
      let maxLat = Number(points[0].lan)

      // 找到所有点位的边界
      points.forEach(point => {
        const lon = Number(point.lon)
        const lat = Number(point.lan)

        if (lon < minLon) minLon = lon
        if (lon > maxLon) maxLon = lon
        if (lat < minLat) minLat = lat
        if (lat > maxLat) maxLat = lat
      })

      // 计算中心点
      const centerLon = (minLon + maxLon) / 2
      const centerLat = (minLat + maxLat) / 2

      // 计算点位分布的范围（度数差）
      const lonRange = maxLon - minLon
      const latRange = maxLat - minLat
      const maxRange = Math.max(lonRange, latRange)

      // 计算边界框的对角线距离（公里）
      const diagonalDistance = this.calculateDistanceBetweenPoints(
        minLon, minLat, maxLon, maxLat
      )

      // 根据分布范围和实际距离计算合适的飞行高度
      let optimalHeight
      // 因为图标高度，微调纬度向南偏移
      let latOffset = 0.0000001

      if (maxRange <= 0.0005) {
        // 点位非常集中（约50米范围内）
        optimalHeight = 300
      } else if (maxRange <= 0.001) {
        // 点位很集中（约100米范围内）
        optimalHeight = 600
      } else if (maxRange <= 0.003) {
        // 点位较为集中（约300米范围内）
        optimalHeight = 1000
      } else if (maxRange <= 0.005) {
        // 点位中等集中（约500米范围内）
        optimalHeight = 2000
      } else if (maxRange <= 0.01) {
        // 点位分布中等（约1公里范围内）
        optimalHeight = 5000
      } else if (maxRange <= 0.02) {
        // 点位分布较广（约2公里范围内）
        optimalHeight = 10000
      } else if (maxRange <= 0.05) {
        // 点位分布很广（约5公里范围内）
        optimalHeight = 25000
      } else if (maxRange <= 0.1) {
        // 点位分布极广（约10公里范围内）
        optimalHeight = 50000
      } else if (maxRange <= 0.5) {
        // 点位分布超广（约50公里范围内）
        optimalHeight = 100000
        latOffset = 0.3
      } else if (maxRange <= 1.0) {
        // 点位分布巨广（约100公里范围内）
        optimalHeight = 200000
        latOffset = 0.4
      } else if (maxRange <= 2.0) {
        // 点位分布极其广（约200公里范围内）
        optimalHeight = 400000
        latOffset = 0.5
      } else if (maxRange <= 5.0) {
        // 点位分布超级广（大于500公里）
        optimalHeight = Math.min(1500000, maxRange * 300000) // 最高不超过1500公里
        latOffset = 1
      } else {
        // 点位分布超超级广（大于500公里）
        optimalHeight = Math.min(2000000, maxRange * 400000) // 最高不超过2000公里
        latOffset = 1.5
      }

      // 根据实际距离进行微调，大幅提高系数
      if (diagonalDistance > 0) {
        // 基于对角线距离的高度系数调整，大幅提高倍率
        const distanceBasedHeight = diagonalDistance * 2000 // 每公里2000米高度（比之前提高3倍多）
        optimalHeight = Math.max(optimalHeight, distanceBasedHeight)
      }

      // 对于超大范围的特殊处理
      if (diagonalDistance > 100) {
        // 距离超过100公里时，使用更激进的高度计算
        optimalHeight = Math.max(optimalHeight, diagonalDistance * 3000)
      }

      if (diagonalDistance > 200) {
        // 距离超过200公里时，使用最激进的高度计算
        optimalHeight = Math.max(optimalHeight, diagonalDistance * 4000)
      }

      // 确保最小和最大高度限制，大幅提高上限
      optimalHeight = Math.max(500, Math.min(2000000, optimalHeight)) // 最高2000公里

      // 添加更大的缓冲区域，确保所有点位都在视野内
      optimalHeight *= 1.5 // 增加到50%缓冲区

      console.log(`点位分布分析（增强版）:`, {
        latOffset: latOffset,
        pointCount: points.length,
        bounds: { minLon, maxLon, minLat, maxLat },
        center: { lon: centerLon, lat: centerLat + latOffset },
        range: { lonRange, latRange, maxRange },
        diagonalDistance: `${diagonalDistance.toFixed(2)}km`,
        optimalHeight: `${(optimalHeight / 1000).toFixed(1)}km`,
        originalHeight: `${(optimalHeight / 1.5 / 1000).toFixed(1)}km`
      })

      return {
        destination: Cesium.Cartesian3.fromDegrees(centerLon, centerLat + latOffset, optimalHeight),
        duration: Math.max(3.0, Math.min(6.0, optimalHeight / 50000)) // 根据高度调整飞行时间，更长的动画时间
      }
    },

    /**
     * 计算两点之间的距离（公里）
     * @param {Number} lon1 经度1
     * @param {Number} lat1 纬度1
     * @param {Number} lon2 经度2
     * @param {Number} lat2 纬度2
     * @returns {Number} 距离（公里）
     */
    calculateDistanceBetweenPoints(lon1, lat1, lon2, lat2) {
      const R = 6371; // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },

    /**
     * 智能移除动态添加的地图内容，保留基础设施和高精度地图
     */
    removePoints() {
      try {
        console.log('开始清理地图动态内容...')

        // 1. 停止随机弹窗
        this.stopRandomPopup()

        // 2. 清理弹窗数据和状态
        this.imagePointsData = []

        // 3. 清理点击事件监听器
        this.clearClickEventHandlers()

        // 4. 智能清除 entities，保留基础设施
        this.removeDynamicEntities()

        // 5. 清除轨迹点
        this.clearTrajectoryPoints()

        // 6. 清除特定前缀的地图元素
        this.removeDynamicMapElements()

        // 7. 清理事件监听器
        this.cleanupEventListeners()

        // 7. 清理吊车圆心位置标签
        this.craneManager.clearCraneEntities()

        // 9. 移除吊车3D模型
        this.removeCraneModels()

        // 8. 清理自定义HTML元素
        const customHtmlElements = document.querySelectorAll('.custom-html')
        customHtmlElements.forEach(element => {
          element.remove()
        })

        // 9. 清理设备图标DOM元素
        const container = document.getElementById('base3dmap');
        if (container) {
          let removedDOMCount = 0;

          // 清理所有设备图标
          const deviceIcons = container.querySelectorAll('.device-icon');
          deviceIcons.forEach(icon => {
            if (icon.parentNode) {
              icon.parentNode.removeChild(icon);
              removedDOMCount++;
              console.log(`🧹 清理设备图标DOM: ${icon.id}`);
            }
          });

          // 清理雷达标签（吊车相关）
          const radarLabels = container.querySelectorAll('.radar-label');
          radarLabels.forEach(label => {
            if (label.parentNode) {
              label.parentNode.removeChild(label);
              removedDOMCount++;
              console.log(`🧹 清理雷达标签DOM: ${label.id}`);
            }
          });

          // 清理可能遗留的其他设备相关DOM
          const deviceRelatedElements = container.querySelectorAll('[id*="device-"], [id*="point-"], [class*="device-"], [class*="radar-"]');
          deviceRelatedElements.forEach(element => {
            if (element.parentNode && !element.closest('.cesium-viewer')) {
              element.parentNode.removeChild(element);
              removedDOMCount++;
              console.log(`🧹 清理设备相关DOM: ${element.id || element.className}`);
            }
          });

          // 强制清理所有可能的设备点位DOM（按ID模式匹配）
          const allPossibleSelectors = [
            '[id^="device-icon-"]',
            '[id^="device-label-"]',
            '[id^="point-label-"]',
            '[id^="point-alarm-label-"]',
            '[id^="radar-label"]',
            '.device-icon-container',
            '.radar-label-content'
          ];

          allPossibleSelectors.forEach(selector => {
            const elements = container.querySelectorAll(selector);
            elements.forEach(element => {
              if (element.parentNode && !element.closest('.cesium-viewer')) {
                element.parentNode.removeChild(element);
                removedDOMCount++;
                console.log(`🧹 强制清理DOM元素: ${element.id || element.className}`);
              }
            });
          });

          if (removedDOMCount > 0) {
            console.log(`✅ 场景切换清理DOM完成，共清理 ${removedDOMCount} 个DOM元素`);
          } else {
            console.log(`ℹ️ 场景切换时未发现需要清理的DOM元素`);
          }
        } else {
          console.warn('⚠️ 未找到base3dmap容器，无法清理DOM元素');
        }

        // 9. 清理全局数据
        if (typeof getClickPositions !== 'undefined') {
          getClickPositions = []
        }

        // 10. 清理弹窗引用
        this.bubbles = null

        // 11. 验证基础设施完整性
        this.verifyBaseInfrastructure()

        // 12. 清除吊车轮训查找数据
        if (this.craneManager) {
          this.craneManager.destroy()
          console.log('已取消吊车数据定时更新')
        }

        // 13. 清理循环事件
        this.clearTimers()

        this.base.constructionAntiInvasion.clear()
        this.base.highFormworkMonitoring.clear()

        console.log('地图动态内容清理完成，基础设施保持完整')

      } catch (error) {
        console.error('清理地图内容时发生错误:', error)
      }
    },

    /**
     * 清理定时器
     */
    clearTimers() {
      try {
        console.log('开始清理定时器...')

        // 清理图片点随机定时器
        if (this.imagePointsRandomTimer) {
          clearInterval(this.imagePointsRandomTimer)
          this.imagePointsRandomTimer = null
          console.log('已清除图片点随机定时器')
        }

        // 清理弹窗调度定时器
        if (this.popupScheduleTimer) {
          clearTimeout(this.popupScheduleTimer)
          this.popupScheduleTimer = null
          console.log('已清除弹窗调度定时器')
        }

        // 清理自动关闭定时器
        if (this.autoCloseTimer) {
          clearTimeout(this.autoCloseTimer)
          this.autoCloseTimer = null
          console.log('已清除自动关闭定时器')
        }

        // 清理地图视图日志防抖定时器
        if (this.mapViewLogTimer) {
          clearTimeout(this.mapViewLogTimer)
          this.mapViewLogTimer = null
          console.log('已清除地图视图日志防抖定时器')
        }

        // 清理弹窗定时器
        if (this.popupTimer) {
          clearInterval(this.popupTimer)
          this.popupTimer = null
          console.log('已清除弹窗定时器')
        }
        if (this.towerCraneDataTimer) {
          console.log('🧹 清理旧的吊车数据定时器');
          clearInterval(this.towerCraneDataTimer);
          this.towerCraneDataTimer = null;
        }

        console.log('✅ 定时器清理完成')
      } catch (error) {
        console.error('清理定时器时发生错误:', error)
      }
    },

    /**
     * 移除动态添加的entities，保留基础设施和永久施工区域
     */
    removeDynamicEntities() {
      const viewer = window.viewer
      if (!viewer || !viewer.entities) return

      // 需要保护的基础entities ID模式
      const protectedPatterns = [
        'baseLayer_',
        'terrain_',
        'infrastructure_',
        'tianditu_'
      ]

      // 需要保护的永久实体ID
      const permanentEntityIds = [
        'PERMANENT_CONSTRUCTION_AREA_BOUNDARY',
        'DEFAULT_CONSTRUCTION_AREA_BOUNDARY',
        'DEFAULT_CONSTRUCTION_AREA_FILL'
      ]

      // 需要保护的实体ID前缀
      const protectedPrefixes = [
        'CONSTRUCTION_AREA_BOUNDARY_', // 边界线和填充区域
        'CONSTRUCTION_AREA_LABEL_',    // 标签
      ]

      // 收集需要移除的entities
      const entitiesToRemove = []
      viewer.entities.values.forEach(entity => {
        let shouldRemove = true

        // 检查是否为永久实体（如施工区域边界线）
        if (entity._isPermanent || entity._isConstructionBoundary) {
          shouldRemove = false
          // console.log(`🔒 保护永久实体: ${entity.id}`)
        }

        // 检查是否在永久实体ID列表中
        if (entity.id && permanentEntityIds.includes(entity.id)) {
          shouldRemove = false
          console.log(`🔒 保护指定永久实体: ${entity.id}`)
        }

        // 检查是否匹配受保护的前缀
        if (entity.id && typeof entity.id === 'string') {
          for (const prefix of protectedPrefixes) {
            if (entity.id.startsWith(prefix)) {
              shouldRemove = false
              // console.log(`🔒 保护前缀匹配实体: ${entity.id}`)
              break
            }
          }
        }

        // 检查是否为受保护的基础设施
        if (entity.id && typeof entity.id === 'string') {
          for (const pattern of protectedPatterns) {
            if (entity.id.startsWith(pattern)) {
              shouldRemove = false
              // console.log(`🔒 保护基础设施: ${entity.id}`)
              break
            }
          }
        }

        // 如果需要移除，则标记
        if (shouldRemove) {
          entitiesToRemove.push(entity)
        }
      })

      // 批量移除动态entities
      entitiesToRemove.forEach(entity => {
        try {
          viewer.entities.remove(entity)
        } catch (error) {
          console.warn('移除entity时发生错误:', entity.id, error)
        }
      })

      console.log(`移除了 ${entitiesToRemove.length} 个动态entities，保留了基础设施和永久实体`)

      // 验证永久施工区域边界线是否仍然存在
      const constructionBoundary = viewer.entities.getById('PERMANENT_CONSTRUCTION_AREA_BOUNDARY')
      if (constructionBoundary) {
        console.log('✅ 永久施工区域边界线已保留')
      } else {
        console.warn('⚠️ 永久施工区域边界线丢失，正在恢复...')
      }
    },

    /**
     * 清理各种事件监听器
     */
    cleanupEventListeners() {
      try {
        // 清理 postRender 事件监听器
        const viewer = window.viewer
        if (viewer && viewer.scene && viewer.scene.postRender) {
          // 移除 updatePosition 监听器（如果存在）
          if (typeof updatePosition !== 'undefined') {
            viewer.scene.postRender.removeEventListener(updatePosition)
          }
        }

        // 清理鼠标事件监听器
        if (viewer && viewer.cesiumWidget && viewer.cesiumWidget.canvas) {
          const canvas = viewer.cesiumWidget.canvas
          // 移除可能的鼠标事件监听器
          canvas.onmousedown = null
          canvas.onmouseup = null
          canvas.onmousemove = null
          canvas.onclick = null
        }

        console.log('事件监听器清理完成')
      } catch (error) {
        console.warn('清理事件监听器时发生错误:', error)
      }
    },

    /**
     * 清除特定的动态地图元素（不包括永久施工区域边界线）
     */
    removeDynamicMapElements() {
      // 清除特定前缀的元素
      const dynamicPrefixes = [
        'craneModel_',           // 吊车模型
        'craneAreaLine_',        // 吊车作业区域线
        'verticalProtectionPolygon_',  // 防护多边形
        'verticalProtectionLine_',     // 防护线条
        'verticalProtectionLabel_',    // 防护标签
        'trajectoryPoint_',      // 轨迹点
        'devicePoint_',          // 设备点位
        'temporaryLine_',        // 临时线条
        'temporaryPolygon_',     // 临时多边形
        'temporaryLabel_',       // 临时标签
        'crane_',               // 吊车相关元素
        'verticalProtection',    // 防护相关元素
      ]

      console.log('🧹 开始清除动态地图元素，但保留永久施工区域边界线...')

      dynamicPrefixes.forEach(prefix => {
        try {
          this.removeMapElement(window.viewer, prefix)
        } catch (error) {
          console.warn(`清除元素前缀 ${prefix} 时发生错误:`, error)
        }
      })

      // 确保施工区域边界线仍然存在
      const hasConstructionBoundary = window.viewer.entities.values.some(entity =>
        entity._isConstructionBoundary ||
        entity.id && (
          entity.id.includes('CONSTRUCTION_AREA_BOUNDARY') ||
          entity.id.includes('CONSTRUCTION_AREA_FILL') ||
          entity.id === 'PERMANENT_CONSTRUCTION_AREA_BOUNDARY' ||
          entity.id === 'DEFAULT_CONSTRUCTION_AREA_BOUNDARY' ||
          entity.id === 'DEFAULT_CONSTRUCTION_AREA_FILL'
        )
      )

      if (hasConstructionBoundary) {
        console.log('✅ 施工区域边界线已保留')
      } else {
        console.warn('⚠️ 施工区域边界线可能已丢失，需要重新绘制')
      }
    },

    /**
     * 验证基础设施完整性
     */
    verifyBaseInfrastructure() {
      console.log('验证基础设施状态:')

      // 检查高精度地图模型
      if (window.current3dModels && window.current3dModels.length > 0) {
        console.log(`✓ 高精度地图模型保持完整: ${window.current3dModels.length} 个`)
        window.current3dModels.forEach(model => {
          console.log(`  - ${model.name} (${model.type})`)
        })
      } else {
        console.log('⚠ 未发现高精度地图模型')
      }

      // 检查天地图底图
      const imageryLayers = window.viewer.scene.imageryLayers
      if (imageryLayers && imageryLayers.length > 0) {
        console.log(`✓ 底图图层保持完整: ${imageryLayers.length} 个图层`)
      } else {
        console.log('⚠ 底图图层可能缺失')
      }

      // 检查地形
      const terrainProvider = window.viewer.terrainProvider
      if (terrainProvider) {
        console.log('✓ 地形数据保持完整')
      } else {
        console.log('⚠ 地形数据可能缺失')
      }
    },

    // 添加地图元素
    addMapElement(viewer, options) {
      const {
        type, positions,
        position, text, image,
        radius, color, width, dashPattern, dashLength,
        url, scale, imageSize, opacity
      } = options

      const entityId = options.id
      const oldEntity = window.viewer.entities.getById(entityId)
      if (oldEntity) {
        window.viewer.entities.remove(oldEntity)
      }

      if (type === 'point') {
        // 添加点(图标)
        return viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(...position),
          billboard: {
            image: image,
            width: 32,
            height: 32,
          }
        })
      }

      if (type === 'image') {
        // 添加图片图标/广告牌
        return viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(...position),
          billboard: {
            image: image,
            width: imageSize?.width || 64,
            height: imageSize?.height || 64,
            scale: scale || 1.0,
            color: Cesium.Color.WHITE.withAlpha(opacity || 1.0),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM
          }
        })
      }

      if (type === 'groundOverlay') {
        // 添加地面覆盖图片
        // positions格式: [west, south, east, north] 或 [[west, south], [east, north]]
        let west, south, east, north;

        if (positions.length === 4) {
          // 格式: [west, south, east, north]
          west = positions[0];
          south = positions[1];
          east = positions[2];
          north = positions[3];
        } else if (positions.length === 2 && Array.isArray(positions[0])) {
          // 格式: [[west, south], [east, north]]
          west = positions[0][0];
          south = positions[0][1];
          east = positions[1][0];
          north = positions[1][1];
        } else {
          console.error('groundOverlay positions格式错误，应为[west,south,east,north]或[[west,south],[east,north]]');
          return;
        }

        // 确保坐标正确性
        if (west >= east) {
          console.error('west必须小于east');
          return;
        }
        if (south >= north) {
          console.error('south必须小于north');
          return;
        }

        const rectangle = Cesium.Rectangle.fromDegrees(west, south, east, north);
        return viewer.scene.imageryLayers.add(
          new Cesium.ImageryLayer(
            new Cesium.SingleTileImageryProvider({
              url: image,
              rectangle: rectangle
            }),
            {
              alpha: opacity || 1.0
            }
          )
        )
      }

      if (type === 'polygonWithImage') {
        // 在多边形区域内贴图
        return viewer.entities.add({
          polygon: {
            hierarchy: Cesium.Cartesian3.fromDegreesArray(positions.flat()),
            material: new Cesium.ImageMaterialProperty({
              image: image,
              transparent: true
            }),
            outline: false,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })
      }

      if (type === 'line') {
        console.log('🔧 addMapElement - line类型处理:', {
          positions: positions,
          flatPositions: positions.flat ? positions.flat() : positions,
          width: width,
          color: color,
          id: options.id
        })

        // 禁用抗锯齿
        if (!window.viewer.scene.postProcessStages.fxaa.enabled) {
          window.viewer.scene.postProcessStages.fxaa.enabled = false;
        }

        // 创建基础材质
        const material = dashPattern
          ? new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString(color),
            dashLength: dashLength || 6,
            dashPattern: 255
          })
          : new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString(color));

        const entity = viewer.entities.add({
          id: options.id,
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray(positions.flat ? positions.flat() : positions),
            width: width || 1,
            material: material,
            clampToGround: false,
            classificationType: Cesium.ClassificationType.NONE,
            shadows: Cesium.ShadowMode.DISABLED,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 5000000.0),
            zIndex: 2,
            show: true,
            debugShowBoundingVolume: false
          }
        })

        // 设置线条渲染属性
        if (entity.polyline && entity.polyline.appearance) {
          entity.polyline.appearance.renderState = {
            lineWidth: Math.min(1.0, viewer.scene.maximumAliasedLineWidth || 1.0),
            blending: Cesium.BlendingState.DISABLED,
            depthTest: { enabled: true },
            depthMask: true
          };
        }

        if (options.animate) {
          const startTime = Cesium.JulianDate.now();
          const endTime = Cesium.JulianDate.addSeconds(startTime, 3600, new Cesium.JulianDate());

          entity.polyline.material = new Cesium.TimeIntervalCollectionProperty();

          const dynamicMaterial = new Cesium.PolylineMaterialProperty({
            color: Cesium.Color.fromCssColorString(color)
          });

          entity.polyline.material.intervals.addInterval(
            new Cesium.TimeInterval({
              start: startTime,
              stop: endTime,
              data: dynamicMaterial
            })
          );

          viewer.clock.startTime = startTime.clone();
          viewer.clock.stopTime = endTime.clone();
          viewer.clock.currentTime = startTime.clone();
          viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
          viewer.clock.multiplier = 1;
          viewer.clock.shouldAnimate = true;
        }

        console.log('✅ addMapElement - line实体创建成功:', entity.id)
        return entity
      }

      if (type === 'circle') {
        // 添加圆
        return viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(...position),
          ellipse: {
            semiMajorAxis: radius,
            semiMinorAxis: radius,
            material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
            outline: false,
            outlineColor: color || Cesium.Color.BLUE,
          }
        })
      }

      if (type === 'label') {
        // 添加文字
        return viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(...position),
          label: {
            text: text,
            font: '18px sans-serif',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -30)
          }
        })
      }

      if (type === 'polygon') {
        // 遮罩
        console.log('🔧 addMapElement - polygon类型处理:', {
          positions: positions,
          flatPositions: positions.flat ? positions.flat() : positions,
          color: color,
          id: options.id
        })

        const entity = viewer.entities.add({
          id: options.id, // 确保设置ID
          polygon: {
            hierarchy: Cesium.Cartesian3.fromDegreesArray(positions.flat ? positions.flat() : positions),
            material: Cesium.Color.fromCssColorString(color).withAlpha(0.25),
            outline: true,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })

        console.log('✅ addMapElement - polygon实体创建成功:', entity.id)
        return entity
      }

      if (type === 'glbModel') {
        // 加载模型
        const pos = Cesium.Cartesian3.fromDegrees(...position)
        // 原有的glb模型加载逻辑
        return Cesium.Model.fromGltfAsync({
          url: url,
          modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(pos),
          scale: scale || 1.0,
        }).then(model => {
          model.id = options.id || 'craneModel'
          viewer.scene.primitives.add(model)
          console.log('glb模型加载完成:', model.id)

          // 保存引用以便后续删除
          this.dynamicEntities[options.id] = {
            type: 'model',
            entity: model
          }

          return model
        }).catch(error => {
          console.error('glb模型加载失败:', error)
          return null
        })
      }
    },

    // 移除地图元素
    removeMapElement(viewer, elementId) {
      if (!viewer || !elementId) {
        console.warn('removeMapElement: viewer或elementId参数无效')
        return false
      }

      try {
        let removedCount = 0

        // 检查是否为前缀匹配（支持批量删除）
        const isPrefix = elementId.includes('*') || elementId.endsWith('_')
        const searchPattern = isPrefix ? elementId.replace('*', '') : elementId

        if (isPrefix) {
          // 批量删除模式
          // 1. 从entities中批量移除
          const entitiesToRemove = []
          viewer.entities.values.forEach(entity => {
            if (entity.id && entity.id.startsWith(searchPattern)) {
              // 检查是否为永久实体，永久实体不能被删除
              if (!entity._isPermanent && !entity._isConstructionBoundary &&
                entity.id !== 'PERMANENT_CONSTRUCTION_AREA_BOUNDARY' &&
                !entity.id.includes('CONSTRUCTION_AREA_BOUNDARY') &&
                !entity.id.includes('CONSTRUCTION_AREA_FILL') &&
                !entity.id.includes('CONSTRUCTION_AREA_LABEL')) {
                entitiesToRemove.push(entity)
              } else {
                console.warn(`⚠️ 跳过删除永久实体: ${entity.id}`)
              }
            }
          })
          entitiesToRemove.forEach(entity => {
            viewer.entities.remove(entity)
            removedCount++
            console.log(`批量移除entities元素: ${entity.id}`)
          })

          // 2. 从primitives中批量移除
          const primitivesToRemove = []
          const primitives = viewer.scene.primitives._primitives
          for (let i = 0; i < primitives.length; i++) {
            const primitive = primitives[i]
            if (primitive.id && primitive.id.startsWith(searchPattern)) {
              primitivesToRemove.push(primitive)
            }
          }
          primitivesToRemove.forEach(primitive => {
            viewer.scene.primitives.remove(primitive)
            removedCount++
            console.log(`批量移除primitives元素: ${primitive.id}`)
          })

          // 3. 从imageryLayers中批量移除
          const layersToRemove = []
          const imageryLayers = viewer.scene.imageryLayers._layers
          for (let i = 0; i < imageryLayers.length; i++) {
            const layer = imageryLayers[i]
            if ((layer.id && layer.id.startsWith(searchPattern)) ||
              (layer._imageryProvider.id && layer._imageryProvider.id.startsWith(searchPattern))) {
              layersToRemove.push(layer)
            }
          }
          layersToRemove.forEach(layer => {
            viewer.scene.imageryLayers.remove(layer)
            removedCount++
            console.log(`批量移除imageryLayers元素: ${layer.id || layer._imageryProvider.id}`)
          })

          // 4. 清理相关的HTML标签（新增功能）
          const container = document.getElementById('base3dmap');
          if (container) {
            let removedLabelCount = 0;

            // 清理设备图标
            const deviceIcons = container.querySelectorAll(`[id*="device-icon-"][id*="${searchPattern}"]`);
            deviceIcons.forEach(icon => {
              if (icon.parentNode) {
                icon.parentNode.removeChild(icon);
                removedLabelCount++;
                console.log(`🧹 批量移除设备图标: ${icon.id}`);
              }
            });

            // 清理设备标签
            const deviceLabels = container.querySelectorAll(`[id*="device-label-"][id*="${searchPattern}"]`);
            deviceLabels.forEach(label => {
              if (label.parentNode) {
                label.parentNode.removeChild(label);
                removedLabelCount++;
                console.log(`🧹 批量移除设备标签: ${label.id}`);
              }
            });

            // 清理点位标签（兼容旧版本）
            const pointLabels = container.querySelectorAll(`[id*="point-label-"][id*="${searchPattern}"]`);
            pointLabels.forEach(label => {
              if (label.parentNode) {
                label.parentNode.removeChild(label);
                removedLabelCount++;
                console.log(`🧹 批量移除点位标签: ${label.id}`);
              }
            });

            // 清理告警标签（兼容旧版本）
            const alarmLabels = container.querySelectorAll(`[id*="point-alarm-label-"][id*="${searchPattern}"]`);
            alarmLabels.forEach(label => {
              if (label.parentNode) {
                label.parentNode.removeChild(label);
                removedLabelCount++;
                console.log(`🧹 批量移除告警标签: ${label.id}`);
              }
            });

            // 如果搜索模式是特定前缀，进行更精确的清理
            if (searchPattern.includes('videoMonitoring_') ||
              searchPattern.includes('personImagePoint_') ||
              searchPattern.endsWith('_')) {
              // 清理所有包含该前缀的HTML标签
              const allMatchingLabels = container.querySelectorAll(`[id^="${searchPattern}"], [id*="${searchPattern}"]`);
              allMatchingLabels.forEach(label => {
                if ((label.classList.contains('radar-label') ||
                  label.classList.contains('device-icon') ||
                  label.id.includes('device-icon-') ||
                  label.id.includes('device-label-')) && label.parentNode) {
                  label.parentNode.removeChild(label);
                  removedLabelCount++;
                  console.log(`🧹 批量移除匹配标签: ${label.id}`);
                }
              });
            }

            if (removedLabelCount > 0) {
              console.log(`批量移除HTML标签完成，共移除 ${removedLabelCount} 个标签`)
              removedCount += removedLabelCount;
            }
          }

          if (removedCount > 0) {
            console.log(`批量移除完成，共移除 ${removedCount} 个元素`)
            return true
          } else {
            console.warn(`未找到匹配前缀的地图元素: ${searchPattern}`)
            return false
          }
        } else {
          // 单个删除模式（原有逻辑）
          // 1. 尝试从entities中移除（处理大部分元素类型）
          const entity = viewer.entities.getById(elementId)
          if (entity) {
            // 检查是否为永久实体（如施工区域边界线）
            if (entity._isPermanent || entity._isConstructionBoundary ||
              elementId === 'PERMANENT_CONSTRUCTION_AREA_BOUNDARY' ||
              elementId.includes('CONSTRUCTION_AREA_BOUNDARY') ||
              elementId.includes('CONSTRUCTION_AREA_FILL')) {
              console.warn(`⚠️ 拒绝删除永久实体: ${elementId} - 这是施工区域相关实体，无法删除`)
              return false
            }

            viewer.entities.remove(entity)
            console.log(`成功从entities中移除元素: ${elementId}`)

            // 清理相关的HTML标签
            const container = document.getElementById('base3dmap');
            if (container) {
              let removedLabelCount = 0;

              // 清理对应的设备图标
              const deviceIcon = container.querySelector(`#device-icon-${elementId}`);
              if (deviceIcon && deviceIcon.parentNode) {
                deviceIcon.parentNode.removeChild(deviceIcon);
                removedLabelCount++;
                console.log(`🧹 移除对应设备图标: ${deviceIcon.id}`);
              }

              // 清理对应的设备标签
              const deviceLabel = container.querySelector(`#device-label-${elementId}`);
              if (deviceLabel && deviceLabel.parentNode) {
                deviceLabel.parentNode.removeChild(deviceLabel);
                removedLabelCount++;
                console.log(`🧹 移除对应设备标签: ${deviceLabel.id}`);
              }

              // 清理对应的点位标签（兼容旧版本）
              const pointLabel = container.querySelector(`#point-label-${elementId}`);
              if (pointLabel && pointLabel.parentNode) {
                pointLabel.parentNode.removeChild(pointLabel);
                removedLabelCount++;
                console.log(`🧹 移除对应点位标签: ${pointLabel.id}`);
              }

              // 清理对应的告警标签（兼容旧版本）
              const alarmLabel = container.querySelector(`#point-alarm-label-${elementId}`);
              if (alarmLabel && alarmLabel.parentNode) {
                alarmLabel.parentNode.removeChild(alarmLabel);
                removedLabelCount++;
                console.log(`🧹 移除对应告警标签: ${alarmLabel.id}`);
              }

              if (removedLabelCount > 0) {
                console.log(`移除HTML标签完成，共移除 ${removedLabelCount} 个标签`)
              }
            }

            return true
          }

          // 2. 尝试从primitives中移除（处理glbModel等3D模型）
          const primitives = viewer.scene.primitives._primitives
          for (let i = 0; i < primitives.length; i++) {
            const primitive = primitives[i]
            if (primitive.id === elementId) {
              viewer.scene.primitives.remove(primitive)
              console.log(`成功从primitives中移除元素: ${elementId}`)
              return true
            }
          }

          // 3. 尝试从imageryLayers中移除（处理groundOverlay类型）
          const imageryLayers = viewer.scene.imageryLayers._layers
          for (let i = 0; i < imageryLayers.length; i++) {
            const layer = imageryLayers[i]
            if (layer.id === elementId || layer._imageryProvider.id === elementId) {
              viewer.scene.imageryLayers.remove(layer)
              console.log(`成功从imageryLayers中移除元素: ${elementId}`)
              return true
            }
          }

          // 4. 如果都没找到，记录警告
          console.warn(`未找到要移除的地图元素: ${elementId}`)
          return false
        }

      } catch (error) {
        console.error(`移除地图元素时发生错误: ${elementId}`, error)
        return false
      }
    },

    /**
     * 添加轨迹点标记（canvas合成图片，彻底防闪烁）
     */
    async addTrajectoryPoints(trajectoryPoints = [], options = {}) {
      const {
        showSequence = true,      // 是否显示序号
        autoRemove = true,        // 是否自动清除之前的轨迹点
        idPrefix = 'trajectoryPoint' // ID前缀
      } = options

      try {
        // 自动清除之前的轨迹点
        if (autoRemove) {
          this.removeMapElement(window.viewer, `${idPrefix}_`)
        }

        if (!Array.isArray(trajectoryPoints) || trajectoryPoints.length === 0) {
          console.warn('轨迹点数据为空或格式不正确')
          return
        }

        for (let index = 0; index < trajectoryPoints.length; index++) {
          const point = trajectoryPoints[index]
          const {
            id,
            lng,
            lat,
            time,
            status = 'normal'
          } = point

          if (!lng || !lat || !time) {
            console.warn(`轨迹点数据不完整:`, point)
            continue
          }

          // 背景图片
          let backgroundImage
          if (status === 'alarm' || status === 'warning') {
            backgroundImage = require('../../assets/images/map/trajectory_alarm.png')
          } else {
            backgroundImage = require('../../assets/images/map/trajectory.png')
          }

          // 序号
          const sequenceNumber = showSequence ? (index + 1) : ''
          // 右侧时间
          const displayTime = time

          // 合成图片（序号+六芒星+时间）
          const imgUrl = await this.createTrajectoryImageWithTime(sequenceNumber, displayTime, backgroundImage)

          window.viewer.entities.add({
            id: `${idPrefix}_background_${id || index}`,
            name: `轨迹点${sequenceNumber}_背景`,
            position: Cesium.Cartesian3.fromDegrees(lng, lat, 2),
            billboard: {
              image: imgUrl,
              scale: 1.0,
              sizeInMeters: false,
              horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
              verticalOrigin: Cesium.VerticalOrigin.LEFT,
              pixelOffset: new Cesium.Cartesian2(-15, -20),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
              heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
              eyeOffset: new Cesium.Cartesian3(0, 0, 30),
              scaleByDistance: undefined,
              translucencyByDistance: undefined,
              pixelOffsetScaleByDistance: undefined
            }
          })
        }

        // 添加连接轨迹点的蓝色线段
        if (trajectoryPoints.length > 1) {
          const linePositions = trajectoryPoints.map(point =>
            Cesium.Cartesian3.fromDegrees(point.lng, point.lat, 1)
          )

          window.viewer.entities.add({
            id: `${idPrefix}_line`,
            name: '轨迹连接线',
            polyline: {
              positions: linePositions,
              width: 3,
              material: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.fromCssColorString('#4FC2FF'),
                dashLength: 16.0
              }),
              clampToGround: true,
              followSurface: true
            }
          })
        }

        console.log(`成功添加 ${trajectoryPoints.length} 个轨迹点和连接线`)

      } catch (error) {
        console.error('添加轨迹点时发生错误:', error)
      }
    },

    /**
     * canvas合成六芒星+序号+时间
     */
    createTrajectoryImageWithTime(num, time, bgImgUrl) {
      return new Promise(resolve => {
        const img = new window.Image();
        img.src = bgImgUrl;
        img.onload = () => {
          const width = img.width;
          const height = img.height;
          const textPadding = 0; // 可根据需要微调
          const ctxFont = '14px PingFang SC';

          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');

          // 1. 绘制整个背景图（六芒星+蓝色条）
          ctx.drawImage(img, 0, 0, width, height);

          // 2. 序号，居中在六芒星内
          ctx.save();
          ctx.font = 'bold 20px PingFang SC';
          ctx.fillStyle = '#fff';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.shadowColor = 'rgba(0,0,0,0.6)';
          ctx.shadowBlur = 4;
          // 假设六芒星在图片左侧，宽度为starWidth
          const starWidth = 55; // 根据你的图片实际六芒星宽度调整
          ctx.fillText(num, starWidth / 3.1, height / 2.2);
          ctx.restore();

          // 3. 时间，绘制在蓝色条上，位置可微调
          ctx.save();
          ctx.font = ctxFont;
          ctx.fillStyle = '#fff';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'middle';
          ctx.shadowColor = 'rgba(0,0,0,0.4)';
          ctx.shadowBlur = 2;
          // 时间文本起点，starWidth右侧加一点padding
          ctx.fillText(time, starWidth - 20, height / 2.2);
          ctx.restore();
          resolve(canvas.toDataURL());
        };
        img.onerror = () => {
          resolve(bgImgUrl);
        };
      });
    },

    /**
     * 清除所有轨迹点
     * @param {string} idPrefix - ID前缀，默认为 'trajectoryPoint'
     */
    clearTrajectoryPoints(idPrefix = 'trajectoryPoint') {
      this.removeMapElement(window.viewer, `${idPrefix}_`)
      console.log('已清除所有轨迹点')
    },

    /**
     * 计算多点位平行显示的位置偏移
     * @param {number} count - 点位数量
     * @param {Object} options - 配置选项
     * @returns {Array} 位置偏移数组
     */
    calculateParallelPositions(count, options = {}) {
      const positions = [];
      const {
        baseOffset = 0.0001, // 基础偏移量（约10米）
        heightOffset = 5, // 高度偏移量（米）
        pattern = 'auto' // 分布模式：'auto', 'linear', 'circle', 'grid'
      } = options;

      if (count === 1) {
        positions.push({ x: 0, y: 0, z: 0 });
        return positions;
      }

      // 获取当前缩放级别并计算动态偏移量
      const currentZoom = cameraViewManager.getCurrentZoomLevel();
      let finalOffset = baseOffset;

      // 如果启用了动态偏移量，则根据缩放级别调整
      if (options.enableDynamicOffset !== false) {
        finalOffset = this.calculateDynamicOffset(baseOffset, currentZoom);
      }

      // 根据点位数量自动选择最佳分布模式
      let distributionPattern = pattern;
      if (pattern === 'auto') {
        if (count === 2) distributionPattern = 'linear';
        else if (count <= 4) distributionPattern = 'grid';
        else distributionPattern = 'circle';
      }

      switch (distributionPattern) {
        case 'linear':
          // 线性分布：适用于2个点位
          for (let i = 0; i < count; i++) {
            const x = (i - (count - 1) / 2) * finalOffset;
            positions.push({ x, y: 0, z: i * heightOffset });
          }
          break;

        case 'grid':
          // 网格分布：适用于3-4个点位
          const gridSize = Math.ceil(Math.sqrt(count));
          for (let i = 0; i < count; i++) {
            const row = Math.floor(i / gridSize);
            const col = i % gridSize;
            const x = (col - (gridSize - 1) / 2) * finalOffset;
            const y = (row - (gridSize - 1) / 2) * finalOffset;
            positions.push({ x, y, z: i * heightOffset });
          }
          break;

        case 'circle':
          // 圆形分布：适用于多个点位
          const radius = finalOffset * Math.max(1, count / 4);
          for (let i = 0; i < count; i++) {
            const angle = (i * 2 * Math.PI) / count;
            const x = radius * Math.cos(angle);
            const y = radius * Math.sin(angle);
            positions.push({ x, y, z: i * heightOffset });
          }
          break;

        default:
          // 默认使用圆形分布
          const defaultRadius = finalOffset * Math.max(1, count / 4);
          for (let i = 0; i < count; i++) {
            const angle = (i * 2 * Math.PI) / count;
            const x = defaultRadius * Math.cos(angle);
            const y = defaultRadius * Math.sin(angle);
            positions.push({ x, y, z: i * heightOffset });
          }
      }

      return positions;
    },

    /**
     * 添加图片点位，支持名称和点击弹窗（包含随机弹窗功能）
     * @param {Array} points - 点位数组
     * @param {Function} onClick - 点击回调（可选，参数为点位data）
     */
    async addImagePoints(points, onClick) {
      return await this.devicePointManager.addImagePoints(points, onClick);
    },
    loadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new window.Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
      });
    },

    /**
     * 启动随机弹窗
     */
    startRandomPopup() {
      // 如果已经启动了，不要重复启动
      if (this.popupTimer) {
        console.log('随机弹窗已经在运行中')
        return
      }

      // 如果没有点位数据，不启动
      if (!this.imagePointsData || this.imagePointsData.length === 0) {
        console.log('没有图片点位数据')
        return
      }

      const showNextPopup = () => {
        // 如果已经有弹窗在显示，先关闭它
        if (this.bubbles) {
          this.bubbles.windowClose()
          this.bubbles = null
        }

        // 清除可能存在的自动关闭定时器
        if (this.autoCloseTimer) {
          clearTimeout(this.autoCloseTimer)
          this.autoCloseTimer = null
        }

        // 随机选择一个点位
        const randomIndex = Math.floor(Math.random() * this.imagePointsData.length)
        const selectedData = this.imagePointsData[randomIndex]

        // 创建弹窗
        this.currentPopupData = selectedData
        const bubbleInstance = new Bubble({
          viewer: window.viewer,
          data: {
            ...selectedData.data,
            position: [Number(selectedData.data.equipmentLng), Number(selectedData.data.equipmentLat)]
          },
          type: 0,
          component: 'CustomPopup'
        })

        // 保存弹窗实例
        this.bubbles = bubbleInstance

        // 设置弹窗关闭事件
        if (this.bubbles && this.bubbles.vmInstance) {
          this.bubbles.vmInstance.$on('close', () => {
            // 执行关闭
            this.bubbles.windowClose()
            this.bubbles = null
            this.currentPopupData = null

            // 清除自动关闭定时器
            if (this.autoCloseTimer) {
              clearTimeout(this.autoCloseTimer)
              this.autoCloseTimer = null
            }
          })
        }

        // 8秒后自动关闭
        this.autoCloseTimer = setTimeout(() => {
          if (this.bubbles) {
            this.bubbles.windowClose()
            this.bubbles = null
            this.currentPopupData = null
          }
          this.autoCloseTimer = null
        }, 8000)
      }

      // 立即显示第一个弹窗
      showNextPopup()

      // 设置定时器，每8.2秒显示下一个弹窗
      this.popupTimer = setInterval(showNextPopup, 8200)
    },

    /**
     * 停止随机弹窗
     */
    stopRandomPopup() {
      try {
        console.log('🛑 停止随机弹窗系统...')

        // 立即设置暂停状态和用户关闭标志，阻止任何新的弹窗创建
        this.isRandomPopupPaused = true
        this._userClosedPopup = true

        // 清除循环定时器
        if (this.popupTimer) {
          clearInterval(this.popupTimer)
          this.popupTimer = null
          console.log('已清除popupTimer')
        }

        // 清除图片点随机定时器
        if (this.imagePointsRandomTimer) {
          clearInterval(this.imagePointsRandomTimer)
          this.imagePointsRandomTimer = null
          console.log('已清除imagePointsRandomTimer')
        }

        // 清除调度定时器
        if (this.popupScheduleTimer) {
          clearTimeout(this.popupScheduleTimer)
          this.popupScheduleTimer = null
          console.log('已清除popupScheduleTimer')
        }

        // 清除自动关闭定时器
        if (this.autoCloseTimer) {
          clearTimeout(this.autoCloseTimer)
          this.autoCloseTimer = null
          console.log('已清除autoCloseTimer')
        }

        // 设置创建锁，阻止新的弹窗创建
        this.isCreatingPopup = true

        // 重置数据状态
        this.currentPopupData = null
        this.nextPopupData = null

        // 关闭当前弹窗
        if (this.bubbles) {
          try {
            // 暂时移除关闭监听器，避免触发新的弹窗调度
            const bubbleInstance = this.bubbles
            if (bubbleInstance.vmInstance && bubbleInstance.vmInstance.$off) {
              bubbleInstance.vmInstance.$off('close')
            }

            this.bubbles.windowClose()
            this.bubbles = null
            console.log('已关闭当前弹窗')
          } catch (error) {
            console.warn('⚠️ 关闭弹窗时发生错误:', error)
            this.bubbles = null
          }
        }

        // 延迟一段时间后只重置创建锁
        setTimeout(() => {
          this.isCreatingPopup = false
          console.log('✅ 随机弹窗系统已完全停止')
        }, 1000)

        console.log('🛑 随机弹窗系统停止指令已发出')

      } catch (error) {
        console.error('❌ 停止随机弹窗系统时发生错误:', error)
        // 强制重置所有状态
        this.popupTimer = null
        this.imagePointsRandomTimer = null
        this.popupScheduleTimer = null
        this.autoCloseTimer = null
        this.bubbles = null
        this.isRandomPopupPaused = false
        this.currentPopupData = null
        this.nextPopupData = null
        this.isCreatingPopup = false
      }
    },

    // 获取设备类型对应的图标
    async getEquipmentIcon(typeName, title, status, useCanvas = true) {
      const img = {
        gzm: {
          '高支模监测': require('../../assets/images/map/icon/21.png'),
          '转体桥': require('../../assets/images/map/icon/17.png'),
          '深基坑': require('../../assets/images/map/icon/1.png'),
          '营业线': require('../../assets/images/map/icon/18.png'),
          baseBg: require('../../assets/images/map/point_gzm.png'),
        },
        td: {
          '塔吊': require('../../assets/images/map/icon/20.png'),
          '吊车': require('../../assets/images/map/icon/16.png'),
          '司机行为识别': require('../../assets/images/map/icon/10.png'),
          '北斗定位': require('../../assets/images/map/icon/15.png'),
          '机械': require('../../assets/images/map/icon/9.png'),
          '通用设备': require('../../assets/images/map/icon/8.png'),
          '电表': require('../../assets/images/map/icon/7.png'),
          '断路器': require('../../assets/images/map/icon/6.png'),
          baseBg: require('../../assets/images/map/point_td.png'),
        },
        sczd: {
          '现场广播': require('../../assets/images/map/icon/5.png'),
          '手持终端': require('../../assets/images/map/icon/14.png'),
          '智能安全帽': require('../../assets/images/map/icon/2.png'),
          baseBg: require('../../assets/images/map/point_sczd.png'),
        },
        dqhz: {
          '电气火灾': require('../../assets/images/map/icon/22.png'),
          '无线烟感': require('../../assets/images/map/icon/4.png'),
          baseBg: require('../../assets/images/map/point_dqhz.png'),
        },
        qxz: {
          '气象站': require('../../assets/images/map/icon/12.png'),
          '温湿度': require('../../assets/images/map/icon/3.png'),
          '混凝土温控': require('../../assets/images/map/icon/13.png'),
          baseBg: require('../../assets/images/map/point_qxz.png'),
        },
        ai: {
          'AI识别': require('../../assets/images/map/icon/19.png'),
          '视频监控': require('../../assets/images/map/icon/11.png'),
          baseBg: require('../../assets/images/map/point_ai.png'),
        },
        // 用电安全设备专用分组
        useElectric: {
          '电表': require('../../assets/images/map/icon/7.png'),
          '断路器': require('../../assets/images/map/icon/6.png'),
          '电气火灾': require('../../assets/images/map/icon/22.png'),
          baseBg: require('../../assets/images/map/point_dqhz.png'),
        },
        gj: require('../../assets/images/map/point_gj.png'),
        lx: require('../../assets/images/map/point_lx.png')
      };

      // 1. 搜索typeName在哪个一级对象
      let foundGroup = null;
      let iconUrl = null;
      let baseBg = null;

      // 优先在用电安全设备分组中查找
      if (img.useElectric[typeName]) {
        foundGroup = img.useElectric;
        iconUrl = img.useElectric[typeName];
        baseBg = img.useElectric.baseBg;
      } else {
        // 在其他分组中查找
        for (const key of Object.keys(img)) {
          const group = img[key];
          if (typeof group === 'object' && group[typeName]) {
            foundGroup = group;
            iconUrl = group[typeName];
            baseBg = group.baseBg;
            break;
          }
        }
      }

      // 2. 没找到则用td组的通用设备
      if (!iconUrl) {
        foundGroup = img.td;
        iconUrl = img.td['通用设备'];
        baseBg = img.td.baseBg;
      }

      // 3. 状态优先：status==1用gj（告警），status==0用lx（离线），否则用typeName图标
      if (status === 1) {
        baseBg = img.gj;
      } else if (status === 0) {
        baseBg = img.lx;
      }

      // 4. 如果不使用canvas，直接返回对应的图标或背景图
      if (!useCanvas) {
        // 根据状态返回对应的图标和背景
        let finalBaseBg, finalIconImg;

        if (status === 1) {
          finalBaseBg = img.gj; // 告警状态背景
          finalIconImg = iconUrl; // 设备类型图标
        } else if (status === 0) {
          finalBaseBg = img.lx; // 离线状态背景
          finalIconImg = iconUrl; // 设备类型图标
        } else {
          finalBaseBg = baseBg; // 正常状态使用原背景
          finalIconImg = iconUrl; // 设备类型图标
        }

        // 返回包含背景和图标的对象
        return {
          baseBg: finalBaseBg,
          iconImg: finalIconImg,
          isComposite: true // 标识这是需要合成的对象
        };
      }

      // 5. 使用canvas合成图标（原有逻辑）
      const loadImage = (src) => {
        return new Promise((resolve, reject) => {
          const img = new window.Image();
          img.crossOrigin = 'anonymous';
          img.onload = () => resolve(img);
          img.onerror = reject;
          img.src = src;
        });
      };
      try {
        const [bgImg, iconImg] = await Promise.all([
          loadImage(baseBg),
          loadImage(iconUrl)
        ]);
        const width = bgImg.width;
        const height = bgImg.height;
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        // 画底图
        ctx.drawImage(bgImg, 0, 0, width, height);
        // iconImg高度固定为16像素，宽度等比缩放
        const iconTargetH = 16;
        const scale = iconTargetH / iconImg.height;
        const iconW = iconImg.width * scale;
        const iconH = iconTargetH;
        const iconX = (height - iconW) / 2.5;
        const iconY = (height - iconH) / 2.4;
        ctx.drawImage(iconImg, iconX, iconY, iconW, iconH);
        // 绘制标题
        if (title) {
          const fontSize = 16;
          ctx.font = `${fontSize}px PingFang SC`;
          ctx.textAlign = 'left';
          ctx.textBaseline = 'middle';
          ctx.strokeStyle = 'rgba(0,0,0,0.8)';
          ctx.lineWidth = 2;
          ctx.fillStyle = '#ffffff';
          // 文字X坐标为六芒星右侧+间隔
          const textX = height - 5;
          // 文字Y坐标为垂直居中
          const textY = height / 2.3;

          // 测量文字宽度，动态调整 Canvas 宽度
          const textWidth = ctx.measureText(title).width;
          const requiredWidth = textX + textWidth + 8;

          // 如果需要的宽度超过当前 Canvas 宽度，则重新创建 Canvas
          if (requiredWidth > width) {
            canvas.width = requiredWidth;
            // 重新绘制底图，保持原始比例，不拉伸
            ctx.drawImage(bgImg, 0, 0, width, height);
            // 重新绘制图标，保持原始位置和大小
            ctx.drawImage(iconImg, iconX, iconY, iconW, iconH);
            // 重新设置字体样式，因为Canvas上下文被重置了
            ctx.font = `${fontSize}px PingFang SC`;
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.strokeStyle = 'rgba(0,0,0,0.8)';
            ctx.lineWidth = 2;
            ctx.fillStyle = '#ffffff';
          }

          // 直接完整显示标题，不截断不加省略号
          ctx.strokeText(title, textX, textY);
          ctx.fillText(title, textX, textY);
        }
        return canvas.toDataURL('image/png');
      } catch (e) {
        // 加载失败时返回类型图标
        return iconUrl;
      }
    },
    /** 全景感知点位图标 end */


    /**
     * 只移除地图点位，不影响其他地图元素
     */
    removeMapPoints() {
      try {
        console.log('开始移除地图点位...')
        this.stopRandomPopup()
        this.imagePointsData = []
        if (this.bubbles) {
          this.bubbles.windowClose()
          this.bubbles = null
        }
        const viewer = window.viewer
        if (!viewer || !viewer.entities) return
        const pointsToRemove = []
        viewer.entities.values.forEach(entity => {
          if (entity._isCustomMapPoint) {
            pointsToRemove.push(entity)
          }
        })
        pointsToRemove.forEach(entity => {
          viewer.entities.remove(entity)
        })
        console.log(`成功移除 ${pointsToRemove.length} 个点位`)
        this.bubbles = null
      } catch (error) {
        console.error('移除地图点位时发生错误:', error)
      }
    },

    /**
     * 确保至少有一个施工区域存在（后备方案）
     */
    ensureConstructionAreaExists() {
      if (!window.viewer) {
        console.error('❌ window.viewer未初始化，无法检查施工区域')
        return
      }

      // 检查是否已经有施工区域边界线
      const hasConstructionBoundary = window.viewer.entities.values.some(entity =>
        entity._isConstructionBoundary ||
        entity.id && (
          entity.id.includes('CONSTRUCTION_AREA_BOUNDARY') ||
          entity.id === 'PERMANENT_CONSTRUCTION_AREA_BOUNDARY'
        )
      )

      if (hasConstructionBoundary) {
        console.log('✅ 施工区域边界线已存在（通过配置或默认）')
        return
      }

      console.log('⚠️ 未发现施工区域边界线，使用默认坐标绘制')

      // 使用默认坐标绘制施工区域
      const defaultPosition = [
        [114.16060204898203, 30.61397907889239],
        [114.16121512010862, 30.613423354452703],
        [114.16078977519139, 30.61307952092333],
        [114.16022145096845, 30.613759010341685],
        [114.16060204898203, 30.61397907889239]
      ]

      try {
        // 确保坐标格式正确 - 转换为一维数组
        const flatPositions = defaultPosition.flat()

        // 绘制默认施工区域保护边界线
        const lineEntity = window.viewer.entities.add({
          id: 'DEFAULT_CONSTRUCTION_AREA_BOUNDARY',
          name: '默认施工区域边界线（永久）',
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray(flatPositions),
            width: 4,
            material: Cesium.Color.YELLOW,
            clampToGround: true,
            zIndex: 1000
          },
          // 标记为永久实体，防止被意外删除
          _isPermanent: true,
          _isConstructionBoundary: true,
          _isDefault: true
        });

        console.log('🎉 默认施工区域边界线绘制成功！')

      } catch (error) {
        console.error('❌ 绘制默认施工区域边界线时发生错误:', error)
      }
    },

    // 添加地图视图信息日志方法
    logMapViewInfo() {
      // 清除之前的定时器
      if (this.mapViewLogTimer) {
        clearTimeout(this.mapViewLogTimer);
      }

      // 设置防抖，500ms后执行日志记录
      this.mapViewLogTimer = setTimeout(() => {
        this.executeMapViewLog();
      }, 500);
    },

    // 执行地图视图信息记录
    executeMapViewLog() {
      try {
        const viewer = window.viewer;
        if (!viewer) {
          console.warn('地图视图日志: Cesium viewer 未初始化');
          return;
        }

        // 获取相机位置信息
        const camera = viewer.scene.camera;
        const canvas = viewer.scene.canvas;

        // 获取屏幕中心点
        const centerX = canvas.clientWidth / 2.0;
        const centerY = canvas.clientHeight / 2.0;
        const centerPoint = new Cesium.Cartesian2(centerX, centerY);

        // 将屏幕坐标转换为世界坐标
        const ray = camera.getPickRay(centerPoint);
        const worldPosition = viewer.scene.globe.pick(ray, viewer.scene);

        let centerLon = 0, centerLat = 0;
        if (Cesium.defined(worldPosition)) {
          // 转换为地理坐标
          const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(worldPosition);
          centerLon = Cesium.Math.toDegrees(cartographic.longitude);
          centerLat = Cesium.Math.toDegrees(cartographic.latitude);
        }

        // 获取相机高度
        const cameraHeight = camera.positionCartographic.height;

        // 获取相机方向信息
        const heading = Cesium.Math.toDegrees(camera.heading); // 方位角（偏航角）
        const pitch = Cesium.Math.toDegrees(camera.pitch);     // 俯仰角
        const roll = Cesium.Math.toDegrees(camera.roll);       // 翻滚角

        // 获取缩放级别
        const zoomLevel = this.getZoom();

        // 格式化输出信息
        const logInfo = {
          timestamp: new Date().toLocaleString('zh-CN'),
          viewCenter: {
            longitude: Number(centerLon.toFixed(6)),
            latitude: Number(centerLat.toFixed(6))
          },
          cameraHeight: Number((cameraHeight / 1000).toFixed(2)) + 'km', // 转换为公里
          cameraOrientation: {
            heading: Number(heading.toFixed(2)) + '°',
            pitch: Number(pitch.toFixed(2)) + '°',
            roll: Number(roll.toFixed(2)) + '°'
          },
          zoomLevel: Number(zoomLevel.toFixed(2))
        };

        console.log('🗺️ 地图视图变化:', {
          '⏰ 时间': logInfo.timestamp,
          '📍 视图中心': `经度: ${logInfo.viewCenter.longitude}°, 纬度: ${logInfo.viewCenter.latitude}°`,
          '📏 相机高度': logInfo.cameraHeight,
          '🧭 相机方向': `方位角: ${logInfo.cameraOrientation.heading}, 俯仰角: ${logInfo.cameraOrientation.pitch}, 翻滚角: ${logInfo.cameraOrientation.roll}`,
          '🔍 缩放级别': logInfo.zoomLevel
        });

        // 可选：将日志信息发送到后端或存储到本地
        // this.saveMapViewLog(logInfo);

      } catch (error) {
        console.error('获取地图视图信息时发生错误:', error);
      }
    },

    getZoom() {
      let viewer = window.viewer;
      let h = viewer.camera.positionCartographic.height
      if (h <= 100) {
        return 19
      } else if (h <= 300) {
        return 18
      } else if (h <= 660) {
        return 17
      } else if (h <= 1300) {
        return 16
      } else if (h <= 2600) {
        return 15
      } else if (h <= 6400) {
        return 14
      } else if (h <= 13200) {
        return 13
      } else if (h <= 26000) {
        return 12
      } else if (h <= 67985) {
        return 11
      } else if (h <= 139780) {
        return 10
      } else if (h <= 250600) {
        return 9
      } else if (h <= 380000) {
        return 8
      } else if (h <= 640000) {
        return 7
      } else if (h <= 1280000) {
        return 6
      } else if (h <= 2600000) {
        return 5
      } else if (h <= 6100000) {
        return 4
      } else if (h <= 11900000) {
        return 3
      } else {
        return 2
      }
    },

    /**
      * 移除吊车3D模型
      */
    removeCraneModels() {
      try {
        console.log('🎯 开始移除吊车3D模型...')

        // 1. 移除 Cesium entities 中的吊车模型
        const viewer = window.viewer
        if (viewer && viewer.entities) {
          const craneModelEntities = []
          viewer.entities.values.forEach(entity => {
            if (entity.id && (
              entity.id.startsWith('craneModel_') ||
              entity.id.startsWith('crane_3d_') ||
              entity.id.includes('_model_') ||
              entity._isCraneModel === true
            )) {
              craneModelEntities.push(entity)
            }
          })

          // 批量移除吊车模型实体
          craneModelEntities.forEach(entity => {
            try {
              viewer.entities.remove(entity)
              console.log(`✅ 已移除吊车实体模型: ${entity.id}`)
            } catch (error) {
              console.warn(`移除吊车实体模型失败 (${entity.id}):`, error)
            }
          })

          console.log(`🎯 移除了 ${craneModelEntities.length} 个吊车实体模型`)
        }

        // 2. 移除 Cesium primitives 中的吊车模型
        if (viewer && viewer.scene && viewer.scene.primitives) {
          const cranePrimitives = []
          const primitives = viewer.scene.primitives._primitives
          for (let i = 0; i < primitives.length; i++) {
            const primitive = primitives[i]
            if (primitive.id && (
              primitive.id.startsWith('craneModel_') ||
              primitive.id.startsWith('crane_3d_') ||
              primitive.id.includes('_model_') ||
              primitive.id === 'craneModel'
            )) {
              cranePrimitives.push(primitive)
            }
          }

          // 批量移除吊车模型primitive
          cranePrimitives.forEach(primitive => {
            try {
              viewer.scene.primitives.remove(primitive)
              console.log(`✅ 已移除吊车primitive模型: ${primitive.id}`)
            } catch (error) {
              console.warn(`移除吊车primitive模型失败 (${primitive.id}):`, error)
            }
          })

          console.log(`🎯 移除了 ${cranePrimitives.length} 个吊车primitive模型`)
        }

        // 3. 清理 dynamicEntities 中的吊车模型引用
        if (this.dynamicEntities) {
          const craneDynamicEntities = Object.keys(this.dynamicEntities).filter(key =>
            key.startsWith('craneModel_') ||
            key.startsWith('crane_3d_') ||
            key.includes('_model_')
          )

          craneDynamicEntities.forEach(key => {
            try {
              delete this.dynamicEntities[key]
              console.log(`✅ 已清理吊车动态实体引用: ${key}`)
            } catch (error) {
              console.warn(`清理吊车动态实体引用失败 (${key}):`, error)
            }
          })

          console.log(`🎯 清理了 ${craneDynamicEntities.length} 个吊车动态实体引用`)
        }

        // 4. 移除 EarthMap 中的3D模型
        if (window.EarthMap && window.EarthMap.model) {
          try {
            // 检查当前3D模型列表
            if (window.current3dModels && Array.isArray(window.current3dModels)) {
              const craneModels = window.current3dModels.filter(model =>
                model.id && (
                  model.id.startsWith('crane') ||
                  model.id.includes('crane') ||
                  model.name && model.name.toLowerCase().includes('crane')
                )
              )

              // 移除吊车相关的3D模型
              craneModels.forEach(model => {
                try {
                  if (window.EarthMap.model.remove3Dtiles) {
                    window.EarthMap.model.remove3Dtiles(model.id)
                    console.log(`✅ 已移除吊车3D模型: ${model.id}`)
                  }
                } catch (error) {
                  console.warn(`移除吊车3D模型失败 (${model.id}):`, error)
                }
              })

              // 从当前模型列表中移除
              window.current3dModels = window.current3dModels.filter(model =>
                !model.id || !(
                  model.id.startsWith('crane') ||
                  model.id.includes('crane') ||
                  model.name && model.name.toLowerCase().includes('crane')
                )
              )

              console.log(`🎯 移除了 ${craneModels.length} 个吊车3D模型`)
            }
          } catch (error) {
            console.warn('移除EarthMap吊车模型时发生错误:', error)
          }
        }

        console.log('✅ 吊车3D模型移除完成')

      } catch (error) {
        console.error('移除吊车模型时发生错误:', error)
      }
    },

    /**
     * 根据缩放级别计算动态偏移量
     */
    calculateDynamicOffset(baseOffset, zoomLevel) {
      // 缩放级别越高（越近），偏移量应该越小
      // 缩放级别越低（越远），偏移量应该越大
      const scaleFactor = Math.pow(2, 15 - zoomLevel); // 以15级为基准
      return baseOffset * Math.max(0.1, Math.min(10, scaleFactor));
    },

    /**
     * 设置所有事件监听器
     */
    setupEventListeners() {
      console.log('🔧 CesiumBaseMap mounted - 开始设置事件监听器');

      this.setupTrajectoryEventListener();
      this.setupFlyToPointEventListener();
      this.setupChangeMapEventListener();
      this.setupConstructionViewEventListener();
      this.setupCloseLeftEventListener();
      this.setupEquipmentInfoListEventListener();
      this.setupPointScreenEventListener();
      this.setupPointClickedEventListener();
      this.setupUseElectricEventListener();
      this.testEventBusConnection();
    },

    /**
     * 设置轨迹显示事件监听器
     */
    setupTrajectoryEventListener() {
      this.$bus.off('showTrajectory');
      this.$bus.on('showTrajectory', (val) => {
        console.log('🎯 showTrajectory 事件被触发，数据:', val);
        this.removePoints();

        // 轨迹调用方法
        let data = val.map(i => {
          return {
            id: i.id,
            lng: Number(i.locationLng),
            lat: Number(i.locationLat),
            time: i.locationTime
          };
        });

        this.addTrajectoryPoints(data, {
          showSequence: true,
          autoRemove: true
        });
      });

      console.log('📝 已设置 showTrajectory 事件监听器');
    },

    /**
     * 设置飞行到指定点位事件监听器
     */
    setupFlyToPointEventListener() {
      this.$bus.off('flytoPoint');
      this.$bus.on('flytoPoint', (item) => {
        console.log('🎯 flytoPoint 事件被触发，数据:', item);
        this.handleFlyToPoint(item);
      });

      console.log('📝 已设置 flytoPoint 事件监听器');
    },

    /**
     * 处理飞行到指定点位的逻辑
     */
    handleFlyToPoint(item) {
      // 检查是否存在保存的视角，如果存在则跳过flyto操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过飞行到指定点位的操作');
        return;
      }

      // 飞到指定点位
      const lng = Number(item.locationLng);
      const lat = Number(item.locationLat);
      const height = 100; // 高度100米

      window.viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(lng, lat, height),
        duration: 2.0, // 飞行时间2秒
        // complete: () => {
        //   // 飞行完成后创建弹窗显示时间和经纬度
        //   const currentTime = new Date().toLocaleString('zh-CN', {
        //     year: 'numeric',
        //     month: '2-digit',
        //     day: '2-digit',
        //     hour: '2-digit',
        //     minute: '2-digit',
        //     second: '2-digit'
        //   });

        //   this.createBubble(
        //     {
        //       longitude: lng,
        //       latitude: lat,
        //       time: currentTime,
        //       position: [lng, lat, 50] // 弹窗位置稍微低一点
        //     },
        //     0,
        //     'LocationTimePopup'
        //   );
        // }
      });
    },

    /**
     * 设置地图切换事件监听器
     */
    setupChangeMapEventListener() {
      this.$bus.off("changeMap");
      this.$bus.on("changeMap", async (val) => {
        console.log('🎯 changeMap 事件被触发，值:', val);
        this.removePoints();
        this.base.constructionAntiInvasion.clear()
        this.base.highFormworkMonitoring.clear()
        if (val === 'high') {
          await this.handleHighModeChange();
        } else if (val === 'model') {
          await this.handleModelModeChange();
        } else if (val === 'civilAirDefense') {
          // 人防（穿透式）
          await this.base.civilAirDefense.init();
        }
      });
    },

    /**
     * 处理全景感知模式切换
     */
    async handleHighModeChange() {
      // 加载设备点位
      await this.loadEquipmentPoints();

      // 加载吊车防护区域
      await this.craneManager.loadVerticalProtectionData();

      this._userClosedPopup = false;
    },

    /**
     * 加载设备点位
     */
    async loadEquipmentPoints() {
      try {
        const { data: { records } } = await listPage({
          page: {
            current: -1,
            size: 9999,
          },
          customQueryParams: {}
        });

        console.log(records);
        const points = records
          .filter(item => item.equipmentLng !== null && item.equipmentLat !== null)
          .map(item => ({
            id: item.id,
            lng: Number(item.equipmentLng),
            lat: Number(item.equipmentLat),
            imageType: item.equipmentTypeName,
            name: item.equipmentName,
            data: item
          }));

        this.addImagePoints(points, (data, entity) => {
          console.log('点击回调触发:', data, entity);
          this.createBubble(
            {
              ...data,
              position: [
                Number(data.equipmentLng),
                Number(data.equipmentLat)
              ]
            },
            0,
            'CustomPopup'
          );
        });
      } catch (error) {
        console.error('加载设备点位失败:', error);
      }
    },

    /**
     * 处理模型模式切换
     */
    async handleModelModeChange() {
      this.showDeviceCard = false;
      this.addClickEvent();
    },

    /**
     * 设置施工视图切换事件监听器
     */
    setupConstructionViewEventListener() {
      this.$bus.off('switchConstructionView');
      this.$bus.on('switchConstructionView', async (val) => {
        console.log('🎯 switchConstructionView 事件被触发，值:', val);

        // 检查地图是否已初始化
        if (!this.mapInitialized) {
          console.log('⏳ 地图尚未完全初始化，等待中...');
          setTimeout(() => {
            console.log('🔄 重新触发 switchConstructionView 事件');
            this.$bus.emit('switchConstructionView', val);
          }, 500);
          return;
        }

        // 设置当前场景状态
        this.currentScene = val;
        console.log('📝 当前场景设置为:', this.currentScene);

        // 重置视角恢复标志
        this._hasRestoredView = false;

        // 完全清理地图内容，包括所有场景的元素
        this.removePoints();

        // 清理吊车数据定时器（防止多个定时器同时运行）
        if (this.craneDataTimer) {
          console.log('🧹 清理旧的吊车数据定时器');
          clearInterval(this.craneDataTimer);
          this.craneDataTimer = null;
        }

        if (this.towerCraneDataTimer) {
          console.log('🧹 清理旧的吊车数据定时器');
          clearInterval(this.towerCraneDataTimer);
          this.towerCraneDataTimer = null;
        }

        this.base.constructionAntiInvasion.clear()
        this.base.highFormworkMonitoring.clear()

        if (val === 'model') {
          // 吊车安全监测
          await this.handleConstructionModelView();
        } if (val === 'tower') {
          // 塔吊安全监测
          await this.handleConstructionTowerView();
        } if (val === 'prevention') {
          // 群塔防碰撞
          await this.handleConstructionPreventionView();
        } else if (val === 'high') {
          // 施工防侵限
          await this.handleConstructionHighView();
        } else if (val === 'ai') {
          // AI综合安防
          await this.handleConstructionAiView();
        } else if (val === 'highFormwork') {
          // 高支模安全监控
          await this.base.highFormworkMonitoring.init();
        }
      });
    },

    /**
     * 处理施工模型视图
     */
    async handleConstructionModelView() {
      console.log('切换到吊车模型视图');
      this.setupImagePointClickHandler();

      try {
        const { data } = await getMechanical();
        console.log('获取机械数据成功:', data);

        const validEquipments = this.filterValidEquipments(data);
        console.log(`共 ${data.length} 个设备，其中 ${validEquipments.length} 个坐标有效`);

        // 添加3D模型
        this.addCraneModels(validEquipments);

        // 优先尝试恢复保存的视角
        const hasSavedView = this.checkSavedViewExists();
        if (hasSavedView) {
          console.log('🎯 找到保存的视角，正在恢复');
          this.restoreCameraView();
        } else {
          console.log('📝 未找到保存的视角，飞行到默认位置');
          // 如果没有保存的视角，飞行到第一个设备或默认位置
          if (validEquipments.length > 0) {
            this.flyToFirstEquipment(validEquipments[0]);
          }
        }
      } catch (error) {
        console.error('获取设备数据失败:', error);
      }
    },

    /**
     * 处理施工模型视图 => 塔吊
     */
    async handleConstructionTowerView() {
      console.log('切换到塔吊模型视图');
      this.setupImagePointClickHandler();
      try {
        EventBus.$on('showVideoList', (data) => {
          const deviceData = this.towerCrane.processTowerCraneData(data)
          this.openHookVideoModal(deviceData)
        })
        const { data } = await getTowerCraneMechanical();
        console.log('获取机械数据成功:', data);
        this.removePoints();
        this.craneManager.clearCraneEntities()
        this.cleanupEventListeners();
        this.clearTrajectoryPoints()
        this.removeDynamicEntities();
        const validEquipments = this.filterValidEquipments(data);
        console.log(`共 ${data.length} 个设备，其中 ${validEquipments.length} 个坐标有效`);
        console.log(validEquipments, 'validEquipments')
        // 添加3D模型
        this.addTowerCraneModels(validEquipments);

        // 飞行到第一个设备
        if (validEquipments.length > 0) {
          this.flyToFirstEquipment(validEquipments[0]);
        }
      } catch (error) {
        console.error('获取设备数据失败:', error);
      }
    },

    /**
     * 群塔防碰撞
     */
    async handleConstructionPreventionView() {
      this.setupImagePointClickHandler();

      try {
        // 加载垂直防护数据
        this.loadVideoMonitoringPoints(true);
        await this.collisionPrevention.loadView();
        await this.collisionPrevention.setupTowerCraneDataTimer();
        await this.base.constructionAntiInvasion.init();
        await this.towerCrane.init();

      } catch (error) {
        console.error('群塔防碰撞error:', error);
      }
    },

    /**
     * 过滤有效设备
     */
    filterValidEquipments(data) {
      return data.filter(element => {
        const equipmentLocation = element.equipmentLocation;
        if (!equipmentLocation) {
          console.warn(`设备 ${element.id} 缺少位置信息 equipmentLocation`);
          return false;
        }

        const lng = equipmentLocation.equipmentLng;
        const lat = equipmentLocation.equipmentLat;

        if (lng === null || lng === undefined || lat === null || lat === undefined) {
          console.warn(`设备 ${element.id} 坐标信息不完整: lng=${lng}, lat=${lat}`);
          return false;
        }

        const numLng = Number(lng);
        const numLat = Number(lat);

        if (isNaN(numLng) || isNaN(numLat)) {
          console.warn(`设备 ${element.id} 坐标格式无效: lng=${lng}, lat=${lat}`);
          return false;
        }

        // 检查坐标范围是否合理（中国大陆经纬度范围）
        if (numLng < 73 || numLng > 135 || numLat < 18 || numLat > 54) {
          console.warn(`设备 ${element.id} 坐标超出合理范围: lng=${numLng}, lat=${numLat}`);
          return false;
        }

        return true;
      });
    },

    /**
     * 添加吊车模型
     */
    addCraneModels(validEquipments) {
      validEquipments.forEach(element => {
        const equipmentLocation = element.equipmentLocation;
        try {
          this.addMapElement(window.viewer, {
            id: `craneModel_${element.id}`,
            type: 'glbModel',
            position: [Number(equipmentLocation.equipmentLng), Number(equipmentLocation.equipmentLat), 0],
            url: 'http://************:19000/linkapp/linkapp/public/dc.glb',
          });
        } catch (error) {
          console.error(`添加设备 ${element.id} 模型失败:`, error);
        }
      });
    },

    /**
     * 添加塔吊模型
     */
    addTowerCraneModels(validEquipments) {
      validEquipments.forEach(element => {
        const equipmentLocation = element.equipmentLocation;
        try {
          this.addMapElement(window.viewer, {
            id: `craneModel_${element.id}`,
            type: 'glbModel',
            position: [Number(equipmentLocation.equipmentLng), Number(equipmentLocation.equipmentLat), 0],
            url: 'http://************:19000/linkapp/linkapp/public/TD.glb',
          });
        } catch (error) {
          console.error(`添加设备 ${element.id} 模型失败:`, error);
        }
      });
    },

    /**
     * 飞行到第一个设备
     */
    flyToFirstEquipment(firstEquipment) {
      // 检查是否存在保存的视角，如果存在则跳过flyto操作
      if (this.checkSavedViewExists()) {
        console.log('🔒 检测到存在保存的视角，跳过飞行到第一个设备的操作');
        return;
      }

      const firstLocation = firstEquipment.equipmentLocation;

      // 设备位置
      const deviceLng = Number(firstLocation.equipmentLng);
      const deviceLat = Number(firstLocation.equipmentLat);
      const deviceHeight = 0;

      // 计算观察位置：距离设备60米，20度角观察
      const observeDistance = 60;
      const observeAngle = 20;

      const angleRadians = Cesium.Math.toRadians(observeAngle);
      const horizontalDistance = observeDistance * Math.cos(angleRadians);
      const verticalHeight = observeDistance * Math.sin(angleRadians);
      const bearingRadians = Cesium.Math.toRadians(90);

      const deviceCartesian = Cesium.Cartesian3.fromDegrees(deviceLng, deviceLat, deviceHeight);
      const eastNorthUp = Cesium.Transforms.eastNorthUpToFixedFrame(deviceCartesian);

      const cameraENU = new Cesium.Cartesian3(
        horizontalDistance * Math.cos(bearingRadians),
        horizontalDistance * Math.sin(bearingRadians),
        verticalHeight
      );

      const cameraPosition = Cesium.Matrix4.multiplyByPoint(eastNorthUp, cameraENU, new Cesium.Cartesian3());
      const direction = Cesium.Cartesian3.subtract(deviceCartesian, cameraPosition, new Cesium.Cartesian3());
      Cesium.Cartesian3.normalize(direction, direction);

      const up = Cesium.Cartesian3.clone(Cesium.Cartesian3.UNIT_Z);

      window.viewer.camera.flyTo({
        destination: cameraPosition,
        orientation: {
          direction: direction,
          up: up
        },
        duration: 2.0
      });

      console.log(`飞行到第一个设备位置: ${firstEquipment.id}，观察距离: ${observeDistance}米，观察角度: ${observeAngle}°`);
    },

    /**
     * 施工防侵限视图
     */
    async handleConstructionHighView() {
      console.log('切换到施工防侵限视图');

      // 优先尝试恢复保存的视角
      const hasSavedView = this.checkSavedViewExists();
      if (hasSavedView) {
        this.restoreCameraView();
        // 标记已恢复视角，防止后续操作覆盖
        this._hasRestoredView = true;
      } else {
        console.log('📝 未找到保存的视角，将使用默认行为');
        this._hasRestoredView = false;
      }

      // 使用CraneManager处理施工防侵限视图
      await this.craneManager.handleConstructionHighView();

      // 如果没有恢复保存的视角，且没有其他视角操作，尝试恢复默认视角
      if (!this._hasRestoredView) {
        this.tryRestoreDefaultView();
      }
    },

    /**
     * 加载视频监控点位
     */
    async loadVideoMonitoringPoints(isNoFly) {
      const { data: { records: videoList } } = await listPage({
        page: {
          current: -1,
          size: 9999,
        },
        customQueryParams: {
          typeNames: ['视频监控']
        },
      });

      console.log('获取到的视频监控点位:', videoList);
      const points = videoList
        .filter(item => item.equipmentLng !== null && item.equipmentLat !== null)
        .map(item => ({
          id: item.equipmentId,
          lng: Number(item.equipmentLng),
          lat: Number(item.equipmentLat),
          image: this.deviceImageUrl[item.equipmentTypeName] || this.deviceImageUrl['通用设备'],
          name: item.equipmentName,
          data: item
        }));

      this.addImagePoints2(points, (data, entity) => {
        console.log('点击回调触发:', data, entity);
        this.createBubble(
          {
            ...data,
            position: [
              Number(data.equipmentLng),
              Number(data.equipmentLat)
            ]
          },
          0,
          'CustomPopup'
        );
      }, 'videoMonitoring_', isNoFly);
    },

    /**
     * 处理施工AI视图
     */
    async handleConstructionAiView() {
      await this.loadVideoMonitoringPoints();
    },

    /**
     * 设置关闭左侧面板事件监听器
     */
    setupCloseLeftEventListener() {
      this.$bus.off('closeLeft');
      this.$bus.on('closeLeft', (val, pointArr = []) => {
        console.log('🎯 closeLeft 事件被触发，值:', val, '点位数组:', pointArr);
        val ? this.removePoints() : this.addPoints(pointArr);
      });
    },

    /**
     * 设置设备信息列表事件监听器
     */
    setupEquipmentInfoListEventListener() {
      this.$bus.off('equipmentInfoList');
      this.$bus.on('equipmentInfoList', (val) => {
        console.log('🎯 equipmentInfoList 事件被触发，数据:', val);
        this.removeMapPoints();

        if (val.length > 0) {
          this.handleEquipmentInfoList(val);
        } else {
          this.$bus.emit('changeMap', 'high');
        }
      });
    },

    /**
     * 处理设备信息列表
     */
    handleEquipmentInfoList(val) {
      const points = val
        .filter(item => item.equipmentLng !== null && item.equipmentLat !== null)
        .map(item => ({
          id: item.id,
          lng: Number(item.equipmentLng),
          lat: Number(item.equipmentLat),
          image: this.deviceImageUrl[item.equipmentTypeName],
          name: item.equipmentName,
          data: item
        }));

      console.log(points);

      if (points.length) {
        this.addImagePoints(points, (data, entity) => {
          console.log('点击回调触发:', data, entity);
          this.createBubble(
            {
              ...data,
              position: [
                Number(data.equipmentLng),
                Number(data.equipmentLat)
              ]
            },
            0,
            data.equipmentTypeName === '高支模监测' ? 'HighFormworkPopup' : 'CustomPopup'
          );
        });
      } else {
        console.warn('点位加载失败: 暂无点位数据');
      }
    },

    /**
     * 设置点屏事件监听器
     */
    setupPointScreenEventListener() {
      this.$bus.off('enterPointScreen');
      this.$bus.on('enterPointScreen', () => {
        console.log('🎯 enterPointScreen 事件被触发，开始加载模型和绘制施工区域');

        this.addModelLayer();
      });
    },

    /**
     * 设置点击事件监听器
     */
    setupPointClickedEventListener() {
      this.$bus.off('pointClicked');
      this.$bus.on('pointClicked', (val) => {
        console.log('🎯 pointClicked 事件被触发，数据:', val);
        this.bubbles = val;
      });
    },

    /**
     * 测试事件总线连接
     */
    testEventBusConnection() {
      setTimeout(() => {
        console.log('🧪 测试事件总线功能...');
        this.$bus.emit('testEvent', { message: '测试事件总线' });
        this.$bus.on('testEvent', (data) => {
          console.log('✅ 事件总线测试成功:', data);
        });

        console.log('🔍 检查 $bus 对象:', this.$bus);
        console.log('🔍 检查 $bus.on 方法:', typeof this.$bus.on);
        console.log('🔍 检查 $bus.emit 方法:', typeof this.$bus.emit);
        console.log('🔍 检查 $bus.off 方法:', typeof this.$bus.off);
      }, 1000);
    },

    /**
     * 初始化地图视图日志记录
     */
    initializeMapViewLogging() {
      setTimeout(() => {
        console.log('📍 地图初始化完成，记录初始视图:');
        this.logMapViewInfo();
      }, 1000);
    },

    /**
     * 设置用电安全监测事件监听器
     */
    setupUseElectricEventListener() {
      this.$bus.off('useElectricDevices');
      this.$bus.on('useElectricDevices', (val) => {
        console.log('🎯 useElectricDevices 事件被触发，数据:', val);
        this.handleUseElectricDevices(val);
      });
    },

    /**
     * 处理用电安全监测事件
     */
    handleUseElectricDevices(val) {
      console.log('🔌 切换到用电安全监测，开始清理所有施工防侵限相关元素...');

      // 设置当前场景状态
      this.currentScene = 'useElectric';
      console.log('📝 当前场景设置为:', this.currentScene);

      // 完全清理地图内容，包括施工防侵限的所有元素
      this.removePoints();

      // 清理吊车数据定时器
      if (this.craneDataTimer) {
        console.log('🧹 清理吊车数据定时器');
        clearInterval(this.craneDataTimer);
        this.craneDataTimer = null;
      }

      // 加载用电安全设备点位
      this.loadUseElectricDevices();
    },

    /**
     * 加载用电安全设备点位
     */
    async loadUseElectricDevices() {
      try {
        console.log('🔌 开始加载用电安全设备点位...');

        const { data: useElectricDevices } = await getUseElectricDevices();
        console.log('📊 获取到的用电安全设备数据:', useElectricDevices);

        if (!useElectricDevices || !Array.isArray(useElectricDevices)) {
          console.warn('⚠️ 用电安全设备数据格式不正确:', useElectricDevices);
          return;
        }

        // 过滤有效的设备点位（有坐标的设备）
        const validDevices = useElectricDevices.filter(item =>
          item.equipmentLng !== null &&
          item.equipmentLat !== null &&
          !isNaN(Number(item.equipmentLng)) &&
          !isNaN(Number(item.equipmentLat))
        );

        console.log(`📍 共 ${useElectricDevices.length} 个设备，其中 ${validDevices.length} 个有有效坐标`);

        if (validDevices.length === 0) {
          console.warn('⚠️ 没有有效的用电安全设备点位');
          return;
        }

        // 转换为地图点位格式
        const points = validDevices.map(item => ({
          id: item.equipmentId || `use_electric_${Date.now()}_${Math.random()}`,
          lng: Number(item.equipmentLng),
          lat: Number(item.equipmentLat),
          name: item.equipmentName,
          data: {
            ...item,
            // 标准化字段名，确保与CustomPopup组件兼容
            equipmentTypeName: item.equipmentTypeName,
            equipmentName: item.equipmentName,
            equipmentCode: item.equipmentCode,
            equipmentLng: item.equipmentLng,
            equipmentLat: item.equipmentLat,
            onlineState: item.onlineState,
            status: item.isAlarm, // 告警状态映射
            isAlarm: item.isAlarm,
            // 添加用电安全特有字段
            locationType: item.locationType,
            remark: item.remark
          }
        }));

        console.log('🎯 转换后的点位数据:', points);

        // 使用addImagePoints2方法添加点位（不支持随机弹窗，适合用电安全监测）
        this.addImagePoints2(points, (data, entity) => {
          console.log('🖱️ 用电安全设备点击回调触发:', data);

          // 创建设备详情弹窗
          this.createBubble(
            {
              ...data,
              position: [
                Number(data.equipmentLng),
                Number(data.equipmentLat)
              ]
            },
            0,
            'CustomPopup'
          );
        });

        console.log(`✅ 成功加载 ${validDevices.length} 个用电安全设备点位`);

      } catch (error) {
        console.error('❌ 加载用电安全设备点位失败:', error);
      }
    },

    // 保存当前地图视角
    saveCameraView(pageName = 'default') {
      return cameraViewManager.saveCameraView(pageName, this.$route);
    },

    // 恢复保存的地图视角
    restoreCameraView(pageName = 'default', showFlyAnimation = true) {
      return cameraViewManager.restoreCameraView(pageName, showFlyAnimation, this.$route);
    },

    // 获取指定页面的保存视角信息
    getSavedCameraView(pageName = 'default') {
      return cameraViewManager.getSavedCameraView(pageName, this.$route);
    },

    // 删除指定页面的保存视角
    deleteSavedCameraView(pageName = 'default') {
      return cameraViewManager.deleteSavedCameraView(pageName, this.$route);
    },

    // 获取所有保存的视角列表
    getAllSavedCameraViews() {
      return cameraViewManager.getAllSavedCameraViews();
    },

    // 尝试恢复默认视角
    tryRestoreDefaultView() {
      return cameraViewManager.tryRestoreDefaultView(this.$route);
    },

    // 在地图组件销毁前保存当前视角
    saveCurrentViewBeforeDestroy() {
      return cameraViewManager.saveCurrentViewBeforeDestroy(this.$route);
    },

    // 设置当前页面名称（用于区分不同页面的视角保存）
    setCurrentPageName(pageName) {
      return cameraViewManager.setCurrentPageName(pageName);
    },

    // 获取当前页面名称
    getCurrentPageName() {
      return cameraViewManager.getCurrentPageName();
    },

    // 检查当前页面或子页面是否存在保存的视角
    checkSavedViewExists(pageName = null) {
      return cameraViewManager.checkSavedViewExists(pageName, this.$route);
    },

    // 调试和测试方法：手动检查和管理保存的视角
    debugSavedViews() {
      return cameraViewManager.debugSavedViews();
    },

    // 获取当前项目名称，用于构建唯一的缓存key
    getCurrentProjectName() {
      return cameraViewManager.getCurrentProjectName(this.$route);
    },

    // 构建完整的存储键名：cesium_camera_view_项目名称_页面名称
    buildStorageKey(pageName = 'default') {
      return cameraViewManager.buildStorageKey(pageName, this.$route);
    },

    // 打开吊钩可视化弹窗
    async openHookVideoModal(deviceData) {
      console.log('打开吊钩可视化弹窗:', deviceData);

      try {
        // 设置当前设备数据
        this.currentHookDevice = deviceData;

        // 从塔吊实体中获取视频设备列表
        this.hookVideoDeviceList = this.getVideoDeviceListFromTowerCrane(deviceData);

        // 显示弹窗
        this.showHookVideoModal = true;

        console.log('吊钩可视化弹窗已打开，视频设备列表:', this.hookVideoDeviceList);
      } catch (error) {
        console.error('打开吊钩可视化弹窗失败:', error);
        this.$Message.error('打开视频弹窗失败，请稍后重试');
      }
    },

    // 从塔吊实体中获取视频设备列表
    getVideoDeviceListFromTowerCrane(deviceData) {
      try {
        console.log('从塔吊实体获取视频设备列表，设备数据:', deviceData);

        // 直接使用videoList数据
        if (deviceData.videoList && Array.isArray(deviceData.videoList)) {
          console.log('找到塔吊实体中的videoList:', deviceData.videoList);

          // 处理视频设备数据，确保格式正确
          const processedDevices = deviceData.videoList.map(device => {
            // 从childAttributeList中提取视频地址和accessToken
            const videoUrl = this.extractVideoUrl(device);
            const accessToken = this.extractAccessToken(device);
            const lastFrameImage = this.extractLastFrameImage(device);

            return {
              ...device,
              streamUrl: videoUrl,
              hasValidStream: !!videoUrl,
              accessToken: accessToken,
              lastFrameImage: lastFrameImage,
              // onlineState: device.onlineState
            };
          });
          console.log('处理后的视频设备列表:', processedDevices);

          return processedDevices.filter(device =>
            device.name && device.name.trim() !== ''
          );
        } else {
          console.warn('塔吊实体中未找到videoList，使用模拟数据');
          return this.getMockVideoDevices(deviceData);
        }
      } catch (error) {
        console.error('从塔吊实体获取视频设备列表失败:', error);
        return this.getMockVideoDevices(deviceData);
      }
    },

    // 获取模拟视频设备数据（备选方案）
    getMockVideoDevices(deviceData) {
      return [
        {
          id: `hook_camera_1_${deviceData.id}`,
          deviceName: `${deviceData.name}-吊钩摄像头1`,
          areaPath: '吊钩作业区域',
          onlineState: 1,
          streamUrl: 'ezopen://open.ys7.com/xxx/xxx.live',
          accessToken: 'your_access_token_here',
          hasValidStream: true,
          childAttributeList: [
            {
              propCode: 'ezopen',
              propValue: 'ezopen://open.ys7.com/xxx/xxx.live'
            }
          ]
        },
        {
          id: `hook_camera_2_${deviceData.id}`,
          deviceName: `${deviceData.name}-吊钩摄像头2`,
          areaPath: '吊钩作业区域',
          onlineState: 1,
          streamUrl: 'ezopen://open.ys7.com/yyy/yyy.live',
          accessToken: 'your_access_token_here',
          hasValidStream: true,
          childAttributeList: [
            {
              propCode: 'ezopen',
              propValue: 'ezopen://open.ys7.com/yyy/yyy.live'
            }
          ]
        },
        {
          id: `hook_camera_3_${deviceData.id}`,
          deviceName: `${deviceData.name}-吊钩摄像头3`,
          areaPath: '吊钩作业区域',
          onlineState: 0, // 离线状态
          streamUrl: '',
          accessToken: '',
          hasValidStream: false,
          childAttributeList: []
        },
        {
          id: `hook_camera_4_${deviceData.id}`,
          deviceName: `${deviceData.name}-吊钩摄像头4`,
          areaPath: '吊钩作业区域',
          onlineState: 1,
          streamUrl: 'ezopen://open.ys7.com/zzz/zzz.live',
          accessToken: 'your_access_token_here',
          hasValidStream: true,
          childAttributeList: [
            {
              propCode: 'ezopen',
              propValue: 'ezopen://open.ys7.com/zzz/zzz.live'
            }
          ]
        }
      ];
    },

    // 从设备的childAttributeList中提取视频地址
    extractVideoUrl(camera) {
      if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
        return null;
      }

      const ezOpenAttr = camera.childAttributeList.find(attr => attr.propCode === 'ezopen');
      return ezOpenAttr && ezOpenAttr.propValue ? ezOpenAttr.propValue : null;
    },

    // 从设备的childAttributeList中提取accessToken
    extractAccessToken(camera) {
      if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
        return null;
      }

      const tokenAttr = camera.childAttributeList.find(attr => attr.propCode === 'accessToken');
      return tokenAttr && tokenAttr.propValue ? tokenAttr.propValue : null;
    },

    // 从设备的childAttributeList中提取最近一帧图片地址
    extractLastFrameImage(camera) {
      if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
        return null;
      }

      const imageAttr = camera.childAttributeList.find(attr => attr.propCode === 'lastFrameImage');
      return imageAttr && imageAttr.propValue ? imageAttr.propValue : null;
    },

    // 关闭吊钩可视化弹窗
    closeHookVideoModal() {
      this.showHookVideoModal = false;
      this.currentHookDevice = null;
      this.hookVideoDeviceList = [];
      console.log('吊钩可视化弹窗已关闭');
    },

    // 委托给设备点位管理器的方法
    async addImagePoints2(points, onClick, idPrefix, isNoFly) {
      return await this.devicePointManager.addImagePoints2(points, onClick, idPrefix, isNoFly);
    },

  },

  beforeDestroy() {
    // 在组件销毁前保存当前视角
    this.saveCurrentViewBeforeDestroy();

    // 清理吊车管理器
    if (this.craneManager) {
      this.craneManager.destroy();
    }

    // 清理设备点位管理器
    if (this.devicePointManager) {
      this.devicePointManager.destroy();
    }

    // 清理施工区域管理器
    if (this.constructionAreaManager) {
      this.constructionAreaManager.destroy();
    }

    // 清理地图视图日志定时器
    if (this.mapViewLogTimer) {
      clearTimeout(this.mapViewLogTimer);
      this.mapViewLogTimer = null;
    }

    // 清理缩放监听器
    if (this._zoomChangeHandler && window.viewer) {
      window.viewer.camera.changed.removeEventListener(this._zoomChangeHandler);
      this._zoomChangeHandler = null;
    }

    // 清理所有 $bus 事件监听器
    this.$bus.off('showTrajectory');
    this.$bus.off('flytoPoint');
    this.$bus.off('changeMap');
    this.$bus.off('switchConstructionView');
    this.$bus.off('closeLeft');
    this.$bus.off('equipmentInfoList');
    this.$bus.off('enterPointScreen');
    this.$bus.off('useElectricDevices');

    // 清理所有事件监听器和弹窗
    this.cleanupEventListeners();
    this.forceCleanupPopup();
    if (this.base.constructionAntiInvasion) {
      this.base.constructionAntiInvasion.clear()
    }
    if (this.base.highFormworkMonitoring) {
      this.base.highFormworkMonitoring.clear()
    }

    // 强制清理所有设备图标DOM元素
    const container = document.getElementById('base3dmap');
    if (container) {
      let removedDOMCount = 0;

      // 清理所有设备相关DOM元素
      const allDeviceSelectors = [
        '.device-icon',
        '.radar-label',
        '[id^="device-icon-"]',
        '[id^="device-label-"]',
        '[id^="point-label-"]',
        '[id^="point-alarm-label-"]',
        '[id^="radar-label"]',
        '.device-icon-container',
        '.radar-label-content',
        '[class*="device-"]',
        '[class*="radar-"]'
      ];

      allDeviceSelectors.forEach(selector => {
        const elements = container.querySelectorAll(selector);
        elements.forEach(element => {
          if (element.parentNode && !element.closest('.cesium-viewer')) {
            element.parentNode.removeChild(element);
            removedDOMCount++;
          }
        });
      });

      if (removedDOMCount > 0) {
        console.log(`✅ 组件销毁时清理DOM完成，共清理 ${removedDOMCount} 个DOM元素`);
      }
    }
  }
};

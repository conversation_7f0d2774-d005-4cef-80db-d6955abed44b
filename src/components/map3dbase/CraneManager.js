import {
  getCraneArea,
  getCraneVerticalProtection,
  getMechanical,
} from '@/api/index'
import { listPage } from '@/api/xxindex'
import Bubble from './Bubble.js'

/**
 * 吊车管理器类
 * 负责处理施工防侵限吊车绘制相关的所有功能
 */
export default class CraneManager {
  constructor(vm) {
    this.vm = vm; // Vue组件实例
    this.craneDataTimer = null; // 吊车数据定时器
    this.radarDataCache = new Map(); // 雷达数据缓存
    console.log('🏗️ CraneManager 初始化完成');
  }

  /**
   * 处理施工高视图
   */
  async handleConstructionHighView() {
    this.setupImagePointClickHandler();

    try {
      // 先绘制固定的防护区域（只绘制一次）
      await this.loadCraneAreaData();
      
      
      // 然后启动吊车数据定时器（只处理动态的吊车数据）
      await this.setupCraneDataTimer();
      await this.loadVideoMonitoringPoints();
      await this.vm.base.constructionAntiInvasion.init();
    } catch (error) {
      console.error('处理施工高视图失败:', error);
    }
  }

  /**
   * 设置图片点位点击事件处理器
   */
  setupImagePointClickHandler() {
    try {
      // 先清理之前的事件监听器
      if (window._imagePointClickHandler) {
        window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }

      // 创建新的点击事件处理器
      window._imagePointClickHandler = (movement) => {
        const picked = window.viewer.scene.pick(movement.position);
        console.log('点击检测:', picked); // 调试信息

        // 检查是否点击到了吊车
        if (picked && picked.id && picked.id._isCrane && picked.id._craneData) {
          console.log('点击到吊车:', picked.id._craneData); // 调试信息
          this.handleCraneClick(picked.id._craneData, picked.id);
          return; // 阻止事件继续冒泡
        }

        // 检查是否点击到了图片点位
        if (picked && picked.id && picked.id._customData && picked.id._customCallback) {
          console.log('触发图片点位点击回调:', picked.id._customData); // 调试信息
          picked.id._customCallback(picked.id._customData, picked.id);
          return; // 阻止事件继续冒泡
        }

        // 如果没有点击到图片点位，执行原有的地图点击逻辑
        this.vm.handleMapClick(picked);
      };

      // 重新注册点击事件
      window.viewer.screenSpaceEventHandler.setInputAction(
        window._imagePointClickHandler,
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      );

      console.log('图片点位点击事件处理器设置完成');

    } catch (error) {
      console.error('设置图片点位点击事件处理器时发生错误:', error);
    }
  }

  /**
   * 处理吊车点击事件
   */
  handleCraneClick(craneData, entity) {
    console.log('🚛 处理吊车点击事件:', craneData);

    // 验证数据完整性
    if (!craneData) {
      console.error('❌ 吊车数据为空');
      return;
    }

    if (!craneData.coordinates || !Array.isArray(craneData.coordinates) || craneData.coordinates.length < 2) {
      console.error('❌ 吊车坐标数据无效:', craneData.coordinates);
      return;
    }

    // 确保坐标是数字类型
    const lng = Number(craneData.coordinates[0]);
    const lat = Number(craneData.coordinates[1]);

    if (isNaN(lng) || isNaN(lat)) {
      console.error('❌ 吊车坐标转换失败:', craneData.coordinates);
      return;
    }

    console.log('✅ 吊车坐标验证通过:', { lng, lat });

    // 创建弹窗数据
    const bubbleData = {
      ...craneData,
      position: [lng, lat, 0], // 添加高度参数
      coordinates: [lng, lat], // 保持原有格式
      // 确保数据完整性
      name: craneData.name || '未知吊车',
      id: craneData.id || 'unknown',
      // 添加必要的属性
      data: craneData, // 将原始数据作为data属性传递
      propsData: craneData, // 兼容组件props
      // 确保吊车数据包含必要字段
      craneBusinessRecord: craneData.craneBusinessRecord || {},
      driverList: craneData.driverList || [],
      workState: craneData.workState || 0,
      onlineState: craneData.onlineState || 0,
      limitAlarmFlag: craneData.limitAlarmFlag || false,
      heading: craneData.heading || 0
    };

    console.log('📱 准备创建吊车弹窗:', bubbleData);

    // 验证Bubble构造函数参数
    const bubbleParams = {
      viewer: window.viewer,
      data: bubbleData,
      type: 0,
      component: 'DcCarousel',
      store: this.vm.$store,
      router: this.vm.$router
    };

    console.log('🔧 Bubble构造参数:', bubbleParams);

    try {
      // 先关闭已有弹窗
      if (this.vm.bubbles) {
        this.vm.bubbles.windowClose();
        this.vm.bubbles = null;
      }

      // 创建新的弹窗
      this.vm.bubbles = new Bubble(bubbleParams);
      console.log('✅ 吊车弹窗创建成功');

    } catch (error) {
      console.error('❌ 创建吊车弹窗失败:', error);
    }
  }

  /**
   * 设置吊车数据定时器
   */
  async setupCraneDataTimer() {
    const fetchAndRedrawCraneData = async () => {
      try {
        console.log('🔄 开始获取和重绘吊车数据...');
        
        // 检查地图上是否存在吊车雷达图
        const radarExists = this.checkCraneRadarExists();
        
        if (!radarExists) {
          console.log('🎯 地图上不存在吊车雷达图，强制创建所有吊车雷达图...');
          // 清空缓存，强制重新创建
          if (this.radarDataCache) {
            this.radarDataCache.clear();
            console.log('🧹 已清空雷达数据缓存，强制重新创建');
          }
        } else {
          console.log('✅ 地图上存在吊车雷达图，继续执行数据变化检测逻辑...');
        }
        await this.loadVerticalProtectionData();
        // 注意：防护区域是固定的，不需要在定时器中重复绘制
        // 防护区域已在 handleConstructionHighView 中初始化绘制

        let { data: mechanical } = await getMechanical();
        console.log('获取到的吊车数据:', mechanical);

        if (!mechanical || !Array.isArray(mechanical)) {
          console.warn('吊车数据格式不正确:', mechanical);
          return;
        }

        // 检查Cesium viewer是否可用
        if (!window.viewer || window.viewer.isDestroyed()) {
          console.error('Cesium viewer 不可用，跳过吊车数据处理');
          return;
        }

        const processCranePromises = mechanical.map(async (item, index) => {
          try {
            console.log(`处理第 ${index + 1} 个吊车数据:`, item);

            if (!item.craneBusinessRecord || !item.equipmentLocation) {
              console.warn('跳过不完整的吊车数据:', item);
              return;
            }

            const processedData = this.processCraneData(item, index);

            if (!processedData) {
              console.warn('吊车数据处理失败，跳过创建雷达图');
              return;
            }

            if (!window.viewer || window.viewer.isDestroyed()) {
              console.error('Cesium viewer 不可用，跳过雷达图创建');
              return;
            }

            // 为每个吊车创建雷达图，包含错误处理
            await this.createRadar(processedData);
            console.log(`✅ 吊车 ${processedData.id} 雷达图创建成功`);

          } catch (error) {
            console.error(`❌ 处理吊车数据失败 (设备 ${item.id || index}):`, error);
            // 继续处理其他吊车，不因为单个失败而中断
          }
        });

        // 等待所有吊车处理完成
        await Promise.allSettled(processCranePromises);
        
        // 清理不再存在的吊车实体
        this.cleanupRemovedCranes(mechanical);
        
        
        console.log('🎉 所有吊车数据处理完成');

      } catch (error) {
        console.error('❌ 处理吊车数据时发生错误:', error);
        // 定时器会继续运行，下次再试
      }
    };

    // 清理可能存在的旧定时器
    if (this.craneDataTimer) {
      console.log('🧹 清理旧的吊车数据定时器');
      clearInterval(this.craneDataTimer);
      this.craneDataTimer = null;
    }

    // 立即执行一次获取数据和重绘
    await fetchAndRedrawCraneData();

    // 设置定时器，每10秒执行一次获取数据和重绘
    this.craneDataTimer = setInterval(fetchAndRedrawCraneData, 10 * 1000);
    console.log('⏰ 吊车数据定时器已启动，间隔10秒');
  }

  /**
   * 处理吊车数据
   */
  processCraneData(item, index) {
    try {
      const lng = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLng) || 0);
      const lat = parseFloat((item.equipmentLocation && item.equipmentLocation.equipmentLat) || 0);

      console.log('设备坐标:', { lng, lat });

      if (lng === 0 || lat === 0 || isNaN(lng) || isNaN(lat)) {
        console.warn('跳过坐标无效的设备:', item.equipmentLocation?.equipmentName || '未知设备');
        return null;
      }

      let key = (item.equipmentLocation && item.equipmentLocation.equipmentId) || item.id || `mechanical_${index}`;
      const mechanicalId = item.id || item.mechanicalId || key;

      // 安全获取北斗设备航向角
      let beidouHeading = 0;
      try {
        if (item.beidouDevice && item.beidouDevice.deviceAttributeStatusList) {
          const headingAttr = item.beidouDevice.deviceAttributeStatusList.find(i => i.propCode === 'heading');
          if (headingAttr && headingAttr.propValue) {
            beidouHeading = Number(headingAttr.propValue) || 0;
          }
        }
      } catch (error) {
        console.warn('获取北斗航向角失败:', error);
      }

      return {
        bigArmLen: item.craneBusinessRecord.bigArmLen || 0,
        moment: item.craneBusinessRecord.moment || 0,
        armForce: item.craneBusinessRecord.armForce || 0,
        rotation: item.craneBusinessRecord.rotation || 0,
        heading: beidouHeading,
        weight: item.craneBusinessRecord.weight || 0,
        windSpeed: item.craneBusinessRecord.windSpeed || 0,
        elevationAngle: item.craneBusinessRecord.pitch || 0,
        horizontalAngleX: item.craneBusinessRecord.levelX || 0,
        horizontalAngleY: item.craneBusinessRecord.levelY || 0,
        tiltAngle: item.craneBusinessRecord.inclination || 0,
        name: item.name || '未知设备',
        id: key,
        coordinates: [lng, lat],
        targetPositions: item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0
          ? item.verticalProtectionAreaList.map(area => ({
            middlePoint: area.middlePoint || null,
            distance: area.distance || 0,
          })).filter(item => item.middlePoint !== null)
          : [],
        distance: (item.verticalProtectionAreaList && item.verticalProtectionAreaList.length > 0 && item.verticalProtectionAreaList[0].distance) || 0,
        onlineState: (item.device && item.device.onlineState) || 0,
        workState: item.beidouDevice?.onlineState || 0,
        alarmFlag: item.alarmFlag || false,
        limitAlarmFlag: item.limitAlarmFlag || false,
        newlestLimitAlarm: item.newlestLimitAlarm || {},
      };
    } catch (error) {
      console.error('处理吊车数据时发生错误:', error);
      return null;
    }
  }

  /**
   * 检查地图上是否存在吊车雷达图
   * @returns {boolean} 是否存在吊车雷达图
   */
  checkCraneRadarExists() {
    if (!window.viewer) {
      console.warn('Cesium viewer 未初始化，无法检查吊车雷达图');
      return false;
    }

    // 检查是否存在吊车实体
    const craneEntities = window.viewer.entities.values.filter(entity => {
      return entity.id && (
        entity.id.startsWith('crane_') ||
        entity._isCrane === true ||
        entity._craneData
      );
    });

    // 检查是否存在HTML标签
    const radarLabels = document.querySelectorAll('[id^="radar-label"]');

    const exists = craneEntities.length > 0 || radarLabels.length > 0;

    console.log(`🔍 检查吊车雷达图存在性: 实体${craneEntities.length}个, HTML标签${radarLabels.length}个, 存在: ${exists}`);
    
    return exists;
  }

  /**
   * 加载吊车作业区域数据
   */
  async loadCraneAreaData() {
    const { data: craneArea } = await getCraneArea({
      page: {
        current: -1,
        size: 9999,
      },
      customQueryParams: {
        keyword: ""
      },
    });
    
    console.log('🏗️ 开始绘制吊车作业区域，共', craneArea.records.length, '个区域');
    
    craneArea.records.forEach((item, index) => {
      console.log(item);
      if (item.enabledFlag === 1) {
        let lineArr = JSON.parse(item.location);
        
        // 定义实体ID
        const polygonId = `craneAreaPolygon_${item.id || index}`;
        const lineId = `craneAreaLine_${item.id || index}`;

        // 检查是否已存在，避免重复绘制
        const existingPolygon = window.viewer.entities.getById(polygonId);
        const existingLine = window.viewer.entities.getById(lineId);
        
        if (existingPolygon || existingLine) {
          console.log(`⚠️ 吊车作业区域 ${item.id || index} 已存在，跳过绘制`);
          return;
        }

        this.vm.addMapElement(window.viewer, {
          id: polygonId,
          type: 'polygon',
          positions: lineArr,
          color: item.alarmFlag ? '#C40003' : '#F68F21'
        });
        this.vm.addMapElement(window.viewer, {
          id: lineId,
          type: 'line',
          positions: [...lineArr, lineArr[0], lineArr[1]],
          color: item.alarmFlag ? '#C40003' : '#F68F21',
          width: 3,
          dashPattern: true
        });
        
        console.log(`✅ 吊车作业区域 ${item.id || index} 绘制完成`);
      }
    });
  }

  /**
   * 加载垂直防护数据
   */
  async loadVerticalProtectionData() {
    console.log('loadVerticalProtectionData');
    const { data: verticalProtection } = await getCraneVerticalProtection({
      page: {
        current: -1,
        size: 9999,
      },
      customQueryParams: {
        keyword: ""
      },
    });

    console.log('🛡️ 开始绘制垂直防护区域，共', verticalProtection.records.length, '个区域');
    
    verticalProtection.records.forEach((item, index) => {
      if (item.enabledFlag === 1) {
        // 检查是否已存在，避免重复绘制
        const labelId = `verticalProtectionLabel_${item.id || index}`;
        const existingLabel = window.viewer.entities.getById(labelId);
        
        if (existingLabel) {
          this.checkVerticalProtectionExists(item, index);
          console.log(`⚠️ 垂直防护区域 ${item.id || index} 已存在，跳过绘制`);
          return;
        }
        
        this.drawVerticalProtectionArea(item, index);
        console.log(`✅ 垂直防护区域 ${item.id || index} 绘制完成`);
      } else {
        this.checkVerticalProtectionExists(item, index);
      }
    });
  }
  checkVerticalProtectionExists(item, index) {
    const labelId = `verticalProtectionLabel_${item.id || index}`;
    const existingLabel = window.viewer.entities.getById(labelId);
    const polygonId = `verticalProtectionPolygon_${item.id || index}`;
    const lineId = `verticalProtectionLine_${item.id || index}`;
    const existingPolygon = window.viewer.entities.getById(polygonId);
    const existingLine = window.viewer.entities.getById(lineId);
    let color = item.alarmFlag ? '#C40003' : '#4FC2FF'
    
    if (existingLabel) {
      let lineArr = JSON.parse(item.location);
      existingPolygon.polygon.positions = lineArr
      existingPolygon.polygon.material = Cesium.Color.fromCssColorString(color).withAlpha(0.25)
      let linePositions = [...lineArr, lineArr[0]];
      existingLine.polyline.positions = Cesium.Cartesian3.fromDegreesArray(linePositions.flat ? linePositions.flat() : linePositions)
      existingLine.polyline.material = new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.fromCssColorString(color),
        dashLength: 6
      })
      console.log(`⚠️ 垂直防护区域 ${item.id || index} 已存在，跳过绘制`);
    }
  }

  /**
   * 绘制垂直防护区域
   */
  drawVerticalProtectionArea(item, index) {
    try {
      console.log('verticalProtection item.location:', item.location);
      let lineArr = JSON.parse(item.location);
      console.log('解析后的lineArr:', lineArr);

      // 验证数据格式
      if (!Array.isArray(lineArr) || lineArr.length < 3) {
        console.warn('verticalProtection lineArr格式无效:', lineArr);
        return;
      }

      // 定义实体ID
      const polygonId = `verticalProtectionPolygon_${item.id || index}`;
      const lineId = `verticalProtectionLine_${item.id || index}`;

      // 添加多边形
      this.vm.addMapElement(window.viewer, {
        id: polygonId,
        type: 'polygon',
        positions: lineArr,
        color: item.alarmFlag ? '#C40003' : '#4FC2FF'
      });

      // 为线条创建闭合路径
      let linePositions = [...lineArr, lineArr[0]];
      this.vm.addMapElement(window.viewer, {
        id: lineId,
        type: 'line',
        positions: linePositions,
        width: 1,
        color: item.alarmFlag ? '#C40003' : '#4FC2FF',
        dashPattern: true,
        dashLength: 6.0
      });

      // 添加标签
      this.addVerticalProtectionLabel(item, index, lineArr);
    } catch (error) {
      console.error('处理verticalProtection时发生错误:', error);
      console.error('item.location:', item.location);
    }
  }

  /**
   * 添加垂直防护区域标签
   */
  addVerticalProtectionLabel(item, index, lineArr) {
    // 计算中心点位置
    let centerLng, centerLat;
    if (item.centerLon && item.centerLat) {
      centerLng = item.centerLon;
      centerLat = item.centerLat;
    } else {
      const firstPoint = lineArr[0];
      if (Array.isArray(firstPoint) && firstPoint.length >= 2) {
        centerLng = firstPoint[0];
        centerLat = firstPoint[1];
      } else {
        centerLng = lineArr[0];
        centerLat = lineArr[1];
      }
    }

    // 定义标签ID
    const labelId = `verticalProtectionLabel_${item.id || index}`;

    // 添加以图片为背景的、大小固定的文字标签
    window.viewer.entities.add({
      id: labelId,
      name: item.regionName,
      position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat),
      billboard: {
        image: item.alarmFlag ? require('../../assets/images/map/logo_alarm.png') : require('../../assets/images/map/logo.png'),
        scale: 1.0,
        sizeInMeters: false,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      label: {
        text: item.regionName,
        font: '14px "PingFang SC"',
        fillColor: Cesium.Color.fromCssColorString('#ffffff'),
        style: Cesium.LabelStyle.FILL,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(-20, -2.5),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        scale: 1.0,
        scaleByDistance: undefined,
        translucencyByDistance: undefined
      }
    });
  }

  /**
   * 加载视频监控点位
   */
  async loadVideoMonitoringPoints() {
    const { data: { records: videoList } } = await listPage({
      page: {
        current: -1,
        size: 9999,
      },
      customQueryParams: {
        typeNames: ['视频监控']
      },
    });

    console.log('获取到的视频监控点位:', videoList);
    const points = videoList
      .filter(item => item.equipmentLng !== null && item.equipmentLat !== null)
      .map(item => ({
        id: item.equipmentId,
        lng: Number(item.equipmentLng),
        lat: Number(item.equipmentLat),
        image: this.vm.deviceImageUrl[item.equipmentTypeName] || this.vm.deviceImageUrl['通用设备'],
        name: item.equipmentName,
        data: item
      }));

    this.vm.addImagePoints2(points, (data, entity) => {
      console.log('点击回调触发:', data, entity);
      this.vm.createBubble(
        {
          ...data,
          position: [
            Number(data.equipmentLng),
            Number(data.equipmentLat)
          ]
        },
        0,
        'CustomPopup'
      );
    }, 'videoMonitoring_');
  }

  /**
   * 生成雷达数据的哈希值，用于检测数据变化
   */
  generateRadarDataHash(deviceData) {
    // 提取关键数据字段
    const keyData = {
      id: deviceData.id,
      coordinates: deviceData.coordinates,
      armForce: deviceData.armForce,
      bigArmLen: deviceData.bigArmLen,
      onlineState: deviceData.onlineState,
      limitAlarmFlag: deviceData.limitAlarmFlag,
      workState: deviceData.workState,
      heading: deviceData.heading,  // 添加航向角字段
      targetPositions: deviceData.targetPositions?.map(pos => ({
        middlePoint: pos.middlePoint,
        distance: pos.distance
      })) || []
    };

    // 生成简单的哈希值
    return JSON.stringify(keyData);
  }

  /**
   * 清理雷达数据缓存
   * @param {string} deviceId - 设备ID，不传则清理所有缓存
   */
  clearRadarDataCache(deviceId = null) {
    if (!this.radarDataCache) {
      this.radarDataCache = new Map();
      return;
    }

    if (deviceId) {
      this.radarDataCache.delete(deviceId);
      console.log(`已清理设备 ${deviceId} 的雷达数据缓存`);
    } else {
      this.radarDataCache.clear();
      console.log('已清理所有雷达数据缓存');
    }
  }

  /**
   * 精细化清理指定设备的相关实体
   * @param {string} baseId - 设备ID
   * @param {Object} newDeviceData - 新的设备数据
   */
  clearSpecificCraneEntities(baseId, newDeviceData) {
    if (!window.viewer) return;

    console.log(`🧹 精细化清理设备 ${baseId} 的相关实体`);

    // 获取旧数据进行对比
    const oldDataStr = this.radarDataCache.get(baseId);
    let oldDeviceData = null;
    try {
      oldDeviceData = oldDataStr ? JSON.parse(oldDataStr) : null;
    } catch (error) {
      console.warn('解析旧数据失败，将清理所有相关实体');
    }

    // 需要清理的实体ID列表
    const entitiesToRemove = [];

    // 1. 检查基础实体（图标、圆形、扫描线）是否需要重绘
    if (!oldDeviceData || 
        oldDeviceData.onlineState !== newDeviceData.onlineState ||
        oldDeviceData.limitAlarmFlag !== newDeviceData.limitAlarmFlag ||
        oldDeviceData.armForce !== newDeviceData.armForce ||
        oldDeviceData.bigArmLen !== newDeviceData.bigArmLen ||
        oldDeviceData.heading !== newDeviceData.heading ||  // 添加航向角变化检测
        JSON.stringify(oldDeviceData.coordinates) !== JSON.stringify(newDeviceData.coordinates)) {
      
      // 清理基础实体
      entitiesToRemove.push(
        `crane_icon_${baseId}`,
        `crane_circle_${baseId}`,
        `crane_scanning_line_${baseId}`
      );

      // 清理虚线圆的多个线段
      for (let i = 0; i < 60; i++) {
        entitiesToRemove.push(`crane_dashed_line_${baseId}_${i}`);
      }

      console.log(`📍 设备 ${baseId} 基础属性发生变化，将重绘图标和圆形`);
    }

    // 2. 检查目标位置是否发生变化
    const oldTargets = oldDeviceData?.targetPositions || [];
    const newTargets = newDeviceData?.targetPositions || [];
    
    if (JSON.stringify(oldTargets) !== JSON.stringify(newTargets)) {
      // 清理所有目标位置相关的实体
      const maxTargets = Math.max(oldTargets.length, newTargets.length, 10); // 预留更多清理空间
      for (let i = 0; i < maxTargets; i++) {
        entitiesToRemove.push(
          `${baseId}_target_line_${i}`,
          `${baseId}_target_arrow_${i}`
        );
      }
      
      // 清理标签元素
      for (let i = 0; i < Math.max(oldTargets.length, 10); i++) {
        const labelElement = document.getElementById(`radar-label-safety-${baseId}-${i}`);
        if (labelElement) {
          labelElement.remove();
        }
      }
      console.log(`🎯 设备 ${baseId} 目标位置发生变化，将重绘目标线和标签`);
    }

    // 3. 清理主标签和半径标签（如果基础属性变化）
    if (!oldDeviceData || 
        oldDeviceData.name !== newDeviceData.name ||
        oldDeviceData.onlineState !== newDeviceData.onlineState ||
        oldDeviceData.armForce !== newDeviceData.armForce ||
        oldDeviceData.bigArmLen !== newDeviceData.bigArmLen) {
      
      // 清理HTML标签
      const labelsToRemove = [
        `radar-label-${baseId}`,
        `radar-label-radius-${baseId}`,
        `radar-label-overturn-${baseId}`
      ];

      labelsToRemove.forEach(labelId => {
        const labelElement = document.getElementById(labelId);
        if (labelElement) {
          labelElement.remove();
        }
      });
    }

    // 执行实体清理
    let removedCount = 0;
    entitiesToRemove.forEach(entityId => {
      const entity = window.viewer.entities.getById(entityId);
      if (entity) {
        window.viewer.entities.remove(entity);
        removedCount++;
      }
    });

    console.log(`✅ 精细化清理完成，共移除 ${removedCount} 个实体`);
  }

  /**
   * 清理不再存在的吊车实体
   * @param {Array} currentMechanicalData - 当前的机械设备数据
   */
  cleanupRemovedCranes(currentMechanicalData) {
    if (!window.viewer || !this.radarDataCache) return;

    // 获取当前数据中的所有吊车ID
    const currentCraneIds = new Set();
    currentMechanicalData.forEach(item => {
      if (item.craneBusinessRecord && item.equipmentLocation) {
        const processedData = this.processCraneData(item, 0);
        if (processedData && processedData.id) {
          currentCraneIds.add(processedData.id);
        }
      }
    });

    // 获取缓存中的所有吊车ID
    const cachedCraneIds = Array.from(this.radarDataCache.keys());

    // 找出需要清理的吊车ID（缓存中有但当前数据中没有的）
    const cranesToRemove = cachedCraneIds.filter(craneId => !currentCraneIds.has(craneId));

    if (cranesToRemove.length > 0) {
      console.log(`🧹 发现 ${cranesToRemove.length} 个需要清理的吊车:`, cranesToRemove);
      
      cranesToRemove.forEach(craneId => {
        // 清理实体
        this.clearCraneEntities(craneId);
        // 清理缓存
        this.radarDataCache.delete(craneId);
        console.log(`🗑️ 已清理不存在的吊车: ${craneId}`);
      });
      
      console.log(`✅ 清理不存在的吊车完成，共清理 ${cranesToRemove.length} 个`);
    } else {
      console.log('✅ 没有需要清理的不存在吊车');
    }
  }

  /**
   * 清理吊车实体
   * @param {string} [craneId] - 吊车ID，不传则清理所有吊车实体
   * @param {string} [type] - 类型，可选：'radar'（圆心/雷达）、'target'（目标线/标签），不传则全部清理
   */
  clearCraneEntities(craneId, type) {
    if (!window.viewer) return;

    console.log(`🧹 开始彻底清理吊车实体 - ID: ${craneId || '全部'}, 类型: ${type || '全部'}`);

    let entitiesToRemove = [];

    if (!craneId) {
      // 不传ID，彻底清理所有吊车相关实体
      entitiesToRemove = window.viewer.entities.values.filter(entity => {
        if (!entity.id) return false;
        
        // 检查所有可能的吊车相关实体ID模式
        const isCraneEntity = 
          entity.id.startsWith('crane_') ||           // 基础吊车实体
          entity.id.includes('_target_line_') ||      // 目标线段
          entity.id.includes('_target_arrow_') ||     // 目标箭头
          entity.id.includes('crane') ||              // 包含crane的实体
          entity._isCrane === true ||                 // 标记为吊车的实体
          entity._craneData;                          // 包含吊车数据的实体
          
        if (isCraneEntity) {
          console.log(`🎯 标记清理吊车实体: ${entity.id}`);
        }
        
        return isCraneEntity;
      });

      // 彻底清理所有HTML标签
      const allRadarLabels = document.querySelectorAll('[id^="radar-label"]');
      allRadarLabels.forEach(label => {
        if (label.parentNode) {
          label.parentNode.removeChild(label);
          console.log(`🧹 移除所有HTML标签: ${label.id}`);
        }
      });

      // 额外清理可能遗留的标签
      const additionalSelectors = [
        '.radar-label',
        '.radar-label-north', 
        '.radar-label-east',
        '.radar-label-content',
        '[class*="radar-label"]'
      ];
      
      additionalSelectors.forEach(selector => {
        const labels = document.querySelectorAll(selector);
        labels.forEach(label => {
          if (label.parentNode) {
            label.parentNode.removeChild(label);
            console.log(`🧹 移除遗留HTML标签: ${label.id || label.className}`);
          }
        });
      });

      // 清理雷达数据缓存
      if (this.radarDataCache) {
        this.radarDataCache.clear();
        console.log('🧹 已清理所有雷达数据缓存');
      }

      console.log(`🧹 准备清理所有吊车实体，共 ${entitiesToRemove.length} 个`);
    } else {
      // 传入了具体的吊车ID，清理指定吊车的实体
      console.log(`🎯 清理指定吊车 ${craneId} 的实体`);

      // 需要清理的前缀和模式
      let prefixes = [];
      let patterns = [];

      if (!type) {
        // 全部类型
        prefixes = [
          `crane_icon_${craneId}`,              // 中心图标
          `crane_circle_${craneId}`,            // 基础圆形
          `crane_scanning_line_${craneId}`,     // 扫描线
          `${craneId}_target_line_`,            // 目标线段
          `${craneId}_target_arrow_`,           // 目标箭头
        ];

        // 批量创建的实体模式（如虚线圆的60个线段）
        patterns = [
          `crane_dashed_line_${craneId}_`, // 虚线圆的多个线段
        ];
      } else if (type === 'radar') {
        prefixes = [
          `crane_icon_${craneId}`,
          `crane_circle_${craneId}`,
          `crane_scanning_line_${craneId}`
        ];

        patterns = [
          `crane_dashed_line_${craneId}_`,
        ];
      } else if (type === 'target') {
        prefixes = [
          `${craneId}_target_line_`,
          `${craneId}_target_arrow_`,
        ];
      }

      // 筛选需要移除的实体
      entitiesToRemove = window.viewer.entities.values.filter(entity => {
        if (!entity.id) return false;

        // 检查是否匹配任何前缀
        const matchesPrefix = prefixes.some(prefix => entity.id.startsWith(prefix));

        // 检查是否匹配任何模式
        const matchesPattern = patterns.some(pattern => entity.id.startsWith(pattern));

        // 检查是否包含吊车ID
        const containsCraneId = 
          entity.id.includes(`_${craneId}_`) || 
          entity.id === craneId ||
          entity.id.includes(`-${craneId}-`) ||
          entity.id.includes(`${craneId}_`) ||
          entity.id.includes(`_${craneId}`);

        // 检查是否为该吊车的实体
        const isCraneEntity = entity._craneData && entity._craneData.id === craneId;

        const shouldRemove = matchesPrefix || matchesPattern || containsCraneId || isCraneEntity;

        if (shouldRemove) {
          console.log(`🎯 标记删除指定吊车实体: ${entity.id}`);
        }

        return shouldRemove;
      });

      // 清理指定吊车的所有HTML标签
      const labelSelectors = [
        `radar-label-${craneId}`,                    // 主标签
        `radar-label-radius-${craneId}`,             // 作业半径标签  
        `radar-label-overturn-${craneId}`,           // 侵覆半径标签
      ];

      // 清理基础标签
      labelSelectors.forEach(selector => {
        const label = document.getElementById(selector);
        if (label && label.parentNode) {
          label.parentNode.removeChild(label);
          console.log(`🧹 移除HTML标签: ${selector}`);
        }
      });

      // 清理安全限界标签（可能有多个）
      for (let i = 0; i < 20; i++) { // 预留足够的索引清理空间
        const safetyLabelId = `radar-label-safety-${craneId}-${i}`;
        const safetyLabel = document.getElementById(safetyLabelId);
        if (safetyLabel && safetyLabel.parentNode) {
          safetyLabel.parentNode.removeChild(safetyLabel);
          console.log(`🧹 移除安全限界HTML标签: ${safetyLabelId}`);
        }
      }

      // 清理所有以该吊车ID开头的HTML标签
      const allLabels = document.querySelectorAll(`[id^="radar-label-${craneId}"]`);
      allLabels.forEach(label => {
        if (label.parentNode) {
          label.parentNode.removeChild(label);
          console.log(`🧹 移除HTML标签: ${label.id}`);
        }
      });

      console.log(`🧹 准备清理吊车 ${craneId} 的实体，共 ${entitiesToRemove.length} 个`);
    }

    // 执行实体移除
    let removedCount = 0;
    entitiesToRemove.forEach(entity => {
      try {
        window.viewer.entities.remove(entity);
        removedCount++;
      } catch (error) {
        console.error(`❌ 移除实体失败: ${entity.id}`, error);
      }
    });

    console.log(`🎉 吊车实体清理完成，共移除 ${removedCount} 个实体`);
  }

  /**
   * 创建雷达图（重要：从CesiumBaseMap.js复制的核心方法）
   */
  async createRadar(deviceData) {
    console.log('createRadar', deviceData)

    // 1. 基本验证
    if (!window.viewer) {
      console.error('Cesium viewer 未初始化')
      return
    }

    if (!deviceData?.coordinates?.length) {
      console.error('无效的坐标数据')
      return
    }

    const baseId = deviceData.id || 'default';

    // 2. 数据变化检测
    const currentDataHash = this.generateRadarDataHash(deviceData);
    
    // 检查是否已存在相同ID的雷达数据
    if (!this.radarDataCache) {
      this.radarDataCache = new Map();
    }
    
    const existingHash = this.radarDataCache.get(baseId);
    if (existingHash === currentDataHash) {
      console.log(`雷达 ${baseId} 数据未发生变化，跳过重绘`);
      return;
    }

    // 3. 数据发生变化，只清理并重绘变化的实体
    console.log(`雷达 ${baseId} 数据发生变化，开始重新绘制变化的实体`);
    try {
      // 精细化清理：只清理当前设备的相关实体
      this.clearSpecificCraneEntities(baseId, deviceData);
      
      // 更新缓存
      this.radarDataCache.set(baseId, currentDataHash);

      // 清理可能残留的相机事件监听器
      if (window.viewer && window.viewer.camera) {
        const cameraEvents = window.viewer.camera.changed._listeners;
        if (cameraEvents && cameraEvents.length > 50) {
          console.warn(`⚠️ 检测到相机事件监听器过多 (${cameraEvents.length} 个)，可能存在内存泄漏`);
        }
      }

      // 清理场景渲染事件监听器
      if (window.viewer && window.viewer.scene) {
        const sceneEvents = window.viewer.scene.postRender._listeners;
        if (sceneEvents && sceneEvents.length > 50) {
          console.warn(`⚠️ 检测到场景渲染事件监听器过多 (${sceneEvents.length} 个)，可能存在内存泄漏`);
        }
      }

    } catch (error) {
      console.warn('清理吊车实体时发生错误:', error);
    }

    const [lng, lat] = deviceData.coordinates
    console.log('原始坐标:', { lng, lat })

    // 检查 viewer 状态
    console.log('Viewer 状态:', {
      isDestroyed: window.viewer.isDestroyed(),
      scene: {
        mode: window.viewer.scene.mode,
        globe: window.viewer.scene.globe.show
      }
    })

    try {
      // 确保时钟启用
      window.viewer.clock.shouldAnimate = true;
      window.viewer.scene.globe.enableLighting = false;

      const radarCenter = Cesium.Cartesian3.fromDegrees(lng, lat, 0)

      // 吊车图标
      const craneLabelBg = require('@/assets/images/map/Crane-label4.png')
      const craneZx = require('@/assets/images/map/Crane-zx1.png')
      const craneZy = require('@/assets/images/map/Crane-zy.png')
      const craneLx = require('@/assets/images/map/Crane-lx1.png')
      const craneTj = require('@/assets/images/map/Crane-tj1.png')
      const craneLabelBg2 = require('@/assets/images/map/Crane-label3.png')

      // 半径参数
      const workRadius = Number(deviceData.armForce) || 40;
      const overturnRadius = Number(deviceData.bigArmLen) || 50;

      // 1. 添加中心点图标
      try {
        const iconEntity = window.viewer.entities.add({
          id: `crane_icon_${baseId}`,
          position: radarCenter,
          billboard: {
            image: deviceData.onlineState === 0 ? require('../../assets/images/map/Crane_icon_lx.png') :
              deviceData.limitAlarmFlag ? require('../../assets/images/map/Crane_icon_alarm.png')
                : require('../../assets/images/map/Crane_icon.png'),
            scale: 1,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            show: true
          },
          // 添加吊车数据，用于点击时弹出弹窗
          _craneData: deviceData,
          _isCrane: true
        });
        console.log('中心图标创建完成:', iconEntity);
      } catch (error) {
        console.error('创建中心图标失败:', error);
      }

      // 2. 添加基础圆
      try {
        const circleEntity = window.viewer.entities.add({
          id: `crane_circle_${baseId}`,
          position: radarCenter,
          polygon: {
            hierarchy: new Cesium.CallbackProperty(function () {
              const positions = [];
              const segments = 360;
              for (let i = 0; i <= segments; i++) {
                const angle = (i / segments) * 2 * Math.PI;
                const x = workRadius * Math.cos(angle);
                const y = workRadius * Math.sin(angle);
                const latCorrection = Math.cos(Cesium.Math.toRadians(lat));
                positions.push(
                  Cesium.Cartesian3.fromDegrees(
                    lng + (x / 111000) / latCorrection,
                    lat + (y / 111000),
                    1
                  )
                );
              }
              return new Cesium.PolygonHierarchy(positions);
            }, false),
            material: deviceData.onlineState === 0 ? Cesium.Color.fromCssColorString('#909090').withAlpha(0.9)
              : deviceData.limitAlarmFlag ?
                Cesium.Color.fromCssColorString('#C40003').withAlpha(0.4) :
                Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4),
            outline: true,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            perPositionHeight: false,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            classificationType: Cesium.ClassificationType.TERRAIN,
            show: true
          }
        });
        console.log('基础圆创建完成:', circleEntity);
      } catch (error) {
        console.error('创建基础圆失败:', error);
      }

      // 3. 添加目标位置箭头、线段和安全限界标签（在基础圆之上）
      if (deviceData.targetPositions && Array.isArray(deviceData.targetPositions) && deviceData.targetPositions.length > 0) {
        for (const [index, target] of deviceData.targetPositions.entries()) {
          if (target.middlePoint && Array.isArray(target.middlePoint) && target.middlePoint.length >= 2) {
            try {
              const [startLng, startLat] = deviceData.coordinates;
              const [endLng, endLat] = target.middlePoint;

              // 计算线条角度
              const dx = endLng - startLng;
              const dy = endLat - startLat;
              // SVG箭头默认朝上，rotation需减去Math.PI/2
              const angle = Math.atan2(dy, dx);

              // 获取线条颜色
              const lineColor = Cesium.Color.WHITE;

              // 添加贴地目标线段 - 使用唯一ID避免重复
              const targetLineId = `${deviceData.id}_target_line_${index}`;
              console.log(`准备创建目标线段，ID: ${targetLineId}`);

              // 检查是否已存在同ID的实体
              const existingEntity = window.viewer.entities.getById(targetLineId);
              if (existingEntity) {
                console.warn(`目标线段 ${targetLineId} 已存在，先移除`);
                window.viewer.entities.remove(existingEntity);
              }

              window.viewer.entities.add({
                id: targetLineId,
                polyline: {
                  positions: [
                    Cesium.Cartesian3.fromDegrees(startLng, startLat, 0),
                    Cesium.Cartesian3.fromDegrees(endLng, endLat, 0)
                  ],
                  width: 2,
                  material: lineColor,
                  clampToGround: true,
                  classificationType: Cesium.ClassificationType.TERRAIN,
                  zIndex: 9999 // 设置为最高层级
                }
              });

              function getArrowTrianglePoints(endLng, endLat, angle, length, width) {
                // 箭头尖端与终点重合，tip点高度3米，底边点高度1.5米，扩大显示效果
                const tip = [
                  endLng,
                  endLat,
                  3
                ];
                const leftAngle = angle + Math.PI - Math.PI / 6;
                const rightAngle = angle + Math.PI + Math.PI / 6;
                const left = [
                  endLng + Math.cos(leftAngle) * length,
                  endLat + Math.sin(leftAngle) * length,
                  1.5
                ];
                const right = [
                  endLng + Math.cos(rightAngle) * length,
                  endLat + Math.sin(rightAngle) * length,
                  1.5
                ];
                const bottom = [
                  endLng,
                  endLat,
                  1.5
                ];
                return [tip, left, right, bottom];
              }
              const trianglePoints = getArrowTrianglePoints(endLng, endLat, angle, 0.00003, 0.0001);
              window.viewer.entities.add({
                id: `${deviceData.id}_target_arrow_${index}`,
                polygon: {
                  hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights([
                    ...trianglePoints[0], ...trianglePoints[1], ...trianglePoints[2]
                  ]),
                  material: lineColor,
                  heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                  zIndex: 9999 // 设置为最高层级
                }
              });
              // 添加安全限界标签
              const midPointLng = (startLng + endLng) / 2;
              const midPointLat = (startLat + endLat) / 2;

              const labelText = `${target.distance.toFixed(2)}`;

              const labelDiv = document.createElement('div')
              labelDiv.className = 'radar-label radar-label-east'
              labelDiv.id = `radar-label-safety-${deviceData.id}-${index}`
              labelDiv.innerHTML = `
                <div class="radar-label-content" style="
                  background-image: url('${craneLabelBg2}');
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  background-position: center center;
                  padding: 4px 4px;
                  display: flex;
                  align-items: center;
                  height: 33px;
                ">
                  <div class="radar-label-content-name" 
                    style="color: #fff;
                    font-family: 'HuXiaoBo_KuHei';">
                    <span style="font-size: 14px;">距离安全限界：</span>
                    <span style="font-size: 20px;">${labelText}<span style="font-size: 12px;">m</span></span>
                  </div>
                </div>
              `
              labelDiv.style.position = 'absolute'
              labelDiv.style.pointerEvents = 'auto'
              labelDiv.style.transform = 'translate(-50%, -100%)'
              const container = document.getElementById('base3dmap')
              if (container) {
                container.appendChild(labelDiv)
              }

              function updateLabelPosition() {
                // 计算正东方向的标签位置
                const cartesian = Cesium.Cartesian3.fromDegrees(midPointLng, midPointLat, 0)
                const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
                if (Cesium.defined(canvasPosition)) {
                  labelDiv.style.left = `${canvasPosition.x + 75}px`
                  labelDiv.style.top = `${canvasPosition.y}px`
                }
              }

              window.viewer.camera.changed.addEventListener(updateLabelPosition)
              window.viewer.scene.postRender.addEventListener(updateLabelPosition)
              updateLabelPosition()
              console.log(`目标线段创建成功: ${targetLineId}`);
            } catch (error) {
              console.error(`创建目标线段 ${index} 失败:`, error);
            }
          }
        }
      }

      // 4. 创建固定扫描线（基于航向角）
      try {
        // 获取航向角，默认为0（正北方向）
        const headingAngle = Number(deviceData.heading) || 0;
        console.log(headingAngle, '航向角123123123')
        // 将航向角转换为弧度，顺时针为正，正北为0度
        // 注意：数学坐标系中，0度是正东方向，而航向角0度是正北方向
        // 所以需要转换：航向角度数 -> 数学角度 = 90° - 航向角度数
        const mathAngleInDegrees = 90 - headingAngle;
        const angleInRadians = Cesium.Math.toRadians(mathAngleInDegrees);

        // 计算扫描线终点坐标
        const x = workRadius * Math.cos(angleInRadians);
        const y = workRadius * Math.sin(angleInRadians);
        const scanLineLatCorrection = Math.cos(Cesium.Math.toRadians(lat));

        const scanLineEndPos = Cesium.Cartesian3.fromDegrees(
          lng + (x / 111000) / scanLineLatCorrection,
          lat + (y / 111000),
          0
        );

        // 添加固定扫描线
        const scanningLine = window.viewer.entities.add({
          id: `crane_scanning_line_${baseId}`,
          polyline: {
            positions: [radarCenter, scanLineEndPos],
            width: 4,
            material: deviceData.onlineState === 0 ?
              new Cesium.ColorMaterialProperty({
                color: Cesium.Color.fromCssColorString('#909090').withAlpha(1),
                dashLength: 16.0
              }) : deviceData.limitAlarmFlag ?
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#C40003').withAlpha(1),
                  dashLength: 16.0
                }) :
                new Cesium.ColorMaterialProperty({
                  color: Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(1),
                  dashLength: 16.0
                }),
            clampToGround: true,
            classificationType: Cesium.ClassificationType.BOTH
          }
        });

        console.log(`扫描线创建完成，航向角: ${headingAngle}°, 数学角度: ${mathAngleInDegrees}°`);
      } catch (error) {
        console.error('创建扫描线失败:', error);
      }

      // 添加外圈虚线圆
      try {
        const radius = overturnRadius;
        const segments = 60;
        const dashLength = 8;
        const gapLength = 4;
        const latCorrection = Math.cos(Cesium.Math.toRadians(lat));
        for (let i = 0; i < segments; i++) {
          const startAngle = (i / segments) * 2 * Math.PI;
          const endAngle = ((i + dashLength / (dashLength + gapLength)) / segments) * 2 * Math.PI;
          const startX = radius * Math.cos(startAngle);
          const startY = radius * Math.sin(startAngle);
          const endX = radius * Math.cos(endAngle);
          const endY = radius * Math.sin(endAngle);

          const startPos = Cesium.Cartesian3.fromDegrees(
            lng + (startX / 111000) / latCorrection,
            lat + (startY / 111000),
            0
          );
          const endPos = Cesium.Cartesian3.fromDegrees(
            lng + (endX / 111000) / latCorrection,
            lat + (endY / 111000),
            0
          );

          window.viewer.entities.add({
            id: `crane_dashed_line_${baseId}_${i}`,
            polyline: {
              positions: [startPos, endPos],
              width: 3,
              material: deviceData.onlineState === 0 ? Cesium.Color.fromCssColorString('#909090').withAlpha(0.9)
               : deviceData.limitAlarmFlag ?
                 Cesium.Color.fromCssColorString('#C40003').withAlpha(0.4) :
                 Cesium.Color.fromCssColorString('#6CF8FF').withAlpha(0.4),
              clampToGround: true,
              classificationType: Cesium.ClassificationType.BOTH
            }
          });
        }
        console.log('虚线圆创建完成');
      } catch (error) {
        console.error('创建虚线圆失败:', error);
      }

      // 3. 添加吊车标签
      try {
        const northLat = lat + (overturnRadius / 111000);

        const labelDiv = document.createElement('div')
        labelDiv.className = 'radar-label radar-label-north'
        labelDiv.id = `radar-label-${baseId}`
        labelDiv.innerHTML = `
          <div class="radar-label-content" style="
            background-image: url('${craneLabelBg}');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center center;
            padding: 4px 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 28px;
          ">
            <div class="radar-label-content-name" style="color: #fff; font-size: 14px; font-family: 'HuXiaoBo_KuHei';margin-right: 16px;margin-left: 4px;">${deviceData.name}</div>
            <img src="${deviceData.onlineState === 1 ? craneZx : craneLx}" style="width: auto; height: auto; margin-right: 4px;">
            <img src="${deviceData.workState === 1 ? craneZy : craneTj}" style="width: auto; height: auto;">
            </div>
          </div>
        `
        labelDiv.style.position = 'absolute'
        labelDiv.style.pointerEvents = 'auto'
        labelDiv.style.transform = 'translate(-50%, -100%)'
        const container = document.getElementById('base3dmap')
        if (container) {
          container.appendChild(labelDiv)
        }

        function updateLabelPosition() {
          const cartesian = Cesium.Cartesian3.fromDegrees(lng, northLat, 0)
          const canvasPosition = window.viewer.scene.cartesianToCanvasCoordinates(cartesian)
          if (Cesium.defined(canvasPosition)) {
            labelDiv.style.left = `${canvasPosition.x + 160}px`
            labelDiv.style.top = `${canvasPosition.y}px`
          }
        }

        window.viewer.camera.changed.addEventListener(updateLabelPosition)
        window.viewer.scene.postRender.addEventListener(updateLabelPosition)
        updateLabelPosition()

        console.log('主标签创建完成');
      } catch (error) {
        console.error('创建主标签失败:', error);
      }

      // 添加半径标签
      try {
        // 圆外标签参数 - 统一位置上下排列，锚点在右侧
        const labels = [
          {
            id: 'radius',
            text: '作业半径：',
            value: `${deviceData.armForce}`,
            angle: 90, // 正东方向（右侧）
            radius: Number(deviceData.armForce) || 40,
          },
          {
            id: 'overturn',
            text: '侵覆半径：',
            value: `${deviceData.bigArmLen}`,
            angle: 90, // 正东方向（右侧）
            radius: Number(deviceData.armForce) || 50,
          }
        ];

        // 计算圆外点经纬度（直接用半径，不加margin）
        function getLabelPosition(centerLng, centerLat, radius, angle) {
          const earthRadius = 6378137;
          const rad = angle * Math.PI / 180;
          const dist = radius;
          const dLat = (dist * Math.cos(rad)) / earthRadius;
          const dLng = (dist * Math.sin(rad)) / (earthRadius * Math.cos(centerLat * Math.PI / 180));
          const lat = centerLat + dLat * 180 / Math.PI;
          const lng = centerLng + dLng * 180 / Math.PI;
          return { lng, lat };
        }

        // 只用第一个标签的半径作为所有标签的锚点
        function getAnchorCanvasPosition() {
          const anchor = labels[0];
          const { lng: labelLng, lat: labelLat } = getLabelPosition(
            lng, lat, anchor.radius, anchor.angle
          );
          const anchorCartesian = Cesium.Cartesian3.fromDegrees(labelLng, labelLat, 0);
          return window.viewer.scene.cartesianToCanvasCoordinates(anchorCartesian);
        }

        function updateAllLabelPositions() {
          const baseCanvasPosition = getAnchorCanvasPosition();
          if (baseCanvasPosition && Cesium.defined(baseCanvasPosition)) {
            labels.forEach((labelInfo, index) => {
              const labelDiv = document.getElementById(`radar-label-${labelInfo.id}-${baseId}`);
              if (labelDiv) {
                const pixelGap = 8;
                const labelHeight = labelDiv.offsetHeight || 33;
                labelDiv.style.left = `${baseCanvasPosition.x}px`;
                labelDiv.style.top = `${baseCanvasPosition.y + index * (labelHeight + pixelGap)}px`;
              }
            });
          }
        }

        labels.forEach((labelInfo, i) => {
          try {
            const labelDiv = document.createElement('div')
            labelDiv.className = 'radar-label radar-label-east'
            labelDiv.id = `radar-label-${labelInfo.id}-${baseId}`
            labelDiv.innerHTML = `
              <div class=\"radar-label-content\" style=\"
                background-image: url('${craneLabelBg2}');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: center center;
                padding: 4px 4px;
                display: flex;
                align-item: center;
                height: 33px;
              \">
                <div class="radar-label-content-name" style="display: flex; align-items: center; justify-content: space-between;color: #fff; font-family: 'HuXiaoBo_KuHei';">
                  <span style="font-size: 14px;">${labelInfo.text}</span>
                  <span style="font-size: 20px;">${labelInfo.value}<span style="font-size: 12px;">m</span></span>
                </div>
              </div>
            `
            labelDiv.style.position = 'absolute'
            labelDiv.style.pointerEvents = 'auto'
            labelDiv.style.transform = 'translate(0%, -100%)'
            const container = document.getElementById('base3dmap')
            if (container) {
              container.appendChild(labelDiv)
            }
            console.log(`半径标签 ${labelInfo.id} 创建完成`);
          } catch (error) {
            console.error(`创建半径标签 ${labelInfo.id} 失败:`, error);
          }
        })

        window.viewer.camera.changed.addEventListener(updateAllLabelPositions)
        window.viewer.scene.postRender.addEventListener(updateAllLabelPositions)
        updateAllLabelPositions()
        console.log('所有半径标签创建完成');
      } catch (error) {
        console.error('创建半径标签失败:', error);
      }
      // 先清理该设备之前的弹窗
      const deviceId = deviceData.id || deviceData.mechanicalId;
      if (deviceId && this.vm.bubbles) {
        // 如果存在旧的弹窗，先移除
        this.vm.bubbles.windowClose();
      }
      if (deviceData.limitAlarmFlag) {

       // 创建新的弹窗
       this.vm.createBubble(
         {
           ...deviceData,
           position: [
             Number(deviceData.coordinates[0]),
             Number(deviceData.coordinates[1]),
             0
           ],
           above: true,
           offset: {
             offsetX: 170,
             offsetY: -160
           },
           // 添加唯一标识，便于后续清理
           bubbleId: `alert_${deviceId || Date.now()}`
         },
         0,
         'alert'
       );
      }

      console.log(`🎉 吊车 ${baseId} 雷达图创建完成`);

    } catch (error) {
      console.error(`❌ 创建吊车雷达图失败 (${baseId}):`, error)
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 清理定时器和所有相关资源
   */
  destroy() {
    console.log('🧹 开始清理 CraneManager 资源...');
    
    if (this.craneDataTimer) {
      clearInterval(this.craneDataTimer);
      this.craneDataTimer = null;
      console.log('✅ 已清理吊车数据定时器');
    }
    
    // 清理所有吊车实体
    this.clearCraneEntities();
    
    // 清理防护区域实体
    this.clearProtectionAreaEntities();
    
    // 清理缓存
    if (this.radarDataCache) {
      this.radarDataCache.clear();
      console.log('✅ 已清理雷达数据缓存');
    }
    
    console.log('🎉 CraneManager 资源清理完成');
  }

  /**
   * 清理防护区域相关实体
   */
  clearProtectionAreaEntities() {
    if (!window.viewer) return;

    try {
      console.log('🧹 开始清理防护区域实体...');

      // 需要清理的实体ID前缀
      const prefixesToClear = [
        'verticalProtectionPolygon_',
        'verticalProtectionLine_', 
        'verticalProtectionLabel_',
        'craneAreaPolygon_',  // 修正ID前缀
        'craneAreaLine_'
      ];

      let removedCount = 0;

      // 遍历所有实体，清理匹配前缀的实体
      const entitiesToRemove = window.viewer.entities.values.filter(entity => {
        if (!entity.id || typeof entity.id !== 'string') return false;
        
        return prefixesToClear.some(prefix => entity.id.startsWith(prefix));
      });

      // 批量移除实体
      entitiesToRemove.forEach(entity => {
        try {
          window.viewer.entities.remove(entity);
          removedCount++;
        } catch (error) {
          console.warn(`清理防护区域实体失败 (${entity.id}):`, error);
        }
      });

      console.log(`✅ 防护区域实体清理完成，共清理 ${removedCount} 个`);

    } catch (error) {
      console.error('清理防护区域实体时发生错误:', error);
    }
  }
} 
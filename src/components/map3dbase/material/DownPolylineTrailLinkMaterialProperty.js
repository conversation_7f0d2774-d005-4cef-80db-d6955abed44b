// 动态线材质
class DownPolylineTrailLinkMaterialProperty {
  constructor(color, duration) {
    this._definitionChanged = new Cesium.Event();
    this._color = undefined;
    this._colorSubscription = undefined;
    this.color = color;
    this.duration = duration;
    this._time = new Date().getTime();
  }

  get isConstant() {
    return false;
  }

  get definitionChanged() {
    return this._definitionChanged;
  }

  getType(time) {
    return "PolylineTrailLink";
  }

  getValue(time, result) {
    if (!Cesium.defined(result)) {
      result = {};
    }
    result.color = Cesium.Property.getValueOrClonedDefault(
      this._color,
      time,
      Cesium.Color.WHITE,
      result.color
    );
    result.time =
      ((new Date().getTime() - this._time) % this.duration) / this.duration;
    return result;
  }

  equals(other) {
    return (
      this === other ||
      (other instanceof DownPolylineTrailLinkMaterialProperty &&
        Cesium.Property.equals(this._color, other._color))
    );
  }
}

Object.defineProperties(DownPolylineTrailLinkMaterialProperty.prototype, {
  color: Cesium.createPropertyDescriptor("color"),
});

Cesium.DownPolylineTrailLinkMaterialProperty = DownPolylineTrailLinkMaterialProperty;
Cesium.Material.PolylineTrailLinkType = "PolylineTrailLink";
// Cesium.Material.PolylineTrailLinkImage = require("../../public/img/jcimg/arrow06.svg"); // 道路样式的png
Cesium.Material.PolylineTrailLinkImage = require("../../../../public/img/yjya/dongtailuxian.png");
Cesium.Material.PolylineTrailLinkSource = `
    czm_material czm_getMaterial(czm_materialInput materialInput) {
        czm_material material = czm_getDefaultMaterial(materialInput);
        vec2 st = materialInput.st;
        vec4 colorImage = texture(image, vec2(fract(st.s - time), st.t));
        material.alpha = colorImage.a * color.a;
        material.diffuse = color.rgb;
        return material;
    }
`;

Cesium.Material._materialCache.addMaterial(
  Cesium.Material.PolylineTrailLinkType,
  {
    fabric: {
      type: Cesium.Material.PolylineTrailLinkType,
      uniforms: {
        color: new Cesium.Color(1.0, 1.0, 0.0, 0.5),
        image: Cesium.Material.PolylineTrailLinkImage,
        time: 0,
        constantSpeed: 300,
        depthFailMaterial: true,
      },
      source: Cesium.Material.PolylineTrailLinkSource,
    },
    translucent: function (material) {
      return true;
    },
  }
);

export default DownPolylineTrailLinkMaterialProperty;

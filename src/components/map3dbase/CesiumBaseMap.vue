<template>
  <div id="cesiummapdiv">
    <div id="base3dmap" style="position: relative;"></div>

    <!-- 吊钩可视化弹窗 -->
    <SimpleHookVideoModal
      v-model="showHookVideoModal"
      :device-data="currentHookDevice"
      :video-device-list="hookVideoDeviceList"
    />
  </div>
</template>
<script>
import CesiumBaseMap from './CesiumBaseMap';
import SimpleHookVideoModal from '../modal/SimpleHookVideoModal.vue';

export default {
  ...CesiumBaseMap,
  components: {
    ...CesiumBaseMap.components,
    SimpleHookVideoModal
  }
};
</script>

<style lang="less" scoped>
#cesiummapdiv {
  height: 100%;
  width: 100%;
  // height: 100vh;
  // width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}
#base3dmap {
  height: 100%;
  width: 100%;
}

.imagesArrCal {
  position: absolute;
  left: -100000;
  top: 0;
  visibility: hidden;
}

/deep/.cesium-viewer-toolbar {
  display: none;
}
/deep/.earthview-popup-head {
  color: #5f91fc;
}
/deep/.compass {
  display: none;
  right: 25%;
  top: 130px;
}
/deep/.navigation-controls {
  display: none;
}
/deep/#distanceLegendDiv {
  display: none;
}
/deep/.earthview-popup-body::-webkit-scrollbar {
  height: 0 !important;
  width: 0 !important;
}

.right-bottom-tool {
  position: absolute;
  right: 45px;
  bottom: 9rem;
  display: flex;
  width: 42px;
  height: 94px;
  flex-direction: column;

  .tool-item {
    width: 42px;
    height: 94px;
    // background: #232523;
    // background: url(/img/homeImage/tree-bg.png) no-repeat;
    // border-radius: 4px 4px 4px 4px;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 12px;
    justify-content: center;
    cursor: pointer;

    .item-top {
      width: 42px;
      height: 42px;
      line-height: 42px;
      background: url(/img/homeImage/bigMapLayer.png) no-repeat;
      // margin: 8px 0 3px 0;
    }

    .item-bottom {
      margin-top: 5px;
      font-size: 32px;
      color: #fff;
      line-height: 42px;
      width: 42px;
      height: 42px;
      background: url(/img/homeImage/smallMapLayer.png) no-repeat;
    }
  }

  .tool-item-active {
    background: #4893f8;
  }
}
</style>

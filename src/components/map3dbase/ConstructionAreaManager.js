/**
 * 高支模管理器类
 * 负责处理高支模绘制相关的所有功能
 */
export default class ConstructionAreaManager {
  constructor(vm) {
    this.vm = vm; // Vue组件实例
    console.log("🏗️ ConstructionAreaManager 初始化完成");
  }

  /**
   * 从配置数据中绘制施工区域边界线和高支模
   * @param {Object} shigongAreaData - 施工区域数据
   */
  drawConstructionAreasFromConfig(shigongAreaData) {
    console.log("🏗️ 开始绘制施工区域边界线，数据:", shigongAreaData);

    // 延迟执行，确保viewer完全初始化
    setTimeout(() => {
      if (!window.viewer) {
        console.error("❌ window.viewer未初始化，无法绘制施工区域");
        return;
      }

      try {
        // 解析施工区域数据
        const areas = this.parseConstructionAreaData(shigongAreaData);

        if (!areas || areas.length === 0) {
          console.warn("⚠️ 没有有效的施工区域数据");
          return;
        }

        console.log(`📍 发现 ${areas.length} 个施工区域`);

        // 绘制每个施工区域
        areas.forEach((area, index) => {
          if (area.name.includes("高支模")) {
            this.drawGaoZhiMo(area, index);
          } else {
            this.drawSingleConstructionArea(area, index);
          }
        });
      } catch (error) {
        console.error("❌ 绘制施工区域时发生错误:", error);
      }
    }, 1000); // 延迟1秒执行
  }

  /**
   * 绘制高支模
   * @param {Object} area - 区域数据
   * @param {Number} index - 区域索引
   */
  drawGaoZhiMo(area, index) {
    // 1. 经纬度点数组
    const lngs = [],
      lats = [];
    for (let i = 0; i < area.coordinates.length; i += 2) {
      lngs.push(area.coordinates[i]);
      lats.push(area.coordinates[i + 1]);
    }

    // 2. 计算区域的主方向和边界
    // 2.1 计算区域的主方向（基于区域首边方向）
    const calculateOptimalDirection = () => {
      // 1. 使用区域首点作为原点，确保贴合
      const originLng = lngs[0];
      const originLat = lats[0];

      // 2. 计算区域边界框的主方向
      let minLng = lngs[0],
        maxLng = lngs[0];
      let minLat = lats[0],
        maxLat = lats[0];

      for (let i = 1; i < lngs.length; i++) {
        minLng = Math.min(minLng, lngs[i]);
        maxLng = Math.max(maxLng, lngs[i]);
        minLat = Math.min(minLat, lats[i]);
        maxLat = Math.max(maxLat, lats[i]);
      }

      // 计算边界框的宽高
      const width = maxLng - minLng;
      const height = maxLat - minLat;

      // 根据边界框的宽高比确定主方向
      let ux, uy, vx, vy;

      if (width > height) {
        // 东西方向为主方向
        ux = 1;
        uy = 0;
        vx = 0;
        vy = 1;
      } else {
        // 南北方向为主方向
        ux = 0;
        uy = 1;
        vx = -1;
        vy = 0;
      }

      return {
        originLng: originLng,
        originLat: originLat,
        ux: ux,
        uy: uy,
        vx: vx,
        vy: vy,
      };
    };

    const direction = calculateOptimalDirection();
    const originLng = direction.originLng;
    const originLat = direction.originLat;
    const ux = direction.ux;
    const uy = direction.uy;
    const vx = direction.vx;
    const vy = direction.vy;

    // 2.1.1 输出主方向信息用于调试
    console.log(
      `高支模区域 ${index}: 主方向向量 (${ux.toFixed(4)}, ${uy.toFixed(
        4
      )}), 垂直方向 (${vx.toFixed(4)}, ${vy.toFixed(
        4
      )}), 原点 (${originLng.toFixed(6)}, ${originLat.toFixed(6)})`
    );

    // 验证方向向量的正交性
    const dotProduct = ux * vx + uy * vy;
    console.log(
      `高支模区域 ${index}: 方向向量正交性检查: ${dotProduct.toFixed(
        6
      )} (应该接近0)`
    );

    // 2.2 创建统一的坐标转换函数
    const transformToLngLat = (u, v) => {
      return {
        lng: originLng + u * ux + v * vx,
        lat: originLat + u * uy + v * vy,
      };
    };

    const transformFromLngLat = (lng, lat) => {
      const px = lng - originLng;
      const py = lat - originLat;
      return {
        u: px * ux + py * uy,
        v: px * vx + py * vy,
      };
    };

    // 2.3 将区域点投影到主方向和垂直方向
    let minU = 0,
      maxU = 0,
      minV = 0,
      maxV = 0;
    for (let i = 0; i < lngs.length; i++) {
      const coords = transformFromLngLat(lngs[i], lats[i]);
      const u = coords.u;
      const v = coords.v;
      if (i === 0) {
        minU = maxU = u;
        minV = maxV = v;
      } else {
        minU = Math.min(minU, u);
        maxU = Math.max(maxU, u);
        minV = Math.min(minV, v);
        maxV = Math.max(maxV, v);
      }
    }

    // 8. 使用统一的方向系统
    const finalUx = ux;
    const finalUy = uy;
    const finalVx = vx;
    const finalVy = vy;

    console.log(
      `高支模区域 ${index}: 统一方向 - 主方向 (${finalUx.toFixed(
        4
      )}, ${finalUy.toFixed(4)}), 垂直方向 (${finalVx.toFixed(
        4
      )}, ${finalVy.toFixed(4)})`
    );

    // 输出方向类型
    const directionType = "区域首边方向（自适应）";
    console.log(`高支模区域 ${index}: 方向类型 - ${directionType}`);

    // 2.4 更新坐标转换函数使用统一方向
    const finalTransformToLngLat = (u, v) => {
      return {
        lng: originLng + u * finalUx + v * finalVx,
        lat: originLat + u * finalUy + v * finalVy,
      };
    };

    const finalTransformFromLngLat = (lng, lat) => {
      const px = lng - originLng;
      const py = lat - originLat;
      return {
        u: px * finalUx + py * finalUy,
        v: px * finalVx + py * finalVy,
      };
    };

    // 3. 设定网格尺寸（米）并动态调整
    let gridSizeInMeters = 10; // 初始10米 x 10米的网格

    // 4. 计算网格数量（确保完全覆盖）
    const gridWidthInMeters =
      (maxU - minU) * 111320 * Math.cos((originLat * Math.PI) / 180);
    const gridHeightInMeters = (maxV - minV) * 110540;
    let gridX = Math.ceil(gridWidthInMeters / gridSizeInMeters);
    let gridY = Math.ceil(gridHeightInMeters / gridSizeInMeters);

    // 4.1 动态调整网格密度，确保完全覆盖
    const targetGridX = Math.max(
      3,
      Math.floor(gridWidthInMeters / gridSizeInMeters)
    );
    const targetGridY = Math.max(
      3,
      Math.floor(gridHeightInMeters / gridSizeInMeters)
    );

    // 如果网格数量太少，减小网格尺寸
    if (gridX < targetGridX || gridY < targetGridY) {
      gridSizeInMeters = Math.min(
        gridWidthInMeters / targetGridX,
        gridHeightInMeters / targetGridY
      );
      gridX = Math.ceil(gridWidthInMeters / gridSizeInMeters);
      gridY = Math.ceil(gridHeightInMeters / gridSizeInMeters);
    }

    // 5. 计算每个网格在主方向坐标系下的范围
    const gridWidthInU = (maxU - minU) / gridX;
    const gridHeightInV = (maxV - minV) / gridY;

    // 6. 检查点是否在区域内的函数
    const isPointInPolygon = (pointLng, pointLat) => {
      let inside = false;
      for (let i = 0, j = lngs.length - 1; i < lngs.length; j = i++) {
        if (
          lats[i] > pointLat !== lats[j] > pointLat &&
          pointLng <
            ((lngs[j] - lngs[i]) * (pointLat - lats[i])) / (lats[j] - lats[i]) +
              lngs[i]
        ) {
          inside = !inside;
        }
      }
      return inside;
    };

    console.log(
      `高支模区域 ${index}: 主方向尺寸 ${gridWidthInMeters.toFixed(
        1
      )}m x ${gridHeightInMeters.toFixed(
        1
      )}m, 网格 ${gridX} x ${gridY}, 网格尺寸 ${gridSizeInMeters.toFixed(1)}m`
    );

    // 5.2 验证网格覆盖率
    let coveragePoints = 0;
    let totalPoints = 0;
    for (let ix = 0; ix <= gridX; ix++) {
      for (let iy = 0; iy <= gridY; iy++) {
        const u = minU + ix * gridWidthInU;
        const v = minV + iy * gridHeightInV;
        const coords = finalTransformToLngLat(u, v);
        const lng = coords.lng;
        const lat = coords.lat;
        if (isPointInPolygon(lng, lat)) {
          coveragePoints++;
        }
        totalPoints++;
      }
    }
    console.log(
      `高支模区域 ${index}: 网格覆盖率 ${(
        (coveragePoints / totalPoints) *
        100
      ).toFixed(1)}%`
    );

    // 6.2 找到线段与多边形边界的交点（增强版）
    const findBoundaryIntersection = (
      x1,
      y1,
      x2,
      y2,
      polygonLngs,
      polygonLats
    ) => {
      let closestIntersection = null;
      let minDistance = Infinity;

      // 遍历多边形的每条边
      for (let i = 0; i < polygonLngs.length; i++) {
        const j = (i + 1) % polygonLngs.length;
        const x3 = polygonLngs[i],
          y3 = polygonLats[i];
        const x4 = polygonLngs[j],
          y4 = polygonLats[j];

        // 计算线段交点
        const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
        if (Math.abs(denom) < 1e-10) continue; // 平行线

        const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
        const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

        // 检查交点是否在两条线段上
        if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
          const intersectionX = x1 + t * (x2 - x1);
          const intersectionY = y1 + t * (y2 - y1);

          // 计算到线段起点的距离
          const distance = Math.sqrt(
            (intersectionX - x1) * (intersectionX - x1) +
              (intersectionY - y1) * (intersectionY - y1)
          );

          if (distance < minDistance) {
            minDistance = distance;
            closestIntersection = { lng: intersectionX, lat: intersectionY };
          }
        }
      }

      return closestIntersection;
    };

    // 7. 绘制黄色虚线网格和圆点（按照主方向排列，边缘贴合）
    // 7.1 绘制水平虚线网格（沿主方向）
    for (let iy = 0; iy <= gridY; iy++) {
      const v = minV + iy * gridHeightInV;

      // 找到这条线与区域边界的交点
      let linePositions = [];
      let lastValidPoint = null;

      // 从边界外开始，确保找到第一个交点
      for (let ix = -2; ix <= gridX + 2; ix++) {
        const u = minU + ix * gridWidthInU;
        // 转换回经纬度（使用最终方向）
        const coords = finalTransformToLngLat(u, v);
        const lng = coords.lng;
        const lat = coords.lat;

        if (isPointInPolygon(lng, lat)) {
          if (lastValidPoint === null) {
            // 这是第一个在区域内的点，需要找到边界交点
            const prevU = u - gridWidthInU;
            const prevCoords = finalTransformToLngLat(prevU, v);
            const prevLng = prevCoords.lng;
            const prevLat = prevCoords.lat;
            // 在边界附近插值找到精确的交点
            const intersection = findBoundaryIntersection(
              prevLng,
              prevLat,
              lng,
              lat,
              lngs,
              lats
            );
            if (intersection) {
              linePositions.push(
                Cesium.Cartesian3.fromDegrees(
                  intersection.lng,
                  intersection.lat
                )
              );
            }
          }
          linePositions.push(Cesium.Cartesian3.fromDegrees(lng, lat));
          lastValidPoint = { lng, lat };
        } else if (lastValidPoint !== null) {
          // 从区域内部到外部，找到边界交点
          const intersection = findBoundaryIntersection(
            lastValidPoint.lng,
            lastValidPoint.lat,
            lng,
            lat,
            lngs,
            lats
          );
          if (intersection) {
            linePositions.push(
              Cesium.Cartesian3.fromDegrees(intersection.lng, intersection.lat)
            );
          }
          break;
        }
      }

      if (linePositions.length >= 2) {
        window.viewer.entities.add({
          id: `GAOZHIMO_HORIZONTAL_LINE_${index}_${iy}`,
          name: "高支模水平网格线",
          polyline: {
            positions: linePositions,
            width: 2,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.YELLOW.withAlpha(0.4),
            }),
            clampToGround: true,
          },
          isGaoZhiMo: true,
          _isPermanent: true,
        });
      }
    }

    // 7.2 绘制垂直虚线网格（垂直于主方向）
    for (let ix = 0; ix <= gridX; ix++) {
      const u = minU + ix * gridWidthInU;

      // 找到这条线与区域边界的交点
      let linePositions = [];
      let lastValidPoint = null;

      // 从边界外开始，确保找到第一个交点
      for (let iy = -2; iy <= gridY + 2; iy++) {
        const v = minV + iy * gridHeightInV;
        // 转换回经纬度（使用最终方向）
        const coords = finalTransformToLngLat(u, v);
        const lng = coords.lng;
        const lat = coords.lat;

        if (isPointInPolygon(lng, lat)) {
          if (lastValidPoint === null) {
            // 这是第一个在区域内的点，需要找到边界交点
            const prevV = v - gridHeightInV;
            const prevCoords = finalTransformToLngLat(u, prevV);
            const prevLng = prevCoords.lng;
            const prevLat = prevCoords.lat;
            // 在边界附近插值找到精确的交点
            const intersection = findBoundaryIntersection(
              prevLng,
              prevLat,
              lng,
              lat,
              lngs,
              lats
            );
            if (intersection) {
              linePositions.push(
                Cesium.Cartesian3.fromDegrees(
                  intersection.lng,
                  intersection.lat
                )
              );
            }
          }
          linePositions.push(Cesium.Cartesian3.fromDegrees(lng, lat));
          lastValidPoint = { lng, lat };
        } else if (lastValidPoint !== null) {
          // 从区域内部到外部，找到边界交点
          const intersection = findBoundaryIntersection(
            lastValidPoint.lng,
            lastValidPoint.lat,
            lng,
            lat,
            lngs,
            lats
          );
          if (intersection) {
            linePositions.push(
              Cesium.Cartesian3.fromDegrees(intersection.lng, intersection.lat)
            );
          }
          break;
        }
      }

      if (linePositions.length >= 2) {
        window.viewer.entities.add({
          id: `GAOZHIMO_VERTICAL_LINE_${index}_${ix}`,
          name: "高支模垂直网格线",
          polyline: {
            positions: linePositions,
            width: 2,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.YELLOW.withAlpha(0.4),
            }),
            clampToGround: true,
          },
          isGaoZhiMo: true,
          _isPermanent: true,
        });
      }
    }

    // 7.3 绘制网格交叉点（黄色圆点）
    for (let ix = 0; ix <= gridX; ix++) {
      for (let iy = 0; iy <= gridY; iy++) {
        const u = minU + ix * gridWidthInU;
        const v = minV + iy * gridHeightInV;
        // 转换回经纬度（使用最终方向）
        const coords = finalTransformToLngLat(u, v);
        const lng = coords.lng;
        const lat = coords.lat;

        // 只绘制在区域内的点
        if (isPointInPolygon(lng, lat)) {
          window.viewer.entities.add({
            id: `GAOZHIMO_GRID_POINT_${index}_${ix}_${iy}`,
            name: "高支模网格交叉点",
            position: Cesium.Cartesian3.fromDegrees(lng, lat),
            point: {
              pixelSize: 4,
              color: Cesium.Color.YELLOW.withAlpha(0.4),
              outlineColor: Cesium.Color.YELLOW.withAlpha(0.4),
              outlineWidth: 1,
              heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            isGaoZhiMo: true,
            _isPermanent: true,
          });
        }
      }
    }

    // 7.4 绘制每个格子的米字格线条（严格边界控制）
    const drawGridLine = (pointsArr) => {
      window.viewer.entities.add({
        polyline: {
          positions: pointsArr.map((p) =>
            Cesium.Cartesian3.fromDegrees(p.lng, p.lat)
          ),
          width: 2,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.YELLOW.withAlpha(0.4),
          }),
          clampToGround: true,
        },
        isGaoZhiMo: true,
        _isPermanent: true,
      });
    };

    // 检查点是否在区域内的辅助函数
    const isPointInPolygonStrict = (pointLng, pointLat) => {
      return isPointInPolygon(pointLng, pointLat);
    };

    // 检查线段是否完全在区域内
    const isLineInPolygon = (p1, p2) => {
      const midLng = (p1.lng + p2.lng) / 2;
      const midLat = (p1.lat + p2.lat) / 2;
      return isPointInPolygonStrict(midLng, midLat);
    };

    // 检查线段是否与区域边界相交
    const isLineIntersectsBoundary = (p1, p2) => {
      // 检查线段的多个点是否都在区域内
      const steps = 5;
      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const lng = p1.lng + t * (p2.lng - p1.lng);
        const lat = p1.lat + t * (p2.lat - p1.lat);
        if (!isPointInPolygonStrict(lng, lat)) {
          return true; // 线段与边界相交
        }
      }
      return false; // 线段完全在区域内
    };

    // 获取线段在区域内的有效部分
    const getValidLineSegment = (p1, p2) => {
      const steps = 20;
      let validStart = null;
      let validEnd = null;

      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const lng = p1.lng + t * (p2.lng - p1.lng);
        const lat = p1.lat + t * (p2.lat - p1.lat);

        if (isPointInPolygonStrict(lng, lat)) {
          if (validStart === null) {
            validStart = { lng, lat };
          }
          validEnd = { lng, lat };
        } else if (validStart !== null) {
          break;
        }
      }

      if (validStart && validEnd) {
        return [validStart, validEnd];
      }
      return null;
    };

    for (let ix = 0; ix < gridX; ix++) {
      for (let iy = 0; iy < gridY; iy++) {
        // 四角u/v
        const u0 = minU + ix * gridWidthInU;
        const v0 = minV + iy * gridHeightInV;
        const u1 = u0 + gridWidthInU;
        const v1 = v0 + gridHeightInV;

        // 四角经纬度
        const p00 = finalTransformToLngLat(u0, v0);
        const p10 = finalTransformToLngLat(u1, v0);
        const p11 = finalTransformToLngLat(u1, v1);
        const p01 = finalTransformToLngLat(u0, v1);

        // 格子中心
        const center = finalTransformToLngLat((u0 + u1) / 2, (v0 + v1) / 2);

        // 判断格子中心在区域内
        if (isPointInPolygonStrict(center.lng, center.lat)) {
          // 只绘制两对角线，不绘制四边（避免与主网格线重叠）
          const diagonal1 = getValidLineSegment(p00, p11);
          if (diagonal1) drawGridLine(diagonal1);

          const diagonal2 = getValidLineSegment(p10, p01);
          if (diagonal2) drawGridLine(diagonal2);
        }
      }
    }

    // 9. 绘制区域边界线
    const boundaryPositions = [];
    for (let i = 0; i < lngs.length; i++) {
      boundaryPositions.push(Cesium.Cartesian3.fromDegrees(lngs[i], lats[i]));
    }
    // 闭合边界
    boundaryPositions.push(Cesium.Cartesian3.fromDegrees(lngs[0], lats[0]));

    window.viewer.entities.add({
      id: `GAOZHIMO_BOUNDARY_${index}`,
      name: "高支模边界线",
      polyline: {
        positions: boundaryPositions,
        width: 3,
        material: Cesium.Color.YELLOW,
        clampToGround: true,
      },
      isGaoZhiMo: true,
      _isPermanent: true,
    });
  }

  /**
   * 解析施工区域数据
   * @param {Object} shigongAreaData - 原始施工区域数据
   * @returns {Array} 解析后的区域数组
   */
  parseConstructionAreaData(shigongAreaData) {
    const areas = [];

    try {
      // 检查数据格式并提取坐标
      if (Array.isArray(shigongAreaData)) {
        // 如果是数组格式
        shigongAreaData.forEach((item, index) => {
          const coords = this.extractCoordinatesFromArea(item, index);
          if (coords && coords.length >= 3) {
            areas.push({
              name: item.name || `施工总平面图-北区${index + 1}`,
              coordinates: coords,
              index: index,
            });
          }
        });
      } else if (typeof shigongAreaData === "object") {
        // 如果是对象格式，遍历对象属性
        Object.keys(shigongAreaData).forEach((key, index) => {
          const item = shigongAreaData[key];
          const coords = this.extractCoordinatesFromArea(item, index);
          if (coords && coords.length >= 3) {
            areas.push({
              name: key || `施工区域${index + 1}`,
              coordinates: coords,
              index: index,
            });
          }
        });
      }

      console.log("✅ 解析出的施工区域:", areas);
      return areas;
    } catch (error) {
      console.error("❌ 解析施工区域数据失败:", error);
      return [];
    }
  }

  /**
   * 从区域数据中提取坐标点
   * @param {Object} areaItem - 单个区域数据
   * @param {Number} index - 区域索引
   * @returns {Array} 坐标数组
   */
  extractCoordinatesFromArea(areaItem, index) {
    try {
      let coordinates = [];

      // 尝试多种可能的数据结构
      if (Array.isArray(areaItem)) {
        // 如果直接是坐标数组
        coordinates = areaItem;
      } else if (areaItem && Array.isArray(areaItem.coordinates)) {
        // 如果有coordinates属性
        coordinates = areaItem.coordinates;
      } else if (areaItem && Array.isArray(areaItem.location)) {
        // 如果有location属性
        coordinates = areaItem.location;
      } else if (areaItem && typeof areaItem === "object") {
        // 如果是对象，尝试找到数组属性
        const arrayProps = Object.values(areaItem).filter((val) =>
          Array.isArray(val)
        );
        if (arrayProps.length > 0) {
          coordinates = arrayProps[0]; // 取第一个数组属性
        }
      }

      // 验证坐标格式并转换
      const processedCoords = this.processCoordinates(coordinates);

      console.log(`区域 ${index + 1} 坐标处理结果:`, {
        original: coordinates,
        originalLength: coordinates ? coordinates.length : 0,
        processed: processedCoords,
        processedPoints: processedCoords ? processedCoords.length / 2 : 0,
      });

      return processedCoords;
    } catch (error) {
      console.error(`提取区域 ${index + 1} 坐标失败:`, error);
      return [];
    }
  }

  /*
   * 处理坐标数据，确保格式正确
   * @param {Array} coordinates - 原始坐标数组
   * @returns {Array} 处理后的坐标数组 [lng1, lat1, lng2, lat2, ...]
   */
  processCoordinates(coordinates) {
    if (!Array.isArray(coordinates) || coordinates.length === 0) {
      return [];
    }

    try {
      let processedCoords = [];

      // 检查第一个元素的类型来判断数据格式
      const firstElement = coordinates[0];

      if (typeof firstElement === "string") {
        // 字符串格式：["114.1492223,29.80303966", "114.1499305,29.80250085", ...]
        console.log("检测到字符串格式坐标，开始解析...");
        coordinates.forEach((coordStr, index) => {
          if (typeof coordStr === "string" && coordStr.includes(",")) {
            const parts = coordStr.split(",");
            if (parts.length >= 2) {
              const lng = Number(parts[0].trim());
              const lat = Number(parts[1].trim());
              if (!isNaN(lng) && !isNaN(lat)) {
                processedCoords.push(lng, lat);
                console.log(`坐标点 ${index + 1}: [${lng}, ${lat}]`);
              } else {
                console.warn(`坐标点 ${index + 1} 格式无效: ${coordStr}`);
              }
            }
          } else {
            console.warn(
              `坐标点 ${index + 1} 不是有效的字符串格式: ${coordStr}`
            );
          }
        });
      } else if (Array.isArray(firstElement)) {
        // 二维数组格式：[[lng, lat], [lng, lat], ...]
        console.log("检测到嵌套数组格式坐标，开始解析...");
        coordinates.forEach((coord, index) => {
          if (Array.isArray(coord) && coord.length >= 2) {
            const lng = Number(coord[0]);
            const lat = Number(coord[1]);
            if (!isNaN(lng) && !isNaN(lat)) {
              processedCoords.push(lng, lat);
              console.log(`坐标点 ${index + 1}: [${lng}, ${lat}]`);
            }
          }
        });
      } else {
        // 一维数组格式：[lng, lat, lng, lat, ...]
        console.log("检测到平铺数组格式坐标，开始解析...");
        for (let i = 0; i < coordinates.length; i += 2) {
          if (i + 1 < coordinates.length) {
            const lng = Number(coordinates[i]);
            const lat = Number(coordinates[i + 1]);
            if (!isNaN(lng) && !isNaN(lat)) {
              processedCoords.push(lng, lat);
              console.log(`坐标点 ${Math.floor(i / 2) + 1}: [${lng}, ${lat}]`);
            }
          }
        }
      }

      // 验证坐标有效性
      const validCoords = [];
      for (let i = 0; i < processedCoords.length; i += 2) {
        const lng = processedCoords[i];
        const lat = processedCoords[i + 1];

        // 检查经纬度范围是否合理
        if (
          !isNaN(lng) &&
          !isNaN(lat) &&
          lng >= -180 &&
          lng <= 180 &&
          lat >= -90 &&
          lat <= 90
        ) {
          validCoords.push(lng, lat);
        } else {
          console.warn(`坐标点超出有效范围或无效: [${lng}, ${lat}]`);
        }
      }

      console.log(`坐标处理完成，有效坐标点数: ${validCoords.length / 2}`);
      return validCoords;
    } catch (error) {
      console.error("处理坐标数据失败:", error);
      return [];
    }
  }

  /**
   * 绘制单个施工区域
   * @param {Object} area - 区域数据
   * @param {Number} index - 区域索引
   */
  drawSingleConstructionArea(area, index) {
    try {
      const { name, coordinates } = area;

      if (!coordinates || coordinates.length < 6) {
        // 至少需要3个点(6个数值)
        console.warn(
          `跳过无效区域 ${name}: 坐标点不足，当前有 ${
            coordinates ? coordinates.length / 2 : 0
          } 个点，至少需要3个点`
        );
        return;
      }

      // 检查是否已存在相同ID的边界线
      const entityId = `CONSTRUCTION_AREA_BOUNDARY_${index}`;
      const existingEntity = window.viewer.entities.getById(entityId);
      if (existingEntity) {
        console.log(`✅ 施工区域 ${name} 边界线已存在，跳过重复创建`);
        return;
      }

      // 确保线条闭合（如果第一个点和最后一个点不同，则添加第一个点到末尾）
      let lineCoordinates = [...coordinates];
      const firstPoint = [coordinates[0], coordinates[1]];
      const lastPoint = [
        coordinates[coordinates.length - 2],
        coordinates[coordinates.length - 1],
      ];

      if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        lineCoordinates.push(coordinates[0], coordinates[1]); // 添加起始点形成闭合
        console.log(`区域 ${name} 自动闭合，添加起始点`);
      }

      // 创建边界线实体
      const lineEntity = window.viewer.entities.add({
        id: entityId,
        name: `${name}（永久边界线）`,
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(lineCoordinates),
          width: 2,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.YELLOW,
            outlineWidth: 0,
          }),
          clampToGround: false,
          classificationType: Cesium.ClassificationType.NONE,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0.0,
            5000000.0
          ),
          zIndex: 2,
          show: true,
          debugShowBoundingVolume: false,
        },
        // 标记为永久实体，防止被意外删除
        _isPermanent: true,
        _isConstructionBoundary: true,
        _areaIndex: index,
      });

      console.log(`🎉 施工区域 "${name}" 边界线绘制成功！`);

      // 可选：添加区域标签
      // if (coordinates.length >= 4) {
      //   this.addConstructionAreaLabel(name, coordinates, index)
      // }
    } catch (error) {
      console.error(`绘制施工区域 ${area.name || "未知"} 失败:`, error);
    }
  }

  /**
   * 添加施工区域标签
   * @param {String} name - 区域名称
   * @param {Array} coordinates - 坐标数组
   * @param {Number} index - 区域索引
   */
  addConstructionAreaLabel(name, coordinates, index) {
    try {
      // 计算区域中心点
      let sumLng = 0,
        sumLat = 0,
        pointCount = 0;

      for (let i = 0; i < coordinates.length; i += 2) {
        sumLng += coordinates[i];
        sumLat += coordinates[i + 1];
        pointCount++;
      }

      const centerLng = sumLng / pointCount;
      const centerLat = sumLat / pointCount;

      // 添加标签
      window.viewer.entities.add({
        id: `CONSTRUCTION_AREA_LABEL_${index}`,
        name: `${name} 标签`,
        position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, 0),
        billboard: {
          image: require("../../assets/images/map/logo.png"),
          scale: 1.0,
          sizeInMeters: false,
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: name,
          font: "bold 16px PingFang SC",
          fillColor: Cesium.Color.WHITE,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineColor: Cesium.Color.fromCssColorString("#183a6e"),
          outlineWidth: 4,
          showBackground: false,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(-20, -2.5),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          scale: 1.0,
        },
        // 标记为永久实体
        _isPermanent: true,
        _isConstructionBoundary: true,
        _areaIndex: index,
      });

      console.log(`📍 施工区域 "${name}" 标签添加成功`);
    } catch (error) {
      console.error(`添加施工区域标签失败:`, error);
    }
  }

  /**
   * 清理高支模相关元素
   */
  clearConstructionAreaElements() {
    if (!window.viewer) {
      console.warn("viewer未初始化，无法清理高支模元素");
      return;
    }

    const entitiesToRemove = [];

    // 查找所有高支模相关的实体
    window.viewer.entities.values.forEach((entity) => {
      if (
        entity.isGaoZhiMo ||
        (entity.id &&
          (entity.id.includes("GAOZHIMO_") ||
            entity.id.includes("CONSTRUCTION_AREA_")))
      ) {
        entitiesToRemove.push(entity);
      }
    });

    // 批量移除
    entitiesToRemove.forEach((entity) => {
      window.viewer.entities.remove(entity);
    });

    console.log(`🧹 已清理 ${entitiesToRemove.length} 个高支模相关元素`);
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearConstructionAreaElements();
    this.vm = null;
    console.log("🏗️ ConstructionAreaManager 已销毁");
  }
} 
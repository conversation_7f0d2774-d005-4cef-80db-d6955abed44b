<template>
    <div class="data-trend">
        <Title3 title="数据趋势" style="margin-bottom: 1.48vh;" />
        <div v-if="itemList && itemList.length > 0" class="data-trend-main">
            <div class="s-tab">
                <div class="list">
                    <div v-for="(item) in itemList" :class="{active: currentItem.id === item.id}" @click="handleTabClick(item)" :key="item.id" class="tab-item">{{ item.itemName }}</div>
                </div>
            </div>
            <div v-for="(item, index) in chartData" :key="index" class="s-chart">
                <Title5 :title="item.title" :tabList="item.tabs" @change="(tindex, tab) => chartTabChange(tab, item, index)" />
                <lineEchart
                    :series="item.series"
                    :legend="item.legend"
                    :unit="item.unit"
                    :day="item.day"
                    :colorList="colorList"
                    :max="item.max"
                    :min="item.min" />
            </div>
        </div>
        <div v-else class="data-trend-empty">
            <noData />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import Title5 from "@/components/commonView/title5.vue";
import lineEchart from "./lineEchart.vue";
import { hexToRgba } from "@/utils/tool";
import { getHighFormworkDataTrend, getAlarmSetting, getHighFormworkDataTrend2 } from "@/api/highFormworkMonitoring";
const colorList = ['#165DFF', '#FF9F39']
export default {
    name: 'dataTrend',
    components: {
        Title3,
        Title5,
        lineEchart
    },
    props: {
        itemList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            colorList,
            currentItem: {},
            chartData: [],
            alarmConfig: {}
        }
    },
    watch: {
        itemList: {
            handler(newVal) {
                if(newVal.length > 0) {
                    this.currentItem = newVal[0]
                    this.setupDataTimer()
                } else {
                    this.currentItem = {}
                }
            },
            immediate: true,
            deep: true
        }
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        // 定时刷新数据
        setupDataTimer() {
            if (this.timer) {
                clearInterval(this.timer)
            }
            this.getChartData()
            this.timer = setInterval(() => {
                this.getChartData()
            }, 1000 * 60 * 5)
        },
        chartTabChange(tab, item, index) {
            item.series.forEach((ser, sindex) => {
                ser.data = tab.values[sindex]
            })
        },
        handleTabClick(item) {
            this.currentItem = item
            this.setupDataTimer()
        },
        async getChartData() {
            await this.loadAlarmThresholds()
            let params = {
                itemId: this.currentItem.id,
                // endCreate: "2025-07-30 23:00:00",
                // startCreate: "2025-07-20 00:00:00",
                // intervalValue: 50
            }
            getHighFormworkDataTrend(params).then(res => {
                let chartData = []
                res.data.forEach((item, index) => {
                    chartData.push(this.getSeriesData(item, index))
                })
                this.chartData = chartData
                console.log('chartData', chartData)
            })
        },
        getSeriesData(data) {
            let obj = {
                day: data.date,
                series: [],
                unit: data.unit,
                title: data.type,
                legend: [],
                tabs: []
            }
            if (data.type === '倾斜监测') {
                obj.tabs = [
                    { name: 'X轴', key: 'values_x', values: [] },
                    { name: 'Y轴', key: 'values_y', values: [] }
                ]
                obj.tabsIndex = 0
            }
            let typeMapping = {
                '荷重压力': 'pressure',// 荷重告警
                '倾斜监测': 'tiltX',// 倾斜告警
                '竖向沉降': 'settlement',// 沉降告警
                '水平位移': 'horizontalDisplacement'// 水平位置告警
            }
            let typeObj = this.alarmConfig[typeMapping[data.type]]
            data.data_v.forEach((item, index) => {
                let color = colorList[index % colorList.length]
                let ser = {
                    type: "line",
                    name: item.deviceName,
                    showSymbol: false,
                    lineStyle: {
                        color: color
                    },
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                            {
                                offset: 0,
                                color: hexToRgba(color, 0.15), // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: hexToRgba(color, 0), // 100% 处的颜色
                            },
                            ],
                            global: false, // 缺省为 false
                        },
                    },
                    data: []
                }
                if (obj.tabs && obj.tabs.length > 1) {
                    obj.tabs.forEach(tab => {
                        // 处理undefined值，确保数据完整性
                        let values = item[tab.key] || []
                        tab.values.push(values)
                    })
                    ser.data = obj.tabs[0].values[index] || []
                } else {
                    ser.data = item.values || []
                }
                obj.series.push(ser)
                obj.legend.push({
                    name: item.deviceName,
                    itemStyle: {color}
                })
            })
            if (obj.series.length > 0 && typeObj && (typeObj.max || typeObj.min)) {
                obj.series[0].markLine = {
                    symbol: 'none',
                    data: []
                }
                let xs = obj.day[0]
                let xe = obj.day[obj.day.length - 1]
                if (typeObj.max) {
                    obj.max = typeObj.max
                    obj.series[0].markLine.data.push([
                        {xAxis: xs, yAxis: typeObj.max},
                        {xAxis: xe, yAxis: typeObj.max, lineStyle: {color: '#B20003'}}
                    ])
                }
                // if (typeObj.min) {
                //     obj.min = typeObj.min
                //     obj.series[0].markLine.data.push([
                //         {xAxis: xs, yAxis: typeObj.min},
                //         {xAxis: xe, yAxis: typeObj.min, lineStyle: {color: '#9137FF'}}
                //     ])
                // }
            }
            return obj
        },
        // 获取告警阈值配置
        async loadAlarmThresholds() {
            const res = await getAlarmSetting()
            let typeMapping = {
                '43': 'pressure',// 荷重告警
                '40': 'tiltX',// 倾斜告警
                '41': 'settlement',// 沉降告警
                '42': 'horizontalDisplacement'// 水平位置告警
            }
            let alarmConfig = {}
            res.data.forEach(item => {
                if (typeMapping[item.type]) {
                    if (!alarmConfig[typeMapping[item.type]]) {
                        alarmConfig[typeMapping[item.type]] = {}
                    }
                    if (item.level == 1) {
                        alarmConfig[typeMapping[item.type]].max = item.threshValue
                    } else if (item.level == 2) {
                        alarmConfig[typeMapping[item.type]].min = item.threshValue
                    }
                }
            })
            this.alarmConfig = alarmConfig
            console.log('alarmConfig', alarmConfig)
        }
    }
}
</script>
<style lang="less" scoped>
.data-trend-empty{
    height: 60vh;
}
.data-trend{
    .data-trend-main{
        padding: 0 1.48vh;
    }
    .s-tab{
        overflow: auto;
        height: 3.33vh;
        .list{
            display: flex;
            align-items: center;
            width: fit-content;
            column-gap: 1.48vh;
            .tab-item{
                background: rgba(149, 149, 149, 0.15);
                padding: 0 1.48vh;
                font-size: 1.29vh;
                line-height: 1.85vh;
                height: 2.59vh;
                color: rgba(255, 255, 255, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 2px;
                min-width: 8.3vh;
                cursor: pointer;
                &.active{
                    background: linear-gradient(180deg, #125DFF 0%, #003199 100%);
                    border: 0.8px solid rgba(39, 108, 255, 1);
                    color: #fff;
                    backdrop-filter: blur(4px);
                    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
                }
            }
        }
    }
}
</style>
<template>
    <div class="menu-list">
        <div class="menu-list-item" v-for="item in highFormworkList" :class="{'active': currentObj.id === item.id}" @click="handleClick(item)" :key="item.id">
            <img src="@/assets/images/icon/device_menu_bg.png" class="menu-bg" alt="">
            <img src="@/assets/images/icon/device_menu_bg1.png" class="menu-bg1" alt="">
            <div class="menu-box">
                <div class="icon-box">
                    <img src="@/assets/images/icon/device_menu_gzm.png" class="menu-img" alt="">
                    <img src="@/assets/images/icon/device_menu_gzm1.png" class="menu-img1" alt="">
                </div>
                <div class="name">{{ item.name }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import { getHighFormworkList } from '@/api/highFormworkMonitoring'
export default {
    name: 'MenuList',
    data() {
        return {
            highFormworkList: [],
            currentObj: {}
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        handleClick(item) {
            this.currentObj = item || {}
            this.$emit('change', item)
        },
        getData() {
            getHighFormworkList().then(res => {
                this.highFormworkList = res.data
                this.handleClick(this.highFormworkList[0])
            })
        }
    }
}
</script>
<style lang="less" scoped>
.menu-list{
    position: absolute;
    top: 1.48vh;
    left: 1.48vh;
    .menu-list-item{
        pointer-events: auto;
        min-width: 14.8vh;
        position: relative;
        padding-bottom: 0.83vh;
        cursor: pointer;
        margin-bottom: 0.74vh;
        width: fit-content;
        .menu-bg1,
        .menu-bg{
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 0;
            display: block;
        }
        .menu-bg1{
            display: none;
        }
        .menu-box{
            position: relative;
            z-index: 5;
            display: flex;
            align-items: center;
            padding-left: 2.22vh;
            padding-right: 0.74vh;
            .icon-box{
                width: 2.96vh;
                height: 2.77vh;
                position: relative;
                margin-right: 0.74vh;
                img{
                    width: 100%;
                }
            }
            .menu-img{
                display: block;
            }
            .menu-img1{
                display: none;
            }
            .name{
                font-size: 1.85vh;
                font-family: YouSheBiaoTiHei;
                line-height: 2.4vh;
                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 20.53%, rgba(12, 54, 143, 0.8) 100%);
                -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                -webkit-text-fill-color: transparent;/*给文字设置成透明*/
            }
        }
        &.active{
            min-width: 16.48vh;
            .menu-bg1{
                display: block;
            }
            .menu-bg{
                display: none;
            }
            .menu-box{
                padding-left: 2.5vh;
                padding-right: 1.2vh;
                .menu-img{
                    display: none;
                }
                .menu-img1{
                    display: block;
                }
                .name{
                    font-size: 2.22vh;
                    line-height: 2.87vh;
                    background: linear-gradient(180deg, #FFFFFF 20.53%, #F68F21 100%);
                    -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                    -webkit-text-fill-color: transparent;/*给文字设置成透明*/
                }
            }
        }
    }
}
</style>
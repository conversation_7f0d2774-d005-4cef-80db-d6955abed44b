<template>
    <div class="real-time-data">
        <Title3 title="实时数据" style="margin-bottom: 1.48vh;" />
        <div class="real-time-main">
            <swiper v-if="realTimeList.length > 0" :options="swiperOptions" ref="mySwiper" class="real-swiper">
                <swiper-slide v-for="(item, index) in realTimeList" :key="index" class="real-slide">
                    <div class="real-slide-item">
                        <div class="top-box">
                            <div class="name">{{ item.highName }} {{ item.deviceName }}</div>
                            <div class="status">
                                <img v-if="item.alarmStatus == 1" src="@/assets/images/icon/status_alarm.png" class="alarm-icon" alt="">
                                <img v-if="item.onlineState == 1" src="@/assets/images/icon/online.png" class="online-icon" alt="">
                                <img v-else src="@/assets/images/icon/status_offline.png" class="offline-icon" alt="">
                            </div>
                        </div>
                        <div class="info-list">
                            <div class="info-item">
                                <div class="name">监测分项:</div>
                                <div class="text"><TooltipAutoShow :content="item.itemName" /></div>
                            </div>
                            <div class="info-item">
                                <div class="name">测量值:</div>
                                <div class="text" :class="{'text-red': item.alarmStatus == 1 || item.ergAlarm == 1}"><TooltipAutoShow :content="item.attrContent" /></div>
                            </div>
                        </div>
                        <div class="info-list">
                            <div class="info-item">
                                <div class="name">设备类型:</div>
                                <div class="text"><TooltipAutoShow :content="item.deviceSubType" /></div>
                            </div>
                            <div class="info-item">
                                <div class="name">更新时间:</div>
                                <div class="text"><TooltipAutoShow :content="item.dataTime" /></div>
                            </div>
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
            <no-data v-else />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { getHighFormworkRealTimeData } from '@/api/highFormworkMonitoring'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
import 'swiper/css/swiper.min.css';
export default {
    name: 'realTimeData',
    components: {
        Title3,
        Swiper, SwiperSlide
    },
    props: {
        highCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            realTimeList: [],
            swiperOptions: {
                direction: 'vertical',
                slidesPerView: 4,
                loop: false,
                observer: true,
                observeParents: true,
                speed: 600,
                autoHeight: true,
                autoplay: {
                    disableOnInteraction: false,
                    delay: 5000
                }
            },
            timer: null
        }
    },
    watch: {
        highCode(val) {
            if(val) {
                this.getData()
            }
        }
    },
    mounted() {
        this.getData()
        this.timer = setInterval(() => {
            this.getData()
        }, 1000 * 60)
    },
    beforeDestroy() {
        clearInterval(this.timer)
    },
    methods: {
        getData() {
            if(this.highCode) {
                let params = {
                    page: {
                        current: 1,
                        size: -1
                    },
                    customQueryParams: {
                        code: this.highCode
                    }
                }
                getHighFormworkRealTimeData(params).then(res => {
                    this.realTimeList = (res.data.records || []).map(item => {
                        item.attrContent = this.getContent(item)
                        return item
                    })
                })
            }
        },
        getContent(item) {
            let content = ''
            switch(item.deviceSubType) {
                case '水平位移':
                    content = item.horizontalDisplacement + 'mm'
                    break
                case '竖向沉降':
                    content = item.sedimentation + 'mm'
                    break
                case '倾斜监测':
                    content = `X轴:${item.angleX}°, Y轴:${item.angleY}°`
                    break
                case '荷重压力':
                    content = item.pressure + 'KN'
                    break
            }
            return content
        }
    }
}
</script>
<style lang="less" scoped>
.real-time-data{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    &>div{
        width: 100%;
    }
    .real-time-main{
        flex: 1;
        overflow: hidden;
        padding: 0 1.48vh;
    }
    .real-swiper{
        height: 100%;
    }
    .real-slide{
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.08) 0%, rgba(153, 153, 153, 0.08) 100%);
        border-left: 1.4px solid rgba(255, 255, 255, 0.3);
        &:nth-child(2n){
            background: linear-gradient(90deg, rgba(0, 68, 255, 0.12) 0%, rgba(0, 41, 153, 0.12) 100%);
            border-color: transparent;
        }
    }
    .real-slide-item{
        padding: 0.74vh 1.48vh;
        color: #fff;
        .top-box{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.74vh;
            .name{
                font-size: 1.38vh;
                line-height: 1.94vh;
                flex: 1;
                margin-right: 0.74vh;
            }
            .status{
                display: flex;
                align-items: center;
                column-gap: 0.74vh;
                img{
                    width: 2.78vh;
                    height: 1.48vh;
                }
            }
        }
        .info-list{
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.74vh;
            column-gap: 0.74vh;
            .info-item{
                display: flex;
                align-items: flex-start;
                column-gap: 0.74vh;
                font-size: 1.2vh;
                line-height: 1.66vh;
                .name{
                    color: rgba(255, 255, 255, 0.5);
                }
                .text{
                    flex: 1;
                    overflow: hidden;
                }
                &:nth-child(1){
                    flex: 1;
                    overflow: hidden;
                }
                &:nth-child(2){
                    width: 18.31vh;
                }
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
}
</style>
<template>
    <div class="alarm-record">
        <Title3 title="告警记录" style="margin-bottom: 1.48vh;" />
        <div class="alarm-record-main">
            <div v-show="alarmRecordList.length > 0" class="alarm-record-list" ref="alarmRecordList">
                <Timeline>
                <TimelineItem
                  v-for="item in alarmRecordList"
                  :key="item.id"
                >
                  <div class="time">{{ item.createTime }}</div>
                  <div class="timeConetent">
                    <div class="title">
                        <img
                          src="@/assets/images/ProjectAnti/warnIcon.png"
                        />{{ item.testingItem }}
                    </div>
                    <div class="content">
                      <div class="txt">{{ item.deviceName }}</div>
                      <div class="txt">{{ item.content }}</div>
                    </div>
                  </div>
                </TimelineItem>
            </Timeline>
            </div>
            <no-data v-show="alarmRecordList.length === 0" />
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { getAlarmRecord } from '@/api/highFormworkMonitoring'
export default {
    name: 'alarmRecord',
    components: {
        Title3
    },
    props: {
        highCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            alarmRecordList: [],
            // 添加轮播控制相关数据
            autoScroll: {
                timer: null,
                speed: 1, // 滚动速度(px)
                isHover: false, // 鼠标是否悬停
                scrollElement: null, // 滚动容器元素
            },
        }
    },
    watch: {
        highCode(val) {
            if(val) {
                this.getData()
            }
        }
    },
    mounted() {
        this.getData()
         // 初始化自动轮播
         this.$nextTick(() => {
            this.autoScroll.scrollElement = this.$refs.alarmRecordList
        });
        this.timer = setInterval(() => {
            this.getData()
        }, 1000 * 60)
    },
    destroyed() {
        // 清除定时器
        this.stopAutoScroll();
        clearInterval(this.timer)
    },
    methods: {
        getData() {
            if(this.highCode) {
                getAlarmRecord(this.highCode).then(res => {
                    this.alarmRecordList = res.data || []
                    this.startAutoScroll()
                })
            }
        },
        // 开始自动滚动
        startAutoScroll() {
            if (this.autoScroll.timer) {
                clearInterval(this.autoScroll.timer);
            }
            this.autoScroll.timer = setInterval(() => {
                if (this.autoScroll.isHover) return;
                
                const element = this.autoScroll.scrollElement;
                if (!element) return;

                // 如果滚动到底部，重置到顶部
                if (element.scrollTop >= (element.scrollHeight - element.clientHeight)) {
                    element.scrollTop > 0 && this.resetToTop();
                    // console.log('--------', element.scrollTop)
                } else {
                    element.scrollTop += this.autoScroll.speed;
                }
            }, 50);
        },
        // 重置到顶部
        resetToTop() {
            const element = this.autoScroll.scrollElement;
            if (!element) return;
            element.scrollTop = 0;
            this.stopAutoScroll();
            setTimeout(() => {
                this.startAutoScroll();
            }, 1000);
        },
        // 停止自动滚动
        stopAutoScroll() {
            if (this.autoScroll.timer) {
                clearInterval(this.autoScroll.timer);
                this.autoScroll.timer = null;
            }
        },
        // 处理鼠标进入
        handleMouseEnter() {
            this.autoScroll.isHover = true;
        },
        // 处理鼠标离开
        handleMouseLeave() {
            this.autoScroll.isHover = false;
        },
    }
}
</script>
<style lang="less" scoped>
.alarm-record{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    &>div{
        width: 100%;
    }
    .alarm-record-main{
        flex: 1;
        overflow: hidden;
        padding: 0 1.48vh;
    }
    .alarm-record-list{
        height: 100%;
        overflow: auto;
        .ivu-timeline{
            position: relative;
            &::before{
                content: '';
                position: absolute;
                top: 0.27vh;
                left: 0.465vh;
                width: 1.2px;
                height: ~"calc(100% - 0.27vh)";
                background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 14%, rgba(255, 255, 255, 0.5) 45%, rgba(255, 255, 255, 0) 100%);
                display: block;
            }
        }
        /deep/.ivu-timeline-item{
            padding-bottom: 0.37vh;
            .ivu-timeline-item-tail{
                display: none;
            }
            .ivu-timeline-item-head{
                top: 0.27vh;
                width: 0.93vh;
                height: 0.93vh;
                background: radial-gradient(50% 50% at 50% 50%, #0051FF 0%, #001B56 100%);
                border: 0.4px solid rgba(142, 182, 255, 1);
                backdrop-filter: blur(15px);
                box-shadow: 0px 0px 4px 0px rgba(0, 153, 255, 0.4),0px 4px 4px 0px rgba(0, 0, 0, 0.25);
                &::after{
                    content: '';
                    display: block;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 0.27vh;
                    height: 0.27vh;
                    border-radius: 50%;
                   background: #fff;
                   backdrop-filter: blur(4px);
                   box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 1);
                }
            }
        }
        .time{
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.38vh;
            line-height: 1.85vh;
            margin-bottom: 0.37vh;
        }
        .timeConetent {
            width: 100%;
            height: 8.88vh;
            display: flex;
            flex-direction: column;
            background: url(../../../assets/images/civilAirDefense/alarm_bg.png) no-repeat center top;
            background-size: 100% 100%;
            color: #fff;
            font-size: 1.29vh;
            line-height: 1.85vh;

            .title {
                height: 100%;
                display: flex;
                align-items: center;
                padding: 0 0.74vh;
                height: 2.59vh;
                align-items: center;
                img {
                    width: 1.48vh;
                    height: 1.48vh;
                    object-fit: contain;
                    margin-right: 0.37vh;
                    display: block;
                }
            }

            .content {
                box-sizing: border-box;
                padding: 0.74vh;

                .txt {
                    width: 100%;
                    text-align: left;
                    margin-bottom: 0.74vh;
                    &:last-child{
                        margin-bottom: 0;
                    }
                }

                .txt::before {
                content: "";
                display: inline-block;
                width: 0.37vh;
                height: 0.37vh;
                background-color: #fff;
                margin: 0 0.74vh;
                transform: rotate(45deg);
                vertical-align: middle;
                }
            }
            }
    }
    
}
</style>
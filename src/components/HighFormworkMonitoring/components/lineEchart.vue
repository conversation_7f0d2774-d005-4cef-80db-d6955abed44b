<template>
  <div class="line-box">
    <div class="res-box">
      <div v-if="max" class="res-item max">
        <div class="line"></div>
        <div class="text">预警线({{ max }})</div>
      </div>
      <!-- <div v-if="min" class="res-item min">
        <div class="line"></div>
        <div class="text">下限预警线({{ min }})</div>
      </div> -->
    </div>
    <AEcharts ref="lineEchart" :options="options" class="line-echart" />
  </div>
</template>
<script>
import AEcharts from "@/components/commonView/AEcharts.vue";
export default {
    name: 'lineEchart',
    components: {
        AEcharts
    },
    props: {
        series: {
            type: Array,
            default: () => []
        },
        unit: {
            type: String,
            default: ''
        },
        day: {
          type: Array,
            default: () => []
        },
        colorList: {
            type: Array,
            default: () => []
        },
        legend: {
            type: Array,
            default: () => []
        },
        max: {
            type: [Number, String],
            default: 0
        },
        min: {
            type: [Number, String],
            default: 0
        }
    },
    watch: {
        series: {
            handler(newVal) {
                // 处理series数据中的undefined值
                if (newVal && newVal.length > 0) {
                    let processedSeries = newVal.map(series => {
                        if (series.data && Array.isArray(series.data)) {
                            // 过滤掉undefined值，保持数据连续性
                            series.data = series.data.map(value => 
                                (value === undefined || value === null || value === '') ? null : value
                            )
                        }
                        return series
                    })
                    this.options.series = processedSeries
                } else {
                    this.options.series = []
                }
            },
            deep: true,
            immediate: true
        },
        day: {
            handler(newVal) {
                this.options.xAxis.data = newVal || []
            },
            deep: true,
            immediate: true
        },
        unit: {
            handler(newVal) {
                this.options.yAxis.name = newVal ? `(${newVal})` : ''
            },
            immediate: true
        },
        legend: {
            handler(newVal) {
                this.options.legend.data = newVal || []
            },
            deep: true,
            immediate: true
        }
    },
    data() {
        return {
            options: {
              grid: {
                top: 30,
                left: 10,
                right: 5,
                bottom: 2,
                containLabel: true,
              },
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  type: "line",
                },
                formatter: (params) => {
                  let html = `<div>${params[0].name}</div>`
                  params.forEach((item) => {
                    let color = this.colorList[item.seriesIndex % this.colorList.length]
                    let marker = `<span style="background-color:${color};display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;"></span>`
                    // 处理undefined、null或空值的情况，显示横杠且不显示单位
                    let displayValue = (item.value === undefined || item.value === null || item.value === '') ? '-' : item.value
                    let displayUnit = (item.value === undefined || item.value === null || item.value === '') ? '' : this.unit
                    html += `<div>${marker}${item.seriesName}：${displayValue}${displayUnit}</div>`
                  })
                  return html
                },
              },
              legend: {
                show: true,
                itemWidth: 4,
                itemHeight: 4,
                icon: 'circle',
                top: 1,
                left: 40,
                textStyle: { color: '#FFFFFF' },
                data: []
              },
              xAxis: {
                type: "category",
                boundaryGap: false,
                data: [],
                axisLine: {
                  lineStyle: {
                    color: "rgba(255, 255, 255, 0.1)",
                    width: 1,
                  },
                },
                axisLabel: {
                  color: "rgba(255, 255, 255, 1)",
                  fontSize: 10,
                },
              },
              yAxis: {
                name: "",
                nameTextStyle: {
                  color: "rgba(255, 255, 255, 1)",
                  // align: 'right'
                },
                type: "value",
                axisLine: {
                  show: true,
                  lineStyle: {
                    color: "rgba(255, 255, 255, 0.1)",
                  },
                },
                axisLabel: {
                  color: "rgba(255, 255, 255, 1)",
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "rgba(255, 255, 255, 0.1)",
                    width: 1,
                    type: "dashed",
                  },
                },
              },
              series: [],
            }
        }
    }
}
</script>
<style lang="less" scoped>
.line-box{
    position: relative;
    .res-box{
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      column-gap: 0.74vh;
      .res-item{
        display: flex;
        align-items: center;
        column-gap: 0.37vh;
        color: rgba(255, 255, 255, 0.7);
        font-size: 1.11vh;
        line-height: 1.57vh;
        .line{
          width: 0.74vh;
          border-top: 1px dashed transparent;
        }
        &.max{
          .line{
            border-color: #B20003;
          }
        }
        &.min{
          .line{
            border-color: #9137FF;
          }
        }
      }
    }
}
.line-echart{
    width: 100%;
    height: 16.1vh;
    margin-bottom: 0.74vh;
}
</style>
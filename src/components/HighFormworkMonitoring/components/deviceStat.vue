<template>
    <div>
        <Title3 title="设备统计" style="margin-bottom: 1.48vh;" />
        <div class="device-stat">
            <div class="device-stat-left">
                <div class="name-box">
                    <img src="@/assets/images/icon/device_icon.png" class="device-icon" alt="">
                    <div class="name-text">设备总数</div>
                </div>
                <div class="info-box">
                    <div class="value-bg">
                        <img src="@/assets/images/civilAirDefense/box_bg4.png" alt="">
                    </div>
                    <div class="value-box">
                        <div class="value-text">{{ statData.sum }}</div>
                        <div class="unit-text">个</div>
                    </div>
                </div>
            </div>
            <div class="device-stat-right">
                <div class="stat-box">
                    <div class="rect">
                        <img src="@/assets/images/icon/onlinestauts.png" alt="">
                    </div>
                    <div class="name">在线设备数:</div>
                    <div class="val"><span>{{ statData.onLineSum }}</span>个</div>
                </div>
                <div class="stat-box">
                    <div class="rect">
                        <img src="@/assets/images/icon/work.png" alt="">
                    </div>
                    <div class="name">在线率:</div>
                    <div class="val"><span>{{ statData.onLineRate }}</span>%</div>
                </div>
                <div class="stat-box">
                    <div class="dec">
                        <img src="@/assets/images/civilAirDefense/box_bg5.png" alt="">
                    </div>
                    <div class="rect">
                        <img src="@/assets/images/icon/alarm_rect.png" alt="">
                    </div>
                    <div class="name">告警设备数:</div>
                    <div class="val"><span>{{ statData.alarmSum }}</span>个</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Title3 from "@/components/commonView/title3.vue";
import { getDeviceStatByCode } from '@/api/highFormworkMonitoring'
export default {
    name: 'DeviceStat',
    components: {
        Title3
    },
    props: {
    },
    data() {
        return {
            statData: {},
            timer: null
        }
    },
    mounted() {
        this.getData()
        this.timer = setInterval(() => {
            this.getData()
        }, 1000 * 8)
    },
    beforeDestroy() {
        clearInterval(this.timer)
    },
    methods: {
        getData() {
            getDeviceStatByCode('').then(res => {
                res.data.onLineRate = res.data.onLineRate.replace('%', '')
                this.statData = res.data || {}
            })
        }
    }
}
</script>
<style lang="less" scoped>
.device-stat{
    display: flex;
    align-items: center;
    padding: 0 1.48vh;
    column-gap: 1.48vh;
}
.device-stat-left{
    width: 12.96vh;
    .name-box{
        background: linear-gradient(90deg, rgba(2, 39, 116, 0) 0%, rgba(2, 56, 168, 0.56) 50%, rgba(2, 39, 116, 0) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.6px solid transparent;
        border-image-source: linear-gradient(90deg, rgba(3, 73, 218, 0) 0%, #46ACFF 50%, rgba(3, 73, 218, 0) 100%);
        border-image-slice: 1;
        width: 100%;
        height: 2.77vh;
        .device-icon{
            width: 1.48vh;
            height: 1.48vh;
            margin-right: 0.74vh;
        }
        .name-text{
            font-size: 1.48vh;
            color: rgba(255, 255, 255, 0.8);
        }
    }
    .info-box{
        position: relative;
        height: 8.33vh;
        .value-bg{
            position: absolute;
            width: 100%;
            bottom: -0.37vh;
            left: 0;
            img{
                width: 100%;
                display: block;
            }
        }
        .value-box{
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            .value-text{
                font-size: 5vh;
                color: #fff;
                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                line-height: 6.48vh;
                font-family: YouSheBiaoTiHei;
            }
            .unit-text{
                color: rgba(255, 255, 255, 0.6);
                font-size: 1.29vh;
                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                line-height: 3vh;
                margin-top: 1.48vh;
            }
        }
    }
}
.device-stat-right{
    flex: 1;
    .stat-box{
        display: flex;
        align-items: center;
        width: 100%;
        background: linear-gradient(90deg, rgba(0, 172, 195, 0.36) 0%, rgba(0, 89, 101, 0) 100%);
        height: 3.518vh;
        border-left: 1px solid rgba(0, 225, 255, 1);
        margin-bottom: 0.83vh;
        position: relative;
        z-index: 1;
        border-radius: 1px;
        padding: 0 0.74vh;
        .rect{
            width: 1.48vh;
            height: 1.48vh;
            margin-right: 0.185vh;
            img{
                width: 100%;
                display: block;
            }
        }
        .name{
            font-size: 1.38vh;
            color: rgba(255, 255, 255, 0.8);
            flex: 1;
        }
        .val{
            font-size: 1.11vh;
            color: rgba(255, 255, 255, 0.6);
            span{
                font-size: 2.22vh;
                color: #fff;
                font-family: 'DIN Pro';
                line-height: 2.77vh;
                margin-right: 0.37vh;
            }
        }
        .dec{
            position: absolute;
            z-index: 1;
            width: 3.24vh;
            left: 0;
            bottom: 0;
        }
        &:nth-child(2){
            background: linear-gradient(90deg, rgba(8, 86, 255, 0.3) 0%, rgba(0, 34, 108, 0) 100%);
            border-color: rgba(83, 138, 255, 1);
        }
        &:nth-child(3){
            background: linear-gradient(90deg, #450000 0%, rgba(74, 12, 12, 0) 100%);
            border-color: rgba(255, 83, 83, 1);
            margin-bottom: 0;
            
        }
    }
}
</style>
<template>
    <div class="high-formwork-monitoring">
        <div class="high-formwork-monitoring-mask">
            <div class="mask-left"></div>
            <div class="mask-right"></div>
            <div class="mask-motton"></div>
        </div>
        <div class="high-formwork-monitoring-bg"></div>
        <div class="high-formwork-monitoring-body">
            <div class="high-formwork-monitoring-left">
                <DeviceStat />
                <realTimeData :highCode="currentMenuObj.code" />
                <alarmRecord :highCode="currentMenuObj.code" />
            </div>
            <div class="high-formwork-monitoring-center">
                <MenuList @change="handleMenuChange" />
            </div>
            <div class="high-formwork-monitoring-right">
                <dataTrend :itemList="currentMenuObj.items" />
            </div>
        </div>
    </div>
</template>
<script>
import DeviceStat from './components/deviceStat.vue'
import MenuList from './components/menuList.vue'
import realTimeData from './components/realTimeData.vue'
import alarmRecord from './components/alarmRecord.vue'
import dataTrend from './components/dataTrend.vue'
export default {
    name: 'highFormworkMonitoring',
    components: {
        DeviceStat,
        MenuList,
        realTimeData,
        alarmRecord,
        dataTrend
    },
    data() {
        return {
            currentMenuObj: {}
        }
    },
    mounted(){
        // this.timer = setInterval(() => {
        //     this.refresh()
        // }, 10 * 1000)
    },
    beforeDestroy(){
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        handleMenuChange(item) {
            this.currentMenuObj = item || {}
        }
    }
}
</script>
<style>
body{
    position: relative;
    /* top: -70%; */
    left: -0;
}
</style>
<style lang="less" scoped>
.high-formwork-monitoring{
    height: ~"calc(100vh - 4.75rem)";
    position: relative;
    width: 100%;
    position: relative;
    pointer-events: none;
    padding: 3.7vh 1.48vh 1.57vh;
    .high-formwork-monitoring-mask,
    .high-formwork-monitoring-bg{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }
    .high-formwork-monitoring-mask{
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        z-index: 1;
        .mask-left,
        .mask-right{
            width: 22.39vw;
            height: 100%;
            position: relative;
            z-index: 1;
        }
        .mask-left{
            background: linear-gradient(90deg, #000D1E 0%, rgba(0, 13, 30, 0) 100%);
        }
        .mask-right{
            background: linear-gradient(270deg, #000D1E 0%, rgba(0, 13, 30, 0) 100%);
        }
        .mask-motton{
            position: absolute;
            width: 100%;
            height: 33.33vh;
            bottom: 0;
            left: 0;
            background: linear-gradient(0deg, #000D1E 0%, rgba(0, 13, 30, 0) 50.48%);
        }
    }
    .high-formwork-monitoring-bg{
        background: url('~@/assets/images/pointScreen/point_frame.png') no-repeat center center;
        background-size: 100% 100%;
        z-index: 2;
    }
    .high-formwork-monitoring-body{
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        column-gap: 0.74vh;
        z-index: 5;
        justify-content: space-between;
    }
    .high-formwork-monitoring-left,
    .high-formwork-monitoring-right{
        width: 21.57vw;
        height: 100%;
        display: grid;
        pointer-events: auto;
        row-gap: 0.74vh;
    }
    .high-formwork-monitoring-left{
        grid-template-rows: 18.33vh 39.81vh auto;
        background: linear-gradient(270deg, rgba(0, 13, 30, 0) 0%, rgba(0, 9, 30, 0.16) 7%, #00071E 100%);
    }
    .high-formwork-monitoring-right{
        grid-template-rows: 100%;
        background: linear-gradient(90deg, rgba(0, 13, 30, 0) 0%, rgba(0, 9, 30, 0.16) 7%, #00071E 100%);
    }
    .high-formwork-monitoring-center{
        flex: 1;
        height: 100%;
        position: relative;
    }
}
</style>
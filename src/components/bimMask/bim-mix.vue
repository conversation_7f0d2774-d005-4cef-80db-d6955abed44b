<script>
import { getBimfaceViewToken } from '@/api/visualProgress'
import { getMapConfig } from '@/api/index'
export default {
    data() {
        return {
            viewToken: '',
            modelLocation: [],
        }
    },
    beforeDestroy() {
        this.$bus.off('bim-click-component')
    },
    methods: {
        async getViewToken() {
            // 如果viewerGIS不存在，则获取viewToken，重新加载bimface
            if (!this.viewerGIS) {
                let res = await getBimfaceViewToken({type: 2})
                this.viewToken = res.data
                // this.viewToken = '86b242585aa54bff83685266ae40e7ac'
                await this.getBimFaceConfig()
                this.initLoadBimFace()
            } else {
                this.m.isSpinLoading = false
                this.camera.home()
                this.initShow()
            }
        },
        async getBimFaceConfig() {
            let res = await getMapConfig('SCREEN_LOCATION_CONFIG')
            let modeLayer = JSON.parse(res.data.value)
            this.modelLocation = modeLayer.location.split(',')
        },
        initShow() {
            this.componentManager.restoreColor({all: true})
            this.componentManager.show({all: true})
            let notStartedColor = new Glodon.Web.Graphics.Color('#999999', 1)
            let inProgressColor = new Glodon.Web.Graphics.Color('#0aa5e7', 1)
            let completedColor = new Glodon.Web.Graphics.Color('#0ab04e', 1)
            let notStartedIds = this.m.notStartedComponentList.map(item => item.split('.')[1])
            let inProgressIds = this.m.inProgressComponentList.map(item => item.split('.')[1])
            let completedIds = this.m.completedComponentList.map(item => item.split('.')[1])
            this.componentManager.overrideColor({ids: notStartedIds}, notStartedColor)
            // this.componentManager.overrideColor({all: true}, notStartedColor)
            this.componentManager.overrideColor({ids: inProgressIds}, inProgressColor)
            this.componentManager.overrideColor({ids: completedIds}, completedColor)

            this.viewerGIS.render()
        },
        initLoadBimFace() {
            if (!this.viewToken) {
                console.log('没有bimid')
                this.m.isSpinLoading = false
                return
            }
            console.log('start bim face')
            let loaderConfig = new BimfaceSDKLoaderConfig();
            // console.log(loaderConfig)
            loaderConfig.viewToken = this.viewToken;
            BimfaceSDKLoader.load(loaderConfig, this.successCallback, this.failureCallback);
        },

        initEvent() {
            // 3D模型加载完毕
            this.viewerGIS.addEventListener(Glodon.Bimface.Viewer.ViewerGISEvent.LayerAdded, (data) => {
                console.log('场景加载完毕')
                this.initLockAxis()
            });
            this.viewerGIS.addEventListener(Glodon.Bimface.Viewer.ViewerGISEvent.Error, (err) => {
                console.log('Error', err)
            });
            // 框选的构件列表
            console.log(this.m)
            let orgId = ''
            this.viewerGIS.addEventListener('MouseClicked', (e) => {
                console.log(e)
                if (e.eventType === 'Click' && e.objectType === 'Component') {
                    // 点击构件
                    let id = ''
                    if (e.elementId) {
                        id = `${e.modelId}.${e.elementId}`
                        if (id != orgId) {
                            orgId = id
                        } else {
                            id = ''
                        }
                    }
                    this.$bus.emit('bim-click-component', {id})
                } else {
                    this.$bus.emit('bim-click-component', {id: ''})
                }
            });
        },
        initLockAxis() {
            let layerManager = this.viewerGIS.getLayerManager();
            let modelLayer = layerManager.getLayer('layer_10000934136560')
            this.modelLayer = modelLayer
            // this.bimModel = modelLayer.model
            this.componentManager = modelLayer.getComponentManager()
            this.mapLayer = layerManager.getLayer('layer_map_1')
            this.camera = this.viewerGIS.getCamera()
            // this.modelLocation = [114.16088950317997,30.613402642631158]
            // this.modelLocation = [114.14327279308212,29.80547609387527]
            window.modelLayer = modelLayer

            if (this.modelLocation.length > 0 && this.modelLocation[0]) {
                // console.log('modelLocation', this.modelLocation)
                // this.modelLayer.setLocation({
                //     lon: this.modelLocation[0],
                //     lat: this.modelLocation[1],
                //     alt: 0
                // })
                // this.modelLayer.setRotationZ({
                //     x: 0,
                //     y: 0,
                //     z: 0
                // }, 259)
                // let status = {}
                // status.orientation = {pitch: -1.6778880411778315, yaw: 0.5, roll: 0}
                // status.position = {
                //     lon: this.modelLocation[0],
                //     lat: this.modelLocation[1],
                //     alt: 516
                // }
                // this.camera.setStatus(status)
                // this.camera.setHomeView(status)
                // console.log('camera', status, this.camera.getStatus())
            }
            console.log('layerManager', layerManager)
            this.initShow()
        },
        // 加载成功回调函数
        successCallback(viewMetaData) {
            this.m.isSpinLoading = false
            console.log('加载成功', viewMetaData);
            // 设置WebApplication3D的配置项
            let webAppConfig = new Glodon.Bimface.Earth.Application.WebApplicationGISConfig();
            webAppConfig.domElement = this.$refs.siteMain;
            webAppConfig.orbitButton = Glodon.Bimface.Viewer.OrbitButton.Right
            // 设置地形始终不遮挡模型
          webAppConfig.disableMapOcclusion = true;
            console.log(webAppConfig)
            // 创建球体
            this.app = new Glodon.Bimface.Earth.Application.WebApplicationGIS(webAppConfig);
            
            this.viewerGIS = this.app.getViewer();
            this.viewerGIS.addScene(this.viewToken);
            console.log('viewerGIS', this.viewerGIS)
            window.bimViewerGIS = this.viewerGIS
            this.initEvent()
        },
        // 加载失败回调函数
        failureCallback(error) {
            console.log('加载失败', error);
        }
    }
}
</script>

<template>
    <div class="status-legend">
      <div class="box">已完成</div>
      <div class="box">进行中</div>
      <div class="box">未开始</div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'statusLegend',
    data() {
      return {}
    },
    methods: {}
  }
  </script>
  
  <style lang="less" scoped>
  
  .status-legend {
    position: absolute;
    left: calc(~'21.57vw + 1.48vh');
    bottom: 14.44vh;
  
    .box {
      width: 6.48vh;
      height: 2.4vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0.74vh;
      margin-bottom: 0.93vh;
      font-family: PingFang HK;
      font-size: 1.29vh;
      font-weight: 400;
      line-height: 2vh;
      color: #fff;
      border-radius: 0.37vh;
  
      &:nth-child(1) {
        background: #0ab04e;
      }
  
      &:nth-child(2) {
        background: #0aa5e7;
      }
  
      &:nth-child(3) {
        background: #999;
        margin-bottom: 0;
      }
    }
  }
  </style>
  
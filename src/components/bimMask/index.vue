<template>
    <div class="bimface-container">
        <div ref="siteMain" class="site-main" id="site-main"></div>
        <Spin v-if="m.isSpinLoading" fix></Spin>
        <statusLegend />
    </div>
</template>
<script>
import bimMix from './bim-mix'
import { getActualProgressComponent, getScreenProgressTaskInfo } from '@/api/visualProgress'
import statusLegend from './components/statusLegend.vue'
export default {
    name: 'bimMask',
    mixins: [bimMix],
    components: {
        statusLegend
    },
    data() {
        return {
            m: {
                isSpinLoading: false, // 是否显示加载动画
                completedComponentList: [], // 已完成构件id
                inProgressComponentList: [], // 进行中构件id
                notStartedComponentList: [], // 未开始构件id
            }
        }
    },
    mounted() {
    },
    methods: {
        init() {
            if (this.sitime) {
                clearTimeout(this.sitime)
            }
            this.sitime = setTimeout(() => {
                this.getRealNodeData()
            }, 100)
        },
        // 得到实际进度构件
        async getRealNodeData() {
            this.m.isSpinLoading = true
            let param = {
                customQueryParams: {
                    type: 2,
                    "planType": 1 // 计划类型，1总计划，2期间计划，3月计划，4周计划
                },
                page: {
                    current: 1,
                    size: -1
                }
            }
            getScreenProgressTaskInfo(param).then(res => {
                if (res.success) {
                    let completedComponentList = []
                    let inProgressComponentList = []
                    let notStartedComponentList = []
                    console.log(111, res.data.records)
                    res.data.records.forEach(item => {
                        let ids = item.integrateList.map(k => `${k.resourceId}`)
                        // 进度状态：0-未开始，1-进行中，2-已完成
                        if (item.progressStatus == 2) {
                            completedComponentList = completedComponentList.concat(ids)
                        } else if (item.progressStatus == 1) {
                            inProgressComponentList = inProgressComponentList.concat(ids)
                        } else if (item.progressStatus == 0) {
                            notStartedComponentList = notStartedComponentList.concat(ids)
                        }
                    })
                    this.m.completedComponentList = completedComponentList
                    this.m.inProgressComponentList = inProgressComponentList
                    this.m.notStartedComponentList = notStartedComponentList
                    this.getViewToken()
                }
            })
        },
    }
}
</script>
<style lang="less" scoped>
.bimface-container{
    width: 100%;
    height: 100%;
    .site-main {
        width: 100%;
        height: 100%;
    }
    /deep/.bf-view{
        .bf-northarrow{
            top: 4.75rem !important;
            right: ~"calc(1.48vh + 21.57vw)" !important;
        }
    }
}
</style>
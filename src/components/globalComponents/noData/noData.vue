<template>
    <div class="no-data">
        <img v-if="!loading" src="@/assets/images/ProjectAI/empty.png" class="no-data-img" alt="">
        <Icon v-else type="ios-loading" class="no-data-loading" />
        <div class="no-data-text">{{ loading ? '加载中...' : text }}</div>
    </div>
</template>
<script>
export default {
    name: 'noData',
    props: {
        loading: {
            type: Boolean,
            default: false
        },
        text: {
            type: String,
            default: '暂无数据'
        }
    }
}
</script>
<style lang="less" scoped>
.no-data{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    .no-data-img{
        width: 18vh;
        margin-bottom: 1.48vh;
    }
    .no-data-loading{
        font-size: 3vh;
        color: #fff;
        margin-bottom: 1.48vh;
        animation: loading 1s linear infinite;
    }
    .no-data-text{
        font-size: 1.29vh;
        color: #fff;
    }
}
@keyframes loading {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
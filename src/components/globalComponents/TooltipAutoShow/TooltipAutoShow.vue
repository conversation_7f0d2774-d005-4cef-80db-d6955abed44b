<template>
    <Tooltip @mouseenter.native="mouseenterFun" transfer :disabled="disabled" :max-width="maxWidth" :placement="placement">
        <div :class="{ 'ellipsis-1': true, 'bold-font': bold }">
            <span>
                <slot>{{ content }}</slot>
            </span>
        </div>
        <template slot="content">
            <slot>{{ content }}</slot>
        </template>
    </Tooltip>
</template>

<script>
export default {
    name: 'TooltipAutoShow',
    props: {
        content: { default: '' },
        maxWidth: { default: '200' },
        bold: {
            default: false
        },
        placement: { default: 'top' }
    },
    data() {
        return {
            disabled: true,
            contentArr: []

        }
    },
    mounted() {
    },
    methods: {
        mouseenterFun(e) {
            let el = e.toElement
            const range = document.createRange();
            range.setStart(el, 0);
            range.setEnd(el, el.childNodes.length);
            const rangeWidth = Math.round(range.getBoundingClientRect().width);
            // console.log(rangeWidth)
            if (rangeWidth > el.offsetWidth || el.scrollWidth > el.offsetWidth) {
                this.disabled = false;
            }
        },
    }
}
</script>

<style lang="less" scoped>
.ivu-tooltip {
    max-width: 100%;
    height: 100%;

    /deep/.ivu-tooltip-rel {
        max-width: 100%;
        height: 100%;
    }
}

.bold-font {
    font-weight: 600;
    font-size: 16px;
}

.ellipsis-1 {
    display: flex;
    align-items: center;
    height: 100%;

    span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 100%;

    }
}
</style>

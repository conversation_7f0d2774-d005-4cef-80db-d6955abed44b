<template>
  <div class="project-map-page">
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="side-panel" style="pointer-events: auto">
        <Title title="设备统计"></Title>
        <div class="side-card1">
          <div class="side-card1-left">
            <div class="side-card1-left-title">
              <img src="../../assets/images/ProjectElectrical/icon1.png" />
              <span>设备总数</span>
            </div>
            <div class="processNum">
              <div
                class="back"
                v-for="(item, index) in NumArrLenth(deviceInfo.deviceTotal)"
                :key="index"
              >
                {{ item }}
              </div>
              <div class="back backDisponse">台</div>
            </div>
          </div>
          <div class="side-card1-right">
            <div class="side-card1-right-item">
              <div class="side-card1-right-item-title">在线设备数</div>
              <div class="side-card1-right-item-num">
                <span>{{ deviceInfo.onlineNum }}</span>
                <span>个</span>
              </div>
            </div>
            <div class="side-card1-right-item error">
              <div class="side-card1-right-item-title">告警设备</div>
              <div class="side-card1-right-item-num">
                <span>{{ deviceInfo.warnNum }}</span>
                <span>个</span>
              </div>
            </div>
          </div>
        </div>
        <Title title="运行状态"></Title>
        <div
          style="
            height: calc(100% - 10vh);
            overflow-y: auto;
            overflow-x: hidden;
          "
          id="runStateList"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
        >
          <div
            style="
              width: 100%;
              display: flex;
              flex-direction: column;
              gap: 10px;
            "
            v-for="(item, index) in deviceInfoList"
            :key="index"
          >
            <div class="side-card2" v-if="item.deviceTypeName == '电气火灾'">
              <div class="padcontent">
                <div class="realData">
                  <div class="title">
                    <div class="online">
                      <span>{{ item.device }}</span>
                      <span>
                        <img
                          :src="
                            item.onlineState == 1
                              ? require('@/assets/images/icon/online.png')
                              : require('@/assets/images/icon/offline.png')
                          "
                        />
                      </span>
                    </div>
                    <div class="online time" style="margin-top: -5px">
                      <span>
                        {{
                          item.lastPushTime
                        }}
                      </span>
                      <span></span>
                    </div>
                  </div>
                  <div class="information">
                    <div
                      v-for="(attr, index) in eleFireAttrList"
                      :key="index"
                      :class="
                        'item ' +
                        (betterShoild(attr.label, item[attr.key])
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">
                        {{ attr.label }}
                      </div>
                      <div class="bottom">
                        {{
                          item[attr.key] || item[attr.key] === 0
                            ? item[attr.key]
                            : "-"
                        }}<sub>{{ attr.unit }}</sub>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="side-card2" v-if="item.deviceTypeName == '断路器'">
              <div class="padcontent">
                <div class="realData">
                  <div class="title">
                    <div class="online">
                      <span>{{ item.device }}</span>
                      <span>
                        <img
                          :src="
                            item.onlineState == 1
                              ? require('@/assets/images/icon/online.png')
                              : require('@/assets/images/icon/offline.png')
                          "
                        />
                        <img
                          :src="
                            item.switchState == 1
                              ? require('@/assets/images/ProjectElectrical/he.png')
                              : require('@/assets/images/ProjectElectrical/fen.png')
                          "
                          style="margin-left: 5px"
                        />
                      </span>
                    </div>
                    <div class="online time" style="margin-top: -5px">
                      <span>
                        {{
                          item.lastPushTime
                        }}
                      </span>
                      <span></span>
                    </div>
                  </div>
                  <div class="information">
                    <div
                      v-for="(attr, index) in circuitBreakerAttrList"
                      :key="index"
                      :class="'item '"
                    >
                      <div class="top">
                        {{ attr.label }}
                      </div>
                      <div class="bottom">
                        {{
                          item[attr.key] || item[attr.key] === 0
                            ? item[attr.key]
                            : "-"
                        }}<sub>{{ attr.unit }}</sub>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="side-card3" v-if="item.deviceTypeName == '电表'">
              <div class="padcontent">
                <div class="realData">
                  <div class="title">
                    <div class="online">
                      <span>{{ item.device }}</span>
                      <span>
                        <img
                          :src="
                            item.onlineState == 1
                              ? require('@/assets/images/icon/online.png')
                              : require('@/assets/images/icon/offline.png')
                          "
                        />
                      </span>
                    </div>
                    <div class="online time" style="margin-top: -5px">
                      <span>
                        {{
                          item.lastPushTime
                        }}
                      </span>
                      <span></span>
                    </div>
                  </div>
                  <div class="totalEle">
                    <div class="totalItem">
                      <div class="icon">
                        <img
                          src="../../assets/images/ProjectElectrical/eleertc.png"
                        />
                      </div>
                      <div class="text">
                        <div class="title" style="font-size: 12px">
                          组合有功总电能
                        </div>
                        <div class="detil">
                          {{
                            item.combinedActiveEnergy
                              ? item.combinedActiveEnergy
                              : "-"
                          }}<sub>kWh</sub>
                        </div>
                      </div>
                    </div>
                    <div class="totalItem">
                      <div class="icon">
                        <img
                          src="../../assets/images/ProjectElectrical/eleertc.png"
                        />
                      </div>
                      <div class="text">
                        <div class="title" style="font-size: 12px">
                          有功总功率
                        </div>
                        <div class="detil">
                          {{ item.activePower ? item.activePower : "-"
                          }}<sub>kWh</sub>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="information">
                    <div
                      v-for="(attr, index) in electricMeterAttrList"
                      :key="index"
                      :class="'item '"
                    >
                      <div class="top">
                        {{ attr.label }}
                      </div>
                      <div class="bottom">
                        {{
                          item[attr.key] || item[attr.key] === 0
                            ? item[attr.key]
                            : "-"
                        }}<sub>{{ attr.unit }}</sub>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="right-content">
      <div
        class="side-panel side-panel-right"
        style="pointer-events: auto; height: 100%; gap: 5px"
      >
        <Title title="用电计量"></Title>
        <div class="echartsContent">
          <div class="ehcharts-title">
            <Select v-model="craneId1" @on-change="changeCrane1($event)">
              <Option
                v-for="(item, index) in elecData.data"
                :key="index"
                :label="item.name"
                :value="item.code"
                >{{ item.name }}</Option
              >
            </Select>
            <div class="btn" style="margin-left: auto">
              <div
                :class="
                  'myButton ' + (elecData.monthAndDay == 0 ? 'active' : '')
                "
                @click="onweekandmonth(0)"
              >
                逐时
              </div>
              <div
                :class="
                  'myButton ' + (elecData.monthAndDay == 1 ? 'active' : '')
                "
                @click="onweekandmonth(1)"
              >
                逐日
              </div>
              <div
                :class="
                  'myButton ' + (elecData.monthAndDay == 2 ? 'active' : '')
                "
                @click="onweekandmonth(2)"
              >
                总用电
              </div>
            </div>
          </div>
          <div class="echarts">
            <div id="echarts1" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <Title title="断路器告警统计"></Title>
        <div class="echartsContent" style="height: 30vh">
          <div class="ehcharts-title">
            <div class="btn" style="margin-left: auto">
              <div
                :class="
                  'myButton ' +
                  (faultTypeByColumnarData.weekAndMonth == 0 ? 'active' : '')
                "
                @click="weekandmonth(0)"
              >
                近1周
              </div>
              <div
                :class="
                  'myButton ' +
                  (faultTypeByColumnarData.weekAndMonth == 1 ? 'active' : '')
                "
                @click="weekandmonth(1)"
              >
                开累
              </div>
            </div>
          </div>
          <div class="echarts">
            <div id="echarts2" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <Title title="今日告警记录"></Title>
        <div class="warnNum" style="height: 20vh">
          <div class="right">
            <div
              class="timeLine"
              id="warnList"
              style="overflow-y: auto; overflow-x: hidden"
              @mouseenter="handleWarnMouseEnter"
              @mouseleave="handleWarnMouseLeave"
            >
              <Timeline v-if="warnInfoList.length > 0">
                <TimelineItem
                  v-for="(item, index) in warnInfoList"
                  :key="index"
                >
                  <p class="time">{{ item.createTime }}</p>
                  <div class="timeConetent">
                    <div class="title">
                      <span
                        ><img
                          src="../../assets/images/ProjectAnti/warnIcon.png"
                        />{{ item.deviceName }} --
                        {{ item.alarmTypeText }}</span
                      >
                    </div>
                    <div class="content">
                      <div>{{ item.content }}</div>
                    </div>
                  </div>
                </TimelineItem>
              </Timeline>
              <Empty v-else title="暂无告警记录"></Empty>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="floor-content">
      <div
        class="floorFlex"
        style="pointer-events: auto; width: 100%; height: 100%"
      >
        <img
          src="../../assets/images/ProjectElectrical/title.png"
          class="title"
        />
        <div
          class="floorEcharts"
          style="margin-top: 10px; width: 100%; height: calc(100% - 30px)"
        >
          <div class="echartsContent" style="height: 100%">
            <div class="ehcharts-title">
              <Select
                v-model="craneId3"
                @on-change="changeCrane3($event)"
                style="width: 180px"
              >
                <Option
                  v-for="(item, index) in fireData.data"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
            <div class="echarts" style="height: 100%">
              <div id="echarts3" style="width: 100%; height: 100%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ModalW900H486 :visibleW900h486.sync="isshow" title="监控"></ModalW900H486>
  </div>
</template>

<script>
import Title from "@/components/commonView/title3.vue";
import * as echarts from "echarts";
import ModalW900H486 from "@/components/modal/w900h486.vue";
import {
  selectByTypeNameList,
  selectByTypeNameListSearch,
  electricyByDayAndHour,
  faultTypeByColumnar,
  warnPage,
  boxRecordByhoursOrMonthly,
  electricyAll,
  getThreshold,
} from "@/api/xxindex";

export default {
  name: "ProjectAI",
  components: {
    Title,
    ModalW900H486,
  },
  data() {
    return {
      myCharts1: null,
      myCharts2: null,
      myCharts3: null,
      echarts3Data: [
        {
          data: [],
          name: "A相温度",
          color: "#EFBB47",
        },
        {
          data: [],
          name: "B相温度",
          color: "#2461F6",
        },
        {
          data: [],
          name: "C相温度",
          color: "#F2A250",
        },
        {
          data: [],
          name: "零线温度",
          color: "#7DD7FB",
        },
      ],
      warnInfoList: [
        {
          createTime: "2025-07-09 10:00:00",
          testingItem: "过载告警 电表（一级配电箱）",
          content: "零线电流大于相电流持续时间",
          alarmTypeText: "过载告警",
        },
        {
          createTime: "2025-07-09 10:00:00",
          testingItem: "过载告警 电表（一级配电箱）",
          content: "零线电流大于相电流持续时间",
          alarmTypeText: "过载告警",
        },
        {
          createTime: "2025-07-09 10:00:00",
          testingItem: "过载告警 电表（一级配电箱）",
          content: "零线电流大于相电流持续时间",
          alarmTypeText: "过载告警",
        },
        {
          createTime: "2025-07-09 10:00:00",
          testingItem: "过载告警 电表（一级配电箱）",
          content: "零线电流大于相电流持续时间",
          alarmTypeText: "过载告警",
        },
      ],
      deviceInfoList: [],
      deviceInfo: {
        deviceTotal: 0,
        onlineNum: 0,
        warnNum: 0,
      },

      // 用电统计图
      elecData: {
        data: [],
        echartsData: [],
        monthAndDay: 0,
      },
      craneId1: "",

      // 断路器统计图
      faultTypeByColumnarData: {
        data: [],
        echartsData: [],
        weekAndMonth: 0,
      },
      craneId2: "",
      warnInfo: [],

      // 电气火灾统计图
      fireData: {
        data: [],
        echartsData: [],
        monthAndDay: 0,
      },
      craneId3: "",
      eleFireAttrList: [
        {
          label: "漏电流",
          key: "residualCurrent",
          unit: "mA",
        },
        {
          label: "A相温度",
          key: "temperature1",
          unit: "℃",
        },
        {
          label: "B相温度",
          key: "temperature2",
          unit: "℃",
        },
        {
          label: "C相温度",
          key: "temperature3",
          unit: "℃",
        },
        {
          label: "零线温度",
          key: "temperature4",
          unit: "℃",
        },
      ],
      // 断路器属性列表
      circuitBreakerAttrList: [
        {
          label: "A相电流",
          key: "aphaseCurrent",
          unit: "A",
        },
        {
          label: "B相电流",
          key: "bphaseCurrent",
          unit: "A",
        },
        {
          label: "C相电流",
          key: "cphaseCurrent",
          unit: "A",
        },
        {
          label: "A相电压",
          key: "aphaseVoltage",
          unit: "V",
        },
        {
          label: "B相电压",
          key: "bphaseVoltage",
          unit: "V",
        },
        {
          label: "C相电压",
          key: "cphaseVoltage",
          unit: "V",
        },
      ],
      // 电表属性列表
      electricMeterAttrList: [
        {
          label: "A相电流",
          key: "aphaseCurrent",
          unit: "A",
        },
        {
          label: "B相电流",
          key: "bphaseCurrent",
          unit: "A",
        },
        {
          label: "C相电流",
          key: "cphaseCurrent",
          unit: "A",
        },
        {
          label: "A相电压",
          key: "aphaseVoltage",
          unit: "V",
        },
        {
          label: "B相电压",
          key: "bphaseVoltage",
          unit: "V",
        },
        {
          label: "C相电压",
          key: "cphaseVoltage",
          unit: "V",
        },
      ],
      // 添加轮播控制相关数据
      autoScroll: {
        timer: null,
        speed: 1, // 滚动速度(px)
        isHover: false, // 鼠标是否悬停
        scrollElement: null, // 滚动容器元素
      },
      // 告警记录滚动控制
      warnScroll: {
        timer: null,
        speed: 1,
        isHover: false,
        scrollElement: null,
      },
      // 电缆温度阈值
      Threshold: 0,
    };
  },
  created() {
    this.initRequest();
  },
  mounted() {
    // 初始化自动轮播
    this.$nextTick(() => {
      this.autoScroll.scrollElement = document.getElementById("runStateList");
      this.warnScroll.scrollElement = document.getElementById("warnList");
      this.startAutoScroll();
      this.startWarnScroll();
    });
  },
  destroyed() {
    // 清除定时器
    this.stopAutoScroll();
    this.stopWarnScroll();
  },
  methods: {
    initRequest() {
      selectByTypeNameList().then((res) => {
        if (res.code == "success") {
          console.log(res);
          this.deviceInfoList = res.data.map((i) => {
            if (i.records) {
              Object.assign(i, i.records);
            }
            return i;
          });
          this.deviceInfo.deviceTotal = this.deviceInfoList.length;
          this.deviceInfo.onlineNum = this.deviceInfoList.filter(
            (i) => i.onlineState == 1
          ).length;
          this.deviceInfo.warnNum = this.deviceInfoList.filter(
            (i) => i.status == 1
          ).length;
        }
      });
      // 用电统计图下拉框
      selectByTypeNameListSearch({ deviceTypeName: "电表" }).then((res) => {
        if (res.code == "success") {
          this.elecData.data = res.data;
          this.craneId1 = this.elecData.data[0].code;
          this.changeCrane1(this.craneId1);
        }
      });
      // 断路器统计图下拉框
      selectByTypeNameListSearch({ deviceTypeName: "断路器" }).then((res) => {
        if (res.code == "success") {
          this.faultTypeByColumnarData.data = res.data;
          this.craneId2 = this.faultTypeByColumnarData.data[0].code;
          this.changeCrane2(this.craneId2);
        }
      });
      // 电气火宅统计图下拉框
      selectByTypeNameListSearch({ deviceTypeName: "电气火灾" }).then((res) => {
        if (res.code == "success") {
          this.fireData.data = res.data;
          this.craneId3 = this.fireData.data[0].code;
          this.changeCrane3(this.craneId3);
        }
      });
      // 告警记录
      warnPage({
        customQueryParams: {
          alarmType: "",
          category: 3,
          date: [],
          // 获取当天的0:00 格式要求yyyy-mm-dd hh:mm:ss
          startDate: this.getTodayStart() + " 00:00:00",
          endDate: this.getTodayStart() + " 23:59:59",
          status: "",
          uniKey: "",
        },
        page: {
          current: 1,
          size: 20,
        },
        sort: [],
      }).then((res) => {
        if (res.code == "success") {
          this.warnInfoList = res.data.records;
        }
      });

      // 电缆温度阈值
      getThreshold().then((res) => {
        if (res.code == "success") {
          this.Threshold = Number(res.data.itemValue);
        }
      });
    },
    betterShoild(label, key) {
      if (!key) return false;
      if (label == "漏电流") return false;
      if (key > this.Threshold) {
        return true;
      }
      return false;
    },
    changeCrane1(val) {
      this.craneId1 = val;
      if (this.elecData.monthAndDay == 2) {
        this.totalElectricyRequest();
      } else {
        this.electricyRequest(val, this.elecData.monthAndDay);
      }
    },
    changeCrane2(val) {
      this.craneId2 = val;
      this.faultTypeByColumnarRequest(
        val,
        this.faultTypeByColumnarData.weekAndMonth
      );
    },
    changeCrane3(val) {
      this.craneId3 = val;
      this.fireDataRequest(val, this.fireData.monthAndDay);
    },
    // 电缆温度统计图
    fireDataRequest(val, monthAndDay) {
      boxRecordByhoursOrMonthly({
        deviceCode: val || "",
        dailyOrMonthly: monthAndDay || 0,
      }).then((res) => {
        if (res.code == "success") {
          this.echarts3Data = this.echarts3Data.map((i) => {
            if (i.name == "A相温度") {
              i.data = res.data.temperature_1_array;
              // 把其他的数据给到第一个数组里
              i.abscissa = res.data.abscissa;
              i.unit = res.data.tempUnit;
            }
            if (i.name == "B相温度") {
              i.data = res.data.temperature_2_array;
            }
            if (i.name == "C相温度") {
              i.data = res.data.temperature_3_array;
            }
            if (i.name == "零线温度") {
              i.data = res.data.temperature_4_array;
            }
            return i;
          });
          this.initCharts3();
        }
      });
    },
    //用电统计图
    electricyRequest(val, monthAndDay) {
      let Date = this.getTime();
      if (monthAndDay == 1) {
        Date = this.getTime().split("-");
        Date = Date[0] + "-" + Date[1];
      }
      electricyByDayAndHour({
        deviceCode: val || "",
        dailyOrMonthly: monthAndDay || this.elecData.monthAndDay,
        dailyOrMonthlyDate: Date,
      }).then((res) => {
        if (res.code == "success") {
          if (this.elecData.monthAndDay == 1) {
            let index = res.data.abscissa.length - 14;
            if (index < 0) {
              index = 0;
            }
            let data = res.data;
            data.abscissa = data.abscissa.slice(index);
            data.coordinate = data.coordinate.slice(index);
            this.elecData.echartsData = data;
          } else {
            this.elecData.echartsData = res.data;
          }
          this.elecData.echartsData = res.data;
          this.initCharts1();
        }
      });
    },
    // 断路器统计图
    faultTypeByColumnarRequest(val, weekAndMonth) {
      faultTypeByColumnar({
        deviceCode: "",
        cumulative: weekAndMonth || this.faultTypeByColumnarData.weekAndMonth,
      }).then((res) => {
        if (res.code == "success") {
          this.faultTypeByColumnarData.echartsData = res.data;
          this.initCharts2();
        }
      });
    },
    onweekandmonth(val) {
      this.elecData.monthAndDay = val;
      if (val == 2) {
        this.totalElectricyRequest();
        return;
      }
      this.electricyRequest(this.craneId1, this.elecData.monthAndDay);
    },
    weekandmonth(val) {
      this.faultTypeByColumnarData.weekAndMonth = val;
      this.faultTypeByColumnarRequest(
        this.craneId2,
        this.faultTypeByColumnarData.weekAndMonth
      );
    },
    totalElectricyRequest() {
      electricyAll({
        deviceCode: this.craneId1,
      }).then((res) => {
        if (res.code == "success") {
          this.elecData.echartsData = res.data;
          this.initCharts1();
        }
      });
    },
    // 获取时间
    getTime() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1; // 不足两位补0
      let day = date.getDate(); // 不足两位补0
      return `${year}-${month.toString().padStart(2, "0")}-${day
        .toString()
        .padStart(2, "0")}`;
    },
    getTodayStart() {
      const now = new Date();
      const todayStart = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate()
      );

      // 格式化为 yyyy-mm-dd hh:mm:ss
      const year = todayStart.getFullYear();
      const month = String(todayStart.getMonth() + 1).padStart(2, "0");
      const day = String(todayStart.getDate()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    },
    NumArrLenth(total) {
      return String(total)
        .split("")
        .map((i) => Number(i));
    },
    initCharts1() {
      this.myCharts1 && this.myCharts1.dispose();
      this.myCharts1 = echarts.init(document.getElementById("echarts1"));
      let len = this.elecData.echartsData.abscissa.length;
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          // 显示时间和吊重值
          formatter: (params) => {
            return `时间：${params[0].name}<br/>${params[0].value}`;
          },
        },
        grid: {
          left: "4%",
          right: 0,
          bottom: 0,
          top: 30,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
            formatter: (value) => {
              if (this.elecData.monthAndDay == 1) {
                return value.slice(5);
              } else if (this.elecData.monthAndDay == 2) {
                return value;
              } else {
                return value.split(" ")[1];
              }
            },
          },
          data: this.elecData.echartsData.abscissa,
        },
        yAxis: {
          name: "单位（" + this.elecData.echartsData.unit + "）",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          {
            data: this.elecData.echartsData.coordinate,
            type: "bar",
            barMaxWidth: 12,
            // label: {
            //     show: true,
            //     position: 'top',
            //     color: '#fff',
            //     formatter: (param) => {
            //         if (len > 14) {
            //             return param.dataIndex % 3 == 0 ? param.value : ''
            //         } else if (len > 7) {
            //             return param.dataIndex % 2 == 0 ? param.value : ''
            //         } else {
            //             return param.value
            //         }
            //     }
            // },
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#336CEF", // 0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100%处的颜色
                  },
                ],
              },
              borderColor: "#4872DD",
            },
          },
        ],
      };
      this.myCharts1.setOption(option);
    },
    initCharts2() {
      this.myCharts2 && this.myCharts2.dispose();
      this.myCharts2 = echarts.init(document.getElementById("echarts2"));
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "4%",
          right: "2%",
          bottom: 0,
          top: 30,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
            rotate: 45, // 设置标签旋转45度
            interval: 0,
            // formatter: function(value) {
            //     if (value.length > 2) {
            //         return value.substring(0, 2) + '\n' + value.substring(2);
            //     }
            //     return value;
            // }
          },
          data: this.faultTypeByColumnarData.echartsData.abscissa,
        },
        // 报警为黄色 故障为红色
        legend: {
          right: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          icon: "circle",
          itemHeight: 3,
          itemWidth: 3,
          data: [
            {
              name: "报警",
              itemStyle: {
                color: "#FFCC00",
              },
            },
            {
              name: "故障",
              itemStyle: {
                color: "#FF6B6B",
              },
            },
          ],
        },
        yAxis: {
          name: "单位（次）",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          minInterval: 1, // 最小刻度间隔为1
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "报警",
            data: this.faultTypeByColumnarData.echartsData.giveAlarm,
            type: "bar",
            stack: "total",
            barWidth: "40%",
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#F1D152", // 0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100%处的颜色
                  },
                ],
              },
            },
            label: {
              show: false,
              position: "top",
              formatter: function (params) {
                // 只显示特定值的标签
                return `{styleName|${params.value}}`;
              },
              rich: {
                styleName: {
                  color: "#FFCC00",
                  fontSize: 12,
                  fontWeight: "bold",
                },
              },
            },
          },
          {
            name: "故障",
            data: this.faultTypeByColumnarData.echartsData.giveFault,
            type: "bar",
            stack: "total",
            barWidth: "40%",
            label: {
              show: false,
              position: "top",
              formatter: function (params) {
                // 只显示特定值的标签
                return `{styleName|${params.value}}`;
              },
              rich: {
                styleName: {
                  color: "red",
                  fontSize: 12,
                  fontWeight: "bold",
                },
              },
            },
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#792527", // 0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100%处的颜色
                  },
                ],
              },
            },
          },
        ],
      };
      this.myCharts2.setOption(option);
      this.myCharts2.on("legendselectchanged", (params) => {
        console.log("legendselectchanged", params);
        // 如果报警和故障都选中，则label都不显示，否则显示选中的label
        if (params.selected.报警 && params.selected.故障) {
          option.series[0].label.show = false;
          option.series[1].label.show = false;
        } else {
          option.series[0].label.show = params.selected.报警;
          option.series[1].label.show = params.selected.故障;
        }
        this.myCharts2.setOption(option, { replaceMerge: ["series"] });
      });
    },
    initCharts3() {
      this.myEcharts3 && this.myEcharts3.dispose();
      this.myEcharts3 = echarts.init(document.getElementById("echarts3"));
      let option = {
        legend: {
          data: this.echarts3Data.map((i) => i.name),
          right: 0,
          textStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
        },
        grid: {
          top: "17%",
          left: "2%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        xAxis: {
          type: "category",
          data: this.echarts3Data.filter((i) => i.abscissa)[0].abscissa,
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
          },
        },
        yAxis: {
          name:
            "单位 (" +
            this.echarts3Data
              .filter((i) => i.unit)
              .map((i) => i.unit)
              .join(",") +
            ")",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: this.initServer(),
      };
      this.myEcharts3.setOption(option);
    },
    initServer() {
      return this.echarts3Data.map((i, index) => {
        let serversData = {
          name: i.name,
          itemStyle: {
            opacity: 0,
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              color: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0.2,
                    color: "rgba(255, 255, 255)", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: i.color, // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              borderColor: i.color,
              borderWidth: 1,
            },
            scale: 3,
          },
          data: i.data,
          type: "line",
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: i.color, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "transparent", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },

          lineStyle: {
            color: i.color,
          },
        };
        if (index == 0) {
          let data = {
            data: [
              {
                yAxis: this.Threshold, // 固定值预警线
                name: "预警线",
              },
            ],
            lineStyle: {
              color: "#ff0000",
              width: 2,
              type: "dashed",
            },
            label: {
              formatter: "预警线" + ": {c}",
              position: "middle",
            },
          };
          serversData.markLine = data;
        }
        return serversData;
      });
    },
    // 开始自动滚动
    startAutoScroll() {
      if (this.autoScroll.timer) {
        clearInterval(this.autoScroll.timer);
      }
      this.autoScroll.timer = setInterval(() => {
        if (this.autoScroll.isHover) return;

        const element = this.autoScroll.scrollElement;
        if (!element) return;

        // 如果滚动到底部，重置到顶部
        if (element.scrollTop >= element.scrollHeight - element.clientHeight) {
          element.scrollTop > 0 && this.resetToTop();
          // console.log('--------', element.scrollTop)
        } else {
          element.scrollTop += this.autoScroll.speed;
        }
      }, 50);
    },
    // 重置到顶部
    resetToTop() {
      const element = this.autoScroll.scrollElement;
      if (!element) return;
      element.scrollTop = 0;
      this.stopAutoScroll();
      setTimeout(() => {
        this.startAutoScroll();
      }, 1000);
    },
    // 停止自动滚动
    stopAutoScroll() {
      if (this.autoScroll.timer) {
        clearInterval(this.autoScroll.timer);
        this.autoScroll.timer = null;
      }
    },
    // 处理鼠标进入
    handleMouseEnter() {
      this.autoScroll.isHover = true;
    },
    // 处理鼠标离开
    handleMouseLeave() {
      this.autoScroll.isHover = false;
    },
    // 开始告警记录滚动
    startWarnScroll() {
      if (this.warnScroll.timer) {
        clearInterval(this.warnScroll.timer);
      }
      this.warnScroll.timer = setInterval(() => {
        if (this.warnScroll.isHover) return;

        const element = this.warnScroll.scrollElement;
        if (!element) return;

        // 如果滚动到底部，重置到顶部
        // console.log('element.scrollTop', element.scrollTop, element.scrollHeight - element.clientHeight)
        if (
          element.scrollTop >=
          element.scrollHeight - element.clientHeight - 1
        ) {
          element.scrollTop > 0 && this.resetWarnToTop();
        } else {
          element.scrollTop += this.warnScroll.speed;
        }
      }, 50);
    },
    // 停止告警记录滚动
    stopWarnScroll() {
      if (this.warnScroll.timer) {
        clearInterval(this.warnScroll.timer);
        this.warnScroll.timer = null;
      }
    },
    // 重置告警记录到顶部
    resetWarnToTop() {
      const element = this.warnScroll.scrollElement;
      if (!element) return;
      element.scrollTop = 0;
      this.stopWarnScroll();
      setTimeout(() => {
        this.startWarnScroll();
      }, 1000);
    },
    // 处理告警记录鼠标进入
    handleWarnMouseEnter() {
      this.warnScroll.isHover = true;
    },
    // 处理告警记录鼠标离开
    handleWarnMouseLeave() {
      this.warnScroll.isHover = false;
    },
  },
};
</script>

<style lang="less" scoped>
// 隐藏默认滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
}

// 自定义滚动条轨道
::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

// 自定义滚动条滑块
::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

#runStateList {
  transition: all 0.3s ease;
  scroll-behavior: smooth;
}

#runStateList:hover {
  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

#warnList {
  transition: all 0.3s ease;
  scroll-behavior: smooth;
}

#warnList:hover {
  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

/deep/ .side-panel {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  );
  height: 100%;
  gap: 0;
}

.side-panel-right {
  background: linear-gradient(
    to left,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
  gap: 0px;
}
/deep/ .ivu-timeline-item-head {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/ProjectAnti/point.png) no-repeat center
    center;
  background-size: 100% auto;
  border: none;
  position: absolute;
  left: 0px;
  top: -2px;
}

/deep/ .ivu-timeline-item-tail {
  position: absolute;
  left: 10px;
  top: 5px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

/deep/ .ivu-timeline-item {
  padding: 0;
}

.timeConetent {
  width: 100%;
  height: 75px;
  display: flex;
  flex-direction: column;
  background: url(../../assets/images/ProjectAnti/warn.png) no-repeat center
    center;
  background-size: 100% 100%;
  color: #fff;

  .title {
    height: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 5px;
    height: 35px;
    align-items: center;

    span {
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }

    // span:last-child {
    //     width: 68px;
    //     height: 80%;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     color: #fff;
    //     background: url(../../assets/images/ProjectAnti/Button.png) no-repeat center center;
    //     background-size: 100% 100%;
    //     cursor: pointer;
    // }
  }

  .content {
    height: calc(75px - 35px);
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    div {
      width: 100%;
      text-align: left;
    }

    div::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      margin: 0 8px;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.warnNum {
  width: 100%;
  height: calc(100% - 25vh - 25vh - 80px);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;

    .item {
      width: 100%;
      height: 48%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;

      .icon {
        width: 100%;
        height: 30px;
        background-image: linear-gradient(
          to right,
          transparent 1%,
          #102f97 10%,
          transparent 100%
        );
        display: flex;
        align-items: center;
        color: #fff;
      }

      .icon::before {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../../assets/images/ProjectCrane/iconLeft.png) no-repeat
          center center;
        background-size: contain;
        background-position-y: 3px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
      }

      .value {
        width: 80px;
        height: calc(100% - 30px - 10px);
        background: url(../../assets/images/ProjectCrane/warnNumBack.png)
          no-repeat center center;
        background-size: contain;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 29px;
        color: #fff;
        padding-bottom: 25px;
        box-sizing: border-box;
      }
    }
  }

  .right {
    flex: 1;
    height: 100%;

    .timeLine {
      width: 100%;
      height: 100%;
      overflow-y: auto;

      .time {
        color: #fff;
      }
    }
  }
}

.retardedTotal {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  gap: 5px;
  margin-top: -10px;

  .totalRow {
    width: 100%;
    height: calc(100% / 6.6);
    background-image: linear-gradient(
      to right,
      #4b83b700,
      #4b83b71a 52.5%,
      #4b83b700
    );
    display: flex;
    align-items: center;
    gap: 10px;

    .totalLeft {
      width: 33%;
      height: 100%;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        background: url(../../assets/images/ProjectAI/icon.png) no-repeat center
          center;
        background-size: 100% 100%;
        font-family: "youshebiaotihei";
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .icon.active {
        background: url(../../assets/images/ProjectAI/active.png) no-repeat
          center center !important;
        background-size: 100% 100% !important;
      }
    }

    .totalmiddle {
      width: 45%;
      height: 100%;
      display: flex;
      align-items: center;
      pointer-events: none;

      /deep/ .ivu-slider-wrap {
        height: 10px;
        background: url(../../assets/images/ProjectAI/blur.png) no-repeat center
          center;
        background-size: 100% 100%;
      }

      /deep/ .ivu-slider-bar {
        height: 10px;
        background: url(../../assets/images/ProjectAI/progress.png) no-repeat
          center center;
        background-size: 100% 80%;
      }

      /deep/ .ivu-slider-button {
        width: 30px;
        height: 30px;
        background: url(../../assets/images/ProjectAI/point.png) no-repeat
          center center;
        background-size: 100% 100%;
        background-position-x: -5px;
        border: none;
        transform: scale(1.1);
      }

      /deep/ .ivu-slider-button-wrap {
        position: absolute;
        top: -10px;
      }
    }

    .totalRight {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      span:first-child {
        font-size: 18px;
        color: #fff;
        font-weight: bold;
      }
    }
  }

  .totalRow:first-child {
    background-image: linear-gradient(
      to right,
      #4b83b700,
      #7297b964 52.5%,
      #4b83b700
    ) !important;
  }
}

.totalNums {
  width: 100%;
  height: 20vh;
  display: flex;
  justify-content: space-between;
  gap: 20px;

  .echarts2 {
    width: 35%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(../../assets/images/ProjectAI/bing.png) no-repeat center
      center;
    background-size: 84% auto;
    background-position-y: 55%;
    position: relative;

    .line {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 70%;
      height: 2px;
      background-image: linear-gradient(
        to right,
        transparent,
        #fff 52.5%,
        transparent
      );
      box-shadow: 0 0 10px #fff;
    }
  }

  .totalList {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 5px;

    .content {
      width: 100%;
      height: calc(100% / 6);
      background-image: linear-gradient(
        to right,
        #4b83b700,
        #4b83b71a 52.5%,
        #4b83b700
      );
      display: flex;
      align-items: center;
      gap: 10px;

      .title {
        width: 70%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .leftPoint {
          height: 100%;
          display: flex;
          align-items: center;

          .point {
            margin-right: 10px;
            width: 5px;
            height: 5px;
            border-radius: 50px;
          }
        }
      }

      .percentage {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }

  .right-content {
    width: 408px;
    position: absolute;
    right: 0;
    top: 0;
    height: calc(100%);
    flex: 1;
  }
}

.padcontent {
  width: 100%;
  height: 100%;
  padding-top: 0;
  padding: 5px;
  box-sizing: border-box;
  margin-left: 5px;
}

.side-card1 {
  width: 100%;
  height: 9vh;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  padding: 10px;
  padding-top: 0;
  box-sizing: border-box;
  margin-top: -20px;
  .side-card1-left {
    width: 40%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
    .side-card1-left-title {
      img {
        width: 14px;
        height: 14px;
        object-fit: contain;
      }
      color: #fff;
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(38, 82, 132, 0.4),
        transparent
      );
    }
    .processNum {
      width: 100%;
      height: calc(100% - 30px);
      // background: url(../../assets/images/icon/borderBack.png) no-repeat center center;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      position: relative;
      padding: 3.33px 0;
      .left {
        width: 30px;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: url(../../assets/images/icon/borderLeft.png) no-repeat
          center center;
        background-size: 100% 100%;
      }
      .right {
        width: 30px;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        background: url(../../assets/images/icon/borderRight.png) no-repeat
          center center;
        background-size: 100% 100%;
      }
      .back {
        width: 30px;
        height: 40px;
        background: url(../../assets/images/icon/textBack.png) no-repeat center
          center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: "DINPro";
        font-weight: 700;
        padding: 4px 8px;
      }
      .backDisponse {
        background: url(../../assets/images/icon/disponse.png) no-repeat center
          center !important;
        background-size: 100% 100% !important;
        color: rgba(255, 255, 255, 0.4);
        font-size: 20px;
        font-family: "PingFang SC";
      }
    }
  }
  .side-card1-right {
    width: 60%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 10px;
    .side-card1-right-item {
      width: 100%;
      height: 48%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: url(../../assets/images/ProjectElectrical/icon2.png) no-repeat
        center center;
      background-size: 100% 100%;
      .side-card1-right-item-title {
        color: #fff;
        display: flex;
        align-items: center;
      }
      .side-card1-right-item-title::before {
        content: "";
        width: 10px;
        height: 10px;
        background: url(../../assets/images/ProjectElectrical/icon4.png)
          no-repeat center center;
        background-size: 100% 100%;
        margin-right: 5px;
        margin-left: 10px;
      }
      .side-card1-right-item-num {
        & > span:first-child {
          font-weight: bold;
        }
      }
    }
    .side-card1-right-item.error {
      background: url(../../assets/images/ProjectElectrical/icon3.png) no-repeat
        center center !important;
      background-size: 100% 100% !important;
      .side-card1-right-item-title::before {
        content: "";
        width: 6px;
        height: 6px;
        background: url(../../assets/images/ProjectElectrical/icon5.png)
          no-repeat center center !important;
      }
    }
  }
}

.side-card2 {
  width: 100%;
  height: 33%;
  box-sizing: border-box;
  .realData {
    width: 100%;
    height: 100%;
    background: url(../../assets/images/ProjectElectrical/card.png) no-repeat
      center center;
    background-size: 100% 100%;
    padding: 1px 8px;
    box-sizing: border-box;

    .title {
      width: 100%;
      height: 68px;
      display: flex;
      flex-direction: column;

      .online {
        width: 100%;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span:first-child::before {
          content: "";
          width: 20px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 5px;
          background: url(../../assets/images/ProjectElectrical/icon6.png)
            no-repeat center center;
          background-size: contain;
        }

        span:last-child {
          cursor: pointer;
          color: #184099;
        }

        img {
          width: 42px;
          height: auto;
          margin-top: 5px;
        }
      }

      .time {
        font-size: 14px;
        span:before {
          background: url(../../assets/images/ProjectElectrical/icon7.png)
            no-repeat center center !important;
          background-size: contain !important;
        }
      }
    }

    .information {
      width: 100%;
      height: calc(100% - 80px);
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 10px;
      padding-top: 0;
      box-sizing: border-box;

      .item {
        width: 31%;
        height: 50px;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .top {
          width: 100%;
          height: 45%;
          background-image: linear-gradient(
            to right,
            transparent,
            rgb(24, 46, 83),
            transparent
          );
          display: flex;
          align-items: center;
          padding-left: 10px;
        }

        .bottom {
          width: 100%;
          height: 55%;
          background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
            center center;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #fff;
          box-sizing: border-box;
          padding-left: 15px;

          sub {
            margin-left: 5px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.4);
          }
        }
      }

      .item.active {
        .top {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(179, 55, 55, 0.612),
            transparent
          ) !important;
        }

        .bottom {
          background: url(../../assets/images/ProjectAnti/red.png) no-repeat
            center center !important;
          background-size: 100% 100% !important;
        }
      }
    }
  }
}

.side-card3 {
  width: 100%;
  height: 35%;
  box-sizing: border-box;
  .realData {
    width: 100%;
    height: 100%;
    background: url(../../assets/images/ProjectElectrical/card.png) no-repeat
      center center;
    background-size: 100% 100%;
    padding: 1px 8px;
    box-sizing: border-box;

    .title {
      width: 100%;
      height: 68px;
      display: flex;
      flex-direction: column;

      .online {
        width: 100%;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span:first-child::before {
          content: "";
          width: 20px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 5px;
          background: url(../../assets/images/ProjectElectrical/icon6.png)
            no-repeat center center;
          background-size: contain;
        }

        span:last-child {
          cursor: pointer;
          color: #184099;
        }

        img {
          width: 42px;
          height: auto;
          margin-top: 5px;
        }
      }

      .time {
        font-size: 14px;
        span:before {
          background: url(../../assets/images/ProjectElectrical/icon7.png)
            no-repeat center center !important;
          background-size: contain !important;
        }
      }
    }

    .totalEle {
      width: 95%;
      height: 20%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      margin: 10px auto;
      margin-bottom: 20px;
      .totalItem {
        width: 48%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon {
          width: 30px;
          height: 100%;
          display: flex;
          align-items: center;
          img {
            width: 100%;
            height: auto;
          }
        }
        .text {
          width: calc(100% - 20px - 10px);
          height: 100%;
          display: flex;
          flex-direction: column;
          gap: 5px;
          text-align: center;
          color: white;
          .title {
            height: 20px;
            width: 100%;
            background-image: linear-gradient(
              to right,
              transparent,
              rgba(24, 46, 83, 0.7),
              transparent
            );
          }
          .detil {
            height: calc(100% - 20px);
            width: 100%;
            background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
              center center;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            color: #fff;
            box-sizing: border-box;
            padding-left: 15px;
            // 文字蓝色背影
            text-shadow: 0 0 10px 0 rgb(36, 95, 175);

            sub {
              margin-left: 5px;
              font-size: 14px;
              color: rgba(255, 255, 255, 0.4);
            }
          }
        }
      }
    }

    .information {
      width: 100%;
      height: calc(100% - 120px);
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 10px;
      padding-top: 0;
      box-sizing: border-box;

      .item {
        width: 31%;
        height: 50px;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .top {
          width: 100%;
          height: 45%;
          background-image: linear-gradient(
            to right,
            transparent,
            rgb(24, 46, 83),
            transparent
          );
          display: flex;
          align-items: center;
          padding-left: 10px;
        }

        .bottom {
          width: 100%;
          height: 55%;
          background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
            center center;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          box-sizing: border-box;
          padding-left: 15px;

          sub {
            margin-left: 5px;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.4);
          }
        }
      }

      .item.active {
        .top {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(179, 55, 55, 0.612),
            transparent
          ) !important;
        }

        .bottom {
          background: url(../../assets/images/ProjectAnti/red.png) no-repeat
            center center !important;
          background-size: 100% 100% !important;
        }
      }
    }
  }
}

.echartsContent {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-direction: column;
  margin-top: -10px;
  margin-bottom: 15px;

  .echarts {
    width: 100%;
    height: calc(100% - 20%);
  }
}

.floor-content {
  width: calc(100% - 408px - 408px - 40px);
  position: absolute;
  height: 35%;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  background-image: linear-gradient(
    to top,
    rgba(9, 16, 35, 0.713),
    transparent
  );
  z-index: 99;
  .title {
    height: 30px;
    object-fit: contain;
  }
}

.warnContent {
  width: 100%;
  height: calc(100% - 25vh - 25vh - 40px);
  display: flex;
  flex-direction: column;
  .timeLine {
    width: 100%;
    height: 75px;
    background: url(../../assets/images/ProjectAnti/warn.png) no-repeat center
      center;
    background-size: 100% 100%;
    color: #fff;
    .title {
      height: 100%;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;
      height: 35px;
      align-items: center;

      span {
        img {
          width: 16px;
          height: 16px;
          object-fit: contain;
          margin-bottom: 2px;
          margin-right: 5px;
        }
      }

      // span:last-child {
      //     width: 68px;
      //     height: 80%;
      //     display: flex;
      //     align-items: center;
      //     justify-content: center;
      //     color: #fff;
      //     background: url(../../assets/images/ProjectAnti/Button.png) no-repeat center center;
      //     background-size: 100% 100%;
      //     cursor: pointer;
      // }
    }
    .content {
      height: calc(75px - 35px);
      box-sizing: border-box;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;

      div {
        width: 100%;
        text-align: left;
      }

      div::before {
        content: "";
        display: inline-block;
        width: 5px;
        height: 5px;
        background-color: #fff;
        margin: 0 8px;
        transform: rotate(45deg);
        vertical-align: middle;
      }
    }
  }
}

.ehcharts-title {
  height: 20%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/ .ivu-select {
    width: 40%;
  }

  /deep/.ivu-select-selection {
    background-image: linear-gradient(to bottom, #223d71, #1d3051);
    border: 2px solid #2e5bbf;
    height: 30px;
    color: #fff;
  }

  /deep/ .ivu-select-placeholder {
    color: #fff;
  }

  /deep/ .ivu-select-arrow {
    color: #fff;
  }

  /deep/ .ivu-select-item {
    background-image: linear-gradient(
      to bottom,
      rgba(34, 61, 113, 3),
      rgba(29, 48, 81, 0.9)
    );
    color: #fff;
  }

  /deep/ .ivu-select-dropdown {
    padding: 0;
  }

  .btn {
    display: flex;
    gap: 10px;
  }
}

.myButton {
  width: 62px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center center;
  // background-size: 100% 100%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  border-radius: 3px;
}

.myButton.active {
  background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center
    center;
  background-size: 100% 100%;
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../assets/images/icon/s_title.png) no-repeat center center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -15px;
}

.echarts-total {
  width: 100%;
  height: 25vh;
  margin-top: -10px;
  position: relative;

  .posiButton {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    height: 26px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99;
    div {
      background-color: rgb(149, 149, 149, 0.5);
      width: 64px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      border-radius: 3px;
      cursor: pointer;
    }

    .active {
      background-color: transparent;
      background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat
        center center;
      background-size: 100% 100%;
    }
  }

  .echarts1 {
    width: 100%;
    height: calc(100% - 20px + 26px);
  }
}

.floorOverview {
  height: 73%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;

  .AIWarn {
    width: 100%;
    display: flex;
    gap: 10px;

    .photo {
      width: 40%;
      height: 100%;
      background-image: linear-gradient(to bottom, #ae2319 60%, #3d0704);
      padding: 2px;
      box-sizing: border-box;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .photo.yes::before {
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.2);
      z-index: 99;
      cursor: pointer;
    }

    .photo.yes::after {
      content: "▶";
      width: 18px;
      height: 18px;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      border-radius: 50px;
      cursor: pointer;
    }

    .info {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 3px;

      div {
        width: 100%;
        height: 32%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-radius: 3px;
        border-left: 2px solid #af2319;
        background-image: linear-gradient(to right, #5b1a20 0%, transparent);

        img {
          width: 18px;
          height: 18px;
          object-fit: contain;
          margin: 0 5px;
        }
      }
    }
  }
}

.OneHourWarn {
  width: 100%;
  height: 20vh;
  margin-top: -25px;

  .title {
    font-size: 14px;
    color: #fff;
    text-align: left;
  }

  .content {
    width: 100%;
    height: calc(100% - 20px);
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;

    .safePeople {
      width: 100%;
      height: 17%;
      background-image: linear-gradient(to right, #5b1a20 0%, transparent);
      display: flex;
      justify-content: space-between;
      gap: 10px;
      box-sizing: border-box;
      padding: 3px 5px;

      div:first-child {
        width: 46%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      div:last-child {
        flex: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      div {
        span:first-child {
          color: rgba(255, 255, 255, 0.7);
        }

        span:last-child {
          color: #fff;
        }
      }
    }
  }
}

.floor-nav {
  width: 100%;
  height: 20vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: -10px;
  gap: 10px;

  .floor-item {
    width: 100%;
    height: 25%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    font-size: 13px;

    div {
      // height: 100%;
      padding: 4px 8px;
      background-color: rgba(255, 255, 255, 0.1);
      box-sizing: border-box;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
    }

    .active {
      background-color: transparent;
      background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat
        center center;
      background-size: 100% 100%;
      color: #fff;
    }
  }
}
</style>

<template>
  <AEcharts ref="barEchart" :options="options" :style="{ height: height }" />
</template>

<script>
import AEcharts from "@/components/commonView/AEcharts.vue";
export default {
  name: "radarEchart",
  components: {
    AEcharts,
  },
  props: {
    seriesName: { default: "" },
    list: {
      default() {
        return [];
      },
    },
    unit: { default: "" },
    height: { default: "19.72vh" },
  },
  watch: {
    seriesName: {
      handler() {
        if (this.seriesName) {
          this.options.series[0].name = this.seriesName;
        }
      },
      deep: true,
      immediate: true,
    },
    list: {
      handler() {
        this.nameList = []
        if (this.list) {
            let max = Math.max(...this.list.map(item => item.value))
            let indicator = []
            let data = []
            this.list.forEach(item => {
                this.nameList.push(item.name)
                indicator.push({
                    name: item.name,
                    max: max + 1
                })
                data.push(item.value)
            });
          this.options.radar.indicator =indicator
          this.options.series[0].data = [
            {name: '分布图', value: data}
          ]
        } else {
          this.options.radar.indicator = []
          this.options.series[0].data = []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      nameList: [],
      options: {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            let html = ''
            this.nameList.forEach((item, index) => {
              html += `<div>${item}: ${params.value[index]}${this.unit}</div>`
            })
            return html
          },
          confine: true
        },
        radar: {
          center: ['50%', '54%'],
          indicator: [],
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(71, 121, 192, 0.68)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(71, 121, 192, 0.68)",
            },
          },
          splitArea: {
            areaStyle: {
              color: ["rgba(7, 39, 128, 0.61)", "rgba(71, 121, 192, 0.23)"],
            },
          },
          axisName: {
            color: "#fff",
          },
          nameGap: 5
        },
        series: [
          {
            name: "分布图",
            type: "radar",
            data: [],
            lineStyle: {
              color: "rgba(34, 252, 190, 1)",
            },
            symbolSize: 0,
            areaStyle: {
              color: "rgba(34, 252, 190, 0.2)",
            }
          },
        ],
      },
    };
  },
  methods: {
    resize() {
      this.$refs.barEchart.resize();
    },
  },
};
</script>

<style lang="less" scoped></style>

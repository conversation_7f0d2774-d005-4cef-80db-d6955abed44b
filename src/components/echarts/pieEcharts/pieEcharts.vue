<template>
    <div class="pie-echarts">
        <div class="pie-main" :style="{ width: pieWidth, height: pieHeight }">
            <AEcharts :options="options" @on-load="loadChartPie" />
            <div class="echart-info">
                <div class="value">{{curTooltip.percent}}%</div>
                <div class="line"></div>
                <div class="name">{{curTooltip.name}}</div>
            </div>
        </div>
        <div class="legent-list" :style="{ height: legendHeight }">
            <div class="legent-item" v-for="(item, index) in legendList" :key="index">
                <div class="yuan" :style="{ background: item.itemStyle.color }"></div>
                <div class="name">{{ item.name }}</div>
                <div class="num">{{ item.value }}{{ unit }}</div>
                <div class="percent">{{ item.percent }}%</div>
            </div>
        </div>
    </div>
</template>
<script>
import AEcharts from "@/components/commonView/AEcharts.vue";
import { pieAutoAnimation } from "@/utils/echarts-mixin";
export default {
    name: 'pie-echarts',
    components: {
        AEcharts
    },
    mixins: [pieAutoAnimation],
    props: {
        list: { default() { return [] } },
        radius: { default() { return ['66%', '82%'] } },
        radius2: { default() { return ['80%', '92%'] } },
        unit: { default: '' },
        autoplay: { default: true }, // 是否自动播放
        pieWidth: { default: '11.11vh' },
        pieHeight: { default: '11.11vh' },
        legendHeight: { default: '10.37vh' },
        colorList: { default() { return ['#0052FD', '#0090FF', '#60D8FF', '#FFA340'] } },
    },
    data() {
        return {
            options: {
                series: [
                    {
                        name: '',
                        type: 'pie',
                        center: ['50%', '50%'],
                        radius: this.radius,
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                        },
                        data: []
                    },
                    {
                        name: '',
                        type: 'pie',
                        center: ['50%', '50%'],
                        radius: this.radius2,
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                        },
                        itemStyle: {
                            opacity: 0.3
                        },
                        data: []
                    }
                ]
            },
            legendList: []
        }
    },
    computed: {
        curTooltip() {
            return this.options.series[0].data[this.currentHighlightIndex] || {}
        },
    },
    watch: {
        list: {
            handler() {
                this.initData()
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        initData() {
            if (!this.list || !this.list.length) {
                this.options.series[0].data = []
                this.options.series[1].data = []
                this.legendList = []
                return
            }
            let list = this.list
            let len = this.colorList.length
            let data = []
            let total = 0
            data = list.map((item, index) => {
                total += item.value
                let itemStyle = { color: this.colorList[index % len] }
                return { ...item, itemStyle }
            })
            data.forEach(item => {
                item.percent = total > 0 ? parseFloat((100 * item.value / total).toFixed(2)) : 0
            })
            this.options.series[0].data = data
            this.options.series[1].data = data
            this.legendList = data
            this.startAnim()
        },
        loadChartPie(echart) {
            if (this.autoplay) {
                this.loadChart(echart)
            }
        },
        startAnim() {
            if (this.autoplay) {
                this.dataLen = this.options.series[0].data.length
                if (this.dataLen > 2) {
                    this.currentHighlightIndex = 0
                    this.initChartAnim()
                } else {
                    this.clearAnim()
                }
            }
        }
    }
}
</script>
<style lang="less" scoped>
.pie-echarts{
    width: 100%;
    display: flex;
    align-items: center;
    .pie-main{
        position: relative;
        width: 100%;
        height: 100%;
    }
}
.echart-info{
      position: absolute;
      width: 74%;
      height: 74%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .value{
        font-size: 1.48vh;
        font-family: YouSheBiaoTiHei;
        color: #fff;
        line-height: 2.59vh;
      }
      .line{
        width: 60%;
        height: 0.2px;
        background: #fff;
        margin: 0.185vh 0;
      }
      .name{
        font-size: 1.11vh;
        color: rgba(255, 255, 255, 0.6);
        line-height: 1.57vh;
      }
    }
.legent-list{
    flex: 1;
}
.legent-item{
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, rgba(75, 131, 183, 0) 0%, rgba(75, 131, 183, 0.125) 50%, rgba(75, 131, 183, 0) 100%);
    padding: 0.37vh 0.74vh;
    color: #fff;
    margin-bottom: 0.37vh;
    font-size: 1.11vh;
    line-height: 1.57vh;
    .yuan{
        width: 0.37vh;
        height: 0.37vh;
        background: #fff;
        margin-left: 0.55vh;
        margin-right: 0.94vh;
    }
    .name{
        flex: 1;
        margin-right: 0.74vh;
    }
    .num{
        min-width: 4vh;
    }
    .percent{
        font-family: YouSheBiaoTiHei;
        font-size: 1.2vh;
        width: 4vh;
        text-align: right;
    }
    &:last-child{
        margin-bottom: 0;
    }
}
</style>
<template>
    <AEcharts ref="barEchart" :options="options" />
  </template>
  
  <script>
  import AEcharts from "@/components/commonView/AEcharts.vue";
  export default {
    name: 'barEchart',
    components: {
        AEcharts
    },
    props: {
      seriesName: { default: '数量' },
      barData: { default() {
          return { day: [], d1: [] }
        }},
      unit: { default: '' },
    },
    watch: {
      seriesName: {
        handler() {
          if (this.seriesName) {
            this.options.series[0].name = this.seriesName
          }
        },
        deep: true,
        immediate: true
      },
      barData: {
        handler() {
          if (this.barData && this.barData.day) {
            this.options.xAxis[0].data = this.barData.day
            this.options.series[0].data = this.barData.d1
          }
        },
        deep: true,
        immediate: true
      },
      unit: {
        handler() {
          if (this.unit) {
            this.options.yAxis[0].name = '单位：' + this.unit
          }
        },
        deep: true,
        immediate: true
      },
    },
    data() {
      return {
        options: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
              z: 1
            },
            formatter: (params) => {
              let html = `<div>${params[0].name}</div>`
              params.forEach(item => {
                html += `<div>${item.marker}${item.seriesName}：${item.value}${this.unit}</div>`
              })
              return html
            },
            confine: true
          },
          grid: {
            top: this.unit ? 25 : 10,
            left: 1,
            right: 20,
            bottom: 1,
            containLabel: true
          },
          xAxis: [{
            data: [],
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: (value) => {
                return value
              },
              color: 'rgba(217, 223, 255, 1)'
            },
            boundaryGap: true
          }],
          yAxis: [{
            boundaryGap: [0, '10%'],
            name: '',
            nameGap: 7,
            nameTextStyle: { color: 'rgba(217, 223, 255, 1)', align: 'left' },
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: { show: true, color: 'rgba(217, 223, 255, 1)' },
            splitLine: {
                show: true,
                lineStyle: {
                color: 'rgba(255, 255, 255, 0.1)',
                type: 'dashed'
                }
            }
          }],
          series: [
            {
              name: '',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: 'rgba(35, 105, 255, 1)'
                  }, {
                    offset: 1, color: 'rgba(3, 83, 255, 0)'
                  }],
                  global: false
                },
                borderColor: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: 'rgba(0, 37, 125, 1)'
                  }, {
                    offset: 1, color: 'rgba(77, 97, 255, 0)'
                  }],
                  global: false
                },
                borderWidth: 1
              },
              barWidth: 16,
              label: {
                show: true,
                color: '#fff',
                position: 'top',
                distance: 1,
                formatter: (param) => {
                  return param.value
                }
              },
              data: []
            }
          ]
        }
      }
    },
    methods: {
      resize() {
        this.$refs.barEchart.resize()
      }
    }
  }
  </script>
  
  <style lang="less" scoped>
  
  </style>
  
<template>
  <div class="project-map-page">
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="side-panel side-panel-left" style="pointer-events: auto">
        <Title title="AI告警统计"></Title>
        <div class="cardBack">
          <div class="cardNum">
            <div class="warn">
              <div class="warnTitle">
                <div></div>
                今日告警总次数：
              </div>
              <div class="warnNum youshebiaotihei">
                {{ aiWarnTotalInfo.nowSum }}
              </div>
            </div>
            <div class="yesdayWarn">
              <div class="compareYesday">比较昨日：</div>
              <div class="compareNum">
                <img
                  :src="growthSumClass"
                  style="transform: rotateX(180deg)"
                />{{
                  isNaN(Math.abs(aiWarnTotalInfo.growthSum))
                    ? 0
                    : Math.abs(aiWarnTotalInfo.growthSum)
                }}
              </div>
            </div>
          </div>
          <div class="totalNum">
            <div class="monthNum">
              <span>近一个月总次数：</span>
              <span>{{ aiWarnTotalInfo.intervalThirtySum }}</span>
            </div>
            <div class="monthNum total">
              <span>累计总次数：</span>
              <span>{{ aiWarnTotalInfo.sum }}</span>
            </div>
          </div>
        </div>
        <div class="s_title">视频告警统计</div>
        <div class="echarts-total">
          <div class="posiButton">
            <div
              :class="activeIndex == 1 ? 'active' : ''"
              @click="searchData(1)"
            >
              近七日
            </div>
            <div
              :class="activeIndex == 2 ? 'active' : ''"
              @click="searchData(2)"
            >
              近一月
            </div>
          </div>
          <div class="echarts1">
            <div id="echarts11" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <Title title="近1小时告警"></Title>
        <div class="OneHourWarn">
          <div class="title">
            {{ warnPrompt.msg }}
          </div>
          <div class="content">
            <div class="floorOverview">
              <div
                class="AIWarn"
                v-if="hourwarnInfo && hourwarnInfo.length > 0"
              >
                <div class="photo no">
                  <el-image
                    :src="hourwarnInfo[0].image"
                    :preview-src-list="[hourwarnInfo[0].image]"
                  />
                </div>
                <div class="info">
                  <div>
                    <img src="../../assets/images/ProjectAI/warnIcon.png" />{{
                      hourwarnInfo[0].aiRuleTypeI18n
                    }}
                  </div>
                  <div>
                    <img src="../../assets/images/ProjectAI/photo.png" />图片一
                  </div>
                  <div>
                    <img src="../../assets/images/ProjectAI/time.png" />{{
                      hourwarnInfo[0].createTime
                    }}
                  </div>
                </div>
              </div>
              <div style="width: 100%; height: 150px;display: flex; align-items: center; justify-content: center; " v-else>
                <Empty style="width: 100px; margin: 0px auto;" title="暂无数据"></Empty>
              </div>
              
            </div>

            <div class="safePeople">
              <div>
                <span>安全员：</span>
                <span>{{ safePeopleInfo.safer }}</span>
              </div>
              <div>
                <span>手机号：</span>
                <span>{{ safePeopleInfo.safe_phone }}</span>
              </div>
            </div>
            <div class="safePeople">
              <div>
                <span>施工队负责人：</span>
                <span>{{ safePeopleInfo.construction_team_leader }}</span>
              </div>
              <div>
                <span>手机号：</span>
                <span>{{ safePeopleInfo.construction_phone }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="s_title" :style="{marginTop: hourwarnInfo && hourwarnInfo.length > 0 ? '20px' : '0px'}">告警记录</div>

        <div class="floor-nav" @mouseenter="toggleTimer('timer1')" @mouseleave="toggleTimer('timer1')">
          <div class="floor-item floor-item1">
            <div
              :class="item.active ? 'active' : ''"
              @click="searchActive(item)"
              v-for="(item, index) in navList"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="floorOverview">
            <div
              class="AIWarn"
              v-for="(item, index) in warnTableRecord"
              :key="index"
            >
              <div class="photo">
                <el-image :src="item.image" :preview-src-list="srcList2" />
              </div>
              <div class="info">
                <div>
                  <img src="../../assets/images/ProjectAI/warnIcon.png" />{{
                    item.aiRuleTypeI18n
                  }}
                </div>
                <div>
                  <img src="../../assets/images/ProjectAI/photo.png" />图片{{
                    index + 1
                  }}
                </div>
                <div>
                  <img src="../../assets/images/ProjectAI/time.png" />{{
                    item.createTime
                  }}
                </div>
              </div>
            </div>
            <Empty title="暂无违规记录" style="width: 100px; margin: 0px auto;" v-if="warnTableRecord.length == 0" />
          </div>
        </div>
      </div>
    </div>

    <div class="right-content">
      <div
        class="side-panel side-panel-right"
        style="pointer-events: auto; height: 100%; gap: 5px"
      >
        <Title title="告警类型占比"></Title>
        <div class="totalNums">
          <div class="echarts2">
            <div class="line"></div>
            <div id="echarts12" style="width: 100%; height: 110px"></div>
          </div>
          <div class="totalList">
            <div
              class="content"
              v-for="(item, index) in tableData"
              :key="index"
            >
              <div class="title">
                <div class="leftPoint">
                  <div
                    class="point"
                    :style="{ backgroundColor: item.color }"
                  ></div>
                  <span>{{ item.name }}</span>
                </div>
                <span>{{ item.value }}</span>
              </div>
              <div class="percentage">
                {{ isNaN(item.percentageNum) ? 0 : item.percentageNum }}%
              </div>
            </div>
          </div>
        </div>
        <Title title="违规行为统计" style="margin-top: 10px"></Title>
        <div class="retardedTotal">
          <div
            class="totalRow"
            v-for="(item, index) in retardedTotal"
            :key="index"
          >
            <div class="totalLeft">
              <div :class="'icon ' + (index == 0 ? 'active' : '')">
                {{ index + 1 }}
              </div>
              <span>{{ item.name }}</span>
            </div>
            <div class="totalmiddle">
              <Slider
                :value="Number(item.percentageNum)"
                style="width: 100%"
                :max="100"
              ></Slider>
            </div>
            <div class="totalRight">
              <span>{{ item.value }}</span>
              <span>个</span>
            </div>
          </div>
        </div>
        <div class="s_title" style="margin-top: 10px">违规行为记录</div>
        <div class="floor-nav" style="margin-top: 0; height: 33vh" @mouseenter="toggleTimer('timer2')" @mouseleave="toggleTimer('timer2')">
          <div class="floor-item floor-item2" style="height: 15%">
            <div
              :class="item.active ? 'active' : ''"
              @click="searchActive2(item)"
              v-for="(item, index) in navList2"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="floorOverview" style="height: 77%">
            <div class="AIWarn" v-for="(item, index) in warnTable" :key="index">
              <div class="photo">
                <el-image :src="item.image" :preview-src-list="srcList" />
              </div>
              <div class="info">
                <div>
                  <img src="../../assets/images/ProjectAI/warnIcon.png" />{{
                    item.aiRuleTypeI18n
                  }}
                </div>
                <div>
                  <img src="../../assets/images/ProjectAI/photo.png" />图片{{
                    index + 1
                  }}
                </div>
                <div>
                  <img src="../../assets/images/ProjectAI/time.png" />{{
                    item.createTime
                  }}
                </div>
              </div>
            </div>
            <Empty title="暂无违规记录" v-if="warnTable.length == 0" />
          </div>
        </div>
      </div>
    </div>
    <ModalW900H486 :visibleW900h486.sync="isshow" title="监控"></ModalW900H486>
  </div>
</template>

<script>
import Title from "@/components/commonView/title2.vue";
import * as echarts from "echarts";
import ModalW900H486 from "@/components/modal/w900h486.vue";
import {
  aiBehaviorAlerts,
  getDict,
  getSelectPage,
  getAlamInfo,
  totalOldDayByLevelPieChart,
  getByStatisticsLins,
  getViolationBehaviorPieChart,
  getAiAlarmStatistics,
  getPrompt,
} from "@/api/xxindex";
import { getMapConfig } from "@/api/index";

const up = require("../../assets/images/ProjectAI/goon.png");
const down = require("../../assets/images/ProjectAI/gooff.png");
export default {
  name: "ProjectAI",
  components: {
    Title,
    ModalW900H486,
  },
      data() {
    return {
      activeIndex: 0,
      myCharts1: null,
      myCharts2: null,
      navList: [
        {
          name: "烟火识别",
          active: true,
        },
        {
          name: "外来人员识别",
          active: false,
        },
        {
          name: "区域入侵",
          active: false,
        },
        {
          name: "举手求救",
          active: false,
        },
        {
          name: "跌倒",
          active: false,
        },
      ],
      navList2: [
        {
          name: "未戴安全帽",
          active: false,
        },
        {
          name: "未穿反光服",
          active: true,
        },
        {
          name: "吸烟",
          active: false,
        },
        {
          name: "未系安全绳",
          active: false,
        },
        {
          name: "打架",
          active: false,
        },
      ],
      tableData: [],
      retardedTotal: [],
      color: ["#7DD7FB", "#3990F7", "#0D3FB3", "#8545F6", "#F2A250", "#EFBB47"],
      isshow: false,
      Dict: [],
      aiWarnTotalInfo: {},
      warnTable: [],
      hourwarnInfo: [],
      warnTableRecord: [],
      timer1: null,
      timer2: null,
      // 告警提示语
      warnPrompt: [],
      // 图片预览状态
      isImagePreviewOpen: false,
      // 图片预览检查定时器
      previewCheckTimer: null,
      // 图片预览观察器
      imagePreviewObserver: null,
      safePeopleInfo: {},
      // 自动切换控制状态
      autoSwitchEnabled: {
        timer1: true,
        timer2: true
      },
      // 自动切换定时器状态
      autoSwitchTimers: {
        timer1: null,
        timer2: null
      },
      // 当前轮播索引
      currentIndex: {
        timer1: 0,
        timer2: 0
      },
      // 鼠标悬停状态
      isHovered: {
        timer1: false,
        timer2: false
      }
    };
  },
  mounted() {
    this.initRequest();
    // this.$bus.emit("switchConstructionView", "ai");
    this.$nextTick(() => {
      this.timer1 = this.setTimeoutFn(1);
      this.timer2 = this.setTimeoutFn(2);
      // 监听图片预览的打开和关闭
      this.setupImagePreviewListeners();
    });
  },
  destroyed() {
    // 清理所有定时器
    Object.keys(this.autoSwitchTimers).forEach(key => {
      if (this.autoSwitchTimers[key]) {
        clearInterval(this.autoSwitchTimers[key]);
        this.autoSwitchTimers[key] = null;
      }
    });
    
    clearInterval(this.timer1);
    clearInterval(this.timer2);
    clearInterval(this.previewCheckTimer);
    this.timer1 = null;
    this.timer2 = null;
    this.previewCheckTimer = null;

    // 清理 MutationObserver
    if (this.imagePreviewObserver) {
      this.imagePreviewObserver.disconnect();
      this.imagePreviewObserver = null;
    }
    
    // 重置状态
    this.currentIndex = {
      timer1: 0,
      timer2: 0
    };
    this.isHovered = {
      timer1: false,
      timer2: false
    };
    this.isImagePreviewOpen = false;
    this.autoSwitchEnabled = {
      timer1: false,
      timer2: false
    };
  },
  computed: {
    growthSumClass() {
      if (this.aiWarnTotalInfo.oldSum <= this.aiWarnTotalInfo.nowSum) {
        return down;
      } else {
        return up;
      }
    },
    srcList() {
      return this.warnTable.map((i) => i.image);
    },
    srcList2() {
      return this.warnTableRecord.map((i) => i.image);
    },
    // 定时器状态信息
    timerStatus() {
      return {
        timer1: {
          enabled: this.autoSwitchEnabled.timer1,
          running: !!this.autoSwitchTimers.timer1
        },
        timer2: {
          enabled: this.autoSwitchEnabled.timer2,
          running: !!this.autoSwitchTimers.timer2
        }
      };
    }
  },
  methods: {
    setTimeoutFn(index) {
      let dom = null;
      let timerKey = `timer${index}`;
      
      if (index == 1) {
        dom = document.querySelectorAll(".floor-item1");
      }
      if (index == 2) {
        dom = document.querySelectorAll(".floor-item2");
      }
      
      // 清除已存在的定时器
      if (this.autoSwitchTimers[timerKey]) {
        clearInterval(this.autoSwitchTimers[timerKey]);
      }
      
      const interval = setInterval(() => {
        // 检查是否启用自动切换
        if (!this.autoSwitchEnabled[timerKey]) {
          console.log(`自动切换 ${timerKey} 已暂停`);
          return;
        }
        
        // 如果图片预览打开，则暂停自动切换
        if (this.isImagePreviewOpen) {
          console.log("图片预览打开，暂停自动切换");
          return;
        }
        
        // 如果鼠标悬停，则暂停自动切换
        if (this.isHovered[timerKey]) {
          console.log(`鼠标悬停 ${timerKey}，暂停自动切换`);
          return;
        }
        
        // 点击当前索引的元素
        if (dom[0] && dom[0].childNodes[this.currentIndex[timerKey]]) {
          dom[0].childNodes[this.currentIndex[timerKey]].click();
        }
        
        // 更新索引，循环到下一个
        this.currentIndex[timerKey]++;
        if (this.currentIndex[timerKey] > 4) {
          this.currentIndex[timerKey] = 0;
        }
      }, 8000);
      
      // 保存定时器引用
      this.autoSwitchTimers[timerKey] = interval;
      
      return interval;
    },
    
    // 暂停自动切换
    pauseAutoSwitch(timerKey) {
      if (this.autoSwitchEnabled.hasOwnProperty(timerKey)) {
        this.autoSwitchEnabled[timerKey] = false;
        console.log(`${timerKey} 自动切换已暂停`);
      }
    },
    
    // 恢复自动切换
    resumeAutoSwitch(timerKey) {
      if (this.autoSwitchEnabled.hasOwnProperty(timerKey)) {
        this.autoSwitchEnabled[timerKey] = true;
        console.log(`${timerKey} 自动切换已恢复`);
        
        // 恢复轮播时，确保从当前位置的下一个开始
        // 不需要立即切换，让定时器自然执行
      }
    },
    

    initRequest() {
      getAiAlarmStatistics().then((res) => {
        this.aiWarnTotalInfo = res.data;
        console.log(this.aiWarnTotalInfo, "42709482");
      });
      getDict("ai_rule_type").then((res) => {
        this.Dict = res.data;
        // 初始化时设置默认选中项和索引
        this.searchActive2(this.navList2[0]); // 默认选中"未戴安全帽"
        this.searchActive(this.navList[0]); // 默认选中"烟火识别"
        // 设置初始索引
        this.currentIndex.timer1 = 0; // 对应"烟火识别"
        this.currentIndex.timer2 = 0; // 对应"未戴安全帽"
      });
      // 提示语
      getPrompt().then((res) => {
        if (res.code == "success") {
          this.warnPrompt = res.data;
        }
      });
      aiBehaviorAlerts().then((res) => {
        // this.proportionInfo = res.data.LeftList[0];
        // this.pieChart = res.data.pieChart[0]
        let total2 = res.data.LeftList.reduce((pre, cur) => {
          return pre + cur.sum;
        }, 0);
        this.retardedTotal = res.data.LeftList.map((i, index) => {
          return {
            name: i.name,
            value: i.sum,
            color: this.color[index],
            percentageNum: ((i.sum / total2) * 100).toFixed(0),
          };
        });

        // 重新计算 percentageNum
        const totalSum = this.retardedTotal.reduce(
          (sum, item) => sum + Number(item.value),
          0
        );
        this.retardedTotal = this.retardedTotal.map((item) => ({
          ...item,
          percentageNum: ((item.value / totalSum) * 100).toFixed(0),
        }));
        this.retardedTotal = this.retardedTotal.sort((a, b) =>
          a.value > b.value ? -1 : 1
        );
        // this.initCharts1();
        this.searchData(1);
      });
      getViolationBehaviorPieChart().then((res) => {
        if (res.code == "success") {
          let total = res.data.LeftList.reduce((pre, cur) => {
            return pre + cur.sum;
          }, 0);
          this.tableData = res.data.LeftList.map((i, index) => {
            return {
              name: i.name,
              value: i.sum,
              color: this.color[index],
              percentageNum: ((i.sum / total) * 100).toFixed(2),
            };
          });
          this.initCharts2();
        }
      });
      getAlamInfo().then((res) => {
        if (res.code == "success") {
          console.log(res.data, "42709482");
          this.hourwarnInfo = res.data;
        }
      });
      
      getMapConfig('SCREEN_AI_CONFIG').then((res) => {
        if (res.code === "success") {
          let data = JSON.parse(res.data.value)
          this.safePeopleInfo = data.safety
        }
      })
    },
    searchData(index) {
      // 1为7天2为30天
      this.activeIndex = index;
      // 调用接口
      getByStatisticsLins({ type: index }).then((res) => {
        console.log(res, "42709482");
        if (res.code === "success") {
          this.initCharts1(res.data.sums, res.data.csc);
        }
      });
    },
    initCharts1(sum, csc) {
      this.myCharts1 && this.myCharts1.dispose();
      this.myCharts1 = echarts.init(document.getElementById("echarts11"));
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            return params[0].name + "<br/>" + params[0].value;
          },
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 0,
          top: "21%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#283140",
              width: 1,
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 10,
            interval: 0,
            formatter: function (value) {
              if (value.length > 5) {
                return value.substring(0, 5) + "\n" + value.substring(5);
              }
              return value;
            },
          },
          data: csc,
        },
        // 设置柱状图最大宽度
        barMaxWidth: 40,
        yAxis: {
          name: "单位：条",
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#283140",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#283140",
              width: 1,
              type: "dashed",
            },
          },
        },
        series: [
          {
            data: sum || [120, 200, 150, 80, 70, 110, 130],
            type: "bar",
            barWidth: "40%",
            label: {
              show: true,
              position: "top",
              color: "#fff",
            },
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#336CEF", // 0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100%处的颜色
                  },
                ],
              },
              borderColor: "#4872DD",
            },
          },
        ],
      };
      this.myCharts1.setOption(option);
    },
    initCharts2() {
      // 分配颜色
      this.tableData = this.tableData.map((i, index) => {
        return {
          ...i,
          color: i.color || this.color[index],
          itemStyle: {
            color: i.color || this.color[index],
          },
        };
      });
      // 获取值最大的那一项
      let max = this.tableData.reduce((prev, cur) => {
        return prev.value > cur.value ? prev : cur;
      });
      // 销毁
      this.myCharts2 && this.myCharts2.dispose();
      this.myCharts2 = echarts.init(document.getElementById("echarts12"));
      let option = {
        graphic: {
          id: "centerText", // 给图形元素添加ID便于更新
          type: "text",
          left: "center",
          top: "center",
          style: {
            text: `${max.value}\n\n${max.name}`, // 默认显示第一条
            textAlign: "center",
            fill: "#fff",
            fontSize: 14,
            fontFamily: "youshebiaotihei",
          },
        },
        series: [
          {
            type: "pie",
            radius: ["90%", "100%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
              // formatter: '{d}%\n\n{b}',
            },
            // emphasis: {
            //     label: {
            //         show: true,
            //         fontSize: 14,
            //         fontWeight: 'bold',
            //         formatter: '{d}%\n\n{b}',
            //     }
            // },
            labelLine: {
              show: false,
            },
            data: this.tableData,
          },
        ],
      };
      function formatObj(obj) {
        return {
          graphic: {
            id: "centerText",
            type: "text",
            left: "center",
            top: "center",
            style: {
              textAlign: "center",
              text: `${obj.value}\n\n${obj.name}`,
            },
          },
        };
      }
      this.myCharts2.on("mouseover", (params) => {
        if (params.seriesType == "pie") {
          this.myCharts2.setOption(formatObj(params));
        }
      });
      this.myCharts2.on("mouseout", (params) => {
        if (params.seriesType == "pie") {
          this.myCharts2.setOption(
            formatObj({
              name: max.name,
              value: max.value,
            })
          );
        }
      });
      this.myCharts2.setOption(option);
    },
    searchActive(item) {
      console.log(item);
      this.navList.forEach((navItem, index) => {
        navItem.active = false;
        if (navItem.name === item.name) {
          // 更新当前索引为点击的项目
          this.currentIndex.timer1 = index;
        }
      });
      item.active = true;
      let string = "";
      if (item.name == "烟火识别") {
        string = "06,07";
        this.getTableList1(string);
        return;
      }
      if (item.name == "未系安全绳") {
        string = "11,10";
        this.getTableList1(string);
        return;
      }
      this.Dict.forEach((i) => {
        if (i.itemText == item.name) {
          this.getTableList1(i.itemValue);
        }
      });
    },
    searchActive2(item) {
      this.navList2.forEach((navItem, index) => {
        navItem.active = false;
        if (navItem.name === item.name) {
          // 更新当前索引为点击的项目
          this.currentIndex.timer2 = index;
        }
      });
      item.active = true;
      let string = "";
      if (item.name == "烟火识别") {
        string = "06,07";
        this.getTableList(string);
        return;
      }
      if (item.name == "未系安全绳") {
        string = "11,10";
        this.getTableList(string);
        return;
      }
      this.Dict.forEach((i) => {
        if (i.itemText == item.name) {
          this.getTableList(i.itemValue);
        }
      });
    },
    getTableList(value) {
      let data = {
        page: {
          current: 1,
          size: 20,
        },
        customQueryParams: {
          aiRuleTypes: value,
        },
      };
      getSelectPage(data).then((res) => {
        this.warnTable = res.data.records;
      });
    },
    getTableList1(value) {
      let data = {
        page: {
          current: 1,
          size: 20,
        },
        customQueryParams: {
          aiRuleTypes: value,
        },
      };
      getSelectPage(data).then((res) => {
        this.warnTableRecord = res.data.records;
      });
    },
    // 设置图片预览监听器
    setupImagePreviewListeners() {
      // 方法1: 使用 MutationObserver 监听 DOM 变化
      this.imagePreviewObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            // 检查是否有图片预览弹窗出现或消失
            const previewContainer =
              document.querySelector(".el-image-viewer__wrapper") ||
              document.querySelector(".el-image-viewer") ||
              document.querySelector('[class*="image-viewer"]') ||
              document.querySelector('[class*="preview"]');
            if (previewContainer) {
              const isVisible =
                previewContainer.style.display !== "none" &&
                previewContainer.style.visibility !== "hidden" &&
                previewContainer.offsetParent !== null;
              this.isImagePreviewOpen = !isVisible;
            } else {
              this.isImagePreviewOpen = false;
            }
          }
        });
      });

      // 监听 body 元素的变化
      this.imagePreviewObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["style", "class"],
      });

      // 方法2: 使用定时器定期检查
      this.previewCheckTimer = setInterval(() => {
        // 检查多种可能的图片预览容器类名
        const previewContainer =
          document.querySelector(".el-image-viewer__wrapper") ||
          document.querySelector(".el-image-viewer") ||
          document.querySelector('[class*="image-viewer"]') ||
          document.querySelector('[class*="preview"]');

        if (previewContainer) {
          const isVisible =
            previewContainer.style.display !== "none" &&
            previewContainer.style.visibility !== "hidden" &&
            previewContainer.offsetParent !== null;
          if (this.isImagePreviewOpen !== isVisible) {
            console.log(
              "图片预览状态变化:",
              isVisible,
              "容器类名:",
              previewContainer.className
            );
            this.isImagePreviewOpen = !isVisible;
            
            // 图片预览状态变化时控制轮播
            if (this.isImagePreviewOpen) {
              console.log("图片预览打开，暂停所有轮播");
              this.pauseAutoSwitch('timer1');
              this.pauseAutoSwitch('timer2');
            } else {
              console.log("图片预览关闭，恢复所有轮播");
              // 只有在鼠标没有悬停时才恢复轮播
              if (!this.isHovered.timer1) {
                this.resumeAutoSwitch('timer1');
              }
              if (!this.isHovered.timer2) {
                this.resumeAutoSwitch('timer2');
              }
            }
          }
        } else {
          if (this.isImagePreviewOpen !== false) {
            console.log("图片预览关闭");
            this.isImagePreviewOpen = false;
            
            // 图片预览关闭时恢复轮播
            console.log("图片预览关闭，恢复所有轮播");
            // 只有在鼠标没有悬停时才恢复轮播
            if (!this.isHovered.timer1) {
              this.resumeAutoSwitch('timer1');
            }
            if (!this.isHovered.timer2) {
              this.resumeAutoSwitch('timer2');
            }
          }
        }
      }, 200);

      // 方法3: 监听键盘事件
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
          this.isImagePreviewOpen = false;
        }
      });

      // 方法4: 监听点击事件
      document.addEventListener("click", (e) => {
        if (
          e.target &&
          (e.target.classList.contains("el-image-viewer__wrapper") ||
            e.target.classList.contains("el-image-viewer") ||
            e.target.className.includes("image-viewer") ||
            e.target.className.includes("preview"))
        ) {
          this.isImagePreviewOpen = false;
        }
      });

      // 方法5: 监听鼠标事件
      document.addEventListener("mouseup", (e) => {
        if (
          e.target &&
          (e.target.classList.contains("el-image-viewer__wrapper") ||
            e.target.classList.contains("el-image-viewer") ||
            e.target.className.includes("image-viewer") ||
            e.target.className.includes("preview"))
        ) {
          this.isImagePreviewOpen = false;
        }
      });

      // 方法6: 重写 Element UI 的图片预览方法
      this.overrideImagePreview();
    },
    // 重写 Element UI 的图片预览方法
    overrideImagePreview() {
      // 监听所有 el-image 元素的点击事件
      document.addEventListener("click", (e) => {
        if (e.target && e.target.closest(".el-image")) {
          // 延迟检查，确保预览弹窗已经打开
          setTimeout(() => {
            const previewContainer =
              document.querySelector(".el-image-viewer__wrapper") ||
              document.querySelector(".el-image-viewer") ||
              document.querySelector('[class*="image-viewer"]') ||
              document.querySelector('[class*="preview"]');
            if (previewContainer) {
              this.isImagePreviewOpen = true;
              // 图片预览打开时暂停轮播
              console.log("图片预览打开，暂停所有轮播");
              this.pauseAutoSwitch('timer1');
              this.pauseAutoSwitch('timer2');
            }
          }, 100);
        }
      });
    },
    
    // 鼠标移入移出控制轮播
    toggleTimer(timerKey) {
      // 切换悬停状态
      this.isHovered[timerKey] = !this.isHovered[timerKey];
      
      if (this.isHovered[timerKey]) {
        console.log(`鼠标移入 ${timerKey}，暂停轮播`);
        // 鼠标移入时暂停轮播
        this.pauseAutoSwitch(timerKey);
      } else {
        console.log(`鼠标移出 ${timerKey}，恢复轮播`);
        // 鼠标移出时恢复轮播，但只有在图片预览未打开时才恢复
        if (!this.isImagePreviewOpen) {
          this.resumeAutoSwitch(timerKey);
        }
      }
    },
    
    // 获取当前轮播状态信息（用于调试）
    getCarouselStatus() {
      return {
        currentIndex: this.currentIndex,
        isHovered: this.isHovered,
        isImagePreviewOpen: this.isImagePreviewOpen,
        autoSwitchEnabled: this.autoSwitchEnabled,
        timerStatus: this.timerStatus
      };
    },
  },
};
</script>

<style lang="less" scoped>
.side-panel-left {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
}
.side-panel-right {
  background: linear-gradient(
    to left,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
}
.retardedTotal {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  gap: 5px;
  margin-top: -10px;

  .totalRow {
    width: 100%;
    height: calc(100% / 6.6);
    background-image: linear-gradient(
      to right,
      #4b83b700,
      #4b83b71a 52.5%,
      #4b83b700
    );
    display: flex;
    align-items: center;
    gap: 10px;

    .totalLeft {
      width: 33%;
      height: 100%;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        background: url(../../assets/images/ProjectAI/icon.png) no-repeat center
          center;
        background-size: 100% 100%;
        font-family: "youshebiaotihei";
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .icon.active {
        background: url(../../assets/images/ProjectAI/active.png) no-repeat
          center center !important;
        background-size: 100% 100% !important;
      }
    }

    .totalmiddle {
      width: 45%;
      height: 100%;
      display: flex;
      align-items: center;
      pointer-events: none;

      /deep/ .ivu-slider-wrap {
        height: 10px;
        background: url(../../assets/images/ProjectAI/blur.png) no-repeat center
          center;
        background-size: 100% 100%;
      }

      /deep/ .ivu-slider-bar {
        height: 10px;
        background: url(../../assets/images/ProjectAI/progress.png) no-repeat
          center center;
        background-size: 100% 80%;
      }

      /deep/ .ivu-slider-button {
        width: 30px;
        height: 30px;
        background: url(../../assets/images/ProjectAI/point.png) no-repeat
          center center;
        background-size: 100% 100%;
        background-position-x: -5px;
        border: none;
        transform: scale(1.1);
      }

      /deep/ .ivu-slider-button-wrap {
        position: absolute;
        top: -10px;
      }
    }

    .totalRight {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      span:first-child {
        font-size: 18px;
        color: #fff;
        font-weight: bold;
      }
    }
  }

  .totalRow:first-child {
    background-image: linear-gradient(
      to right,
      #4b83b700,
      #7297b964 52.5%,
      #4b83b700
    ) !important;
  }
}

.totalNums {
  width: 100%;
  height: 20vh;
  display: flex;
  justify-content: space-between;
  gap: 20px;

  .echarts2 {
    width: 35%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(../../assets/images/ProjectAI/bing.png) no-repeat center
      center;
    background-size: 84% auto;
    background-position-y: 55%;
    position: relative;

    .line {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 70%;
      height: 2px;
      background-image: linear-gradient(
        to right,
        transparent,
        #fff 52.5%,
        transparent
      );
      box-shadow: 0 0 10px #fff;
    }
  }

  .totalList {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 5px;

    .content {
      width: 100%;
      height: calc(100% / 6);
      background-image: linear-gradient(
        to right,
        #4b83b700,
        #4b83b71a 52.5%,
        #4b83b700
      );
      display: flex;
      align-items: center;
      gap: 10px;

      .title {
        width: 70%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .leftPoint {
          height: 100%;
          display: flex;
          align-items: center;

          .point {
            margin-right: 10px;
            width: 5px;
            height: 5px;
            border-radius: 50px;
          }
        }
      }

      .percentage {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }

  .right-content {
    width: 408px;
    position: absolute;
    right: 0;
    top: 0;
    height: calc(100%);
    flex: 1;
  }
}

.cardBack {
  width: 100%;
  height: 15vh;
  background: url(../../assets/images/ProjectAI/cardBack.png) no-repeat center
    center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 15px;
  margin-top: -20px;

  .cardNum {
    width: 100%;
    height: 55%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;

    .warn {
      height: 100%;
      display: flex;
      align-items: center;

      .warnTitle {
        display: flex;
        align-items: center;
        gap: 10px;
        white-space: nowrap;

        div {
          width: 5px;
          height: 5px;
          background-color: #fff;
          border-radius: 50px;
          box-shadow: 0px 0px 20px rgba(255, 255, 255);
        }
      }

      .warnNum {
        font-size: 30px;
        background: url(../../assets/images/icon/cardIcon.png) no-repeat center
          center;
        background-size: contain;
        padding-bottom: 20px;
        letter-spacing: 2px;
      }
    }

    .yesdayWarn {
      height: 100%;
      display: flex;
      align-items: center;
      color: #fff;

      .compareNum {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .totalNum {
    width: 100%;
    height: 27%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    font-size: 16px;

    .monthNum {
      display: flex;
      height: 100%;
      width: 46%;
      align-items: center;
      justify-content: space-between;
      background: url(../../assets/images/ProjectAI/leftBack.png) no-repeat
        center center;
      background-size: 100% 100%;
      padding-left: 7px;
      white-space: nowrap;
    }

    .monthNum.total {
      background: url(../../assets/images/ProjectAI/rightBack.png) no-repeat
        center center !important;
      background-size: 100% 100% !important;
    }
  }
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../assets/images/icon/s_title.png) no-repeat center center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -15px;
}

.echarts-total {
  width: 100%;
  height: 25vh;
  margin-top: -10px;
  position: relative;

  .posiButton {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    height: 26px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99;
    div {
      background-color: rgb(149, 149, 149, 0.5);
      width: 64px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      border-radius: 3px;
      cursor: pointer;
    }

    .active {
      background-color: transparent;
      background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat
        center center;
      background-size: 100% 100%;
    }
  }

  .echarts1 {
    width: 100%;
    height: calc(100% - 20px + 26px);
  }
}

.floorOverview {
  height: 73%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;

  .AIWarn {
    width: 100%;
    display: flex;
    gap: 10px;

    .photo {
      width: 40%;
      height: 100%;
      background-image: linear-gradient(to bottom, #ae2319 60%, #3d0704);
      padding: 2px;
      box-sizing: border-box;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .photo.yes::before {
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.2);
      z-index: 99;
      cursor: pointer;
    }

    .photo.yes::after {
      content: "▶";
      width: 18px;
      height: 18px;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      border-radius: 50px;
      cursor: pointer;
    }

    .info {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 3px;

      div {
        width: 100%;
        height: 32%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-radius: 3px;
        border-left: 2px solid #af2319;
        background-image: linear-gradient(to right, #5b1a20 0%, transparent);

        img {
          width: 18px;
          height: 18px;
          object-fit: contain;
          margin: 0 5px;
        }
      }
    }
  }
}

.OneHourWarn {
  width: 100%;
  height: 20vh;
  margin-top: -25px;

  .title {
    font-size: 14px;
    color: #fff;
    text-align: left;
  }

  .content {
    width: 100%;
    height: calc(100% - 20px);
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;

    .safePeople {
      width: 100%;
      height: 17%;
      background-image: linear-gradient(to right, #5b1a20 0%, transparent);
      display: flex;
      justify-content: space-between;
      gap: 10px;
      box-sizing: border-box;
      padding: 3px 5px;

      div:first-child {
        width: 46%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      div:last-child {
        flex: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      div {
        span:first-child {
          color: rgba(255, 255, 255, 0.7);
        }

        span:last-child {
          color: #fff;
        }
      }
    }
  }
}

.floor-nav {
  width: 100%;
  height: 20vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: -10px;
  gap: 10px;

  .floor-item {
    width: 100%;
    height: 25%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    font-size: 13px;

    div {
      // height: 100%;
      padding: 4px 8px;
      background-color: rgba(255, 255, 255, 0.1);
      box-sizing: border-box;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
    }

    .active {
      background-color: transparent;
      background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat
        center center;
      background-size: 100% 100%;
      color: #fff;
    }
  }
}
</style>


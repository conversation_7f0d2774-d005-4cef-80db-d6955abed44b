<template>
     <div>
        <div class="container">
            <Row style="height: 100% ;">
                <Col :span="5" style="height: 100%">
                <div class="leftTop">
                    <Title title="摄像头列表" desc="&nbsp;&nbsp;&nbsp;CAMERA LIST"></Title>
                    <div class="leftContent">
                        <div class="tree">
                            <Tree :data="data" show-checkbox check-directly @on-check-change="handleCheckChange"></Tree>
                        </div>
                    </div>
                    <div class="leftFloor">
                        <button class="primary" @click="saveSelection">保存</button>
                        <button class="info" @click="resetSelection">重置</button>
                    </div>
                </div>
                <Title title="AI视频监测" desc="&nbsp;&nbsp;&nbsp;AI MONIROEING" style="margin-top: 20px;"></Title>
                <div class="leftButtom">
                    <div class="icon-flex">
                        <div class="icon-item">
                            <img src="../../assets/images/icon/row.png" alt="">
                            <div class="item">
                                <span>当日统计数量</span>
                                <span>{{ totalInfo.todayCount }}</span>
                            </div>
                        </div>
                        <div class="icon-item">
                            <img src="../../assets/images/icon/video.png" alt="">
                            <div class="item">
                                <span>近7日统计数量</span><span>{{ totalInfo.last7DaysCount }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="nav">
                        <div class="nav-title">
                            <div class="back">抓拍记录</div>
                            <span class="detail">
                                <Select v-model="changeData" @on-change="handleChange">
                                    <!-- 默认选中 -->
                                     <Option :value="'全部'" :label="'全部'">全部</Option>
                                    <Option v-for="(item, index) in Dict" :key="index" :value="item.itemText" :label="item.itemText">{{ item.itemText }}</Option>
                                </Select>
                            </span>
                        </div>
                    </div>
                    <div class="small-title" v-if="aiVideoList.length > 0">
                        <div class="video-img">
                            <el-carousel autoplay indicator-position="none" arrow="never" style="height: 100% ">
                                <el-carousel-item v-for="(item, index) in aiVideoList" :key="index">
                                    <div style="height: 100%;">
                                        <div class="title-flex">
                                            <div class="title">{{ item.aiRuleTypeI18n }}</div>
                                            <div class="time">{{ item.createTime }}</div>
                                        </div>
                                        <el-image :src="item.image" alt="" :preview-src-list="srcList"  />
                                    </div>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                    </div>
                    <Empty title="暂无违规记录" v-else></Empty>
                </div>
                </Col>
                <!-- <Col :span="1" style="height: 100%;">
                    <div class="grid-three">
                        <div class="grid-three-active">
                            <el-tooltip class="item" effect="dark" placement="top">
                                <template #content>
                                    <div class="tooltip-content">
                                        <div class="tooltip-item">
                                            <div class="items" v-for="(item, index) in tabList" :key="index"> 
                                                <img :src="item.tabActive == activeTab.tabActive ? item.tabActive : item.tab" @click="changeTab(item)">
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <img :src="activeTab.tab">
                            </el-tooltip>
                        </div>
                    </div>
                </Col> -->
                <Col :span="19" style="height: 100%;">
                    <div class="video-display-container">
                        <DynamicVideoGrid :cameraList="videoInfoList" />
                    </div>
                    <!-- <tab :listData="videoInfoList" /> -->
                     <!-- <component :is="activeTab.component" :listData="videoInfoList"></component> -->
                </Col>
            </Row>
        </div>
    </div>
</template>

<script>
import Title from '@/components/commonView/title.vue';
import { videoList, getVideoTree, getSelectPage, getDict, getStatistics } from '@/api/xxindex';
import DynamicVideoGrid from './components/DynamicVideoGrid.vue';

// 图片
const tab1 = require('../../assets/images/icon/tab1.png');
const tab2 = require('../../assets/images/icon/tab2.png');
const tab3 = require('../../assets/images/icon/tab3.png');
const tab4 = require('../../assets/images/icon/tab4.png');
const tab1Active = require('../../assets/images/icon/tab1-1.png');
const tab2Active = require('../../assets/images/icon/tab2-1.png');
const tab3Active = require('../../assets/images/icon/tab3-1.png');
const tab4Active = require('../../assets/images/icon/tab4-1.png');
export default {
    components: {
        Title,
        DynamicVideoGrid
    },
    data() {
        return {
            data: [],
            videoInfoList: [],
            Dict: [],
            tabList: [
                {
                    id: 1,
                    tab: tab1,
                    tabActive: tab1Active,
                    component: 'DomTab1'
                },
                {
                    id: 2,
                    tab: tab2,
                    tabActive: tab2Active,
                    component: 'DomTab2'
                },
                {
                    id: 3,
                    tab: tab3,
                    tabActive: tab3Active,
                    component: 'DomTab3'
                },
                {
                    id: 4,
                    tab: tab4,
                    tabActive: tab4Active,
                    component: 'DomTab4'
                }
            ],
            activeTab: {},
            changeData: '全部',
            aiVideoList: [],
            totalInfo: {},
            resetVideoInfoList: [],
            // localStorage键名常量
            STORAGE_KEY: 'nanniwan_selected_camera_ids',
            aiVideoTitle: ''
        }
    },
    mounted() {
        this.initRequest();

        // 显示保存状态信息
        this.showSavedStatusInfo();
    },
    computed: {
        srcList() {
            return this.aiVideoList.map(i => i.image)
        }
    },
    methods: {
        changeTab(item) {
            this.activeTab = item
        },
        async initRequest() {
            try {
                // 1. 首先获取区域树结构
                await this.loadVideoTree();

                // 2. 然后获取摄像头列表并挂接到区域
                await this.loadVideoListAndAttachToAreas();

                // 3. 并行获取其他数据
                await Promise.all([
                    this.loadAIDict(),
                    this.loadStatistics()
                ]);

                // 4. 验证数据结构完整性
                this.validateDataStructure();

                // 5. 加载保存的选择状态（在数据完全加载后）
                this.$nextTick(() => {
                    this.loadSavedSelection();
                });
            } catch (error) {
                console.error('初始化失败:', error);
            }
        },

        // 获取区域树结构
        async loadVideoTree() {
            try {
                const res = await getVideoTree({});
                if (res.code == 'success' && res.data) {
                    this.data = res.data;
                    this.renameNameToTitleInArray(this.data);

                    // 调试：打印区域树数据结构
                    if (res.data.length > 0) {
                        console.log('区域树数据示例:', res.data[0]);
                        console.log('区域字段:', Object.keys(res.data[0]));
                    }

                    console.log('区域树加载成功:', this.data);
                } else {
                    console.error('获取区域树失败: 响应数据无效', res);
                    this.data = []; // 设置默认值
                }
            } catch (error) {
                console.error('获取区域树失败:', error);
                this.data = []; // 设置默认值
                throw error;
            }
        },

        // 获取摄像头列表并挂接到区域
        async loadVideoListAndAttachToAreas() {
            try {
                let data = {
                    customQueryParams: {
                        areaId: '',
                        areaPath: '',
                        name: '',
                        spaceId: ''
                    },
                    page: {
                        current: 1,
                        size: 1000  // 增加页面大小以获取所有摄像头
                    },
                    sort: {}
                };

                const res = await videoList(data);
                if (res.code == 'success' && res.data && res.data.records) {
                    // 只保存原始数据，不直接显示
                    this.resetVideoInfoList = res.data.records;
                    // 初始时不显示任何摄像头，等待用户选择
                    this.videoInfoList = [];
                    // xiao
                    this.activeTab = this.tabList[0];
                    // 调试：打印摄像头数据结构
                    if (res.data.records.length > 0) {
                        console.log('摄像头数据示例:', res.data.records[0]);
                        console.log('摄像头字段:', Object.keys(res.data.records[0]));
                    }

                    // 将摄像头挂接到对应区域
                    this.attachCamerasToAreas(res.data.records);
                    console.log('摄像头列表加载并挂接成功，初始显示为空');
                } else {
                    console.error('获取摄像头列表失败: 响应数据无效', res);
                    this.videoInfoList = [];
                    this.resetVideoInfoList = [];
                }
            } catch (error) {
                console.error('获取摄像头列表失败:', error);
                this.videoInfoList = [];
                this.resetVideoInfoList = [];
                throw error;
            }
        },

        // 将摄像头挂接到对应区域
        attachCamerasToAreas(cameras) {
            if (!cameras || cameras.length === 0) {
                console.warn('摄像头列表为空');
                return;
            }

            if (!this.data || this.data.length === 0) {
                console.warn('区域数据为空，无法挂接摄像头');
                return;
            }

            console.log(`开始挂接 ${cameras.length} 个摄像头到区域树`);
            let successCount = 0;

            // 为每个摄像头找到对应的区域并挂接
            cameras.forEach(camera => {
                if (camera && camera.areaId && camera.areaId.trim() !== '') {
                    const targetArea = this.findAreaById(this.data, camera.areaId);
                    if (targetArea) {
                        this.addCameraToAreaNode(targetArea, camera);
                        successCount++;
                        console.log(`摄像头 ${camera.deviceName} 成功挂接到区域 ${targetArea.title} (areaId: ${camera.areaId})`);
                    } else {
                        console.warn(`无法找到匹配的区域，摄像头 ${camera.deviceName} areaId: ${camera.areaId}`);
                    }
                } else {
                    console.warn(`摄像头 ${camera?.deviceName || '未知'} 缺少有效的 areaId 字段`);
                }
            });

            console.log(`摄像头挂接完成: 成功 ${successCount}/${cameras.length}`);

            // 打印区域树结构（用于调试）
            this.printAreaTreeStructure(this.data, 0);
        },

        // 根据areaId精确查找区域节点
        findAreaById(nodes, areaId) {
            for (let node of nodes) {
                // 检查当前节点的id是否匹配
                if (node.id === areaId) {
                    return node;
                }

                // 递归查找子节点
                if (node.children && node.children.length > 0) {
                    // 只在非摄像头的子节点中查找
                    const subAreas = node.children.filter(child => !child.isCamera);
                    const result = this.findAreaById(subAreas, areaId);
                    if (result) {
                        return result;
                    }
                }
            }
            return null;
        },

        // 打印区域树结构（调试用）
        printAreaTreeStructure(nodes, level) {
            const indent = '  '.repeat(level);
            nodes.forEach(node => {
                if (node.isCamera) {
                    console.log(`${indent}📹 ${node.title} (摄像头)`);
                } else {
                    const cameraCount = node.children ? node.children.filter(child => child.isCamera).length : 0;
                    console.log(`${indent}📁 ${node.title || node.name} (摄像头: ${cameraCount})`);

                    if (node.children && node.children.length > 0) {
                        this.printAreaTreeStructure(node.children, level + 1);
                    }
                }
            });
        },

        // 将摄像头添加为区域节点的子节点
        addCameraToAreaNode(areaNode, camera) {
            if (!areaNode.children) {
                areaNode.children = [];
            }

            // 检查是否已经存在该摄像头，避免重复添加
            const existingCamera = areaNode.children.find(child =>
                child.isCamera && child.cameraData && child.cameraData.id === camera.id
            );

            if (existingCamera) {
                console.warn(`摄像头 ${camera.deviceName} 已存在于区域 ${areaNode.title} 中，跳过重复添加`);
                return;
            }

            // 从childAttributeList中提取视频地址和accessToken
            const videoUrl = this.extractVideoUrl(camera);
            const accessToken = this.extractAccessToken(camera);

            // 从childAttributeList中提取最近一帧图片地址
            const lastFrameImage = this.extractLastFrameImage(camera);

            // 增强摄像头数据
            const enhancedCamera = {
                ...camera,
                streamUrl: videoUrl, // 添加视频流地址
                hasValidStream: !!videoUrl, // 标记是否有有效的视频流
                accessToken: accessToken, // 添加accessToken
                lastFrameImage: lastFrameImage // 添加最近一帧图片地址
            };

            // 创建摄像头节点
            const cameraNode = {
                title: `📹 ${camera.deviceName}`, // 使用emoji图标
                id: `camera_${camera.id}`,
                isCamera: true,
                cameraData: enhancedCamera,
                checked: false,
                disabled: false,
                className: 'camera-tree-node' // 添加CSS类名
            };

            areaNode.children.push(cameraNode);
        },

        // 从设备的childAttributeList中提取视频地址
        extractVideoUrl(camera) {
            if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
                console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
                return null;
            }

            // 查找propCode为'ezopen'的属性
            const ezOpenAttr = camera.childAttributeList.find(attr =>
                attr.propCode === 'ezopen'
            );

            if (ezOpenAttr && ezOpenAttr.propValue) {
                console.log(`摄像头 ${camera.deviceName} 视频地址: ${ezOpenAttr.propValue}`);
                return ezOpenAttr.propValue;
            } else {
                console.warn(`摄像头 ${camera.deviceName} 未找到ezopen视频地址`);
                return null;
            }
        },

        // 从设备的childAttributeList中提取accessToken
        extractAccessToken(camera) {
            if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
                console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
                return null;
            }

            // 查找propCode为'accessToken'的属性
            const accessTokenAttr = camera.childAttributeList.find(attr =>
                attr.propCode === 'accessToken'
            );

            if (accessTokenAttr && accessTokenAttr.propValue) {
                console.log(`摄像头 ${camera.deviceName} accessToken: ${accessTokenAttr.propValue}`);
                return accessTokenAttr.propValue;
            } else {
                console.warn(`摄像头 ${camera.deviceName} 未找到accessToken`);
                return null;
            }
        },

        // 从设备的childAttributeList中提取最近一帧图片地址
        extractLastFrameImage(camera) {
            if (!camera.childAttributeList || !Array.isArray(camera.childAttributeList)) {
                console.warn(`摄像头 ${camera.deviceName} 没有childAttributeList数据`);
                return null;
            }

            // 查找propCode为'originImageKey'的属性
            const imageKeyAttr = camera.childAttributeList.find(attr =>
                attr.propCode === 'originImageKey'
            );

            if (imageKeyAttr && imageKeyAttr.propValue) {
                console.log(`摄像头 ${camera.deviceName} 最近一帧图片: ${imageKeyAttr.propValue}`);
                return imageKeyAttr.propValue;
            } else {
                console.warn(`摄像头 ${camera.deviceName} 未找到originImageKey图片地址`);
                return null;
            }
        },

        // 获取AI字典数据
        async loadAIDict() {
            try {
                const res = await getDict('ai_rule_type');
                if (res.code == 'success') {
                    // 需要合并的违规行为项的itemValue列表
                    const violationTypes = ['00', '11', '05', '08', '02', '10']; // 未带安全帽，未系安全绳，未穿反光服，打架，吸烟，未穿安全背带

                    // 需要合并的烟火识别项的itemValue列表
                    const fireTypes = ['06', '07']; // 明火，烟雾

                    // 过滤出需要合并的项和其他项
                    const violationItems = res.data.filter(item => violationTypes.includes(item.itemValue));
                    const fireItems = res.data.filter(item => fireTypes.includes(item.itemValue));
                    const otherItems = res.data.filter(item =>
                        !violationTypes.includes(item.itemValue) && !fireTypes.includes(item.itemValue)
                    );

                    // 合并后的字典数据数组
                    let mergedDict = [];

                    // 如果有需要合并的违规行为项，创建合并后的新选项
                    if (violationItems.length > 0) {
                        // 创建合并后的"违规行为"选项
                        const mergedViolation = {
                            dictId: res.data[0]?.dictId || 12,
                            itemText: "违规行为",
                            itemValue: "violation",
                            description: "违规行为汇总",
                            sortOrder: 0,
                            status: 1,
                            isSync: false,
                            id: 'violation',
                            // 存储所有合并项的itemValue，用于后续查询
                            violationTypes: violationItems.map(item => item.itemValue)
                        };

                        mergedDict.push(mergedViolation);
                    }

                    // 如果有需要合并的烟火识别项，创建合并后的新选项
                    if (fireItems.length > 0) {
                        // 创建合并后的"烟火识别"选项
                        const mergedFire = {
                            dictId: res.data[0]?.dictId || 12,
                            itemText: "烟火识别",
                            itemValue: "fire_detection",
                            description: "烟火识别汇总",
                            sortOrder: 1,
                            status: 1,
                            isSync: false,
                            id: 'fire_detection',
                            // 存储所有合并项的itemValue，用于后续查询
                            violationTypes: fireItems.map(item => item.itemValue)
                        };

                        mergedDict.push(mergedFire);
                    }

                    // 合并后的字典数据：先放合并项，再放其他项
                    this.Dict = [...mergedDict, ...otherItems];

                    this.handleChange(this.Dict[0]);
                    this.changeData = this.Dict[0];
                }
            } catch (error) {
                console.error('获取AI字典失败:', error);
            }
        },

        // 获取统计数据
        async loadStatistics() {
            try {
                const res = await getStatistics();
                if (res.code == 'success') {
                    this.totalInfo = res.data;
                }
            } catch (error) {
                console.error('获取统计数据失败:', error);
            }
        },
        handleCheckChange(item) {
            // 收集所有勾选的摄像头
            const selectedCameras = this.collectSelectedCameras(item);

            // 无论是否有选中摄像头，都直接传递选中的摄像头列表
            // 空数组时显示单个空播放器，有摄像头时按需显示
            this.videoInfoList = selectedCameras;
            console.log(this.videoInfoList, '57290e8')
            console.warn(`选中摄像头数量: ${selectedCameras.length}`);
        },

        // 收集所有勾选的摄像头
        collectSelectedCameras(selectedNodes) {
            const cameras = [];

            selectedNodes.forEach(node => {
                if (node.isCamera) {
                    // 如果是摄像头节点，直接添加
                    cameras.push(node.cameraData);
                } else {
                    // 如果是区域节点，收集其下的所有摄像头
                    this.collectCamerasFromNode(node, cameras);
                }
            });

            // 去重处理
            const uniqueCameras = cameras.filter((camera, index, self) =>
                index === self.findIndex(c => c.id === camera.id)
            );

            return uniqueCameras;
        },

        // 从节点中收集摄像头
        collectCamerasFromNode(node, cameraList) {
            if (node.children && node.children.length > 0) {
                node.children.forEach(child => {
                    if (child.isCamera) {
                        cameraList.push(child.cameraData);
                    } else {
                        // 递归收集子区域的摄像头
                        this.collectCamerasFromNode(child, cameraList);
                    }
                });
            }
        },
        handleChange(item) {
            let data = {
                page: {
                    size: 10,
                    current: 1
                },
                customQueryParams: {}
            }

            // 处理不同类型的选项值
            if (item === '全部') {
                // 全部选项不需要过滤条件
                // 保持customQueryParams为空对象
            } else if (typeof item === 'object' && ((item.itemValue === 'violation' || item.itemValue === 'fire_detection') && item.violationTypes)) {
                // 特殊处理合并选项（违规行为或烟火识别）
                data.customQueryParams.aiRuleTypes = item.violationTypes.join(',');
            } else if (typeof item === 'object') {
                // 常规选项对象，使用单个aiRuleType查询
                data.customQueryParams.aiRuleType = item.itemValue;
            } else {
                // 如果是字符串（选项文本），则查找对应的字典项
                const dictItem = this.Dict.find(d => d.itemText === item);
                if (dictItem) {
                    if ((dictItem.itemValue === 'violation' || dictItem.itemValue === 'fire_detection') && dictItem.violationTypes) {
                        data.customQueryParams.aiRuleTypes = dictItem.violationTypes.join(',');
                    } else {
                        data.customQueryParams.aiRuleType = dictItem.itemValue;
                    }
                }
            }

            getSelectPage(data).then(res => {
                if (res.code == 'success') {
                    this.aiVideoList = res.data.records;

                    // 设置标题，确保使用正确的文本而不是对象
                    if (item === '全部') {
                        this.aiVideoTitle = '全部';
                    } else if (typeof item === 'object') {
                        this.aiVideoTitle = item.itemText;
                    } else {
                        this.aiVideoTitle = item;
                    }
                }
            })
        },
        renameNameToTitleInArray(array) {
            array.forEach(item => {
                if (item.name) {
                    item.title = item.name;
                    delete item.name;
                }
                if (item.children) {
                    this.renameNameToTitleInArray(item.children);
                }
            });
        },
        // 验证数据结构完整性
        validateDataStructure() {
            console.log('=== 数据结构验证 ===');
            console.log(`区域树节点数: ${this.data ? this.data.length : 0}`);
            console.log(`摄像头总数: ${this.videoInfoList ? this.videoInfoList.length : 0}`);

            if (this.data && this.data.length > 0) {
                let totalAttachedCameras = 0;
                this.countAttachedCameras(this.data, (count) => {
                    totalAttachedCameras += count;
                });
                console.log(`已挂接到区域的摄像头数: ${totalAttachedCameras}`);

                if (totalAttachedCameras < this.videoInfoList.length) {
                    console.warn(`有 ${this.videoInfoList.length - totalAttachedCameras} 个摄像头未能挂接到区域`);
                }
            }
            console.log('=== 验证完成 ===');
        },

        // 统计挂接到区域的摄像头数量
        countAttachedCameras(nodes, callback) {
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    // 统计当前节点下的摄像头数量
                    const cameraCount = node.children.filter(child => child.isCamera).length;
                    if (cameraCount > 0) {
                        callback(cameraCount);
                    }

                    // 递归统计子区域
                    const subAreas = node.children.filter(child => !child.isCamera);
                    if (subAreas.length > 0) {
                        this.countAttachedCameras(subAreas, callback);
                    }
                }
            });
        },
        saveSelection() {
            try {
                // 收集当前选中的摄像头ID列表
                const selectedCameraIds = this.collectSelectedCameraIds(this.data);

                // 检查是否有选择的摄像头
                if (selectedCameraIds.length === 0) {
                    this.$Message.warning('请先选择要保存的摄像头');
                    return;
                }

                // 保存到localStorage
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(selectedCameraIds));

                // 显示成功提示
                this.$Message.success(`已保存 ${selectedCameraIds.length} 个摄像头的选择状态`);

                console.log('保存的摄像头ID列表:', selectedCameraIds);
            } catch (error) {
                console.error('保存选择失败:', error);
                this.$Message.error('保存失败，请重试');
            }
        },
        resetSelection() {
            // 添加确认对话框
            this.$confirm('确定要重置所有摄像头选择吗？此操作将清除所有已保存的选择状态。', '确认重置', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                try {
                    // 清除所有勾选状态
                    this.clearAllSelections(this.data);

                    // 清空视频显示列表
                    this.videoInfoList = [];

                    // 清除localStorage中的保存
                    localStorage.removeItem(this.STORAGE_KEY);

                    // 显示成功提示
                    this.$Message.success('已重置所有选择');

                    console.log('已重置所有摄像头选择');
                } catch (error) {
                    console.error('重置选择失败:', error);
                    this.$Message.error('重置失败，请重试');
                }
            }).catch(() => {
                // 用户取消重置
                console.log('用户取消重置操作');
            });
        },
        collectSelectedCameraIds(nodes) {
            const selectedIds = [];

            const traverseNodes = (nodeList) => {
                nodeList.forEach(node => {
                    if (node.isCamera && node.checked) {
                        // 如果是选中的摄像头节点，添加摄像头ID
                        if (node.cameraData && node.cameraData.id) {
                            selectedIds.push(node.cameraData.id);
                        }
                    } else if (!node.isCamera && node.children && node.children.length > 0) {
                        // 如果是区域节点，递归遍历子节点
                        traverseNodes(node.children);
                    }
                });
            };

            traverseNodes(nodes);
            return selectedIds;
        },
        clearAllSelections(nodes) {
            const clearNodes = (nodeList) => {
                nodeList.forEach(node => {
                    // 清除节点的选中状态
                    if (node.checked !== undefined) {
                        node.checked = false;
                    }

                    // 递归清除子节点
                    if (node.children && node.children.length > 0) {
                        clearNodes(node.children);
                    }
                });
            };

            clearNodes(nodes);

            // 强制更新Tree组件显示
            this.$forceUpdate();
        },
        loadSavedSelection() {
            try {
                const savedIds = localStorage.getItem(this.STORAGE_KEY);
                if (savedIds) {
                    const selectedCameraIds = JSON.parse(savedIds);
                    if (Array.isArray(selectedCameraIds) && selectedCameraIds.length > 0) {
                        console.log('加载保存的摄像头选择:', selectedCameraIds);

                        // 恢复选择状态
                        this.restoreSelectionByIds(this.data, selectedCameraIds);

                        // 更新视频显示
                        const selectedCameras = this.getSelectedCamerasByIds(selectedCameraIds);
                        this.videoInfoList = selectedCameras;

                        console.log(`已恢复 ${selectedCameras.length} 个摄像头的选择状态`);
                    }
                }
            } catch (error) {
                console.error('加载保存的选择失败:', error);
                // 如果加载失败，清除无效的localStorage数据
                localStorage.removeItem(this.STORAGE_KEY);
            }
        },
        restoreSelectionByIds(nodes, selectedIds) {
            const restoreNodes = (nodeList) => {
                nodeList.forEach(node => {
                    if (node.isCamera && node.cameraData && node.cameraData.id) {
                        // 如果是摄像头节点，检查是否在选中列表中
                        node.checked = selectedIds.includes(node.cameraData.id);
                    } else if (!node.isCamera && node.children && node.children.length > 0) {
                        // 递归处理子节点
                        restoreNodes(node.children);
                    }
                });
            };

            restoreNodes(nodes);

            // 强制更新Tree组件显示
            this.$forceUpdate();
        },
        getSelectedCamerasByIds(selectedIds) {
            const cameras = [];

            const findCameras = (nodeList) => {
                nodeList.forEach(node => {
                    if (node.isCamera && node.cameraData && selectedIds.includes(node.cameraData.id)) {
                        cameras.push(node.cameraData);
                    } else if (!node.isCamera && node.children && node.children.length > 0) {
                        findCameras(node.children);
                    }
                });
            };

            findCameras(this.data);
            return cameras;
        },
        showSavedStatusInfo() {
            try {
                const savedIds = localStorage.getItem(this.STORAGE_KEY);
                if (savedIds) {
                    const selectedCameraIds = JSON.parse(savedIds);
                    if (Array.isArray(selectedCameraIds) && selectedCameraIds.length > 0) {
                        console.log(`发现保存的选择状态: ${selectedCameraIds.length} 个摄像头`);

                        // 可以添加一个小的提示信息
                        setTimeout(() => {
                            this.$Message.info(`检测到已保存的 ${selectedCameraIds.length} 个摄像头选择，正在恢复...`);
                        }, 1000);
                    }
                } else {
                    console.log('没有发现保存的选择状态');
                }
            } catch (error) {
                console.error('检查保存状态失败:', error);
            }
        },
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-carousel--horizontal{
    overflow: hidden;
}
/deep/ .ivu-select-selection {
    background-color: transparent;
    border: none;
    color: #fff
}
/deep/ .ivu-select-arrow::before {
    content: '';
    display: block;
    width: 15px;
    height: 10px;
    background: url(../../assets//images/icon/down.png) no-repeat center center;
    background-size: contain;
}
.tooltip-content {
    width: 108px;
    height: 108px;
    .tooltip-item {
        width: 100%;
        height: 100%;
        padding: 10px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .items {
            width: 42%;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 30px;
                height: 30px;
            }
        }
    }
}

.grid-three {
    height: 98%;
    display: flex;
    align-items: flex-end;
    justify-content: center;

    .grid-three-active {
        width: 26px;
        height: 26px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}

.leftTop {
    width: 100%;
    height: calc(50vh - 20px);
    position: relative;
    overflow: hidden;
}

.rightTop {
    width: 100%;
    height: 50%;
}

.rightBottom {
    width: 100%;
    height: 100%;
    margin-top: 1px;
}

.video-item2 {
    width: 100%;
    height: 99% !important;
    background: url(../../assets/images/icon/border_back2.png) no-repeat center center !important;
    background-size: 100% 100% !important;
}

.video-item1 {
    width: 100%;
    height: 100%;
    background: url(../../assets/images/icon/border_back.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;

    .default-content {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        letter-spacing: 2px;

        .video {
            width: 100%;
            height: calc(100% - 45px);
        }

        .text-flex {
            width: 100%;
            display: flex;
            height: 45px;
            justify-content: space-between;
            color: #fff;

            div {
                width: 50%;
                display: flex;
                align-items: center;
                margin-left: 20px;
                padding: 10px;

                img {
                    width: 22px;
                    height: 20px;
                }

                span {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

.leftButtom {
    width: 100%;
    height: calc(30vh - 28px);
    margin-top: 10px;
    padding: 10px;
    box-sizing: border-box;

    .icon-flex {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: space-between;

        .icon-item {
            width: 50%;
            display: flex;
            align-items: center;
            gap: 10px;

            .item {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                &:first-child {
                    color: rgba(255, 255, 255, .6);
                    font-size: 12px;
                }

                &:last-child {
                    color: #fff;
                    font-size: 16px;
                }
            }
        }
    }

    .nav {
        width: 100%;
        height: 28px;
        margin-top: 20px;
        border-bottom: 1px dashed #ccc;

        .nav-title {
            display: flex;
            justify-content: space-between;
            gap: 40px;
            align-items: center;

            .back {
                width: 101px;
                height: 28px;
                background: url(../../assets/images/icon/smallTitle.png) no-repeat center center;
                background-size: contain;
                background-position-y: -2px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                font-size: 18px;
                font-weight: 600;
            }

            .detail {
                width: 60%;
                display: flex;
                align-items: center;

                span {
                    width: 90%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #fff;
                    font-size: 14px;
                }

                img {
                    width: 15px;
                    height: 10px;
                }
            }
        }
    }

    .small-title {
        width: 100%;
        margin-top: 10px;

        .title-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                color: #fff;
                font-size: 14px;
            }

            .time {
                color: rgba(255, 255, 255, .6);
                font-size: 12px;
            }
        }

        .video-img {
            width: 100%;
            height: calc(30vh - 28px - 40px - 10px);
            object-fit: cover;
            display: flex;
            flex-direction: column;
            gap: 10px;
            border: 1px solid;
            .image{
                flex-shrink: 0;
            }
        }

    }
}

.grid-three {
    height: 98%;
    display: flex;
    align-items: flex-end;
    justify-content: center;

    .grid-three-active {
        width: 26px;
        height: 26px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}

.leftFloor {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 60px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #111b2c;

    .primary {
        padding: 8px 30px;
        background-color: #b48641;
        color: #fff;
        border-radius: 6px;
        cursor: pointer;
        outline: none;
        border: 1px solid #b48641;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            background-color: #c49652;
            border-color: #c49652;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(180, 134, 65, 0.3);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(180, 134, 65, 0.3);
        }
    }

    .info {
        padding: 8px 30px;
        background-color: rgba(255, 255, 255, .15);
        color: #fff;
        border-radius: 6px;
        cursor: pointer;
        outline: none;
        border: 1px solid rgba(255, 255, 255, .3);
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            background-color: rgba(255, 255, 255, .25);
            border-color: rgba(255, 255, 255, .5);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(255, 255, 255, 0.1);
        }
    }
}

.container {
    width: 100%;
    height: calc(100vh - 60px);
    background-color: #111b2c;
    pointer-events: auto;
    padding: 10px;
    box-sizing: border-box;
}

.leftContent {
    height: calc(100% - 60px - 20px);
    overflow: hidden;
    overflow-y: auto;
    padding: 20px;
    padding-top: 0;
    box-sizing: border-box;
}

/deep/ .ivu-tree-children {
    font-size: 16px;

    li {
        .ivu-checkbox-inner {
            background-color: transparent;
        }

        .ivu-checkbox-inner:hover {
            border-color: #b88340
        }

        .ivu-tree-title {
            color: #fff;
        }
    }
}

/deep/ .ivu-icon-ios-arrow-forward::before {
    content: '▶';
    color: #fff;
    font-size: 10px;
}

/deep/ .ivu-tree-children li:not(.ivu-tree-children[visible]) {
    margin: 20px 0 !important;
}

/deep/ .ivu-tree-title {
    background: transparent !important;
}

/deep/ .ivu-tree li ul {
    padding: 0;
    margin-left: 24px;
}

/deep/ .ivu-tree li ul .ivu-tree-arrow-open {
    margin-left: 0;
}

/deep/ .ivu-tree li ul .ivu-tree-arrow:not(.ivu-tree-arrow-open) {
    margin-left: 0;
}

/deep/ .ivu-tree-children li {
    position: relative;
    line-height: 42px;
}

/deep/ .ivu-tree-arrow::before {
    content: '';
    position: absolute;
    width: 400px;
    height: 50px;
    top: -3px;
    left: -10px;
    background-image: linear-gradient(to right, #4b83b700, #4b83b71a 52.5%, #4b83b700);
}

/deep/ .ivu-tree-arrow:hover::before {
    border: 1px solid #fff;
}

/deep/ .ivu-tree-title:hover {
    background-color: transparent;
}

/* 摄像头节点样式 */
/deep/ .camera-tree-node .ivu-tree-title,
/deep/ .ivu-tree li[data-camera="true"] .ivu-tree-title {
    color: #00d4ff !important; /* 摄像头节点使用蓝色 */
    background: rgba(0, 212, 255, 0.1) !important;
    padding: 4px 8px !important;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: normal;

    &:hover {
        background: rgba(0, 212, 255, 0.2) !important;
        color: #40e0ff !important;
    }
}

/* 区域节点样式增强 */
/deep/ .ivu-tree li .ivu-tree-title:not(.camera-tree-node) {
    color: #fff !important;
    font-weight: 500;
    padding: 2px 4px;

    &:hover {
        color: #b88340 !important;
    }
}

/* 摄像头节点的复选框样式 */
/deep/ .camera-tree-node .ivu-checkbox-inner,
/deep/ .ivu-tree li[data-camera="true"] .ivu-checkbox-inner {
    border-color: #00d4ff;

    &:hover {
        border-color: #40e0ff;
    }
}

/deep/ .camera-tree-node .ivu-checkbox-checked .ivu-checkbox-inner,
/deep/ .ivu-tree li[data-camera="true"] .ivu-checkbox-checked .ivu-checkbox-inner {
    background-color: #00d4ff;
    border-color: #00d4ff;
}

/* 修复树形结构的层级缩进 */
/deep/ .ivu-tree {
    /* 重置所有默认的margin和padding */
    .ivu-tree-children {
        margin-left: 0;
        padding-left: 0;
    }

    /* 为每一级添加递增的缩进 */
    .ivu-tree-children .ivu-tree-children {
        margin-left: 24px;
    }

    .ivu-tree-children .ivu-tree-children .ivu-tree-children {
        margin-left: 24px;
    }

    .ivu-tree-children .ivu-tree-children .ivu-tree-children .ivu-tree-children {
        margin-left: 24px;
    }

    /* 确保箭头和内容对齐 */
    .ivu-tree-arrow {
        margin-right: 8px;
        width: 14px;
        height: 14px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;
    }

    /* 重置原有的不合理margin设置 */
    li ul {
        padding: 0;
        margin-left: 0;
    }

    li ul .ivu-tree-arrow-open,
    li ul .ivu-tree-arrow:not(.ivu-tree-arrow-open) {
        margin-left: 0;
    }

    /* 添加层级指示线 */
    .ivu-tree-children .ivu-tree-children li {
        position: relative;

        &::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 0;
            height: 21px;
            width: 1px;
            background: rgba(255, 255, 255, 0.15);
        }

        &:last-child::before {
            height: 21px;
        }
    }
}

.video-display-container {
    width: 100%;
    height: 100%;
}
</style>

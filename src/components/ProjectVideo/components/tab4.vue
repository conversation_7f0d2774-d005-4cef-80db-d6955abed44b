<template>
     <div style="height: 100%;">
        <Row style="height: 100%;">
            <Col :span="8" style="height: 33.3%;" v-for="(item, index) in 9" :key="index">
                <div class="rightBottom">
                    <div class="video-item1 video-item2">
                        <div class="default-content">
                            <div class="root">
                                <div :class="'video video' + (index + 1)"></div>
                            </div>
                            <div class="text-flex">
                                <div>
                                    <img src="../../../assets/images/icon/home.png" alt="">
                                    <span>邹城至济宁高速公路上跨京沪铁路项目</span>
                                </div>
                                <div>
                                    <img src="../../../assets/images/icon/Camera.png" alt="">
                                    <span>视频2（摄像头）</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Col>
        </Row>
    </div>
</template>

<script>
export default {
    props: ['listData'],
    data() {
        return {
            jessibucaList: [
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
                {
                    stream: 'https://dh5qqwx01.v.cntv.cn/asp/h5e/hls/main/0303000a/3/default/4572f76e54254ed4aa3dbdf740afa31b/main.m3u8?maxbr=2048&contentid=15120519184043',
                    jessibuca: null
                },
            ],
        }
    },
     mounted() {
        this.initData();
    },
    methods: {
        initData() {
            let arr = [];
            this.listData.forEach(item => {
                if (item.childAttributeList) {
                    let items = item.childAttributeList.filter(i => i.propCode == 'hls_url');
                    arr.push({
                        stream: items[0].propValue,
                        jessibuca: null
                    })
                }
            })
            this.jessibucaList = arr;
            this.initRequest();
        },
        initRequest() {
            this.jessibucaList.forEach((item, index) => {
                item.jessibuca = new JessibucaPro({
                    container: document.querySelector('.video' + (index + 1)),
                    // 其他配置...
                    showBandwidth: false,  // 隐藏带宽信息
                    showPerformance: false, // 隐藏性能信息
                    operateBtns: {
                        fullscreen: true,
                        screenshot: true,
                        play: true,
                        audio: true,
                        ptz: true,
                        quality: true,
                        performance: false, // 隐藏性能按钮
                    },
                })
                if (item.stream) {
                    item.jessibuca.play(item.stream)
                } else {
                    this.jessibuca.showErrorMessageTips('播放地址不能为空');
                }
            })
        }
    }

}
</script>

<style lang="less" scoped>
.root {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    .video {
        width: 98% !important;
        height: 90% !important;
        background-color: transparent;
        position: relative;
    }
}
.rightTop {
    width: 100%;
    height: 50%;
}
.rightBottom {
    width: 100%;
    height: 100%;
    margin-top: 1px;
}
.video-item2 {
    width: 100%;
    height: 99% !important;
    background: url(../../../assets/images/icon/border_back2.png) no-repeat center center !important;
    background-size: 100% 100% !important;
}

.video-item1 {
    width: 100%;
    height: 100%;
    background: url(../../../assets/images/icon/border_back.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;

    .default-content {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        letter-spacing: 2px;

        .video {
            width: 100%;
            height: calc(100% - 45px);
        }

        .text-flex {
            width: 100%;
            display: flex;
            height: 45px;
            justify-content: space-between;
            color: #fff;

            div {
                width: 50%;
                display: flex;
                align-items: center;
                margin-left: 20px;
                padding: 10px;

                img {
                    width: 22px;
                    height: 20px;
                }

                span {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}
</style>

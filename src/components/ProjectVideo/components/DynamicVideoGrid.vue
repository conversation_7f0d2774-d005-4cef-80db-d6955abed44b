<template>
  <div class="dynamic-video-grid">
    <div 
      class="video-container" 
      :class="getGridClass()"
      :style="getGridStyle()"
    >
      <div 
        v-for="(item, index) in displayItems" 
        :key="index" 
        class="video-item"
        :class="{
          'empty-item': item.isEmpty,
          'active-item': !item.isEmpty,
          'single-view-item': item.isSingleView,
          'grid-view-item': !item.isSingleView
        }"
      >
        <div class="video-wrapper">
          <div 
            :id="`video-player-${index}`" 
            class="video-player"
            v-show="!item.isEmpty && item.camera.onlineState === 1"
          ></div>
          
          <!-- 设备离线提示 -->
          <div v-if="!item.isEmpty && item.camera.onlineState !== 1" class="offline-message">
            <!-- 最近一帧图片背景 -->
            <div 
              class="offline-background" 
              v-if="item.camera.lastFrameImage"
              :style="{ backgroundImage: `url(${item.camera.lastFrameImage})` }"
            ></div>
            
            <!-- 离线提示覆盖层 -->
            <div class="offline-overlay">
              <div class="offline-content">
                <div class="offline-icon">⚠️</div>
                <p>设备不在线，请优化网络后重启设备再试</p>
              </div>
            </div>
          </div>
          
          <!-- 空播放器占位符 -->
          <div v-if="item.isEmpty" class="empty-placeholder">
            <div class="placeholder-content">
              <img src="../../../assets/images/icon/video.png" alt="视频">
              <p v-if="item.isSingleView">点击左侧摄像头开始播放</p>
              <p v-else>暂无视频</p>
            </div>
          </div>
          
          <!-- 底部信息栏 -->
          <div v-if="!item.isEmpty && showInfoBar" class="video-info">
            <div class="camera-name">
              <img src="../../../assets/images/icon/video.png" alt="">
              <div class="name-info">
                <span class="device-name">{{ item.camera.deviceName }}</span>
                <span class="area-path">{{ item.camera.areaPath || '未知区域' }}</span>
              </div>
            </div>
            <div class="camera-status">
              <span :class="item.camera.onlineState === 1 ? 'online' : 'offline'">
                {{ item.camera.onlineState === 1 ? '在线' : '离线' }}
              </span>
              <span v-if="!item.camera.hasValidStream && item.camera.onlineState === 1" class="no-stream">无视频流</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DynamicVideoGrid',
  props: {
    cameraList: {
      type: Array,
      default: () => []
    },
    showInfoBar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      videoPlayers: {}, // 存储视频播放器实例
      displayItems: [] // 显示的项目列表
    }
  },
  watch: {
    cameraList: {
      handler(newList) {
        this.updateDisplayItems(newList);
        this.$nextTick(() => {
          // 等待DOM更新完成后初始化播放器
          setTimeout(() => {
            this.initVideoPlayers();
          }, 100);
          
          // 处理布局变化
          setTimeout(() => {
            this.handleLayoutChange();
          }, 300);
        });
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.updateDisplayItems(this.cameraList);
    this.$nextTick(() => {
      this.initVideoPlayers();
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleWindowResize);
  },
  beforeDestroy() {
    console.log('DynamicVideoGrid 组件即将销毁，开始清理资源...');
    
    try {
      // 清理视频播放器
      this.destroyAllPlayers();
    } catch (error) {
      console.error('清理视频播放器时发生错误:', error);
      
      // 如果正常清理失败，进行强制清理
      try {
        this.forceCleanupAllPlayers();
      } catch (forceError) {
        console.error('强制清理播放器失败:', forceError);
      }
    }
    
    try {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', this.handleWindowResize);
    } catch (error) {
      console.error('移除窗口监听器失败:', error);
    }
    
    // 清理所有可能的定时器
    this.clearAllTimers();
    
    console.log('DynamicVideoGrid 组件资源清理完成');
  },
  methods: {
    // 更新显示项目
    updateDisplayItems(cameraList) {
      const cameras = cameraList || [];
      
      if (cameras.length === 0) {
        // 没有摄像头时，显示1个空播放器占满整个区域
        this.displayItems = [{
          isEmpty: true,
          camera: null,
          index: 0,
          isSingleView: true // 标记为单视图模式
        }];
      } else if (cameras.length === 1) {
        // 只有1个摄像头时，显示1个播放器占满整个区域
        this.displayItems = [{
          isEmpty: false,
          camera: cameras[0],
          index: 0,
          isSingleView: true // 标记为单视图模式
        }];
      } else {
        // 多个摄像头时，使用优化的网格布局
        this.displayItems = cameras.map((camera, index) => ({
          isEmpty: false,
          camera: camera,
          index,
          isSingleView: false // 标记为网格视图模式
        }));
      }
      
      console.log(`更新显示项目: ${cameras.length} 个摄像头, ${this.displayItems.length} 个格子, 单视图模式: ${this.displayItems[0]?.isSingleView}`);
    },
    
    // 计算最优网格布局
    calculateOptimalGrid(count) {
      if (count <= 1) return { cols: 1, rows: 1 };
      if (count === 2) return { cols: 2, rows: 1 };
      if (count === 3) return { cols: 3, rows: 1 };
      if (count === 4) return { cols: 2, rows: 2 };
      if (count <= 6) return { cols: 3, rows: 2 };
      if (count <= 9) return { cols: 3, rows: 3 };
      if (count <= 12) return { cols: 4, rows: 3 };
      if (count <= 16) return { cols: 4, rows: 4 };
      
      // 对于更多的摄像头，计算最接近正方形的布局
      const cols = Math.ceil(Math.sqrt(count));
      const rows = Math.ceil(count / cols);
      return { cols, rows };
    },
    
    // 获取网格CSS类
    getGridClass() {
      if (this.displayItems.length === 0) return {};
      
      // 检查是否为单视图模式
      const isSingleView = this.displayItems[0]?.isSingleView;
      
      if (isSingleView) {
        return {
          'single-view': true
        };
      }
      
      // 网格视图模式
      return {
        'grid-view': true
      };
    },
    
    // 获取网格样式
    getGridStyle() {
      if (this.displayItems.length === 0) return {};
      
      // 检查是否为单视图模式
      const isSingleView = this.displayItems[0]?.isSingleView;
      
      if (isSingleView) {
        return {};
      }
      
      // 网格视图模式 - 使用内联样式确保动态布局生效
      const totalItems = this.displayItems.length;
      const { cols, rows } = this.calculateOptimalGrid(totalItems);
      
      const style = {
        'grid-template-columns': `repeat(${cols}, 1fr)`,
        'grid-template-rows': `repeat(${rows}, 1fr)`
      };
      
      console.log(`网格样式: ${totalItems}个摄像头, ${cols}列${rows}行`);
      
      return style;
    },
    
    // 初始化视频播放器
    initVideoPlayers() {
      // 先清理现有播放器
      this.destroyAllPlayers();
      
      this.displayItems.forEach((item, index) => {
        if (!item.isEmpty && item.camera && item.camera.onlineState === 1) {
          // 只为在线设备创建播放器
          this.createVideoPlayer(index, item.camera);
        } else if (!item.isEmpty && item.camera && item.camera.onlineState !== 1) {
          console.log(`设备 ${item.camera.deviceName} 不在线，跳过播放器创建`);
        }
      });
      
      // 多次延迟调整播放器尺寸，确保容器尺寸稳定后调整
      setTimeout(() => {
        this.resizeAllPlayers();
        console.log('第一次尺寸调整完成');
      }, 800);
      
      setTimeout(() => {
        this.resizeAllPlayers();
        console.log('第二次尺寸调整完成');
      }, 1500);
      
      setTimeout(() => {
        this.resizeAllPlayers();
        console.log('第三次尺寸调整完成');
      }, 2500);
    },
    
    // 创建单个视频播放器
    createVideoPlayer(index, camera) {
      const containerId = `video-player-${index}`;
      const container = document.querySelector(`#${containerId}`);
      
      if (!container) {
        console.warn(`找不到视频容器: ${containerId}`);
        return;
      }
      
      try {
        // 检查是否有EZUIKit
        if (typeof EZUIKit === 'undefined') {
          console.error('EZUIKit 未加载');
          return;
        }
        
        // 检查必要参数
        if (!camera.accessToken) {
          console.error(`摄像头 ${camera.deviceName} 缺少accessToken`);
          return;
        }
        
        if (!camera.streamUrl) {
          console.error(`摄像头 ${camera.deviceName} 缺少视频流地址`);
          return;
        }
        
        // 清空容器
        container.innerHTML = '';
        
        // 获取容器尺寸
        const containerRect = container.getBoundingClientRect();
        const width = Math.floor(containerRect.width) || 250;
        const height = Math.floor(containerRect.height) || 200;
        
        console.log(`创建EZUIKit播放器: ${camera.deviceName}, 容器尺寸: ${width}x${height}`);
        
        // 使用Promise确保播放器初始化完成
        this.initEZUIKitPlayer(containerId, camera, width, height).then(player => {
          if (player) {
            // 存储播放器实例
            this.videoPlayers[index] = player;
            
            // 播放器初始化完成后自动播放，添加重试机制
            this.startVideoPlayback(player, camera, 0);
            
            // 播放器创建后调整尺寸
            setTimeout(() => {
              this.resizePlayer(index);
            }, 1200);
            
            console.log(`创建视频播放器成功 ${index}: ${camera.deviceName}`);
          }
        }).catch(error => {
          console.error(`创建视频播放器失败 ${index}:`, error);
        });
        
      } catch (error) {
        console.error(`创建视频播放器失败 ${index}:`, error);
      }
    },
    
    // 初始化EZUIKit播放器（使用Promise）
    initEZUIKitPlayer(containerId, camera, width, height) {
      return new Promise((resolve, reject) => {
        try {
          console.log(`开始初始化播放器: ${camera.deviceName}`);
          console.log(`播放器参数:`, {
            id: containerId,
            accessToken: camera.accessToken ? '***已设置***' : '未设置',
            url: camera.streamUrl,
            width: width,
            height: height
          });
          
          // 检查EZUIKit是否可用
          if (typeof EZUIKit === 'undefined' || !EZUIKit.EZUIKitPlayer) {
            const error = new Error('EZUIKit 未正确加载或EZUIKitPlayer不可用');
            console.error(error);
            reject(error);
            return;
          }
          
          // 清理可能存在的旧播放器
          const oldPlayerId = `EZUIKitPlayer-${containerId}`;
          const oldIframe = document.getElementById(oldPlayerId);
          if (oldIframe) {
            console.log(`清理旧播放器iframe: ${oldPlayerId}`);
            try {
              if (oldIframe.parentNode) {
                oldIframe.parentNode.removeChild(oldIframe);
              }
            } catch (cleanupError) {
              console.warn(`清理旧播放器失败:`, cleanupError);
            }
          }
          
          // 创建EZUIKit播放器
          const player = new EZUIKit.EZUIKitPlayer({
            autoplay: true,
            id: containerId,
            accessToken: camera.accessToken,
            url: camera.streamUrl,
            width: width,
            height: height,
            template: 'standard', // standard-标准版 / simple-简洁版
            // 添加回调函数来监听播放器状态
            openSoundCallBack: data => console.log(`${camera.deviceName} 开启声音回调:`, data),
            closeSoundCallBack: data => console.log(`${camera.deviceName} 关闭声音回调:`, data),
            startSaveCallBack: data => console.log(`${camera.deviceName} 开始录像回调:`, data),
            stopSaveCallBack: data => console.log(`${camera.deviceName} 录像回调:`, data),
            capturePictureCallBack: data => console.log(`${camera.deviceName} 截图成功回调:`, data),
            fullScreenCallBack: data => console.log(`${camera.deviceName} 全屏回调:`, data),
            getOSDTimeCallBack: data => console.log(`${camera.deviceName} 获取OSDTime回调:`, data)
          });
          
          if (!player) {
            const error = new Error('播放器对象创建失败');
            console.error(`播放器对象创建失败: ${camera.deviceName}`);
            reject(error);
            return;
          }
          
          console.log(`播放器对象创建成功: ${camera.deviceName}`, player);
          
          // 等待播放器初始化完成，并验证iframe是否正确创建
          const checkPlayerReady = (attemptCount = 0) => {
            const maxAttempts = 10;
            const playerId = `EZUIKitPlayer-${containerId}`;
            const iframe = document.getElementById(playerId);
            
            if (iframe && iframe.contentWindow) {
              // iframe创建成功且有效
              console.log(`播放器iframe创建成功: ${camera.deviceName}, 尝试次数: ${attemptCount + 1}`);
              
              // 为播放器添加销毁检查方法
              player._isValid = () => {
                const currentIframe = document.getElementById(playerId);
                return currentIframe && currentIframe.contentWindow;
              };
              
              // 重写stop方法，添加安全检查
              const originalStop = player.stop;
              player.stop = function() {
                try {
                  const currentIframe = document.getElementById(playerId);
                  if (currentIframe && currentIframe.contentWindow) {
                    return originalStop.call(this);
                  } else {
                    console.warn(`播放器 ${containerId} 的iframe不存在，跳过stop调用`);
                    return Promise.resolve();
                  }
                } catch (error) {
                  console.error(`播放器 ${containerId} stop方法执行失败:`, error);
                  throw error;
                }
              };
              
              resolve(player);
            } else if (attemptCount < maxAttempts) {
              // iframe还未创建，继续等待
              setTimeout(() => {
                checkPlayerReady(attemptCount + 1);
              }, 200);
            } else {
              // 超过最大尝试次数，认为创建失败
              const error = new Error(`播放器iframe创建失败，已尝试 ${maxAttempts} 次`);
              console.error(`播放器iframe创建失败: ${camera.deviceName}`, error);
              reject(error);
            }
          };
          
          // 开始检查播放器是否准备就绪
          checkPlayerReady();
          
        } catch (error) {
          console.error(`播放器初始化异常: ${camera.deviceName}`, error);
          reject(error);
        }
      });
    },
    
    // 开始视频播放（带重试机制）
    startVideoPlayback(player, camera, retryCount) {
      const maxRetries = 3;
      
      if (retryCount >= maxRetries) {
        console.error(`播放失败，已达最大重试次数: ${camera.deviceName}`);
        return;
      }
      
      console.log(`尝试播放摄像头 (第${retryCount + 1}次): ${camera.deviceName}`);
      
      try {
        if (player && typeof player.play === 'function') {
          // 尝试播放
          const playResult = player.play();
          
          // 如果play方法返回Promise
          if (playResult && typeof playResult.then === 'function') {
            playResult.then(() => {
              console.log(`播放成功: ${camera.deviceName}`);
            }).catch(error => {
              console.warn(`播放失败 (第${retryCount + 1}次): ${camera.deviceName}`, error);
              // 延迟后重试
              setTimeout(() => {
                this.startVideoPlayback(player, camera, retryCount + 1);
              }, 1000 * (retryCount + 1)); // 递增延迟时间
            });
          } else {
            // 如果play方法不返回Promise，延迟检查播放状态
            setTimeout(() => {
              // 这里可以添加播放状态检查逻辑
              console.log(`播放命令已发送: ${camera.deviceName}`);
            }, 500);
          }
        } else {
          console.error(`播放器没有play方法: ${camera.deviceName}`, player);
          // 重试
          setTimeout(() => {
            this.startVideoPlayback(player, camera, retryCount + 1);
          }, 1000);
        }
      } catch (error) {
        console.error(`播放异常 (第${retryCount + 1}次): ${camera.deviceName}`, error);
        // 延迟后重试
        setTimeout(() => {
          this.startVideoPlayback(player, camera, retryCount + 1);
        }, 1000 * (retryCount + 1));
      }
    },
    
    // 销毁所有播放器
    destroyAllPlayers() {
      console.log('开始销毁所有播放器...');
      
      Object.keys(this.videoPlayers).forEach(index => {
        this.destroyPlayer(index);
      });
      
      // 清空播放器对象
      this.videoPlayers = {};
      console.log('所有播放器销毁完成');
    },

    // 销毁单个播放器
    destroyPlayer(index) {
      try {
        const player = this.videoPlayers[index];
        if (!player) {
          console.log(`播放器 ${index} 不存在，跳过销毁`);
          return;
        }

        console.log(`销毁播放器 ${index}...`);
        
        // 获取播放器对应的容器ID
        const containerId = `video-player-${index}`;
        const playerId = `EZUIKitPlayer-${containerId}`;
        
        // 检查iframe是否存在且有效
        const iframe = document.getElementById(playerId);
        if (iframe && iframe.contentWindow) {
          // iframe存在且有效，尝试正常停止
          try {
            if (typeof player.stop === 'function') {
              player.stop();
              console.log(`播放器 ${index} 正常停止`);
            }
          } catch (stopError) {
            console.warn(`播放器 ${index} 停止时发生错误:`, stopError);
            // 如果正常停止失败，强制清理DOM
            this.forceCleanupPlayer(containerId, iframe);
          }
        } else {
          // iframe不存在或无效，直接清理DOM
          console.warn(`播放器 ${index} 的iframe不存在或无效，直接清理DOM`);
          this.forceCleanupPlayer(containerId, iframe);
        }
        
        // 清理播放器引用
        delete this.videoPlayers[index];
        
      } catch (error) {
        console.error(`销毁播放器 ${index} 失败:`, error);
        
        // 发生错误时强制清理
        try {
          const containerId = `video-player-${index}`;
          const container = document.querySelector(`#${containerId}`);
          if (container) {
            container.innerHTML = '';
          }
          delete this.videoPlayers[index];
        } catch (cleanupError) {
          console.error(`强制清理播放器 ${index} 失败:`, cleanupError);
        }
      }
    },

    // 强制清理播放器DOM
    forceCleanupPlayer(containerId, iframe) {
      try {
        // 清理iframe
        if (iframe && iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
          console.log(`强制移除iframe: ${iframe.id}`);
        }
        
        // 清理容器
        const container = document.querySelector(`#${containerId}`);
        if (container) {
          container.innerHTML = '';
          console.log(`清理容器: ${containerId}`);
        }
        
        // 清理可能的其他相关DOM元素
        const allPlayerElements = document.querySelectorAll(`[id*="${containerId}"]`);
        allPlayerElements.forEach(element => {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        });
        
      } catch (error) {
        console.error(`强制清理播放器DOM失败:`, error);
      }
    },
    
    // 调整所有播放器尺寸
    resizeAllPlayers() {
      Object.keys(this.videoPlayers).forEach(index => {
        this.resizePlayer(index);
      });
    },
    
    // 调整单个播放器尺寸
    resizePlayer(index) {
      try {
        const player = this.videoPlayers[index];
        if (!player) return;
        
        const containerId = `video-player-${index}`;
        const container = document.querySelector(`#${containerId}`);
        if (!container) return;
        
        // 强制触发重排，确保获取到正确的尺寸
        container.offsetHeight;
        
        // 等待一帧后获取尺寸
        requestAnimationFrame(() => {
          const containerRect = container.getBoundingClientRect();
          const wrapperRect = container.parentElement.getBoundingClientRect();
          
          // 现在video-info使用绝对定位，不占用布局空间
          // 播放器可以占满整个wrapper，只需要减去video-item的padding
          const availableWidth = Math.floor(wrapperRect.width - 8); // wrapper减去video-item的padding 4px*2
          const availableHeight = Math.floor(wrapperRect.height - 32); // wrapper减去video-item的padding 4px*2
          
          // 使用可用空间作为播放器尺寸
          const width = availableWidth || Math.floor(containerRect.width) || 250;
          const height = availableHeight || Math.floor(containerRect.height) || 200;
          
          console.log(`调整播放器尺寸 ${index}:`, {
            容器尺寸: `${Math.floor(containerRect.width)}x${Math.floor(containerRect.height)}`,
            wrapper尺寸: `${Math.floor(wrapperRect.width)}x${Math.floor(wrapperRect.height)}`,
            可用空间: `${availableWidth}x${availableHeight}`,
            最终尺寸: `${width}x${height}`
          });
          
          // EZUIKit播放器尺寸调整
          if (typeof player.reSize === 'function') {
            player.reSize(width, height);
            console.log(`播放器尺寸已调整 ${index}: ${width}x${height}`);
          } else if (typeof player.resize === 'function') {
            // 尝试其他可能的resize方法名
            player.resize(width, height);
            console.log(`播放器尺寸已调整(resize) ${index}: ${width}x${height}`);
          } else {
            console.warn(`播放器 ${index} 没有可用的尺寸调整方法`);
          }
          
          // 强制设置播放器DOM尺寸，确保完全贴合
          const playerElement = container.querySelector('.ezuikit-container');
          if (playerElement) {
            playerElement.style.width = width + 'px';
            playerElement.style.height = height + 'px';
            
            // 同时设置内部video元素的尺寸
            const videoElement = playerElement.querySelector('video');
            if (videoElement) {
              videoElement.style.width = width + 'px';
              videoElement.style.height = height + 'px';
              videoElement.style.objectFit = 'fill'; // 强制填充整个容器
            }
          }
        });
        
      } catch (error) {
        console.error(`调整播放器尺寸失败 ${index}:`, error);
      }
    },
    
    // 处理布局变化
    handleLayoutChange() {
      console.log('布局变化，开始调整播放器');
      
      // 等待CSS动画完成
      setTimeout(() => {
        this.resizeAllPlayersWithRetry(0);
      }, 200);
    },
    
    // 带重试的播放器尺寸调整
    resizeAllPlayersWithRetry(retryCount) {
      const maxRetries = 3;
      
      if (retryCount >= maxRetries) {
        console.log('播放器尺寸调整完成');
        return;
      }
      
      console.log(`第 ${retryCount + 1} 次播放器尺寸调整`);
      this.resizeAllPlayers();
      
      // 递归重试
      setTimeout(() => {
        this.resizeAllPlayersWithRetry(retryCount + 1);
      }, 500 * (retryCount + 1));
    },
    
    // 处理窗口大小变化
    handleWindowResize() {
      this.resizeAllPlayers();
    },

    // 强制清理所有播放器（当正常清理失败时使用）
    forceCleanupAllPlayers() {
      console.log('开始强制清理所有播放器...');
      
      try {
        // 清理所有EZUIKit相关的iframe
        const allEZUIKitIframes = document.querySelectorAll('[id^="EZUIKitPlayer-"]');
        allEZUIKitIframes.forEach(iframe => {
          try {
            if (iframe.parentNode) {
              iframe.parentNode.removeChild(iframe);
              console.log(`强制移除iframe: ${iframe.id}`);
            }
          } catch (error) {
            console.warn(`移除iframe失败: ${iframe.id}`, error);
          }
        });
        
        // 清理所有播放器容器
        Object.keys(this.videoPlayers).forEach(index => {
          try {
            const containerId = `video-player-${index}`;
            const container = document.querySelector(`#${containerId}`);
            if (container) {
              container.innerHTML = '';
              console.log(`强制清理容器: ${containerId}`);
            }
          } catch (error) {
            console.warn(`清理容器失败: video-player-${index}`, error);
          }
        });
        
        // 清空播放器对象
        this.videoPlayers = {};
        
        console.log('强制清理所有播放器完成');
      } catch (error) {
        console.error('强制清理播放器过程中发生错误:', error);
      }
    },

    // 清理所有定时器
    clearAllTimers() {
      try {
        // 清理可能存在的定时器（如果有的话）
        if (this.resizeTimer) {
          clearTimeout(this.resizeTimer);
          this.resizeTimer = null;
        }
        
        if (this.playbackRetryTimer) {
          clearTimeout(this.playbackRetryTimer);
          this.playbackRetryTimer = null;
        }
        
        console.log('定时器清理完成');
      } catch (error) {
        console.error('清理定时器失败:', error);
      }
    }
  }
}
</script>

<style lang="less" scoped>
.dynamic-video-grid {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.video-container {
  width: 100%;
  height: 100%;
  display: grid;
  gap: 10px;
  
  /* 单视图模式：1个播放器占满整个区域 */
  &.single-view {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }
  
  /* 网格视图模式：使用内联样式动态设置 */
  &.grid-view {
    /* 默认网格布局，会被内联样式覆盖 */
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }
}

.video-item {
  width: 100%;
  height: 100%;
  background: url(../../../assets/images/icon/border_back.png) no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 4px;
  box-sizing: border-box;
  
  /* 单视图模式样式 */
  &.single-view-item {
    .empty-placeholder {
      .placeholder-content {
        img {
          width: 60px;
          height: 60px;
        }
        
        p {
          font-size: 16px;
          margin-top: 15px;
        }
      }
    }
  }
  
  /* 网格视图模式样式 */
  &.grid-view-item {
    .empty-placeholder {
      .placeholder-content {
        img {
          width: 40px;
          height: 40px;
        }
        
        p {
          font-size: 14px;
          margin-top: 10px;
        }
      }
    }
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative; /* 改为相对定位，为video-info提供定位基准 */
  border-radius: 4px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
}

.video-player {
  width: 100%; /* 占满整个wrapper */
  height: 100%; /* 占满整个wrapper */
  background: #000;
  position: relative;
  
  // EZUIKit播放器样式适配 - 确保完全贴合
  /deep/ .ezuikit-container {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
    
    .ezuikit-video-container {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      box-sizing: border-box !important;
      position: relative !important;
      
      video {
        width: 100% !important;
        height: 100% !important;
        object-fit: fill !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        display: block !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
      }
    }
    
    .loading-container {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      
      .loading-item {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }
    
    // 隐藏可能存在的控制栏边距
    .ezuikit-controls {
      margin: 0 !important;
      padding: 0 !important;
    }
    
    // 重置所有可能的内部元素
    * {
      margin: 0 !important;
      padding: 0 !important;
      box-sizing: border-box !important;
    }
    
    // 确保播放器本身没有边框和间距
    .ezuikit-player {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      box-sizing: border-box !important;
    }
  }
  
  // 兼容原有的video标签样式
  /deep/ video {
    width: 100% !important;
    height: 100% !important;
    object-fit: fill !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    display: block !important;
  }
}

.empty-placeholder {
  position: absolute; /* 改为绝对定位 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  
  .placeholder-content {
    text-align: center;
    
    img {
      width: 40px;
      height: 40px;
      opacity: 0.6;
      margin-bottom: 10px;
    }
    
    p {
      font-size: 14px;
      margin: 0;
    }
  }
}

.video-info {
  position: absolute; /* 改为绝对定位 */
  bottom: 0; /* 固定在底部 */
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  color: #fff;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10; /* 确保在视频上方显示 */
  
  .camera-name {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0; // 确保flex子元素可以收缩
    
    img {
      width: 18px;
      height: 16px;
      margin-right: 8px;
      flex-shrink: 0;
    }
    
    .name-info {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-width: 0;
      
      .device-name {
        font-size: 13px;
        font-weight: 500;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 1.2;
      }
      
      .area-path {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.7);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-top: 2px;
        line-height: 1.2;
      }
    }
  }
  
  .camera-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex-shrink: 0;
    
    .online {
      color: #52c41a;
      font-size: 12px;
      font-weight: 500;
    }
    
    .offline {
      color: #f5222d;
      font-size: 12px;
      font-weight: 500;
    }
    
    .no-stream {
      color: #faad14;
      font-size: 10px;
      margin-top: 2px;
    }
  }
}

.empty-item {
  opacity: 0.6;
}

.active-item {
  opacity: 1;
}

.offline-message {
  position: absolute; /* 改为绝对定位 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  
  .offline-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 0;
  }
  
  .offline-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1;
    
    .offline-content {
      text-align: center;
      background: rgba(0, 0, 0, 0.8);
      padding: 20px;
      border-radius: 8px;
      border: 2px solid #ff4757;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
      
      .offline-icon {
        font-size: 32px;
        margin-bottom: 12px;
        color: #ff4757;
      }
      
      p {
        font-size: 14px;
        margin: 0;
        color: #ff4757;
        font-weight: 500;
        line-height: 1.4;
        max-width: 200px;
      }
    }
  }
}
</style> 
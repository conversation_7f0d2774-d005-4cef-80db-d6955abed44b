<template>
    <div class="project-map-page">
        <div class="main-content">
            <!-- 左侧信息面板 -->
            <div class="side-panel" style="pointer-events: auto; padding-right: 0 !important;">
                <Title title="设备统计"></Title>
                <div class="first-title">
                    <div class="process">
                        <img src="../../assets/images/icon/processIcom.png" alt="">
                    </div>
                    <div class="content">
                        <div class="title">
                            <span>在场施工机械</span>
                        </div>
                        <div class="processNum">
                            <div class="left"></div>
                            <div class="right"></div>
                            <div class="back" v-for="(item, index) in String(JXequipmentList?.equipmentList?.length || 0).split('')" :key="index">
                                {{ item }}
                            </div>
                            <div class="back backDisponse">
                                台
                            </div>
                        </div>
                    </div>
                </div>
                <div class="s_small">
                    设备统计
                </div>
                <div class="Card">
                    <div class="Card-item">
                        <div class="totalNum">
                            <div class="text">设备总数：</div>
                            <div class="num">
                                {{ totalEquipmentInfo.sum }}
                            </div>
                        </div>
                        <div class="totalDetail">
                            <div class="online">
                                <span>运行设备：</span>
                                <span>{{ totalEquipmentInfo.onlineSum }}</span>
                            </div>
                            <div class="online offline">
                                <span>离线设备：</span>
                                <span>{{ totalEquipmentInfo.offlineSum }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="Card-process">
                        <div class="staute">
                            <div class="state-item">
                                <div class="state">
                                    <span>在线</span>
                                </div>
                                <span>运行设备</span>
                            </div>
                            <div class="state-item">
                                <div class="state offline">
                                    <span>离线</span>
                                </div>
                                <span>离线设备</span>
                            </div>
                        </div>
                        <div class="process">
                            <Progress :percent="totalEquipmentInfo.percentSum" status="active" :stroke-width="5" />
                        </div>
                    </div>
                </div>
                <div class="Card-content">
                    <div :class="'card-item ' + (item.active ? 'active' : '')" v-for="(item, index) in data" :key="index" @click="setActive(item)">
                        <div class="cardleft">
                            <img :src="item.img" alt="">
                        </div>
                        <div class="cardright">
                            <div class="title">{{ item.name }}</div>
                            <div class="content">
                                <div class="total">
                                    <span>运行：</span>
                                    <detail class="detail">{{ item.onlineNum }}</detail>
                                </div>
                                <div class="total">
                                    <span>离线：</span>
                                    <detail class="detail">{{ item.offlineNum }}</detail>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Title from '@/components/commonView/title2.vue';
import { 
    getEquipmentTypeList, 
    listPage, 
    deviceStatistics, 
    deviceGetStatistics,
    getGjPage
} from '@/api/xxindex';

// 图片
const detection = require('@/assets/images/panoramic/detection.png'); // 高支模监测
const video = require('@/assets/images/panoramic/video.png'); // 视频监控
const monitoring = require('@/assets/images/panoramic/monitoring.png'); // 塔吊监测
const crane = require('@/assets/images/panoramic/crane.png'); // 吊车监测
const Deep = require('@/assets/images/panoramic/Deep.png'); // 深基坑监测
const bridge = require('@/assets/images/panoramic/bridge.png'); // 转体桥监测
const money = require('@/assets/images/panoramic/money.png'); // 营业线监测
const rain = require('@/assets/images/panoramic/rain.png'); // 气象站
const AI = require('@/assets/images/panoramic/AI.png'); // AI行为识别
const broadcast = require('@/assets/images/panoramic/broadcast.png'); // 电台广播
const driver = require('@/assets/images/panoramic/driver.png'); // 司机监测 8
const fie = require('@/assets/images/panoramic/fie.png'); // 智能安全 18
const fire = require('@/assets/images/panoramic/fire.png'); // 电气火宅 14
const hun = require('@/assets/images/panoramic/hun.png'); // 混泥土 4
const icon = require('@/assets/images/panoramic/icon.png'); // 通用图标 default
const phone = require('@/assets/images/panoramic/phone.png'); // 手持终 6
const position = require('@/assets/images/panoramic/position.png'); // 北斗定位 7
const setting = require('@/assets/images/panoramic/setting.png'); // 机械 -1
const wifi = require('@/assets/images/panoramic/wifi.png'); // 无线 13
const wind = require('@/assets/images/panoramic/wind.png'); // 温湿度 12
const db = require('@/assets/images/panoramic/db.png') // 电表 20
const dlq = require('@/assets/images/panoramic/dlq.png') // 断路器 21

export default {
    name: 'Panoramic',
    components: {
        Title,
    },
    data() {
        return {
            data: [
                {
                    name: '高支模检测',
                    onlineNum: 10,
                    offlineNum: 2,
                },
                {
                    name: '视频监控',
                    onlineNum: 10,
                    offlineNum: 2,
                },
                {
                    name: '塔吊监测',
                    onlineNum: 10,
                    offlineNum: 2,
                }
            ],
            // 机械设备
            JXequipmentList: [],
            totalEquipmentInfo: {},
            gjPageTimer: null,
        }
    },
    mounted() {
        this.initRequest();
        this.$bus.emit('changeMap', 'high')
        this.getGjPage()
        this.gjPageTimer = setInterval(() => {
            this.getGjPage()
        }, 60000)
    },
    computed: {
        // 百分比
        percent() {
            // 避免除零错误，当总数为 0 时返回 0
            if (!this.totalEquipmentInfo.sum) {
                return 0;
            }
            // 计算运行设备的百分比
            return ((this.totalEquipmentInfo.sum - this.totalEquipmentInfo.offlineSum) / this.totalEquipmentInfo.sum) * 100;
        }
    },
    methods: {
        initRequest() {
            getEquipmentTypeList().then(res => {
                if (res.code == 'success') {
                    let data = {
                        customQueryParams: {},
                        page: {
                            current: 1,
                            size: 9999
                        }
                    }
                    listPage(data).then(r => {
                        if (r.code == 'success') {
                            res.data = res.data.filter(i => i.count > 0)
                            this.data = res.data.map(i => {
                                let items = r.data.records.filter(j => j.equipmentTypeName == i.name).reduce((prev, cur) => {
                                    if (cur.onlineState == 1) {
                                        prev.onlineNum++
                                    } else {
                                        prev.offlineNum++
                                    }
                                    return {
                                        ...prev,
                                        name: cur.equipmentTypeName
                                    }
                                }, {
                                    name: '',
                                    onlineNum: 0,
                                    offlineNum: 0,
                                })
                                return {
                                    ...items,
                                    id: i.id,
                                    active: false
                                }
                            });
                            this.initCard();
                        }
                    })
                }

            })
            deviceStatistics().then(res => {
                if (res.code == 'success') {
                    this.JXequipmentList = res.data.filter(i => i.typeName == '机械')[0];
                }
            })
            deviceGetStatistics().then(res => {
                if (res.code == 'success') {
                    this.totalEquipmentInfo.sum = res.data.totalCount;
                    this.totalEquipmentInfo.onlineSum = res.data.onlineCount;
                    this.totalEquipmentInfo.offlineSum = res.data.offlineCount;
                    this.totalEquipmentInfo.percentSum = res.data.onlineCount / res.data.totalCount * 100;
                }
            })
        },
        initCard() {
            console.log(this.data, 'this.data');
            this.data.forEach((item, index) => {
                switch (item.id) {
                    case '1': // 视频监控
                        item.img = video;
                        break;
                    case '2': // 气象站
                        item.img = rain;
                        break;
                    case '3': // 高支模检测
                        item.img = detection;
                        break;
                    case '4': // 混凝土温控
                        item.img = hun;
                        break;
                    case '5': // 转体桥检测
                        item.img = bridge;
                        break;
                    case '6': // 手持终端
                        item.img = phone;
                        break;
                    case '7': // 北斗定位
                        item.img = position;
                        break;
                    case '8': // 司机监测
                        item.img = driver;
                        break;
                    case '9': // AI行为识别
                        item.img = AI;
                        break;
                    case '10': // 吊车监测
                        item.img = crane;
                        break;
                    case '11': // 塔吊监测
                        item.img = monitoring;
                        break;
                    case '12': // 温湿度传感器
                        item.img = wind;
                        break;
                    case '13': // 无线烟感传感器
                        item.img = wifi;
                        break;
                    case '14': // 电气火灾
                        item.img = fie;
                        break;
                    case '15': // 深基坑监测
                        item.img = Deep;
                        break;
                    case '16': // 营业线监测
                        item.img = money;
                        break;
                    case '17': // 电台广播
                        item.img = broadcast;
                        break;
                    case '18': // 智能安全
                        item.img = fire;
                        break;
                    case '19': // 电子围栏
                        item.img = dlq;
                        break;
                    case '20': // 电表
                        item.img = db;
                        break;
                    case '21': // 断路器
                        item.img = dlq
                        break;
                    case '-1': // 机械
                        item.img = setting;
                        break;
                    default: 
                        item.img = icon;
                        break
                }
            });
        },
        setActive(item) {
            // 更新当前项的激活状态
            item.active = !item.active;

            // 获取所有激活的类型
            const activeTypes = this.data.filter(i => i.active).map(i => i.name);

            if (activeTypes.length > 0) {
                // 构建查询参数
                const queryParams = {
                    customQueryParams: {
                        deviceNameOrCode: '',
                        typeNames: activeTypes // 使用数组传递所有激活的类型
                    },
                    page: {
                        current: 1,
                        size: 999 // 增大size以获取更多数据
                    }
                };

                // 请求数据
                listPage(queryParams).then(res => {
                    console.log('设备列表查询结果:', res);
                    if (res.code === 'success') {
                        // 为返回的数据添加active标记
                        const records = res.data.records.map(record => ({
                            ...record,
                            active: true // 设置为激活状态
                        }));
                        console.log('🚀 发送 equipmentInfoList 事件，数据:', records);
                        setTimeout(() => {
                            this.$bus.emit('equipmentInfoList', records);
                        }, 200);
                    } else {
                        this.$message.error('获取设备列表失败');
                    }
                }).catch(error => {
                    console.error('请求设备列表出错:', error);
                    this.$message.error('获取设备列表失败');
                });
            } else {
                // 如果没有激活的类型，发送空数组
                console.log('🚀 发送 equipmentInfoList 事件，空数组');
                this.$bus.emit('equipmentInfoList', []);
            }
        },
        getGjPage() { // 全景感知获取所有报警信息
            // 获取当前时间
            const now = new Date();
            
            // 计算20分钟前的时间
            const twentyMinutesAgo = new Date(now.getTime() - 20 * 60 * 1000);
            
            // 格式化时间为 YYYY-MM-DD HH:mm:ss 格式
            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            };
            
            let data = {
                page: {
                    size: -1,
                    current: 1
                },
                customQueryParams: {
                    uniKey: "", //关键词
                    alarmType: "", //告警类型，查询告警类型列表，输入id
                    // status: "0",// 0未处理，1已处理，2误报
                    startDate: formatDate(twentyMinutesAgo),//开始时间（20分钟前）
                    endDate: formatDate(now)//结束时间（当前时间）
                }
            }
            getGjPage(data).then(res => {
                if (res.code == 'success' && res.data.records.length > 0) {
                    console.log(res.data, 'res.data');
                    this.$alert.close()
                    res.data.records.forEach(item => {
                        this.$alert.error({
                            title: item.alarmTypeText,
                            message: item.bigScreenAlarmContent,
                            time: item.createTime,
                            duration: 1 * 60 * 1000,
                            rightMargin: 10,
                        })
                    })
                }
            })
        }
    },
    beforeDestroy() {
        console.log('Panoramic销毁',)
        this.$bus.off('equipmentInfoList')
        this.$alert.close()
        clearInterval(this.gjPageTimer)
        this.gjPageTimer = null
    }
};
</script>

<style lang="less" scoped>
/deep/ .side-panel {
    background: linear-gradient(to right, rgba(20, 40, 70, 0.95) 60%, rgba(20, 40, 70, 0.3) 100%);
    height: 100%;
}
/deep/ .ivu-progress-text-inner {
    display: none;
}
/deep/ .ivu-progress-show-info .ivu-progress-outer{
    margin-right: 0;
    padding-right: 0;
}

/deep/ .ivu-progress-inner {
    background-color: transparent;
    background: url(../../assets/images/icon/process.png) center center;
    background-size: cover;
}

/deep/ .ivu-progress-bg {
    background-color: transparent;
    background-image: linear-gradient(to right, rgba(0, 216, 240, .5) 60%, #00E5FF 100%);
    border-radius: 0;
    border: 1px solid #00E5FF;
}

.s_small {
    width: 100%;
    height: 26px;
    background: url(../../assets/images/icon/s_title.png) no-repeat center center;
    background-size: 100% 100%;
    padding-left: 30px;
    display: flex;
    align-items: center;
}

.detail {
    position: absolute;
    right: 0;
}

.Card-content {
    width: 100%;
    max-height: calc(100vh - 10vh - 20vh - 30px);
    display: flex;
    flex-wrap: wrap;
    row-gap: 10px;
    overflow-y: auto;
    .card-item.active {
        background: url(../../assets/images/panoramic/back.png) no-repeat;
        background-size: 100% 100%;
    }
    .card-item {
        width: 50%;
        min-width: 190px;
        height: 88px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 5px;
        cursor: pointer;
        padding: 4px 8px;
        margin-bottom: 32px;
        .cardleft {
            width: 35%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
        .cardright {
            width: 60%;
            height: 100%;
            display: flex;
            flex-direction: column;
            .title {
                width: 100%;
                padding: 4px 8px;
                height: 29px;
                span {
                    font-size: 15px;
                    font-family: 'PingFang SC';
                    color: #fff;
                }
            }
            .content {
                display: flex;
                flex-direction: column;
                width: 100%;
                padding: 0 8px;
                .total {
                    height: 25px;
                    display: flex;
                    align-items: center;
                    span {
                        font-family: 'PingFang SC';
                        font-size: 14px;
                        color: #fff;
                    }
                    .detail {
                        font-size: 20px;
                        color: #fff;
                    }
                    &:first-child {
                        color: #fff;
                        background-image: linear-gradient(to right, transparent, rgba(0, 255, 255, .4), transparent);
                        position: relative;
                    }
                    &:last-child {
                        color: #fff;
                        position: relative;
                    }
                    &:first-child::before {
                        content: '';
                        background: url(../../assets/images/icon/onlinestauts.png) no-repeat center center;
                        background-size: contain;
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        margin-right: 5px;
                        vertical-align: middle;
                    }
                    &:last-child::before {
                        content: '';
                        background: url(../../assets/images/icon/onfficestauts.png) no-repeat center center;
                        background-size: contain;
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        margin-right: 5px;
                        vertical-align: middle;
                    }
                }
                // span {
                //     font-family: 'PingFang SC';
                //     font-size: 14px;
                //     height: 25px;
                //     display: flex;
                //     align-items: center;
                //     .detail {
                //         font-size: 20px;
                //         color: #fff;
                //     }
                // }
                // span:first-child {
                //     color: #fff;
                //     background-image: linear-gradient(to right, transparent, rgba(0, 255, 255, .4), transparent);
                //     position: relative;
                // }
                // span:last-child {
                //     color: #fff;
                //     position: relative;
                // }
                // span:first-child::before {
                //     content: '';
                //     background: url(../../assets/images/icon/onlinestauts.png) no-repeat center center;
                //     background-size: contain;
                //     display: inline-block;
                //     width: 20px;
                //     height: 20px;
                //     margin-right: 5px;
                //     vertical-align: middle;
                // }
                // span:last-child::before {
                //     content: '';
                //     background: url(../../assets/images/icon/onfficestauts.png) no-repeat center center;
                //     background-size: contain;
                //     display: inline-block;
                //     width: 20px;
                //     height: 20px;
                //     margin-right: 5px;
                //     vertical-align: middle;
                // }
            }
        }
    }
}

.Card {
    width: 100%;
    max-height: 20vh;
    height: 178px;
    background: url(../../assets/images/icon/warningCard.png) no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 16px;
    .Card-item {
        width: 100%;
        height: 60%;
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .totalNum {
            width: 46%;
            height: 60px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            padding: 14px 0;
            .text {
                color: #fff;
                margin-right: 16px;
            }
            .num {
                font-size: 32px;
                font-family: 'YouSheBiaoTiHei';
                font-weight: bold;
                color: #fff;
                font-style: italic;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                margin-bottom: 20px;
                &::after {
                    content: '';
                    width: 46px;
                    height: 50px;
                    display: block;
                    background: url(../../assets/images/icon/cardIcon.png)  no-repeat center center;
                    background-size: contain;
                    background-position: 50% 50%;
                    position: absolute;
                    bottom: -15px;
                    left: 0;
                }
            }
        }
        .totalDetail {
            width: 49%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            .online {
                width: 100%;
                height: 45%;
                min-height: 33px;
                background-image: linear-gradient(to right, #265b83 10%, transparent);
                background-size: 100% 100%;
                border-radius: 5px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                box-sizing: border-box;
                padding: 0px 10px;
                span:first-child {
                    color: #fff;
                }
                span:last-child {
                    color: #fff;
                    font-size: 25px;
                }
            }
            .offline {
                background-image: linear-gradient(to right, #2d3751 10%, transparent);
                background-size: 100% 100%;
            }
        }
    }

    .Card-process {
        width: 100%;
        height: 35%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        box-sizing: border-box;
        padding: 8px 0;
        .staute {
            width: 100%;
            height: 45%;
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;

            .state-item {
                display: flex;
                align-items: center; /* 取消此行注释 */
                justify-content: space-between;
                height: 20px;

                .state {
                    background: url(../../assets/images/map/Crane-zx.png)no-repeat;
                    background-size: 100% 100%;
                    width: 40px;
                    height: 100%;
                    margin-right: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    span {
                        font-family: 'HuXiaoBo_KuHei';
                        font-size: 16px;
                        color: #fff;
                    }
                }

                .offline {
                    background: url(../../assets/images/map/Crane-lx.png)no-repeat;
                    background-size: 100% 100%;
                }

                img {
                    width: 40px;
                    height: 100%;
                    margin-right: 8px;
                    display: block;
                }
                span {
                    font-size: 14px;
                    font-family: 'PingFang SC';
                    height: 100%;
                }
            }
        }
        .process {
            width: 100%;
            height: 45%;
            display: flex;
            align-items: center;
        }
    }
}

.first-title{
    width: 100%;
    max-height: 10vh;
    margin-top: -20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .process {
        width: 100px;
        height: 90px;
        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
    .content{
        display: flex;
        flex-direction: column;
        width: 70%;
        height: 100%;
        .title {
            margin: 0px auto;
            width: 129px;
            padding: 4px;
            height: 29px;
            background-image: linear-gradient(to right, transparent, rgba(13, 125, 230, .2), transparent);
            background-size: 100% 100% ;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-bottom: 4px;
            span {
                font-size: 15px;
                font-family: PingFang SC;
                color: #FFFFFF;
            }
            span::after{
                content: '';
                position: absolute;
                display: block;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                background: url(../../assets//images/icon/work.png) no-repeat center center;
                background-size: 100% 100%;
                margin-right: 4px;
            }
            span::before{
                content: '';
                display: block;
                position: absolute;
                left: 50%;
                bottom: -16px;
                transform: translateX(-50%);
                width: 20px;
                height: 20px;
                background: url(../../assets/images/icon/go_bottom.png) no-repeat center center;
                background-size: contain;
                z-index: 99;
            }
        }
        .processNum{
            width: 100%;
            // height: calc(100% - 35px);
            background: url(../../assets/images/icon/borderBack.png) no-repeat center center;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            position: relative;
            padding: 3.33px 0;
            .left {
                width: 30px;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                background: url(../../assets/images/icon/borderLeft.png) no-repeat center center;
                background-size: 100% 100%;
            };
            .right {
                width: 30px;
                height: 100%;
                position: absolute;
                right: 0;
                top: 0;
                background: url(../../assets/images/icon/borderRight.png) no-repeat center center;
                background-size: 100% 100%;
            }
            .back {
                width: 30px;
                height: 40px;
                background: url(../../assets/images/icon/textBack.png) no-repeat center center;
                background-size: 100% 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28px;
                font-family: 'DINPro';
                font-weight: 700;
                padding: 4px 8px;
            }
            .backDisponse {
                background: url(../../assets/images/icon/disponse.png) no-repeat center center !important;
                background-size: 100% 100%  !important;
                color: rgba(255, 255, 255, .4);
                font-size: 20px;
                font-family: 'PingFang SC';

            }
        }
    }
}

.project-map-page {
    width: 100%;
    height: calc(100% - 75px);
    margin-top: 75px;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;

    .top-bar {
        width: 100vw;
        height: 100px;
        background: url('../../assets/images/ProjectMap/top-bg2.png') no-repeat center center;
        color: #fff;
        display: flex;
        padding: 0 40px;
        justify-content: space-between;
        /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

        .tabs {
            width: 100%;
            background: url('../../assets/images/ProjectMap/top-bg.png') no-repeat center center;
            background-size: cover;
            height: 87.14px;


            .tab-nav {
                display: flex;
                align-items: flex-end;
                justify-content: flex-end;
                width: 33%;
                height: 82.73px;
                gap: 10px;

                .el-button--primary.is-plain {
                    width: 136px;
                    height: 30px;
                    font-size: 16px;
                    color: #fff;
                    background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat center center;
                    background-size: cover;
                    border: none;
                    border-radius: 0;
                    /* 为按钮添加黑色阴影 */
                    text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
                }

                .active {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .el-button--primary.is-plain {
                        background: url(../../assets/images/ProjectMap/nav-bg-active.png) no-repeat center center;
                        background-size: cover;
                        font-weight: bold;
                    }

                    &::before {
                        content: '';
                        background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
                        background-size: cover;
                        width: 6.26px;
                        height: 6.26px;
                        position: absolute;
                        left: 20px;
                    }

                    &::after {
                        content: '';
                        background: url(../../assets/images/ProjectMap/active-jt.png) no-repeat center center;
                        background-size: cover;
                        width: 6.26px;
                        height: 6.26px;
                        transform: rotate(90deg);
                        position: absolute;
                        right: 20px;
                    }
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        height: calc(100% - 100px);
        width: 408px;
    }
}

</style>

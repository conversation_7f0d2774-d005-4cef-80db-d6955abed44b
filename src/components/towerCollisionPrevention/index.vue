<template>
  <div class="project-map-page">
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="side-panel" style="pointer-events: auto">
        <div class="s_title">今日人员机械统计</div>
        <div class="title-content">
          <div class="item">
            <div class="item-left">
              <img src="../../assets/images/ProjectAnti/top.png" />
            </div>
            <div class="item-right">
              <div class="itemRight-item">
                <div class="num">{{ todayTotal.totalCount }}</div>
                <div class="text">监测塔吊数量</div>
              </div>
              <div class="itemRight-item">
                <div class="num">{{ todayTotal.warningCount }}</div>
                <div class="text">告警塔吊数量</div>
              </div>
            </div>
          </div>
          <div class="item">
            <div class="item-left">
              <img src="../../assets/images/ProjectAnti/bottom.png" />
            </div>
            <div class="item-right">
              <div class="itemRight-item">
                <div class="num">{{ todayTotal.personCount }}</div>
                <div class="text">人员定位总数量</div>
              </div>
              <div class="itemRight-item">
                <div class="num">{{ todayTotal.personAlarmCount }}</div>
                <div class="text">侵限预警人员数量</div>
              </div>
            </div>
          </div>
        </div>
        <div class="s_title" style="margin-top: 0px">告警统计</div>
        <div class="card">
          <div class="warnNum">
            <div class="warn">今日告警总次数：</div>
            <div class="num youshebiaotihei">{{ systemAlarmInfo.nowSum }}</div>
            <div class="better">
              比较昨日：<img
                :src="growthSumClass"
                style="transform: rotateX(180deg)"
              />{{ isNaN(Math.abs(growthSum)) ? 0 : Math.abs(growthSum) }}
            </div>
          </div>
          <div class="total">
            <div class="totalItem">
              <span>近一月总次数：</span>
              <span>{{ systemAlarmInfo.intervalThirtySum }}</span>
            </div>
            <div class="totalItem">
              <span>累计总次数：</span>
              <span>{{ systemAlarmInfo.sum }}</span>
            </div>
          </div>
        </div>
        <div class="s_title">今日预警分类占比</div>
        <div class="totalNums">
          <div class="echarts2">
            <div class="line"></div>
            <div id="echarts10" style="width: 100%; height: 100%"></div>
          </div>
          <div class="totalList">
            <div
              class="content"
              v-for="(item, index) in tableData"
              :key="index"
            >
              <div class="title">
                <div class="leftPoint">
                  <div
                    class="point"
                    :style="{ backgroundColor: item.color }"
                  ></div>
                  <span>{{ item.name }}</span>
                </div>
              </div>
              <div class="percentage">{{ item.percentageNum }}%</div>
            </div>
          </div>
        </div>
        <div class="s_title">今日预警记录</div>
        <div class="timeLine" ref="timelineContainer">
          <div
            class="timeline-scroll-container"
            ref="scrollContainer"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
          >
            <div class="timeline-scroll-content" ref="scrollContent">
              <TimelineItem
                v-for="item in infiniteScrollList"
                :key="item._scrollKey"
              >
                <p class="time">{{ item.createTime }}</p>
                <div class="timeConetent">
                  <div class="title">
                    <span
                      ><img
                        src="../../assets/images/ProjectAnti/warnIcon.png"
                      />{{ item.testingItem }}</span
                    >
                    <span
                      class="warnType"
                      v-if="item.alarmTypeText == '防倾倒侵限告警'"
                      @click="searchTrajectory(item)"
                      >查看轨迹</span
                    >
                    <span
                      class="warnType"
                      v-if="item.alarmType == 36 || item.alarmType == 37"
                      @click="searchPersonTrajectory(item)"
                      >查看轨迹</span
                    >
                  </div>
                  <div class="content">
                    <div>{{ item.content }}</div>
                  </div>
                </div>
              </TimelineItem>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ModalW403H304
      :visibleW403h304.sync="isshow"
      title="告警记录"
      @cancel="$bus.emit('switchConstructionView', 'high')"
    >
      <div class="trajectoryContainer">
        <div class="title">
          <span>告警机械/人员</span>
          <span>{{ warnInfo.testingItem }}</span>
        </div>
        <div class="line"></div>
        <div class="title">
          <span>告警时间</span>
          <span>{{ warnInfo.createTime }}</span>
        </div>
        <div class="line"></div>
        <div class="content">
          <div class="title">
            <span>序号</span>
            <span>轨迹时间</span>
          </div>
          <div class="contentList">
            <div
              @click="handleClick(item)"
              :class="'item ' + (item.active ? 'active' : '')"
              v-for="(item, index) in trajectoryList"
              :key="index"
            >
              <div
                :class="'youshebiaotihei back ' + (item.active ? 'active' : '')"
              >
                {{ index + 1 }}
              </div>
              <div>{{ item.locationTime }}</div>
            </div>
          </div>
        </div>
      </div>
    </ModalW403H304>
  </div>
</template>

<script>
import Title from "@/components/commonView/title3.vue";
import * as echarts from "echarts";
import ModalW403H304 from "@/components/modal/w403h304.vue";
import {
  getCategorytoTotall,
  alarmTypeCount,
  systemAlarmPage,
  todayInfringementLimit,
  towerCraneTodayInfringementLimit,
  queryByCondition,
  alarmTypeTodayCount,
  alarmTypeCountWithTime,
  getTowerCraneAlarmStatistics,
  getAlarmStatistics,
} from "@/api/xxindex";
import { getRandomColor } from "./index";

const up = require("../../assets/images/ProjectAI/goon.png");
const down = require("../../assets/images/ProjectAI/gooff.png");
export default {
  name: "ProjectAnti",
  components: {
    ModalW403H304,
  },
  data() {
    return {
      myCharts: null,
      tableData: [],
      trajectoryList: [
        // {
        //     time: '2025-04-02 09:00:00 - 2025-04-02 17:00:00',
        //     active: false
        // },
      ],
      isshow: false,
      systemAlarmInfo: {},
      warnInfoList: [],
      todayTotal: {},
      // monthTotalCount: 0,
      // 无缝滚动相关
      scrollTimer: null,
      scrollSpeed: 1, // 每次滚动的像素，适中的速度
      scrollInterval: 60, // 滚动间隔(毫秒)，降低频率减少卡顿
      isUserScrolling: false,
      userScrollTimer: null,
      animationId: null, // 用于requestAnimationFrame
      scrollDebounceTimer: null, // 防抖定时器
      containerRef: null, // 缓存容器引用
      warnInfo: {}, // 告警信息
      timer: null,
    };
  },
  mounted() {
    this.initRequest();

    if (this.timer) clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.initRequest();
    }, 10000);
    // this.$timerManager.start('ProjectAnti', () => {
    //     this.initRequest();
    //     this.$bus.emit('switchConstructionView', 'high')
    // }, 10000)

    // this.$bus.on('showTrajectory', (data) => {
    //     this.$timerManager.start('ProjectAnti', () => {
    //         this.initRequest();
    //         this.$bus.emit('switchConstructionView', 'high')
    //     }, 10000);
    // })

    // 等待DOM渲染完成后设置滚动
    this.$nextTick(() => {
      // 设置滚动事件监听
      this.setupScrollListener();

      // 调试信息
      console.log(
        "告警数据量:",
        this.warnInfoList ? this.warnInfoList.length : 0
      );
      console.log("无缝滚动数据量:", this.infiniteScrollList.length);

      const timelineEl = this.$refs.timelineContainer;
      if (timelineEl) {
        console.log("时间线容器高度:", timelineEl.offsetHeight);
      }
    });
  },
  beforeDestroy() {
    // 清理滚动事件监听
    this.cleanupScrollListener();
    this.$bus.off("showTrajectory");
    if (this.timer) clearInterval(this.timer);
    // this.$timerManager.stop('ProjectAnti');
  },
  computed: {
    // 比较昨日
    growthSum() {
      return this.systemAlarmInfo.nowSum - this.systemAlarmInfo.oldSum;
    },
    // 上升或者下降
    growthSumClass() {
      if (this.systemAlarmInfo.oldSum <= this.systemAlarmInfo.nowSum) {
        return down;
      } else {
        return up;
      }
    },
    scrollableWarnList() {
      // 获取基础数据
      let baseList = this.warnInfoList;

      // 如果数据不够，创建一些默认数据
      // if (!baseList || baseList.length === 0) {
      //     baseList = [
      //         {
      //             createTime: '2025-01-15 14:30:00',
      //             testingItem: '汽车吊1536',
      //             alarmTypeText: '防倾倒侵限告警',
      //             content: '有防倾倒侵入安全限界风险，请注意'
      //         },
      //         {
      //             createTime: '2025-01-15 14:25:00',
      //             testingItem: '塔吊2048',
      //             alarmTypeText: '超载告警',
      //             content: '当前大臂长度为100.15 m，超安全值，请注意'
      //         },
      //         {
      //             createTime: '2025-01-15 14:20:00',
      //             testingItem: '履带吊3072',
      //             alarmTypeText: '防倾倒侵限告警',
      //             content: '检测到设备进入危险区域，请立即停止作业'
      //         },
      //         {
      //             createTime: '2025-01-15 14:15:00',
      //             testingItem: '汽车吊4096',
      //             alarmTypeText: '风速告警',
      //             content: '当前风速超过安全阈值，建议暂停高空作业'
      //         },
      //         {
      //             createTime: '2025-01-15 14:10:00',
      //             testingItem: '塔吊5120',
      //             alarmTypeText: '防倾倒侵限告警',
      //             content: '设备倾斜角度异常，存在倾倒风险'
      //         }
      //     ];
      // }

      // 不重复数据，只返回原始列表并添加唯一key
      return baseList.map((item, index) => ({
        ...item,
        _scrollKey: `scroll-${index}`,
      }));
    },
    infiniteScrollList() {
      // 获取基础数据
      let baseList = this.warnInfoList;

      // 如果数据不够，创建一些默认数据
      // if (!baseList || baseList.length === 0) {
      //   baseList = [
      //     {
      //       createTime: "2025-01-15 14:30:00",
      //       testingItem: "汽车吊1536",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "有防倾倒侵入安全限界风险，请注意",
      //     },
      //     {
      //       createTime: "2025-01-15 14:25:00",
      //       testingItem: "塔吊2048",
      //       alarmTypeText: "超载告警",
      //       content: "当前大臂长度为100.15 m，超安全值，请注意",
      //     },
      //     {
      //       createTime: "2025-01-15 14:20:00",
      //       testingItem: "履带吊3072",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "检测到设备进入危险区域，请立即停止作业",
      //     },
      //     {
      //       createTime: "2025-01-15 14:15:00",
      //       testingItem: "汽车吊4096",
      //       alarmTypeText: "风速告警",
      //       content: "当前风速超过安全阈值，建议暂停高空作业",
      //     },
      //     {
      //       createTime: "2025-01-15 14:10:00",
      //       testingItem: "塔吊5120",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "设备倾斜角度异常，存在倾倒风险",
      //     },
      //   ];
      // }

      // 不重复数据，只返回原始列表并添加唯一key
      return baseList.map((item, index) => ({
        ...item,
        _scrollKey: `item-${index}`,
      }));
    },
  },
  methods: {
    handleClick(item) {
      this.$bus.emit("flytoPoint", item);
    },
    initRequest() {
      // 获取当前日期和30天前的日期
      const now = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(now.getDate() - 30);

      // 格式化日期为 yyyy-MM-dd HH:mm:ss
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day} 00:00:00`;
      };

      // 格式化当前完整时间为 yyyy-MM-dd HH:mm:ss
      const formatFullTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      const endDate = formatFullTime(now);
      const startDate = formatDate(thirtyDaysAgo);

      let categroy = "7,9";
      getCategorytoTotall(categroy).then((res) => {
        // sum 总数  growthSum 比较昨日   nowSum已处理  unHandleSum未处理
        let {
          growthSum,
          handleSum,
          nowSum,
          oldSum,
          sum,
          unHandleSum,
          intervalThirtySum,
        } = res.data;
        this.systemAlarmInfo = {
          sum,
          growthSum,
          nowSum,
          unHandleSum,
          handleSum,
          oldSum,
          intervalThirtySum,
        };
      });

      // // 获取近一月总次数
      // alarmTypeCountWithTime({
      //   category: 1,
      //   startDate: startDate,
      //   endDate: endDate,
      // }).then((res) => {
      //   // 累加所有告警类型的count值
      //   this.monthTotalCount = res.data.reduce(
      //     (total, item) => total + item.count,
      //     0
      //   );
      // });

      getTowerCraneAlarmStatistics().then((res) => {
        let total = res.data.alarmTypeCounts.reduce(
          (total, item) => total + item.count,
          0
        );
        this.tableData = res.data.alarmTypeCounts.map((i) => {
          return {
            name: i.name,
            percentageNum: Math.round((i.count / total) * 100),
            color: getRandomColor(),
          };
        });
        this.initEcharts();
      });
      let data = {
        customQueryParams: {
          categoryList: ["7", "9"],
          // 获取当日开始时间和结束时间  2025-07-16 00:00:00
          startDate:
            new Date().toLocaleDateString().split("/").join("-") + " 00:00:00",
          endDate:
            new Date().toLocaleDateString().split("/").join("-") + " 23:59:59",
        },
        page: {
          current: 1,
          size: 10,
        },
        sorts: [],
      };
      systemAlarmPage(data).then((res) => {
        this.warnInfoList = res.data.records;
        console.log(this.warnInfoList, "42379032809");

        // 数据加载完成后启动滚动
        this.$nextTick(() => {
          setTimeout(() => {
            this.startInfiniteScroll();
          }, 500);
        });
      });
      towerCraneTodayInfringementLimit().then((res) => {
        if (res.code == "success") {
          console.log(res, "towerCraneTodayInfringementLimit.res");
          // 触发响应式更新，确保DOM渲染
          this.$set(this.todayTotal, "totalCount", res.data.totalCount);
          this.$set(this.todayTotal, "warningCount", res.data.warningCount);
          this.$set(this.todayTotal, "personCount", res.data.personCount);
          this.$set(
            this.todayTotal,
            "personAlarmCount",
            res.data.personAlarmCount
          );
        } else {
          // 若接口异常，确保数据为0并触发渲染
          this.$set(this.todayTotal, "totalCount", 0);
          this.$set(this.todayTotal, "warningCount", 0);
          this.$set(this.todayTotal, "personCount", 0);
          this.$set(this.todayTotal, "personAlarmCount", 0);
        }
      });
    },
    searchTrajectory(item) {
      this.isshow = true;
      this.warnInfo = item;

      // this.$timerManager.stop('ProjectAnti');

      let data = {
        // equipmentId: item.refId,
        // startTime: item.createTime,
        equipmentId: item.refId,
        startTime: item.createTime,
      };
      queryByCondition(data).then((res) => {
        if (res.code == "success") {
          this.trajectoryList = res.data;
          console.log(
            "🚀 发送 showTrajectory 事件，数据:",
            this.trajectoryList
          );
          this.$bus.emit("showTrajectory", this.trajectoryList);
        }
      });
    },
    // 人员告警
    searchPersonTrajectory(item) {
      this.isshow = true;
      this.warnInfo = item;
      let data = {
        personId: item.refId,
        startTime: item.createTime,
      };
      queryByConditionPerson(data).then((res) => {
        if (res.code == "success") {
          this.trajectoryList = res.data;
          console.log(
            "🚀 发送 人员 showTrajectory 事件，数据:",
            this.trajectoryList
          );
          this.$bus.emit("togglePersonTrajectory", true, this.trajectoryList);
        }
      });
    },
    initEcharts() {
      // 分配颜色
      this.tableData = this.tableData.map((i) => {
        return {
          ...i,
          value: i.percentageNum,
          itemStyle: {
            color: i.color,
          },
        };
      });
      // 销毁
      this.myCharts && this.myCharts.dispose();
      this.myCharts = echarts.init(document.getElementById("echarts10"));
      let option = {
        graphic: {
          id: "centerText", // 给图形元素添加ID便于更新
          type: "text",
          left: "center",
          top: "center",
          style: {
            text: `${this.tableData[0].value}%\n\n${this.tableData[0].name}`, // 默认显示第一条
            textAlign: "center",
            fill: "#fff",
            fontSize: 14,
            fontFamily: "youshebiaotihei",
          },
        },
        series: [
          {
            type: "pie",
            radius: ["90%", "100%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
              // formatter: '{d}%\n\n{b}',
            },
            // emphasis: {
            //     label: {
            //         show: true,
            //         fontSize: 14,
            //         fontWeight: 'bold',
            //         formatter: '{d}%\n\n{b}',
            //     }
            // },
            labelLine: {
              show: false,
            },
            data: this.tableData,
          },
        ],
      };
      function formatObj(obj) {
        return {
          graphic: {
            id: "centerText",
            type: "text",
            left: "center",
            top: "center",
            style: {
              textAlign: "center",
              text: `${obj.value}%\n\n${obj.name}`,
            },
          },
        };
      }
      this.myCharts.on("mouseover", (params) => {
        if (params.seriesType == "pie") {
          console.log(params);
          this.myCharts.setOption(formatObj(params));
        }
      });
      this.myCharts.on("mouseout", (params) => {
        if (params.seriesType == "pie") {
          this.myCharts.setOption(
            formatObj({
              name: this.tableData[0].name,
              value: this.tableData[0].value,
            })
          );
        }
      });
      this.myCharts.setOption(option);
    },
    // 开始无缝滚动
    startInfiniteScroll() {
      if (this.animationId) return;

      // 缓存容器引用，避免重复查询DOM
      this.containerRef = this.$refs.scrollContainer;
      if (!this.containerRef) return;

      let lastTime = 0;
      const animate = (currentTime) => {
        if (currentTime - lastTime >= this.scrollInterval) {
          if (!this.isUserScrolling && this.containerRef) {
            // 检查是否已经滚动到底部
            const scrollHeight = this.containerRef.scrollHeight;
            const scrollTop = this.containerRef.scrollTop;
            const containerHeight = this.containerRef.clientHeight;

            // 如果已经滚动到底部，暂停滚动
            if (scrollTop >= scrollHeight - containerHeight) {
              this.stopInfiniteScroll();
              return;
            }

            // 自动向下滚动
            this.containerRef.scrollTop += this.scrollSpeed;
          }
          lastTime = currentTime;
        }

        this.animationId = requestAnimationFrame(animate);
      };

      this.animationId = requestAnimationFrame(animate);
    },

    // 停止无缝滚动
    stopInfiniteScroll() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
      // 清理容器引用
      this.containerRef = null;
    },

    // 处理用户滚动 - 使用防抖优化
    handleUserScroll() {
      this.isUserScrolling = true;

      // 清除之前的定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
      }

      // 用户停止滚动1.5秒后恢复自动滚动（减少延迟）
      this.userScrollTimer = setTimeout(() => {
        this.isUserScrolling = false;
      }, 1500);
    },

    // 防抖处理用户滚动
    debounceUserScroll() {
      // 立即标记为用户滚动状态，避免冲突
      this.isUserScrolling = true;

      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
      }

      this.scrollDebounceTimer = setTimeout(() => {
        this.handleUserScroll();
      }, 50); // 减少防抖时间到50ms，提高响应性
    },

    // 设置滚动事件监听
    setupScrollListener() {
      const container = this.$refs.scrollContainer;
      if (container) {
        // 监听用户滚动 - 使用防抖
        container.addEventListener("scroll", this.debounceUserScroll, {
          passive: true,
        });
      }
    },

    // 清理滚动监听器
    cleanupScrollListener() {
      const container = this.$refs.scrollContainer;
      if (container) {
        container.removeEventListener("scroll", this.debounceUserScroll);
      }

      // 清理所有定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
      }
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
      }

      this.stopInfiniteScroll();
    },
    // 鼠标进入时间线区域 - 暂停自动滚动
    handleMouseEnter() {
      this.isUserScrolling = true;
      console.log("鼠标进入，暂停自动滚动");
    },

    // 鼠标离开时间线区域 - 恢复自动滚动
    handleMouseLeave() {
      // 清除用户滚动相关的定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
        this.userScrollTimer = null;
      }
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
        this.scrollDebounceTimer = null;
      }

      // 延迟恢复自动滚动，给用户一点缓冲时间
      setTimeout(() => {
        this.isUserScrolling = false;
        console.log("鼠标离开，恢复自动滚动");
      }, 300); // 300ms延迟
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .side-panel {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  );
  height: 100vh; /* 确保占满整个视窗高度 */
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少间距 */
  overflow: hidden; /* 防止整体滚动 */
  box-sizing: border-box;
  padding: 10px; /* 减少内边距 */
}

/deep/ .ivu-timeline-item-head {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/ProjectAnti/point.png) no-repeat center
    center;
  background-size: 100% auto;
  border: none;
  position: absolute;
  left: 0px;
  top: -2px;
}
/deep/ .ivu-timeline-item-tail {
  position: absolute;
  left: 10px;
  top: 5px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

/deep/ .ivu-timeline-item {
  padding: 0;
}

.trajectoryContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  box-sizing: border-box;
  padding: 0 15px;
  > .title {
    width: 100%;
    height: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    span:first-child::before {
      content: "";
      display: inline-block;
      background-color: #fff;
      vertical-align: middle;
      width: 5px;
      height: 5px;
      margin-right: 10px;
      transform: rotate(45deg);
    }
  }
  .line {
    flex-shrink: 0;
    width: 100%;
    height: 2px;
    background-image: linear-gradient(
      to right,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
  }
  .content {
    width: 100%;
    height: calc(100% - 27px);
    display: flex;
    flex-direction: column;
    .title {
      width: 100%;
      height: 29px;
      font-size: 18px;
      color: #c4e1fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      font-weight: bold;
    }
    .contentList {
      height: calc(100% - 29px - 20px - 20px);
      width: 100%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      .item {
        flex-shrink: 0;
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 5px;
        .back {
          width: 25px;
          height: 25px;
          background: url(../../assets/images/ProjectAnti/info.png) no-repeat
            center center;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 17px;
        }
        .back.active {
          background: url(../../assets/images/ProjectAnti/error.png) no-repeat
            center center !important;
          background-size: 100% 100% !important;
        }
      }
      .item > div:last-child {
        font-size: 16px;
      }
      .item:nth-child(odd) {
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
      }
      .item:nth-child(even) {
        background-color: transparent;
      }
      .item.active {
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(125, 28, 25, 0.5),
          transparent
        ) !important;
      }
    }
  }
}

.time {
  color: #ccced2;
}

.timeConetent {
  width: 100%;
  height: 65px; /* 减少高度 */
  display: flex;
  flex-direction: column;
  background: url(../../assets/images/ProjectAnti/warn.png) no-repeat center
    center;
  background-size: 100% 100%;
  .title {
    height: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 5px;
    height: 35px;
    align-items: center;
    span {
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }
    .warnType {
      width: 68px;
      height: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      background: url(../../assets/images/ProjectAnti/Button.png) no-repeat
        center center;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
  .content {
    height: calc(65px - 35px); /* 调整高度计算 */
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    div {
      width: 100%;
      text-align: left;
    }
    div::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      margin: 0 8px;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
  }
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../assets/images/icon/s_title.png) no-repeat center center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -15px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  flex-shrink: 0; /* 防止标题被压缩 */
}

.title-content,
.card {
  flex-shrink: 0; /* 防止这些区域被压缩 */
}

.title-content {
  width: 100%;
  height: 12vh; /* 减少高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  flex-shrink: 0;

  .item {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-left {
      width: 20%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .item-right {
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: space-between;
      gap: 5px;

      .itemRight-item {
        width: 48%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .num {
          width: 100%;
          height: 55%;
          font-size: 25px;
          color: #fff;
          box-sizing: border-box;
          padding-left: 10px;
          display: flex;
          align-items: center;
        }

        .text {
          width: 100%;
          height: 43%;
          box-sizing: border-box;
          padding-left: 10px;
          display: flex;
          align-items: center;
          font-size: 13px;
        }
      }

      .itemRight-item:first-child {
        .num {
          background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
            center center;
          background-size: 100% 100%;
        }

        .text {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(9, 106, 224, 0.3) 50%,
            transparent
          );
        }
      }

      .itemRight-item:last-child {
        .num {
          background: url(../../assets/images/ProjectAnti/red.png) no-repeat
            center center;
          background-size: 100% 100%;
        }

        .text {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(151, 0, 0, 0.3) 50%,
            transparent
          );
        }
      }
    }
  }
}

.card {
  width: 100%;
  height: 12vh; /* 减少高度 */
  display: flex;
  flex-direction: column;
  gap: 5px;
  background: url(../../assets/images/ProjectAnti/card.png) no-repeat center
    center;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 5px;
  flex-shrink: 0;

  .warnNum {
    display: flex;
    height: 60%;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .warn::before {
      content: "";
      width: 10px;
      height: 10px;
      background: url(../../assets/images/icon/work.png) no-repeat center center;
      background-size: contain;
      display: inline-block;
      vertical-align: middle;
    }

    .num {
      width: 70px;
      height: 50px;
      font-size: 32px;
      color: #fff;
      background: url(../../assets/images/icon/cardIcon.png) no-repeat center
        center;
      background-size: 100% auto;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 15px;
    }

    .better {
      display: flex;
      align-items: center;

      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-right: 10px;
      }
    }
  }

  .total {
    height: 30%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 5px;

    .totalItem {
      width: 48%;
      height: 100%;
      background-image: linear-gradient(
        to right,
        rgba(14, 48, 93, 0.8) 40%,
        transparent
      );
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      //   padding-left: 15px;

      span:last-child {
        font-size: 25px;
        color: #fff;
      }
    }
  }
}

.totalNums {
  width: 100%;
  height: 150px; /* 进一步减少高度 */
  display: flex;
  justify-content: space-between;
  gap: 20px;
  flex-shrink: 0;

  .echarts2 {
    width: 35%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(../../assets/images/ProjectAI/bing.png) no-repeat center
      center;
    background-size: 84% auto;
    background-position-y: 60%;
    position: relative;

    .line {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 70%;
      height: 2px;
      background-image: linear-gradient(
        to right,
        transparent,
        #fff 52.5%,
        transparent
      );
      box-shadow: 0 0 10px #fff;
    }
  }

  .totalList {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 5px;
    overflow-y: auto;
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    .content {
      width: 100%;
      min-height: 25px; /* 改为最小高度 */
      background-image: linear-gradient(
        to right,
        #4b83b700,
        #89a0b71a 52.5%,
        #4b83b700
      );
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      display: flex;
      align-items: center;

      .title {
        width: 70%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .leftPoint {
          height: 100%;
          display: flex;
          align-items: center;

          .point {
            margin-right: 10px;
            width: 5px;
            height: 5px;
            border-radius: 50px;
          }
        }
      }

      .percentage {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        font-size: 20px;
        font-weight: bold;
      }
    }
  }
}

.timeLine {
  height: calc(100vh - 12vh - 12vh - 150px - 380px);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保不会超出边界 */
  min-height: 300px; /* 设置最小高度 */
}

.timeline-scroll-container {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
  /* 移除smooth滚动以提高性能 */
  scroll-behavior: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 鼠标悬停效果 */
  transition: background-color 0.3s ease;
}

/* 鼠标悬停时的视觉反馈 */
.timeline-scroll-container:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

/* Webkit浏览器滚动条样式 */
.timeline-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.timeline-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.timeline-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.timeline-scroll-content {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减少间距 */
  padding-right: 10px;
  padding-bottom: 20px; /* 增加底部内边距，确保最后一条数据完全显示 */
  /* 移除动画，改用JavaScript控制滚动 */
}
</style>

<template>
  <div class="project-map-page">
    <div class="main-content">
      <div class="selectTower">
        <div v-for="(item, index) in towerCrane" :key="index">
          <div v-if="currentID === item.id" class="selectBox">
            <div class="selecTowerName">吊塔{{ index + 1 }}</div>
            <img
              class="selectImg"
              src="@/assets/images/towerCrane/tower_crane_selected.png"
            />
          </div>

          <div v-else class="unselectBox" @click="goSelect(index)">
            <div class="selecTowerName">吊塔{{ index + 1 }}</div>
            <img
              class="selectImg"
              src="@/assets/images/towerCrane/tower_crane_unselected.png"
            />
          </div>
        </div>
      </div>

      <!-- 左侧信息面板 -->
      <div
        class="side-panel"
        style="pointer-events: auto"
        @mouseover="autoplay = false"
        @mouseleave="autoplay = true"
      >
        <MainTitle title="塔吊信息"></MainTitle>
        <!-- 临时结构，待后续接口完善后填充 -->
        <Carousel
          v-model="CarouselValue"
          dots="none"
          arrow="never"
          :autoplay="autoplay"
          :autoplay-speed="3000"
        >
          <div v-if="towerCrane.length">
            <CarouselItem
              v-for="(item, index) in [towerCrane[currentIndex]]"
              :key="index"
            >
              <div class="padcontent">
                <div class="statistics">
                  <div class="title">
                    <div class="titleBox">
                      <div>
                        {{ item.workPointName ? item.workPointName : "-" }}
                        {{ item.name }}
                      </div>
                      <img
                        :src="
                          item.onlineState == 1
                            ? require('@/assets/images/icon/online.png')
                            : require('@/assets/images/icon/offline.png')
                        "
                      />
                    </div>
                  </div>

                  <!-- <div class="totalNum">
                  <div class="num">
                    <div class="youshebiaotihei">总吊重</div>
                    <div class="youshebiaotihei">
                      {{
                        item.totalWeight != null
                          ? (item.totalWeight / 1000).toFixed(2)
                          : "-"
                      }}<span>KT</span>
                    </div>
                  </div>
                  <div class="num">
                    <div class="youshebiaotihei">总吊次</div>
                    <div class="youshebiaotihei">
                      {{
                        item.totalHoistingTimes &&
                        item.totalHoistingTimes != null
                          ? item.totalHoistingTimes
                          : "-"
                      }}<span>次</span>
                    </div>
                  </div>
                </div> -->
                  <div class="time">
                    <div class="item">
                      <div class="top">最大重量</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.zdzl != null
                            ? item.specificalInfoJSON.zdzl
                            : "-"
                        }}
                        <!-- <sub>t</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">大臂长</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.dbc != null
                            ? item.specificalInfoJSON.dbc
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">小臂长</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.xbc != null
                            ? item.specificalInfoJSON.xbc
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                  </div>

                  <div class="time">
                    <div class="item">
                      <div class="top">最大起重力矩</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.zdqzlj != null
                            ? item.specificalInfoJSON.zdqzlj
                            : "-"
                        }}
                        <!-- <sub>kn/m</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">塔机高度</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.tjgd != null
                            ? item.specificalInfoJSON.tjgd
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">工作幅度</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.gzfd != null
                            ? item.specificalInfoJSON.gzfd
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                  </div>
                  <div class="time">
                    <div class="item">
                      <div class="top">基础承载力</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.jcczl != null
                            ? item.specificalInfoJSON.jcczl
                            : "-"
                        }}
                        <!-- <sub>t</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">塔臂高</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.tbg != null
                            ? item.specificalInfoJSON.tbg
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">塔帽高</div>
                      <div class="hour">
                        {{
                          item.specificalInfoJSON &&
                          item.specificalInfoJSON.tmg != null
                            ? item.specificalInfoJSON.tmg
                            : "-"
                        }}
                        <!-- <sub>m</sub> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          </div>
        </Carousel>
        <SubTitle
          title="塔吊实时数据"
          style="margin-top: 10px"
          :time="towerCrane[currentIndex]?.craneBusinessRecord?.uploadTime"
        ></SubTitle>
        <Carousel
          v-model="CarouselValue"
          dots="none"
          arrow="never"
          :autoplay="autoplay"
          :autoplay-speed="3000"
        >
          <div v-if="towerCrane.length">
            <CarouselItem
              v-for="(item, index) in [towerCrane[currentIndex]]"
              :key="index"
            >
              <div class="padcontent">
                <div class="realData">
                  <!-- <div class="title">
                    <div class="online">
                      <span
                        >{{ item.workPointName ? item.workPointName : "-" }}
                        {{ item.name }}</span
                      >
                      <img
                        :src="
                          item.onlineState == 1
                            ? require('@/assets/images/icon/online.png')
                            : require('@/assets/images/icon/offline.png')
                        "
                      />
                    </div>
                  </div> -->

                  <div class="peopleDetail">
                    <div class="profile">
                      <img
                        :src="
                          item.driverList
                            ? item.driverList[0].profilePict
                            : empty
                        "
                      />
                    </div>
                    <div class="profileInfo">
                      <div class="info">
                        <span class="titles"> 司机姓名： </span>
                        <span class="text">
                          {{
                            item.driverList ? item.driverList[0].realName : "-"
                          }}
                        </span>
                      </div>
                      <div class="info">
                        <span class="titles"> 电话号码： </span>
                        <span class="text">
                          {{
                            item.driverList ? item.driverList[0].linkPhone : "-"
                          }}
                        </span>
                      </div>
                      <div class="info">
                        <span class="titles"> 司机证书： </span>
                        <div class="text" @click="search(item)">查看</div>
                      </div>
                    </div>
                  </div>
                  <div class="information">
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmWeight) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">吊重</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.weight != null
                            ? item.craneBusinessRecord.weight
                            : "-"
                        }}<sub>t</sub>
                      </div>
                    </div>
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmWindSpeed) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">风速</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.windSpeed != null
                            ? item.craneBusinessRecord.windSpeed
                            : "-"
                        }}<sub>m/s</sub>
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">变幅</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.range != null
                            ? item.craneBusinessRecord.range
                            : "-"
                        }}<sub>m</sub>
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">回转角度</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.rotation != null
                            ? item.craneBusinessRecord.rotation
                            : "-"
                        }}<sub>°</sub>
                      </div>
                    </div>
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmSlide) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">溜钩距离</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.slide != null
                            ? item.craneBusinessRecord.slide
                            : "-"
                        }}<sub>m</sub>
                      </div>
                    </div>
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmTorquePercentage) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">力矩比</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.torquePercentage != null
                            ? item.craneBusinessRecord.torquePercentage
                            : "-"
                        }}<sub>%</sub>
                      </div>
                    </div>
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmHorizontal) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">水平角度</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.horizontal != null
                            ? item.craneBusinessRecord.horizontal
                            : "-"
                        }}<sub>°</sub>
                      </div>
                    </div>
                    <div
                      :class="
                        'item ' +
                        (((item.craneBusinessRecord &&
                          item.craneBusinessRecord.alarmVertical) ||
                          0) == 1
                          ? 'active'
                          : '')
                      "
                    >
                      <div class="top">垂直角度</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.vertical != null
                            ? item.craneBusinessRecord.vertical
                            : "-"
                        }}<sub>°</sub>
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">吊钩下降深度</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.depth != null
                            ? item.craneBusinessRecord.depth
                            : "-"
                        }}<sub>m</sub>
                      </div>
                    </div>
                    <div class="item">
                      <div class="top">吊钩高度</div>
                      <div class="bottom">
                        {{
                          item.craneBusinessRecord &&
                          item.craneBusinessRecord.height != null
                            ? item.craneBusinessRecord.height
                            : "-"
                        }}<sub>m</sub>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          </div>
        </Carousel>

        <MainTitle title="今日告警" style="margin-top: 10px"></MainTitle>
        <div>
          <div class="title-content">
            <div class="item">
              <div class="item-left">
                <img src="../../assets/images/ProjectAnti/top.png" />
              </div>
              <div class="item-right">
                <div class="itemRight-item">
                  <div class="num">{{ alarmData.totalCount }}</div>
                  <div class="text">告警数量</div>
                </div>
                <div class="itemRight-item">
                  <div class="num">
                    {{ alarmData.alarmMultipleCollisionCount }}
                  </div>
                  <div class="text">群塔防碰撞告警数量</div>
                </div>
              </div>
            </div>
          </div>

          <div class="timeLine" ref="timelineContainer">
            <div
              class="timeline-scroll-container"
              ref="scrollContainer"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <div class="timeline-scroll-content" ref="scrollContent">
                <TimelineItem
                  v-for="item in infiniteScrollList"
                  :key="item._scrollKey"
                >
                  <p class="time">{{ item.createTime }}</p>
                  <div class="timeConetent">
                    <div class="title">
                      <span
                        ><img
                          src="../../assets/images/ProjectAnti/warnIcon.png"
                        />{{ item.testingItem }}</span
                      >
                      <!-- <span
                        class="warnType"
                        v-if="item.alarmTypeText == '防倾倒侵限告警'"
                        @click="searchTrajectory(item)"
                        >查看轨迹</span
                      >
                      <span
                        class="warnType"
                        v-if="item.alarmType == 36 || item.alarmType == 37"
                        @click="searchPersonTrajectory(item)"
                        >查看轨迹</span
                      > -->
                    </div>
                    <div class="content">
                      <div>{{ item.content }}</div>
                    </div>
                  </div>
                </TimelineItem>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="selectMonitor" @click="goShowVideo">
        <img class="video" src="@/assets/images/icon/video_towerCrane.png" />
      </div>

      <div
        class="side-panel side-panel-right"
        style="pointer-events: auto; height: 100%"
      >
        <Title title="吊重幅度对比统计"></Title>
        <div class="echartsContent">
          <div class="echarts">
            <div id="echarts_crane1" style="width: 100%; height: 100%"></div>
          </div>
        </div>

        <Title title="力矩曲线"></Title>
        <div class="beforeChartsTitle">
          <div>型号 {{ specification }}</div>
          <div>臂长 {{ dbc }}m</div>
        </div>
        <div class="echartsContent">
          <div class="echarts">
            <div id="echarts_crane2" style="width: 100%; height: 100%"></div>
          </div>
        </div>

        <Title title="倾角曲线"></Title>
        <div class="monitorContent">
          <div class="echarts">
            <div id="echarts_crane3" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </div>

    <ModalW610H380 :visibleW610h380.sync="isshow" title="司机证书">
      <el-carousel indicator-position="none" arrow="hover">
        <el-carousel-item v-for="(item, index) in currentCertList" :key="index">
          <div class="cardContent">
            <div class="card">
              <Empty
                title="暂无图片"
                v-if="!item.specialAuthBookContent"
              ></Empty>
              <img :src="item.specialAuthBookContent" v-else />
            </div>
            <div class="content">
              <div class="beforeTime" v-if="false">
                <div>证书已过期</div>
              </div>
              <!-- <div class="contentItem">
                                <span>证书名称：</span>
                                <span>这里是一个证书的名称</span>
                                <div class="line"></div>
                            </div>
                            <div class="contentItem">
                                <span>证书编号：</span>
                                <span>这里是一个证书的编号</span>
                                <div class="line"></div>
                            </div> -->
              <div class="contentItem">
                <span>证书类型：</span>
                <span>{{ initDict(item.specialAuthBookType) }}</span>
                <div class="line"></div>
              </div>
              <!-- <div class="contentItem">
                                <span>证书等级：</span>
                                <span>这里是一个证书的等级</span>
                                <div class="line"></div>
                            </div> -->
              <div class="contentItem">
                <span>证书有效起始日期：</span>
                <span>{{ item.specialAuthBookDate.split("~")[0] }}</span>
                <div class="line"></div>
              </div>
              <div class="contentItem">
                <span>证书有效截止：</span>
                <span>{{ item.specialAuthBookDate.split("~")[1] }}</span>
                <div class="line"></div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </ModalW610H380>
    <ModalW610H380 :visibleW610h380.sync="picShow" title="机械五定图">
      <div class="picContent">
        <span>
          {{ picData.name }}
        </span>
        <span> 时间：{{ picData.fiveChartTime }} </span>
        <span> 作业内容： {{ picData.fiveChartContent }} </span>
        <div class="pic">
          <el-image
            :src="item"
            v-for="(item, index) in picData.fiveChartImg"
            :key="index"
            :preview-src-list="picData.fiveChartImg"
          />
        </div>
      </div>
    </ModalW610H380>
    <ModalW403H304 :visibleW403h304.sync="PlainShow" title="吊车施工计划值">
      <div class="information2">
        <div
          :class="'item'"
          v-for="(item, index) in planValueList"
          :key="index"
          :style="{ width: index == planValueList.length - 1 ? 'auto' : '30%' }"
        >
          <div class="top">
            {{ item.text }}
          </div>
          <div class="bottom">
            {{ item.value
            }}<sub>
              <span
                v-if="
                  item.type == 12 ||
                  item.type == 16 ||
                  item.type == 17 ||
                  item.type == 19
                "
                >m</span
              >
              <span
                v-if="
                  item.type == 9 ||
                  item.type == 20 ||
                  item.type == 13 ||
                  item.type == 14 ||
                  item.type == 15
                "
                >°</span
              >
              <span v-if="item.type == 10">t*m</span>
              <span v-if="item.type == 11 || item.type == 18">t</span>
              <span v-if="item.type == 8">m/s</span>
            </sub>
          </div>
        </div>
      </div>
    </ModalW403H304>
  </div>
</template>

<script>
function extractTimeFromISO(isoString) {
  const date = new Date(isoString);
  return `${date.getHours().toString().padStart(2, "0")}:${date
    .getMinutes()
    .toString()
    .padStart(2, "0")}`;
}
import Title from "@/components/commonView/title3.vue";
import SubTitle from "@/components/commonView/title5.vue";
import MainTitle from "@/components/commonView/title3NoMargin";
import ModalW610H380 from "@/components/modal/w610h380.vue";
import ModalW403H304 from "@/components/modal/w403h304Active.vue";
import * as echarts from "echarts";
import {
  getListPage,
  getDict,
  getCategorytoTotall,
  countByDay,
  systemAlarmPage,
  craneBusinessRecordlistPage,
  getWeather,
  giveAlarmTypeConfig,
  getplanValue,
  getAlarmStatistics,
  weathreInfo,
  getTowerCraneAlarmStatistics,
  getTowerCraneAlarmStatisticsByMechanical,
} from "@/api/xxindex";

import { getCharts1, getCharts2, getCharts3 } from "@/api/towerCrane";
import { getTowerCraneMechanical } from "@/api/index";
import { getLastWeekDateRange, getLast30DaysRange } from "./index";
import { getProjectById } from "@/api/project";
import moment from "moment";
import { EventBus } from "@/utils/event-bus.js";

const empty = require("../../assets/images/ProjectCrane/people.png");
export default {
  name: "ProjectCrane",
  components: {
    Title,
    SubTitle,
    MainTitle,
    ModalW610H380,
    ModalW403H304,
  },
  data() {
    return {
      currentID: "",
      specification: "",
      dbc: "",
      currentIndex: 0,
      isUserScrolling: false,
      userScrollTimer: null,
      myEcharts1: null,
      myEcharts2: null,
      myEcharts3: null,
      allWordSpeed: {},
      echarts4Data: [
        {
          data: [7, 7, 5, 5, 4, 4, 3],
          name: "水平度x",
          color: "#7DD7FB",
        },
        {
          data: [10, 10, 8, 9, 7, 7, 6],
          name: "水平度y",
          color: "#2461F6",
        },
        {
          data: [17, 17, 14, 15, 13, 11, 11],
          name: "倾斜度",
          color: "#F2A250",
        },
      ],
      isshow: false,
      dataInfo: {},
      CarouselValue: 0,
      CarouselValue2: 1,
      empty: empty,
      autoplay: true,
      currentCertList: [],
      Dict: [],
      picShow: false,
      picData: {},
      warnInfo: {},
      craneId1: "",
      craneId2: "",
      changeValue1: "",
      changeValue2: "",
      weekandmonth1: "week",
      weekandmonth2: "week",
      initCharts1Data: [],
      warnInfoList: [],
      xyzline: [],
      rainInfoList: [],
      rainInfo: {},
      warnLineInfo: 0,
      DCInfo: {},
      PlainShow: false,
      geoInfo: {},
      weatherWarn: {},
      planValueList: [
        {
          text: "作业半径",
          threshValue: 16,
        },
        {
          text: "倾覆半径",
          threshValue: 12,
        },
        {
          text: "距离安全界限",
          threshValue: 17,
        },
        {
          text: "作业展臂最不利工况可起重吊重",
          threshValue: 18,
        },
      ],
      towerCrane: [],
      alarmData: {
        totalCount: 0,
        alarmMultipleCollisionCount: 0,
      },
      timer: null,
      chartLoading_I: false,
      chartLoading_II: false,
      chartLoading_III: false,
    };
  },
  mounted() {
    this.getPointInfo();
    this.getPlanValueList();

    this.$bus.emit("model"); // 调用吊车模型
  },
  destroyed() {
    this.myEcharts1 && this.myEcharts1.dispose();
    this.myEcharts2 && this.myEcharts2.dispose();
    this.myEcharts3 && this.myEcharts3.dispose();
  },

  computed: {
    infiniteScrollList() {
      // 获取基础数据
      let baseList = this.warnInfoList;

      // 如果数据不够，创建一些默认数据
      // if (!baseList || baseList.length === 0) {
      //   baseList = [
      //     {
      //       createTime: "2025-01-15 14:30:00",
      //       testingItem: "汽车吊1536",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "有防倾倒侵入安全限界风险，请注意",
      //     },
      //     {
      //       createTime: "2025-01-15 14:25:00",
      //       testingItem: "塔吊2048",
      //       alarmTypeText: "超载告警",
      //       content: "当前大臂长度为100.15 m，超安全值，请注意",
      //     },
      //     {
      //       createTime: "2025-01-15 14:20:00",
      //       testingItem: "履带吊3072",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "检测到设备进入危险区域，请立即停止作业",
      //     },
      //     {
      //       createTime: "2025-01-15 14:15:00",
      //       testingItem: "汽车吊4096",
      //       alarmTypeText: "风速告警",
      //       content: "当前风速超过安全阈值，建议暂停高空作业",
      //     },
      //     {
      //       createTime: "2025-01-15 14:10:00",
      //       testingItem: "塔吊5120",
      //       alarmTypeText: "防倾倒侵限告警",
      //       content: "设备倾斜角度异常，存在倾倒风险",
      //     },
      //   ];
      // }

      // 不重复数据，只返回原始列表并添加唯一key
      return baseList.map((item, index) => ({
        ...item,
        _scrollKey: `item-${index}`,
      }));
    },
  },

  methods: {
    goShowVideo() {
      const list = this.towerCrane.filter((item) => item.id === this.currentID);
      if (list.length) {
        EventBus.$emit("showVideoList", list[0]);
      }
    },

    // 风速单位转换：km/h 转 m/s
    convertWindSpeed(kmhValue) {
      if (kmhValue === null || kmhValue === undefined || kmhValue === "") {
        return 0;
      }
      // km/h 转 m/s：除以 3.6
      return Math.round((parseFloat(kmhValue) / 3.6) * 10) / 10; // 保留1位小数
    },
    // 计算总吊次数据
    async calculateTotalHoistingTimes() {
      // 获取近一个月的数据范围
      const monthRange = getLast30DaysRange();

      // 为每个吊车计算总吊次和总吊重
      const promises = this.dataInfo.map(async (crane, index) => {
        try {
          const data = {
            startTime: monthRange.startDate,
            endTime: monthRange.endDate,
            mechanicalId: crane.id,
          };

          const res = await countByDay(data);
          if (res.code === "success" && res.data) {
            // 计算总吊次
            const totalHoistingTimes = res.data.reduce((sum, item) => {
              return sum + (item.totalHoistingTimes || 0);
            }, 0);

            // 计算总吊重
            const totalWeight = res.data.reduce((sum, item) => {
              return sum + (item.totalWeight || 0);
            }, 0);

            // 更新吊车数据
            this.$set(this.dataInfo, index, {
              ...crane,
              totalHoistingTimes:
                totalHoistingTimes || crane.totalHoistingTimes,
              totalWeight: totalWeight || crane.totalWeight,
            });
          }
        } catch (error) {
          console.error(`计算吊车${crane.name}总吊次失败:`, error);
        }
      });

      await Promise.all(promises);
    },
    getPlanValueList() {
      getplanValue({
        category: 1,
      }).then((res) => {
        if (res.code == "success") {
          this.planValueList = this.planValueList.map((i) => {
            let option = {};
            option.text = i.text;
            let item = res.data.find(
              (item) => item.type == String(i.threshValue)
            );
            if (item) {
              option.value = item.threshValue;
              option.type = item.type;
            } else {
              option.value = 0;
              option.type = String(i.threshValue);
            }
            return option;
          });
        }
      });
    },
    getPointInfo() {
      let pointInfo = this.$route.query.projectId;
      getProjectById(pointInfo).then((res) => {
        if (res.code == "success") {
          this.geoInfo = res.data;
          this.initRequest(true);
          if (this.timer) clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.initRequest();
          }, 1000 * 10);
        }
      });
    },

    goSelect(index) {
      this.towerCrane.forEach((item, ind) => {
        if (index === ind) {
          this.currentID = item.id;
          this.specification = item.specification;
          this.dbc = JSON.parse(item.specificalInfo).dbc;
          this.currentIndex = ind;
        }
      });

      this.getPointInfo();
    },

    async initRequest(flag) {
      let data = {
        customQueryParams: {
          alarmState: null,
          codeOrName: "",
          onlineState: null,
          workPointId: null,
        },
        page: {
          current: 1,
          size: 2,
        },
      };
      const res = await getTowerCraneMechanical();
      const arr = res.data;
      arr.forEach((item, index) => {
        if (!index && !this.currentID) {
          // 初始化
          this.currentID = item.id;
          this.specification = item.specification;
          this.dbc = JSON.parse(item.specificalInfo).dbc;
        }
        item.specificalInfoJSON = JSON.parse(item.specificalInfo);
      });
      this.towerCrane = arr;
      console.log(this.towerCrane, "getTowerCraneMechanical");
      // getListPage(data).then((res) => {
      //   if (res.code == "success") {
      //     this.dataInfo = res.data.records;
      //     this.craneId1 = this.dataInfo[0].id;
      //     this.craneId2 = this.dataInfo[0].id;

      //     // 为每个吊车计算总吊次数据
      //     this.calculateTotalHoistingTimes();

      //     this.changeCrane1(this.craneId1, "week", "craneId1");
      //     this.changeCrane2(this.craneId2, "week", "craneId2");
      //     this.changeCrane3(this.craneId1);
      //   }
      // });
      let category = "7,9";
      getCategorytoTotall(category).then((res) => {
        if (res.code == "success") {
          this.warnInfo = res.data;
        }
      });
      let datas = {
        customQueryParams: {
          // alarmType: 20,
          refId: this.currentID,
          categoryList: ["9"],
          // 获取当天的0:00 格式要求yyyy-mm-dd hh:mm:ss
          startDate: this.getTodayStart() + " 00:00:00",
          endDate: this.getTodayStart() + " 23:59:59",
        },
        page: {
          current: 1,
          size: 30,
        },
        sorts: [],
      };
      systemAlarmPage(datas).then((res) => {
        if (res.code == "success") {
          this.warnInfoList = res.data.records;
        }
      });
      let DCwarn = {
        customQueryParams: {
          category: "1", //告警大类 告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护
          uniKey: "", //关键词
          alarmTypes: [21, 22, 23], //告警类型，查询告警类型列表，输入id
          status: "", // 0未处理，1已处理，2误报
          startDate: "2025-06-28 00:00:00",
          endDate: "2025-06-28 23:59:59",
        },
        page: {
          current: 1,
          size: 9999,
        },
        sorts: [],
      };
      // systemAlarmPage(DCwarn).then(res => {
      //     if (res.code == 'success') {
      //         this.DCInfo.fqd = res.data.total; //防倾倒
      //     }
      // })
      // let todayWarn = {
      //   page: {
      //     size: 9999,
      //     current: 1,
      //   },
      //   customQueryParams: {
      //     category: 1, //告警大类 告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护
      //     uniKey: "", //关键词
      //     alarmType: "", //告警类型，查询告警类型列表，输入id
      //     status: "", // 0未处理，1已处理，2误报
      //     startDate: "2025-01-01", //开始时间
      //     endDate: "2025-12-02", //结束时间
      //   },
      // };
      // systemAlarmPage(todayWarn).then(res => {
      //     if (res.code == 'success') {
      //         this.DCInfo.todayWarn = res.data.total; //防倾倒
      //     }
      // })
      getAlarmStatistics().then((res) => {
        if (res.code == "success") {
          this.DCInfo.todayWarn = res.data.craneTotalCount;
          this.DCInfo.fqd = res.data.alarmInclinationCount;
        }
      });
      let paramsType = {
        category: 1,
      };
      giveAlarmTypeConfig(paramsType).then((res) => {
        if (res.code == "success") {
          this.warnLineInfo = res.data.filter((i) => i.text == "倾倒告警")[0];
        }
      });
      let host = "np2mtdt4fv.re.qweatherapi.com";
      // 版本 v7
      let v = "/v7";
      // 武汉地理位置
      let WHlocation =
        Number(this.geoInfo.longitude).toFixed(2) +
        "," +
        Number(this.geoInfo.latitude).toFixed(2);
      // 获取并合并当日完整的天气数据
      // this.getMergedWeatherData();
      // 获取天气预警
      // getWeather(`https://${host}${v}/warning/now?location=${WHlocation}`).then(res => {
      //     if (res.code == '200') {
      //         this.weatherWarn = res.now;
      //     }
      // })

      getTowerCraneAlarmStatisticsByMechanical(this.currentID).then((res) => {
        const totalCount = res.data.towerCraneTotalCount;
        const alarmMultipleCollisionCount =
          res.data.alarmMultipleCollisionCount;
        this.alarmData = {
          totalCount,
          alarmMultipleCollisionCount,
        };
      });

      this.getChartData(flag);
    },

    getChartData(flag) {
      this.goGetChart1(flag);
      this.goGetChart2(flag);
      this.goGetChart3(flag);
    },

    goGetChart1(flag) {
      if (flag) {
        this.initCharts1([]);
      }
      this.chartLoading_I = true;
      const now = moment(new Date()).format("YYYY-MM-DD");
      const postData = {
        page: {
          current: 1,
          size: -1,
        },
        customQueryParams: {
          mechanicalId: this.currentID,
          // queryStartTime: "2025-07-08 17:13:00",
          // queryEndTime: "2025-07-30 18:18:00",
          queryStartTime: now + " 00:00:00",
          queryEndTime: now + " 23:59:59",
        },
        sorts: [],
      };
      getCharts1(postData)
        .then((res) => {
          console.log(res, "getCharts1");
          const chartsData = res.data.records.map((item) => {
            return {
              maxRange: item.maxRange,
              maxWeight: item.maxWeight,
              uploadTime: moment(item.uploadTime).format("HH:mm:ss"),
            };
          });

          console.log(chartsData, "chartsData");
          // 由于后端返回的数据，是按时间倒序（即从晚到早）
          // 反转数组
          chartsData.reverse();
          this.initCharts1(chartsData);
          this.chartLoading_I = false;
        })
        .catch((err) => {
          this.chartLoading_I = false;
        });
    },

    goGetChart2(flag) {
      if (flag) {
        this.initCharts2({ alphaTwo: [], alphaFour: [] });
      }
      this.chartLoading_II = true;
      const postData = {
        model: this.specification,
        boomLength: this.dbc,
      };
      getCharts2(postData)
        .then((res) => {
          console.log(res, "getCharts2");
          const alphaTwo = res.data.multiplierDataList[0].curveData.filter(
            (item) => item.available === true
          );

          const alphaFour = res.data.multiplierDataList[1].curveData.filter(
            (item) => item.available === true
          );

          if (!alphaTwo.length) {
            this.initCharts2({ alphaTwo: [], alphaFour: [] });
          } else {
            this.initCharts2({ alphaTwo, alphaFour });
          }
          this.chartLoading_II = false;
        })
        .catch((err) => {
          this.chartLoading_II = false;
        });
    },

    goGetChart3(flag) {
      if (flag) {
        this.initCharts3([]);
      }
      this.chartLoading_III = false;
      const now = moment(new Date()).format("YYYY-MM-DD");

      const postData = {
        page: {
          current: 1,
          size: -1,
        },
        customQueryParams: {
          mechanicalId: this.currentID,
          startTime: now + " 00:00:00",
          endTime: now + " 23:59:59",
          keyword: "",
          interval: null,
        },
        sorts: [],
      };
      getCharts3(postData)
        .then((res) => {
          // 数据量太大，等间距采样
          const data = this.sampleEvenly(res.data.records, 96);
          const res_data = data.map((item) => {
            return {
              vertical: item.vertical,
              horizontal: item.horizontal,
              uploadTime: moment(item.uploadTime).format("HH:mm:ss"),
            };
          });
          console.log(res_data, "getCharts3_data");
          // 由于后端返回的数据，是按时间倒序（即从晚到早）
          // 反转数组
          res_data.reverse();
          this.initCharts3(res_data);
          this.chartLoading_III = false;
        })
        .catch((err) => {
          this.chartLoading_III = false;
        });
    },

    sampleEvenly(arr, sampleCount = 200) {
      const N = arr.length;
      if (sampleCount >= N) return arr.slice(); // 如果采样数超过数组长度，直接返回全部

      const step = N / sampleCount;
      const result = [];

      for (let i = 0; i < sampleCount; i++) {
        const idx = Math.floor(i * step);
        result.push(arr[idx]);
      }

      return result;
    },

    getTodayStart() {
      const now = new Date();
      const todayStart = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate()
      );

      // 格式化为 yyyy-mm-dd hh:mm:ss
      const year = todayStart.getFullYear();
      const month = String(todayStart.getMonth() + 1).padStart(2, "0");
      const day = String(todayStart.getDate()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    },

    // 鼠标进入时间线区域 - 暂停自动滚动
    handleMouseEnter() {
      this.isUserScrolling = true;
      console.log("鼠标进入，暂停自动滚动");
    },

    // 鼠标离开时间线区域 - 恢复自动滚动
    handleMouseLeave() {
      // 清除用户滚动相关的定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
        this.userScrollTimer = null;
      }
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
        this.scrollDebounceTimer = null;
      }

      // 延迟恢复自动滚动，给用户一点缓冲时间
      setTimeout(() => {
        this.isUserScrolling = false;
        console.log("鼠标离开，恢复自动滚动");
      }, 300); // 300ms延迟
    },
    onweek1(val) {
      this.weekandmonth1 = "week";
      this.initcountByDay(this.changeValue1, "week", "craneId1");
    },
    onweek2(val) {
      this.weekandmonth2 = "week";
      this.initcountByDay(this.changeValue2, "week", "craneId2");
    },
    onmonth1(val) {
      this.weekandmonth1 = "month";
      this.initcountByDay(this.changeValue1, "month", "craneId1");
    },
    onmonth2(val) {
      this.weekandmonth2 = "month";
      this.initcountByDay(this.changeValue2, "month", "craneId2");
    },
    changeCrane1(val) {
      let changeName = "craneId1";
      this.changeValue1 = val;
      this.initcountByDay(val, this.weekandmonth1, changeName);
    },
    changeCrane2(val) {
      let changeName = "craneId2";
      this.changeValue2 = val;
      this.initcountByDay(val, this.weekandmonth2, changeName);
    },
    changeCrane3(val) {
      this.RecordlistPage(val);
    },
    RecordlistPage(val) {
      let data = {
        customQueryParams: {
          // 获取当日0:00
          startTime: new Date(new Date().setHours(0, 0, 0, 0))
            .toLocaleString()
            .replace(/\//g, "-"),
          // 获取当日23:59:59
          endTime: new Date(new Date().setHours(23, 59, 59, 999))
            .toLocaleString()
            .replace(/\//g, "-"),
          keyword: "",
          mechanicalId: val,
          interval: 10,
        },
        page: {
          current: 1,
          size: -1,
        },
      };
      craneBusinessRecordlistPage(data).then((res) => {
        if (res.code == "success") {
          let arr = [];
          arr.push(res.data.records.map((i) => i.levelX || 0));
          arr.push(res.data.records.map((i) => i.levelY || 0));
          arr.push(res.data.records.map((i) => i.inclination || 0));
          let time = res.data.records.map((i) => i.modifyTime);
          this.echarts4Data = this.echarts4Data.map((i, index) => {
            let item = arr[index].map((v, indexs) => {
              return [time[indexs], v];
            });
            return {
              ...i,
              data: item,
            };
          });
        }
      });
    },
    // 合并天气数据：获取当日0:00到23:00的完整天气数据
    async getMergedWeatherData() {
      try {
        // 获取API配置
        const host = "np2mtdt4fv.re.qweatherapi.com";
        // 版本 v7
        const v = "/v7";
        const WHlocation =
          Number(this.geoInfo.longitude).toFixed(2) +
          "," +
          Number(this.geoInfo.latitude).toFixed(2);

        // 获取当前时间
        const now = new Date();
        const currentHour = now.getHours();

        // 准备历史数据请求参数
        const historyParams = {
          startTime: new Date(new Date().setHours(0, 0, 0, 0))
            .toLocaleString()
            .replace(/\//g, "-"),
          endTime: new Date(new Date().setHours(currentHour, 59, 59, 999))
            .toLocaleString()
            .replace(/\//g, "-"),
        };

        // 并发请求历史数据、未来数据和当前天气
        const [historyResponse, futureResponse, currentResponse] =
          await Promise.all([
            // 获取历史天气数据(0:00到当前时间)
            weathreInfo(historyParams),
            // 获取未来24小时天气数据
            getWeather(
              `https://${host}${v}/weather/24h/?location=${WHlocation}`
            ),
            // 获取当前天气
            getWeather(
              `https://${host}${v}/weather/now?location=${WHlocation}`
            ),
          ]);

        // 创建24小时的时间映射表
        const hourlyWeatherMap = new Map();

        // 处理历史数据
        if (historyResponse.code === "success" && historyResponse.data) {
          historyResponse.data.forEach((item) => {
            const itemTime = new Date(item.obsTime);
            const itemHour = itemTime.getHours();
            const isToday = itemTime.toDateString() === now.toDateString();

            // 只保留今天的数据，并且避免重复
            if (isToday && !hourlyWeatherMap.has(itemHour)) {
              hourlyWeatherMap.set(itemHour, {
                ...item,
                obsTime: item.obsTime,
                source: "history",
                windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
              });
            }
          });
        }

        // 处理未来数据
        if (futureResponse.code === "200" && futureResponse.hourly) {
          futureResponse.hourly.forEach((item) => {
            const itemTime = new Date(item.fxTime);
            const itemHour = itemTime.getHours();
            const isToday = itemTime.toDateString() === now.toDateString();

            // 只保留今天的数据，并且避免重复（优先使用历史数据）
            if (isToday && !hourlyWeatherMap.has(itemHour)) {
              hourlyWeatherMap.set(itemHour, {
                ...item,
                obsTime: item.fxTime, // 统一使用obsTime字段
                source: "future",
                windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
              });
            }
          });
        }

        // 生成完整的24小时数据，按小时顺序
        const normalizedData = [];
        for (let hour = 0; hour < 24; hour++) {
          if (hourlyWeatherMap.has(hour)) {
            const item = hourlyWeatherMap.get(hour);
            normalizedData.push({
              id: item.id || `generated-${hour}`,
              tenantId: item.tenantId || "",
              obsTime: item.obsTime,
              temp: item.temp,
              feelsLike: item.feelsLike,
              icon: item.icon,
              text: item.text,
              wind360: item.wind360,
              windDir: item.windDir,
              windScale: item.windScale,
              windSpeed: item.windSpeed,
              humidity: item.humidity,
              precip: item.precip,
              pressure: item.pressure,
              vis: item.vis,
              cloud: item.cloud,
              dew: item.dew,
              createTime:
                item.createTime ||
                new Date().toISOString().replace("T", " ").substr(0, 19),
              source: item.source,
            });
          } else {
            // 如果某个小时没有数据，使用默认值或插值
            normalizedData.push({
              id: `default-${hour}`,
              tenantId: "",
              obsTime: new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate(),
                hour,
                0,
                0
              ).toISOString(),
              temp: "0",
              feelsLike: "0",
              icon: "100",
              text: "无数据",
              wind360: "0",
              windDir: "无风",
              windScale: "0",
              windSpeed: 0, // 默认值直接设为0，已经是m/s单位
              humidity: "0",
              precip: "0.0",
              pressure: "1000",
              vis: "0",
              cloud: "0",
              dew: "0",
              createTime: new Date()
                .toISOString()
                .replace("T", " ")
                .substr(0, 19),
              source: "default",
            });
          }
        }

        // 更新组件数据
        this.rainInfoList = normalizedData;

        // 设置当前天气信息
        if (currentResponse.code === "200") {
          this.rainInfo = {
            ...currentResponse.now,
            windSpeed: this.convertWindSpeed(currentResponse.now.windSpeed), // 转换风速单位
          };
        }

        // 初始化图表
        // this.initCharts3();

        console.log("合并后的天气数据:", normalizedData);
        console.log("数据时间范围:", {
          start: normalizedData[0]?.obsTime,
          end: normalizedData[normalizedData.length - 1]?.obsTime,
          total: normalizedData.length,
        });
      } catch (error) {
        console.error("获取天气数据失败:", error);

        // 如果合并失败，使用原有逻辑作为备用方案
        this.getFallbackWeatherData();
      }
    },
    // 备用天气数据获取方法
    getFallbackWeatherData() {
      const host = "np2mtdt4fv.re.qweatherapi.com";
      const v = "/v7";
      const WHlocation =
        Number(this.geoInfo.longitude).toFixed(2) +
        "," +
        Number(this.geoInfo.latitude).toFixed(2);

      // 获取24小时天气数据
      getWeather(`https://${host}${v}/weather/24h/?location=${WHlocation}`)
        .then((res) => {
          if (res.code == "200") {
            this.rainInfoList = res.hourly.map((item) => ({
              ...item,
              windSpeed: this.convertWindSpeed(item.windSpeed), // 转换风速单位
            }));
            // this.initCharts3();
          }
        })
        .catch((err) => {
          console.error("获取未来天气数据失败:", err);
        });

      // 获取当前天气
      getWeather(`https://${host}${v}/weather/now?location=${WHlocation}`)
        .then((res) => {
          if (res.code == "200") {
            this.rainInfo = {
              ...res.now,
              windSpeed: this.convertWindSpeed(res.now.windSpeed), // 转换风速单位
            };
          }
        })
        .catch((err) => {
          console.error("获取当前天气数据失败:", err);
        });
    },
    initcountByDay(val, time, type) {
      return;
      let weeksandmonth = null;
      if (time == "month") {
        weeksandmonth = getLast30DaysRange();
      }
      if (time == "week") {
        weeksandmonth = getLastWeekDateRange();
      }
      let data = {
        startTime: weeksandmonth.startDate,
        endTime: weeksandmonth.endDate,
        mechanicalId: val,
      };
      countByDay(data).then((res) => {
        if (res.code == "success") {
          let datacarneId1 = res.data.map((i) => {
            return {
              day: i.day,
              totalHoistingTimes: i.totalHoistingTimes,
            };
          });
          let datacarneId2 = res.data.map((i) => {
            return {
              day: i.day,
              totalWeight: i.totalWeight,
            };
          });
          if (type == "craneId1") {
            this.initCharts1(datacarneId1);
          }
          if (type == "craneId2") {
            this.initCharts2(datacarneId2);
          }
        }
      });
    },
    initCharts1(data) {
      this.myEcharts1 && this.myEcharts1.dispose();
      this.myEcharts1 = echarts.init(document.getElementById("echarts_crane1"));

      const hasData = data.length > 0;
      const defaultXAxis = ["00:00", "06:00", "12:00", "18:00", "23:59"];
      const defaultYAxisLeftMax = 10; // 吊重最大值备用
      const defaultYAxisRightMax = 20; // 幅度最大值备用
      const option = {
        // title: {
        //   text: "吊重与幅度变化图",
        // },
        grid: {
          left: "2%",
          right: "10%",
          bottom: 0,
          top: "21%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          backgroundColor: "transparent",
          borderWidth: 0,
          formatter: function (params) {
            const time = params[0].axisValue;
            const weight = params.find((p) =>
              p.seriesName.includes("吊重")
            )?.value;
            const radius = params.find((p) =>
              p.seriesName.includes("幅度")
            )?.value;

            return `
      <div style="width: 160px; background-color: rgba(255, 255, 255, 0  08); backdrop-filter: blur(4px);
        border-radius: 10px; padding: 10px; color: #fff; font-size: 12px; box-sizing: border-box;">
        <div style="margin-bottom: 6px;">${time}</div>
        <div style="display: flex; justify-content: space-between;">
          <span>吊重</span><span>${weight} T</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>幅度</span><span>${radius} m</span>
        </div>
      </div>
    `;
          },
        },
        legend: {
          data: ["吊重(T)", "幅度(m)"],
          textStyle: {
            color: "#a7b0ac",
          },
        },
        xAxis: {
          type: "category",
          data: hasData ? data.map((item) => item.uploadTime) : defaultXAxis,
          axisLabel: {
            color: "#a7b0ac",
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: [
          {
            type: "value",
            name: "吊重 (T)",
            position: "left",
            axisLine: {
              lineStyle: {
                color: "#a7b0ac",
              },
            },
            axisLabel: {
              formatter: "{value} T",
            },
          },
          {
            type: "value",
            name: "幅度 (m)",
            position: "right",
            axisLine: {
              lineStyle: {
                color: "#a7b0ac",
              },
            },
            axisLabel: {
              formatter: "{value} m",
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "吊重 (T)",
            type: "bar",
            data: hasData
              ? data.map((item) => item.maxWeight)
              : new Array(defaultXAxis.length).fill(0),
            yAxisIndex: 0,
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#336CEF", // 0%处的颜色
                },
                {
                  offset: 1,
                  color: "transparent", // 100%处的颜色
                },
              ],
            },
            borderColor: "#4872DD",
          },
          {
            name: "幅度 (m)",
            type: "line",
            data: hasData
              ? data.map((item) => item.maxRange)
              : new Array(defaultXAxis.length).fill(0),
            yAxisIndex: 1,
            smooth: true,
            itemStyle: {
              color: "#ff9f39",
            },
          },
        ],
      };

      this.myEcharts1.setOption(option);
    },
    initCharts2(data) {
      const hasData = data.alphaTwo.length > 0;

      this.myEcharts2 && this.myEcharts2.dispose();
      this.myEcharts2 = echarts.init(document.getElementById("echarts_crane2"));
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
          },
          backgroundColor: "transparent",
          borderWidth: 0,
          formatter: function (params) {
            const point = params[0]; // 假设总有数据
            const rows = params
              .map(
                (p) => `
      <div style="display: flex; justify-content: space-between;">
        <span>${p.seriesName}</span>
        <span>${p.value[1]} T</span>
      </div>
    `
              )
              .join("");
            return `
      <div style="width: 180px; background-color: rgba(255, 255, 255, 0.08); backdrop-filter: blur(4px);
        border-radius: 10px; padding: 10px; color: #fff; font-size: 12px; box-sizing: border-box;">
        <div style="margin-bottom: 6px;">幅度：${point.value[0]} m</div>
        ${rows}
      </div>
    `;
          },
        },
        grid: {
          left: "2%",
          right: "10%",
          bottom: 0,
          top: "21%",
          containLabel: true,
        },
        legend: {
          data: ["α = 2", "α = 4"],
          textStyle: {
            color: "#a7b0ac",
          },
        },
        xAxis: {
          type: "value",
          name: "幅度 (m)",
          nameLocation: "middle",
          nameGap: 25,
          axisLabel: {
            color: "#a7b0ac",
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#444",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "吊重 (T)",
          axisLabel: {
            color: "#a7b0ac",
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#444",
            },
          },
        },
        series: [
          {
            name: "α = 2",
            type: "line",
            data: hasData
              ? data.alphaTwo.map((item) => [item.radius, item.loadCapacity])
              : [
                  [0, 0],
                  [0, 0],
                ],
            smooth: true,
            lineStyle: { color: "#165dff" },
            itemStyle: { color: "#165dff" },
            symbol: "circle",
          },
          {
            name: "α = 4",
            type: "line",
            data: hasData
              ? data.alphaFour.map((item) => [item.radius, item.loadCapacity])
              : [
                  [0, 0],
                  [0, 0],
                ],
            smooth: true,
            lineStyle: { color: "#f68f21" },
            itemStyle: { color: "#f68f21" },
            symbol: "diamond",
          },
        ],
      };

      this.myEcharts2.setOption(option);
    },
    initCharts3(data) {
      const hasData = data.length > 0;
      const defaultXAxis = ["00:00", "06:00", "12:00", "18:00", "23:59"];

      this.myEcharts3 && this.myEcharts3.dispose();
      this.myEcharts3 = echarts.init(document.getElementById("echarts_crane3"));

      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
          },
          backgroundColor: "transparent",
          borderWidth: 0,
          formatter: function (params) {
            const time = params[0].axisValue;
            const rows = params
              .map(
                (p) => `
        <div style="display: flex; justify-content: space-between;">
          <span>${p.seriesName}</span>
          <span>${p.value}°</span
        </div>
      `
              )
              .join("");

            return `
        <div style="width: 180px; background-color: rgba(255, 255, 255, 0.08); backdrop-filter: blur(4px);
          border-radius: 10px; padding: 10px; color: #fff; font-size: 12px; box-sizing: border-box;">
          <div style="margin-bottom: 6px;">时间：${time}</div>
          ${rows}
        </div>
      `;
          },
        },
        legend: {
          data: ["水平倾角", "垂直倾角"],
          textStyle: {
            color: "#a7b0ac",
          },
        },
        grid: {
          left: "2%",
          right: "10%",
          bottom: 0,
          top: "21%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: hasData ? data.map((item) => item.uploadTime) : defaultXAxis,
          axisLine: {
            lineStyle: { color: "#555" },
          },
          axisLabel: {
            color: "#a7b0ac",
          },
        },
        yAxis: {
          type: "value",
          name: "单位 (°)",
          nameTextStyle: {
            color: "#a7b0ac",
          },
          axisLabel: {
            color: "#a7b0ac",
          },
          axisLine: {
            lineStyle: { color: "#555" },
          },
          splitLine: {
            lineStyle: {
              color: "#333",
            },
          },
        },
        series: [
          {
            name: "水平倾角",
            type: "line",
            smooth: true,
            symbol: "circle",
            lineStyle: {
              color: "#31ffff",
            },
            itemStyle: {
              color: "#31ffff",
            },
            data: hasData ? data.map((item) => item.horizontal) : [0],
          },
          {
            name: "垂直倾角",
            type: "line",
            smooth: true,
            symbol: "diamond",
            lineStyle: {
              color: "#21ff72",
            },
            itemStyle: {
              color: "#21ff72",
            },
            data: hasData ? data.map((item) => item.vertical) : [0],
          },
        ],
      };
      this.myEcharts3.setOption(option);
    },

    initServer() {
      return this.echarts4Data.map((i, index) => {
        let serversData = {
          name: i.name,
          itemStyle: {
            opacity: 0,
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              color: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0.2,
                    color: "rgba(255, 255, 255)", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: i.color, // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "transparent", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              borderColor: i.color,
              borderWidth: 1,
            },
            scale: 3,
          },
          data: i.data,
          type: "line",
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: i.color, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "transparent", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },

          lineStyle: {
            color: i.color,
          },
        };
        if (index == 0) {
          let data = {
            data: [
              {
                yAxis: this.warnLineInfo.threshValue, // 固定值预警线
                name: "预警线",
              },
            ],
            lineStyle: {
              color: "#ff0000",
              width: 2,
              type: "dashed",
            },
            label: {
              formatter: this.warnLineInfo.text + ": {c}",
              position: "middle",
            },
          };
          serversData.markLine = data;
        }
        return serversData;
      });
    },
    search(item) {
      // 查看
      try {
        if (
          !item.driverList ||
          !item.driverList[0] ||
          !item.driverList[0].specialAuthBookJson
        ) {
          this.$Message.warning("暂无证书信息");
          return;
        }
        const specialAuthBookJson = item.driverList[0].specialAuthBookJson;
        let certList = [];

        if (typeof specialAuthBookJson === "string") {
          certList = JSON.parse(specialAuthBookJson);
        } else {
          certList = specialAuthBookJson;
        }

        // 确保是数组格式
        if (!Array.isArray(certList)) {
          certList = [certList];
        }
        getDict("special_book_type").then((res) => {
          if (res.code == "success") {
            this.Dict = res.data;
            this.currentCertList = certList;
            this.isshow = true;
            console.log(this.currentCertList);
          }
        });
      } catch (error) {
        console.error("解析证书数据失败:", error);
        this.$Message.error("证书数据格式错误");
      }
    },
    initDict(item) {
      let data = this.Dict.filter((i) => i.itemValue == item);
      return data[0].itemText;
    },
    showPic(item) {
      console.log(item);
      this.picShow = true;
      this.picData = {
        fiveChartContent: item.fiveChartContent,
        fiveChartImg: item.fiveChartImg,
        fiveChartTime: item.fiveChartTime,
        workPointName: item.workPointName,
        name: item.name,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.picContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;

  .pic {
    height: calc(100% - 90px);
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    overflow-y: auto;
  }
}

/deep/ .side-panel {
  background: linear-gradient(
    to right,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  );
  height: 100%;
  gap: 0px;
}

.side-panel-right {
  background: linear-gradient(
    to left,
    rgba(20, 40, 70, 0.95) 60%,
    rgba(20, 40, 70, 0.3) 100%
  ) !important;
  height: 100%;
  gap: 0px;
}

/deep/ .ivu-timeline-item-head {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/ProjectAnti/point.png) no-repeat center
    center;
  background-size: 100% auto;
  border: none;
  position: absolute;
  left: 0px;
  top: -2px;
}

/deep/ .ivu-timeline-item-tail {
  position: absolute;
  left: 10px;
  top: 5px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

/deep/ .ivu-timeline-item {
  padding: 0;
}

.cardContent {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  gap: 10px;

  .card {
    width: 35%;
    height: 100%;
    background: url(../../assets/images/ProjectCrane/border.png) no-repeat
      center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 99%;
      height: 100%;
      object-fit: contain;
    }
  }

  .content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .beforeTime {
      width: 100%;
      height: 40px;
      color: #fff;
      background: url(../../assets/images/ProjectCrane/titleBack.png) no-repeat
        center center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .beforeTime::before {
      content: "";
      display: inline-block;
      width: 25px;
      height: 25px;
      background: url(../../assets/images/ProjectCrane/errorIcon.png) no-repeat
        center center;
      background-size: contain;
      background-position-y: 2px;
      margin-right: 5px;
      margin-left: 2px;
    }

    .contentItem {
      width: 100%;
      height: 12.5%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      margin-bottom: 10px;
      padding-bottom: 10px;

      span:first-child {
        color: rgba(255, 255, 255, 0.5);
      }

      span:first-child::before {
        content: "";
        display: inline-block;
        vertical-align: middle;
        background-color: #fff;
        width: 5px;
        height: 5px;
        transform: rotate(45deg);
        margin-right: 7px;
      }

      span:last-child {
        color: #fff;
        font-weight: bold;
      }

      .line {
        width: 100%;
        height: 2px;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(255, 255, 255, 0.3) 50%,
          transparent 100%
        );
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .contentItem:last-child {
      .line {
        background: transparent !important;
      }
    }
  }
}

.timeConetent {
  width: 100%;
  height: 75px;
  display: flex;
  flex-direction: column;
  background: url(../../assets/images/ProjectAnti/warn.png) no-repeat center
    center;
  background-size: 100% 100%;
  color: #fff;

  .title {
    height: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 5px;
    height: 35px;
    align-items: center;

    span {
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }

    // span:last-child {
    //     width: 68px;
    //     height: 80%;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     color: #fff;
    //     background: url(../../assets/images/ProjectAnti/Button.png) no-repeat center center;
    //     background-size: 100% 100%;
    //     cursor: pointer;
    // }
  }

  .content {
    height: calc(75px - 35px);
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    div {
      width: 100%;
      text-align: left;
    }

    div::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      margin: 0 8px;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.floorFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom, transparent, #1d2d47);

  .floorItem {
    width: 48%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .echartsContents {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0 10px;

      .echarts {
        width: 100%;
        height: calc(100% - 30px);
      }
    }

    .warnNum {
      width: 100%;
      height: 88%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5%;

      .left {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .item {
          width: 100%;
          height: 48%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 4px;

          .icon {
            width: 100%;
            height: 30px;
            background-image: linear-gradient(
              to right,
              transparent 1%,
              #102f97 10%,
              transparent 100%
            );
            display: flex;
            align-items: center;
            color: #fff;
          }

          .icon::before {
            content: "";
            width: 20px;
            height: 20px;
            background: url(../../assets/images/ProjectCrane/iconLeft.png)
              no-repeat center center;
            background-size: contain;
            background-position-y: 3px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
          }

          .value {
            width: 80px;
            height: calc(100% - 30px - 10px);
            background: url(../../assets/images/ProjectCrane/warnNumBack.png)
              no-repeat center center;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 29px;
            color: #fff;
            padding-bottom: 25px;
            box-sizing: border-box;
          }
        }
      }

      .right {
        flex: 1;
        height: 100%;

        .timeLine {
          width: 100%;
          height: 100%;
          overflow-y: auto;

          .time {
            color: #fff;
          }
        }
      }
    }
  }
}

.project-map-page {
  width: 100%;
  height: calc(100% - 75px);
  margin-top: 75px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;

  .top-bar {
    width: 100vw;
    height: 100px;
    background: url("../../assets/images/ProjectMap/top-bg2.png") no-repeat
      center center;
    color: #fff;
    display: flex;
    padding: 0 40px;
    justify-content: space-between;
    /* 路径报错可能是由于 '@' 别名未正确配置，尝试使用相对路径或者检查别名配置。
    这里假设项目结构，可以使用相对路径来解决报错问题。 */

    .tabs {
      width: 100%;
      background: url("../../assets/images/ProjectMap/top-bg.png") no-repeat
        center center;
      background-size: cover;
      height: 87.14px;

      .tab-nav {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 33%;
        height: 82.73px;
        gap: 10px;

        .el-button--primary.is-plain {
          width: 136px;
          height: 30px;
          font-size: 16px;
          color: #fff;
          background: url(../../assets/images/ProjectMap/nav-bg.png) no-repeat
            center center;
          background-size: cover;
          border: none;
          border-radius: 0;
          /* 为按钮添加黑色阴影 */
          text-shadow: 0 0 10px rgba(0, 4, 4, 0.25);
        }

        .active {
          display: flex;
          align-items: center;
          position: relative;

          .el-button--primary.is-plain {
            background: url(../../assets/images/ProjectMap/nav-bg-active.png)
              no-repeat center center;
            background-size: cover;
            font-weight: bold;
          }

          &::before {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            position: absolute;
            left: 20px;
          }

          &::after {
            content: "";
            background: url(../../assets/images/ProjectMap/active-jt.png)
              no-repeat center center;
            background-size: cover;
            width: 6.26px;
            height: 6.26px;
            transform: rotate(90deg);
            position: absolute;
            right: 20px;
          }
        }
      }
    }
  }

  .main-content {
    position: relative;
    flex: 1;
    display: flex;
    height: calc(100% - 100px);
    width: 408px;
    .selectTower {
      position: absolute;
      top: 60px;
      right: -200px;
      z-index: 10000000000;
      pointer-events: auto; /* 明确指定可以点击 */
      .selectBox {
        position: relative;
        width: 180px;
        height: 45px;
        margin-bottom: 15px;
        cursor: pointer;
        .selecTowerName {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-20%, -50%);
          color: #ffffff;
          z-index: 10000002;
        }
        img.selectImg {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          z-index: 10000001;
        }
      }
      .unselectBox {
        position: relative;
        width: 160px;
        height: 25px;
        margin-bottom: 15px;
        transition: all 0.3s;
        cursor: pointer;
        .selecTowerName {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-40%, -50%);
          color: #ffffff;
          z-index: 99;
        }
        img.selectImg {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          z-index: 98;
        }

        &:hover {
          transform: scale(1.06);
        }
      }
    }
  }

  .right-content {
    position: relative;
    width: 408px;
    position: absolute;
    right: 0;
    top: 0;
    height: calc(100%);
    flex: 1;
    .selectMonitor {
      position: absolute;
      bottom: 5px;
      right: 415px;
      width: 40px;
      z-index: 10000000000;
      pointer-events: auto; /* 明确指定可以点击 */
      cursor: pointer;
      opacity: 0.7;
      transition: all 0.3s;
      .video {
        width: 100%;
        img {
          width: 100%;
        }
      }
      &:hover {
        opacity: 0.9;
        transform: scale(1.05);
      }
    }
  }

  .floor-content {
    width: calc(100% - 408px - 408px - 40px);
    position: absolute;
    height: 35%;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.s_title {
  width: 100%;
  height: 30px;
  background: url(../../assets/images/icon/s_title.png) no-repeat center center;
  background-size: 100% 100%;
  padding-left: 30px;
  display: flex;
  align-items: center;
  margin-top: -5px;
}

.padcontent {
  width: 100%;
  padding-top: 0;
  padding: 5px;
  box-sizing: border-box;
  margin-left: 5px;
}

.title-content {
  width: 100%;
  // height: 12vh; /* 减少高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  flex-shrink: 0;
  margin-bottom: 10px;

  .item {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-left {
      width: 20%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .item-right {
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: space-between;
      gap: 5px;

      .itemRight-item {
        width: 48%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .num {
          width: 100%;
          height: 55%;
          font-size: 25px;
          color: #fff;
          box-sizing: border-box;
          padding-left: 10px;
          display: flex;
          align-items: center;
        }

        .text {
          width: 100%;
          height: 43%;
          box-sizing: border-box;
          padding-left: 10px;
          display: flex;
          align-items: center;
          font-size: 13px;
        }
      }

      .itemRight-item:first-child {
        .num {
          background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
            center center;
          background-size: 100% 100%;
        }

        .text {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(9, 106, 224, 0.3) 50%,
            transparent
          );
        }
      }

      .itemRight-item:last-child {
        .num {
          background: url(../../assets/images/ProjectAnti/red.png) no-repeat
            center center;
          background-size: 100% 100%;
        }

        .text {
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(151, 0, 0, 0.3) 50%,
            transparent
          );
        }
      }
    }
  }
}
.myButton {
  width: 62px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center center;
  // background-size: 100% 100%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  border-radius: 3px;
}

.myButton.active {
  background: url(../../assets/images/ProjectAI/activeBack.png) no-repeat center
    center;
  background-size: 100% 100%;
}

.realData {
  width: 100%;
  // height: calc(100vh - 22vh - 10vh - 100px);
  background: url(../../assets/images/ProjectCrane/h617Card.png) no-repeat
    center center;
  background-size: 100% 100%;
  padding: 0px 8px;
  box-sizing: border-box;

  .title {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: column;

    .online {
      width: 100%;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        background: url(../../assets/images/ProjectCrane/icon.png) no-repeat
          center center;
        background-size: contain;
      }

      span:last-child {
        cursor: pointer;
        color: #184099;
      }

      img {
        width: 42px;
        height: auto;
        margin-top: 5px;
      }
    }

    .time {
      span:before {
        background: url(../../assets/images/icon/time.png) no-repeat center
          center !important;
        background-size: contain !important;
      }
    }
  }

  .worktime {
    width: 100%;
    height: 18%;
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 10px;

    .worktime-item {
      width: 47%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon {
        width: 50px;
        height: 50px;
        background: url(../../assets/images/ProjectCrane/leftIcon.png) no-repeat
          center center;
        background-size: contain;
      }

      .times {
        height: 100%;
        width: calc(100% - 40px - 10%);
        display: flex;
        flex-direction: column;
        gap: 5px;

        .text {
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
        }

        .hours {
          width: 100%;
          height: calc(100% - 30px);
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(
            to right,
            transparent,
            rgba(26, 44, 76, 0.9),
            transparent
          );
          text-shadow: 0 0 10px #20549b;
          color: #fff;
          font-size: 29px;
          font-weight: bold;

          sub {
            margin-left: 5px;
            margin-bottom: 2px;
            font-size: 16px;
          }
        }
      }
    }
  }

  .peopleDetail {
    width: 100%;
    height: 10vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 10px;
    gap: 10px;

    .profile {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .profileInfo {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .info {
        width: 100%;
        height: calc(100% / 4);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .titles {
          color: rgba(255, 255, 255, 0.5);
          white-space: nowrap;
        }

        .text {
          color: #fff;
          white-space: nowrap;
        }

        div.text {
          width: 62px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: url(../../assets/images/ProjectAI/activeBack.png)
            no-repeat center center;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }
    }
  }

  .information {
    width: 100%;
    height: 15vh;
    // height: calc(100% - 16vh - 18%);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 5px;
    overflow-y: auto;

    .item {
      width: 30%;
      // height: 50px;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .top {
        width: 100%;
        height: 45%;
        font-size: 14px;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(13, 41, 89, 0.563),
          transparent
        );
        display: flex;
        align-items: center;
        padding-left: 10px;
      }

      .bottom {
        width: 100%;
        height: 55%;
        background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }

    .item.active {
      .top {
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(179, 55, 55, 0.612),
          transparent
        ) !important;
      }

      .bottom {
        background: url(../../assets/images/ProjectAnti/red.png) no-repeat
          center center !important;
        background-size: 100% 100% !important;
      }
    }
  }
}

.information2 {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-y: auto;

  .item {
    width: 31%;
    height: 70px;
    display: flex;
    flex-direction: column;
    gap: 5px;

    .top {
      width: 100%;
      height: 45%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(13, 41, 89, 0.563),
        transparent
      );
      display: flex;
      align-items: center;
      padding-left: 10px;
    }

    .bottom {
      width: 100%;
      height: 55%;
      background: url(../../assets/images/ProjectAnti/blue.png) no-repeat center
        center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      box-sizing: border-box;
      padding-left: 15px;

      sub {
        margin-left: 5px;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .item.active {
    .top {
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(179, 55, 55, 0.612),
        transparent
      ) !important;
    }

    .bottom {
      background: url(../../assets/images/ProjectAnti/red.png) no-repeat center
        center !important;
      background-size: 100% 100% !important;
    }
  }
}

.statistics {
  width: 100%;
  height: 15vh;
  background: url(../../assets/images/ProjectCrane/h201Card.png) no-repeat
    center center;
  background-size: 100% 105%;
  background-position-y: 5%;
  overflow-y: scroll;
  .title {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    box-sizing: border-box;
    padding-left: 5px;

    .titleBox {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 5px;
    }
  }

  .title::before {
    content: "";
    background: url(../../assets/images/ProjectCrane/icon.png) no-repeat center
      center;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
  }

  .totalNum {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 8px;

    .num {
      width: 48%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 8px;

      div:first-child {
        color: transparent;
        // 渐变字体
        background-image: linear-gradient(to right, #adc6fa, #e2ebfd);
        background-image: -webkit-linear-gradient(to right, #adc6fa, #e2ebfd);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 18px;
      }

      div:first-child::before {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../../assets/images/icon/work.png) no-repeat center
          center;
        background-size: contain;
        vertical-align: middle;
        display: inline-block;
        text-align: center;
      }

      div:last-child {
        width: 70px;
        height: 65px;
        font-size: 25px;
        color: #fff;
        background: url(../../assets/images/icon/cardIcon.png) no-repeat center
          center;
        background-size: 80% auto;
        display: flex;
        justify-content: center;
        display: inline-block;
        vertical-align: top;
        text-align: center;

        span {
          font-size: 15px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  .time {
    width: 100%;
    // height: calc(100% - 38px - 45%);
    display: flex;
    justify-content: space-between;
    gap: 5px;
    align-items: center;
    margin-bottom: 5px;

    .item {
      width: 32%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .top {
        width: 100%;
        height: 35%;
        background-image: linear-gradient(
          to right,
          transparent,
          rgba(38, 82, 132, 0.4),
          transparent
        );
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hour {
        width: 100%;
        height: calc(100% - 55%);
        background: url(../../assets/images/ProjectAnti/blue.png) no-repeat
          center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        box-sizing: border-box;
        padding-left: 15px;

        sub {
          margin-left: 5px;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

.echartsContent {
  width: 100%;
  height: 26vh;
  display: flex;
  flex-direction: column;
  margin-top: -10px;
  margin-bottom: 5px;

  .echarts {
    width: 100%;
    height: calc(100% - 10%);
  }
}

.ehcharts-title {
  height: 20%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/ .ivu-select {
    width: 40%;
  }

  /deep/.ivu-select-selection {
    background-image: linear-gradient(to bottom, #223d71, #1d3051);
    border: 2px solid #2e5bbf;
    height: 30px;
    color: #fff;
  }

  /deep/ .ivu-select-placeholder {
    color: #fff;
  }

  /deep/ .ivu-select-arrow {
    color: #fff;
  }

  /deep/ .ivu-select-item {
    background-image: linear-gradient(
      to bottom,
      rgba(34, 61, 113, 3),
      rgba(29, 48, 81, 0.9)
    );
    color: #fff;
  }

  /deep/ .ivu-select-dropdown {
    padding: 0;
  }

  .btn {
    display: flex;
    gap: 10px;
  }
}

.monitorContent {
  height: calc(100vh - 25vh - 35vh - 10vh);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .monitor {
    height: 70px;
    width: 100%;
    box-sizing: border-box;
    padding: 5px;
    display: flex;
    flex-direction: column;
    background: url(../../assets/images/ProjectCrane/titleBack.png) no-repeat
      center center;
    background-size: 100% 100%;

    .text {
      color: #fff;
    }

    .text::before {
      content: "";
      width: 30px;
      height: 30px;
      display: inline-block;
      vertical-align: middle;
      background: url(../../assets/images/ProjectCrane/errorIcon.png) no-repeat
        center center;
      background-size: contain;
    }
  }

  .monitorNo {
    background: url(../../assets/images/ProjectCrane/titleBack2.png) no-repeat
      center center;
    background-size: 100% 100%;
    .text::before {
      background: url(../../assets/images/ProjectCrane/correctIcon.png)
        no-repeat center center;
      background-size: contain;
    }
  }

  .wind {
    width: 100%;
    height: 40px;
    background: url(../../assets/images/ProjectCrane/titleBlueBack.png)
      no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .windContent {
      display: flex;
      width: 45%;
      align-items: flex-end;
    }

    .windNum {
      // width: 110px;
      height: 100%;
      background-image: linear-gradient(
        to right,
        transparent,
        rgba(12, 52, 98, 0.9),
        transparent
      );
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .windNum::before {
      content: "";
      width: 20px;
      height: 20px;
      background: url(../../assets/images/ProjectCrane/windIcon.png) no-repeat
        center center;
      background-size: contain;
    }

    .num {
      // width: 110px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      background: url(../../assets/images/ProjectAnti/blue.png) no-repeat center
        center;
      background-size: 100% 100%;
    }
  }

  .echarts {
    width: 100%;
    height: calc(100% - 60px);
  }
}

.timeLine {
  height: calc(100vh - 12vh - 12vh - 150px - 380px);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保不会超出边界 */
  min-height: 300px; /* 设置最小高度 */
  .timeline-scroll-container {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
    /* 移除smooth滚动以提高性能 */
    scroll-behavior: auto;
    /* 启用硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    /* 鼠标悬停效果 */
    transition: background-color 0.3s ease;
    .timeline-scroll-content {
      width: 100%;
      // height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 8px; /* 减少间距 */
      padding-right: 10px;
      padding-bottom: 20px; /* 增加底部内边距，确保最后一条数据完全显示 */
      /* 移除动画，改用JavaScript控制滚动 */
      margin-bottom: 50px;
    }
  }

  /* 鼠标悬停时的视觉反馈 */
  .timeline-scroll-container:hover {
    background-color: rgba(255, 255, 255, 0.02);
  }

  /* Webkit浏览器滚动条样式 */
  .timeline-scroll-container::-webkit-scrollbar {
    width: 6px;
  }

  .timeline-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .timeline-scroll-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .timeline-scroll-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

.beforeChartsTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>

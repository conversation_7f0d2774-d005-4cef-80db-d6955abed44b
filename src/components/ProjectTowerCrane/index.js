// 近一周
export function getLastWeekDateRange() {
  const dates = [];
  const today = new Date();
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dates.push(`${year}-${month}-${day}`);
  }
  
  return {
    startDate: dates[dates.length - 1], // 最早日期（一周前）
    endDate: dates[0],                 // 最晚日期（今天）
    allDates: dates                    // 包含所有7个日期的数组
  };
}

// 近一月
export function getLast30DaysRange() {
  const dates = [];
  const today = new Date();
  
  for (let i = 0; i < 30; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dates.push(`${year}-${month}-${day}`);
  }
  
  return {
    startDate: dates[dates.length - 1], // 最早日期（30天前）
    endDate: dates[0],                 // 最晚日期（今天）
    allDates: dates                    // 包含所有30个日期的数组
  };
}
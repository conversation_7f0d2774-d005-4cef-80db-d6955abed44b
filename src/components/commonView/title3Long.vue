<template>
    <div class="title3-long">
        <div class="title3-long-bg"></div>
        <img src="@/assets/images/components/title3_hige.png" class="title3-long-bg1" alt="" />
        <div class="title-icon">
            <img src="@/assets/images/components/title_icon1.png" alt="" />
        </div>
        <div class="text-value">{{ title }}</div>
    </div>
</template>
<script>
export default {
    name: 'title3Long',
    props: {
        title: {
            type: String,
            default: ''
        }
    }
}
</script>
<style lang="less" scoped>
@w: 1.98vh;
.title3-long{
    display: flex;
    align-items: flex-start;
    height: 2.407vh;
    margin-bottom: 0.74vh;
    position: relative;
    z-index: 1;
    padding-left: 1.11vh;
    .title3-long-bg{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: linear-gradient(89.99deg, #00BCFB 0%, #0050FB 9.43%, rgba(0, 81, 255, 0) 98.63%);
        clip-path: polygon(@w 0, 100% 0, 100% 100%, 0 100%);
    }
    .title3-long-bg1{
        position: absolute;
        left: 0;
        bottom: 0;
        height: 1.66vh;
        width: auto;
    }
    .title-icon{
        width: 2vh;
        margin-right: 0.74vh;
        position: relative;
        z-index: 5;
        img{
            width: 100%;
            display: block;
        }
    }
    .text-value{
        position: relative;
        z-index: 5;
        font-size: 1.48vh;
        color: #fff;
        font-weight: bold;
        line-height: 2.2vh;
        text-shadow: 0px 4px 4px rgba(12, 12, 12, 0.5);
    }
}
</style>
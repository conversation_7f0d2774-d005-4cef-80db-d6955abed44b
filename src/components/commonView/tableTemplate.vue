<template>
  <div class="rank-template">
    <div v-if="showHeader" class="rank-head">
      <div
        class="th"
        v-for="(item, index) in columns"
        :style="thStyle(item)"
        :key="index"
      >
        <div class="cell">{{ item.title }}</div>
      </div>
    </div>
    <swiper
      :options="swiperOptions"
      :style="{ height: height }"
      ref="mySwiper"
      class="rank-swiper"
      v-if="list.length > 0"
    >
      <swiper-slide v-for="(item, index) in list" :key="index" class="rank-tr">
        <div
          class="rank-td"
          v-for="(c, cindex) in columns"
          :style="thStyle(c)"
          :class="{ tooltip: c.tooltip }"
          :key="cindex"
        >
          <div v-if="c.type === 'index'" class="cell">
            <div class="td-index">
              <span>{{ index + 1 }}</span>
            </div>
          </div>
          <div v-else class="cell" :style="tdStyle(c)">
            <template v-if="c.slot">
              <slot :name="c.slot" :row="item" :index="index"></slot>
            </template>
            <template v-else>
              <TooltipAutoShow v-if="c.tooltip" :content="item[c.key]" />
              <div v-else>{{ item[c.key] }}</div>
            </template>
          </div>
        </div>
      </swiper-slide>
    </swiper>
    <no-data v-else :style="{ height: height }" />
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
export default {
  name: "tableTemplate",
  components: {
    Swiper,
    SwiperSlide,
  },
  props: {
    columns: {
      default() {
        return [
          { type: "index", width: "6.4vh" },
          {
            title: "值",
            key: "value",
            align: "right",
            tooltip: true,
            color: "",
          },
        ];
      },
    },
    list: {
      default() {
        return [];
      },
    },
    height: { default: "23.425vh" },
    slidesPerView: { default: 6.2 },
    showHeader: { default: true }, // 是否显示表头
    color: { default: "" }, // td文字的颜色
  },
  data() {
    return {
      swiperOptions: {
        direction: "vertical",
        slidesPerView: this.slidesPerView,
        loop: false,
        observer: true,
        observeParents: true,
        speed: 600,
        autoHeight: true,
        autoplay: {
          disableOnInteraction: false,
          delay: 5000,
        },
      },
    };
  },
  methods: {
    tdStyle(item) {
      let obj = {};
      if (this.color) {
        obj["color"] = this.color;
      }
      if (item.color) {
        obj["color"] = item.color;
      }
      return obj;
    },
    thStyle(item) {
      let obj = {};
      if (item.width) {
        obj.width = item.width;
      } else {
        obj["flex"] = 1;
      }
      if (item.align) {
        obj["text-align"] = item.align;
      }
      return obj;
    },
  },
};
</script>

<style lang="less" scoped>
.rank-template {
  color: #fff;
  .rank-head {
    display: flex;
    background: rgba(184, 203, 255, 0.16);
    border-bottom: 1px solid rgba(255, 255, 255, 0.13);
    height: 2.96vh;
    .th {
      font-family: OPPOSans;
      font-size: 1.48vh;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .rank-swiper {
    .rank-tr {
      display: flex;
      align-items: center;
      height: 3.7vh;
      .cell {
        padding: 0 0.37vh;
        font-family: OPPOSans;
        font-size: 1.48vh;
        font-weight: 400;
        line-height: 1.85vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .td-index {
        font-family: "OPPOSans";
        font-size: 1.48vh;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2.59vh;
        height: 2.22vh;
        text-align: center;
        line-height: 2.22vh;
        background: linear-gradient(
          360deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 255, 255, 0) 100%
        );
        padding: 0 0.37vh;
        position: relative;
        &::before {
          content: "";
          width: 1px;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          background: rgba(255, 255, 255, 0.2);
        }
        &::after {
          content: "";
          width: 0.37vh;
          height: 0.37vh;
          position: absolute;
          top: 0;
          left: 0;
          border-top: 1px solid #fff;
          border-left: 1px solid #fff;
        }
      }
      &:nth-child(2n + 1) {
        background: rgba(63, 72, 98, 0.27);
      }
      .tooltip {
        overflow: hidden;
      }
    }
  }
}
</style>

<template>
    <div v-if="previewUrl" class="preview-file">
		<div v-if="type === 'pdf'" class="pdf-preview">
			<vue-office-pdf :src="previewUrl" style="height: 100%;"></vue-office-pdf>
		</div>
		<div v-if="type === 'img'" class="img-preview">
			<img :src="previewUrl" alt="">
		</div>
	</div>
</template>
<script>
import VueOfficePdf from '@vue-office/pdf'
export default {
    name: 'PreviewFile',
    components: {
        VueOfficePdf
    },
    props: {
		fileUrl: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			type: '',
			previewUrl: ''
		}
	},
	watch: {
		fileUrl: {
			handler(newVal) {
				this.type = this.getFormatByUrl(newVal)
				this.replacePreviewUrl(newVal)
			},
			immediate: true
		}
	},
	methods: {
		// 替换预览时文件地址
		replacePreviewUrl(url) {
			if (!url) {
				this.previewUrl = ''
				return ''
			}
			let reg = /^http(s)?:\/\/([\w-]+\.)+[\w-]+:\w{1,6}/g
			let host = `${location.origin}`
			if (location.hostname === 'localhost') {
				this.previewUrl = url.replace(reg, host)
				return
			}
			this.previewUrl = url
		},
		getFormatByUrl() {
			if (!this.fileUrl) return ''
			const format = this.fileUrl.split('.').pop().toLocaleLowerCase() || '';
			if (['jpg', 'png', 'jpeg'].indexOf(format) > -1) {
				return 'img'
			}
			if (['pdf'].indexOf(format) > -1) {
				return 'pdf'
			}
			return '';
		},
	}
}
</script>
<style lang="less" scoped>
.img-preview,
.pdf-preview,
.preview-file{
    width: 100%;
    height: 100%;
}
.img-preview{
    img{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}
</style>
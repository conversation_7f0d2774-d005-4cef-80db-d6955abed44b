<template>
  <div class="title5">
    <div class="dec-img">
      <img src="@/assets/images/civilAirDefense/box_bg3.png" alt="" />
    </div>
    <div class="title5-rect"></div>
    <div class="title5-text">{{ title }}</div>
    <div class="line"></div>
    <div v-if="tabList &&tabList.length > 0" class="tab-list">
      <div v-for="(item, index) in tabList" :class="{active: currentIndex === index}" @click="handleTabClick(index, item)" :key="index" class="tab-item">{{ item.name }}</div>
    </div>
    <div v-else-if="time" class="time">
      <Icon class="timeIcon" type="md-time" />
      <div class="text">{{ time }}</div>
    </div>
    <div v-else class="dec-arrow">
      <img src="@/assets/images/civilAirDefense/dec_arrow.png" alt="">
    </div>


  </div>
</template>
<script>
export default {
  name: "title5",
  props: {
    title: {
      type: String,
      default: "",
    },
    time: {
      type: String,
      default: "",
    },
    tabList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  methods: {
    handleTabClick(index, item) {
      this.currentIndex = index
      this.$emit('change', index, item)
    }
  }
};
</script>
<style lang="less" scoped>
.title5 {
  display: flex;
  align-items: center;
  margin-bottom: 0.37vh;
  background: linear-gradient(
    93.45deg,
    rgba(16, 69, 126, 0) 10.88%,
    rgba(16, 69, 125, 0.7) 21.24%,
    rgba(0, 37, 78, 0) 65.87%
  );
  position: relative;
  z-index: 1;
  height: 2.4vh;
  padding-left: 1.85vh;
  column-gap: 0.74vh;
  .dec-img {
    position: absolute;
    width: 2.4vh;
    left: 0.648vh;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
    img {
      width: 100%;
      display: block;
    }
  }
  .title5-rect {
    width: 0.55vh;
    height: 0.55vh;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(194, 233, 255, 1);
    border-radius: 1px;
    box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
    transform: rotate(-135deg);
  }
  .title5-text {
    font-size: 1.203vh;
    background: linear-gradient(180deg, #ffffff 0%, #c5e5ff 100%);
    -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
    -webkit-text-fill-color: transparent; /*给文字设置成透明*/
  }
  .line {
    flex: 1;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }
  .tab-list{
    display: flex;
    align-items: center;
    .tab-item{
      font-size: 1.29vh;
      color: rgba(255, 255, 255, 0.6);
      background: linear-gradient(270deg, rgba(47, 47, 47, 0) 0%, rgba(98, 98, 98, 0.15) 50%, rgba(47, 47, 47, 0) 100%);
      min-width: 4.6vh;
      padding: 0 0.74vh;
      line-height: 2.4vh;
      text-align: center;
      cursor: pointer;
      &.active{
        color: rgba(255, 255, 255, 1);
        background: linear-gradient(90deg, rgba(0, 32, 88, 0) 2.08%, rgba(0, 77, 255, 0.62) 50.63%, rgba(0, 32, 88, 0) 100%);
      }
    }
  }
  .dec-arrow {
    width: 1.2vh;
    img {
      width: 100%;
      display: block;
    }
  }

  .time {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    opacity: 0.8;
    .timeIcon {
      position: relative;
      bottom: 1px;
    }
    .text {
      margin-left: 2px;
    }
  }
}
</style>

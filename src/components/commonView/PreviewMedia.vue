<template>
  <div class="PreviewMedia">
    <iframe
      :src="mediaUrl"
      frameborder="0"
      charset="utf-8"
      style="width: 100%; height: 100%"
      class="midea_table"
      scrolling="auto"
      ref="iframe"
    >
    </iframe>
  </div>
</template>
<script>
export default {
  name: 'PreviewMedia',
  data() {
    return {
      mediaUrl: '',
    };
  },
  props: ['layerid', 'mediaObject'],
  created() {
    this.mediaUrl = this.mediaObject;
  },
  mounted() {
  },
  methods: {
    initIframe() {
      let iframeBox = this.$refs.iframe;
      let img = iframeBox.contentWindow.document.querySelect('img');
      img.style.height = '100%';
    },
  },
};
</script>

<style lang="less" scoped>
.PreviewMedia {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  .midea_table {
    text-align: center;
  }
}
</style>

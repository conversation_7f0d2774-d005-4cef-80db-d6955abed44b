<template>
    <div class="title4">    
        <div class="title4-rect"></div>
        <div class="title4-text">{{ title }}</div>
    </div>
</template>
<script>
export default {
    name: 'title4',
    props: {
        title: {
            type: String,
            default: ''
        }
    }
}
</script>
<style lang="less" scoped>
.title4{
    display: flex;
    align-items: center;
    margin-bottom: 0.74vh;
    .title4-rect{
        width: 0.55vh;
        height: 0.55vh;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(194, 233, 255, 1);
        border-radius: 1px;
        box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
        transform: rotate(-135deg);
        margin-right: 0.74vh;
    }
    .title4-text{
        font-size: 1.203vh;
        background: linear-gradient(180deg, #FFFFFF 0%, #C5E5FF 100%);
        -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;/*给文字设置成透明*/
    }
}
</style>
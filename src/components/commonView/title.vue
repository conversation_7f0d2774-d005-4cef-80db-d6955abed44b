<template>
  <div class="title">
    <div class="section-title">
      <div>
        <img src="../../assets/images/components/title_icon.png" alt="">
        <span style="color: white;">{{ title }}</span>
      </div>
      <span class="englishTitle" style="color: white;" v-html="desc"></span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Title',
  props: {
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 20px;
  background: url(../../assets/images/components/title_bg.png) no-repeat center center;
  background-size: cover;
  .section-title {
    font-size: 17px;
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    .englishTitle {
      font-size: 14px;
      position: absolute;
      top: 10px;
      left: 200px;
    }
  }
}
</style>
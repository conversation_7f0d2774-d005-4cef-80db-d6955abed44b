<template>
    <div class="rank-template">
      <swiper v-if="list.length > 0" :options="swiperOptions" :style="{height: height}" ref="mySwiper" class="rank-swiper">
        <swiper-slide v-for="(item, index) in list" :key="index" class="rank-tr">
          <div class="rank-box">
            <div class="top">
              <div class="name">{{ item.name }}</div>
              <div class="num"><span>{{ item.num }}</span>{{ unit }}</div>
            </div>
            <div class="progress">
              <div :style="{ width: item.rate + '%' }" class="progress-bar">
                <div class="point"></div>
              </div>
              <div class="progress-has"></div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <no-data v-else :style="{ height: height }" />
    </div>
  </template>
  
  <script>
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
  import 'swiper/css/swiper.min.css';
  export default {
    name: 'rankTemplate',
    components: {
      Swiper, SwiperSlide
    },
    props: {
      list: { default() { return [] } },
      height: { default: '21.9vh' },
      slidesPerView: { default: 6 },
      unit: { default: '' },
    },
    data() {
      return {
        swiperOptions: {
          direction: 'vertical',
          slidesPerView: this.slidesPerView,
          loop: false,
          observer: true,
          observeParents: true,
          speed: 600,
          autoHeight: true,
          autoplay: {
            disableOnInteraction: false,
            delay: 5000
          }
        }
      }
    },
    methods: {
    }
  }
  </script>
  
  <style lang="less" scoped>
  .rank-template{
    background: linear-gradient(270deg, rgba(75, 131, 183, 0) 2.23%, rgba(75, 131, 183, 0.04) 46.51%, rgba(75, 131, 183, 0) 100%);

    .rank-box{
      font-size: 1.29vh;
      line-height: 1.85vh;
      padding: 0.37vh 0.74vh;
      color: #fff;
      .top{
        display: flex;
        align-items: center;
        margin-bottom: 0.37vh;
        .name{
          margin-right: 0.74vh;
          flex: 1;
        }
        .num{
          color: rgba(255, 255, 255, 0.8);
          font-weight: 400;
          text-align: right;
          font-size: 1.11vh;
          span{
            font-size: 1.48vh;
            font-family: YouSheBiaoTiHei;
            color: #fff;
            margin-right: 0.37vh;
          }
        }
      }
      .progress{
        position: relative;
        height: 0.55vh;
        display: flex;
        align-items: center;
        .progress-bar{
          height: 0.37vh;
          position: relative;
          background: linear-gradient(270deg, #4ACBFA 0%, #003CFF 100%);
          box-shadow: 0px 0px 4px 0px rgba(0, 132, 255, 0.5);
          border-radius: 0.185vh;
          .point{
            position: absolute;
            width: 1.48vh;
            height: 1.48vh;
            right: -0.74vh;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 50%;
            background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.82) 0%, rgba(255, 255, 255, 0) 100%);
            box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
            display: flex;
            align-items: center;
            justify-content: center;
            &::before{
                content: '';
                width: 0.55vh;
                height: 0.55vh;
                box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.25);
                border-radius: 50%;
                background: #fff;
            }
          }
        }
        .progress-has{
            flex: 1;
            height: 100%;
            background: rgba(203, 203, 203, 1);
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 1) inset;
            border-radius: 0.37vh;
            opacity: 0.15;
        }
      }
    }
    .rank-tr{
      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3){
        .rank-box .top .td-index{
          background: linear-gradient(270deg, #FFD18D 0%, rgba(140, 119, 165, 0.48) 46.92%, rgba(94, 83, 174, 0.31) 96.08%);
          border-radius: 1px;
          border: 1px solid #fff;
          border-image: linear-gradient(270deg, rgba(219, 221, 255, 0.79) 0%, rgba(84, 74, 119, 0.91) 37.04%) 1;
          color: #fff;
          font-weight: 700;
        }
      }
    }
  }
  </style>
  
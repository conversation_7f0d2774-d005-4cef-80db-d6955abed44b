<template>
    <div class="echarts-tem" ref="myEcharts" :style="{height: height}"></div>
</template>

<script>
import * as echarts from 'echarts';
export default {
    name: 'AEcharts',
    props: {
        options: { default() { return {} } },
        replaceMerge: { default: false },
        height: { default: '' }
    },
    data() {
        return {}
    },
    watch: {
        options: {
            handler(obj) {
                if (!this.isObjEmpty(obj) && this.echart) {
                    if (this.replaceMerge) {
                        this.echart.setOption(obj, {
                            replaceMerge: ['xAxis', 'yAxis', 'series']
                        });
                    } else {
                        this.echart.setOption(obj, true);
                        this.$emit('on-load', this.echart)
                    }
                    this.$nextTick(() => this.resize())
                }
            },
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.isObjEmpty(this.options)) {
                setTimeout(() => {
                    this.initEchart()
                    this.$emit('on-load', this.echart)
                }, 500)

            }
        })
        window.addEventListener('resize', () => {
            if (this.setime) {
                clearTimeout(this.setime)
                this.setime = null
            }
            if (this.$refs['myEcharts'] && this.$refs['myEcharts'].firstChild) {
                this.$refs['myEcharts'].firstChild.setAttribute('style', '')
            }
            this.setime = setTimeout(this.resize, 500)
        })
    },
    beforeDestroy() {
        this.dispose()
    },
    methods: {
        initEchart() {
            if (this.echart) {
                this.echart = null;
            }
            this.echart = echarts.init(this.$refs['myEcharts']);
            this.echart.setOption(this.options, true);
            this.$nextTick(() => this.resize())
        },
        dispatchAction(action) {
            if (this.echart) {
                this.echart.dispatchAction(action);
            }
        },
        resize() {
            if (this.echart) {
                this.echart.resize()
            }
        },
        dispose() {
            if (this.echart) {
                this.echart.dispose()
            }
        },
        isEmpty(val) {
    return val === undefined || val === null || val == '' || val.length === 0;
  },
  isObjEmpty(obj) {
    return this.isEmpty(Object.keys(obj));
  },
    }
}
</script>

<style lang="less" scoped>
.echarts-tem{
    width: 100%;
    height: 100%;
    position: relative;
}
</style>

<template>
  <div class="header">
    <div class="navList">
      <div class="left">
        <div class="title">{{ title }}</div>
      </div>
      <!-- 只有当有菜单时才显示菜单区域 -->
      <div class="center" v-if="menuList.length > 0">
        <div class="header-menu">
          <!-- 加载状态 -->
          <div v-if="loading" class="menu-loading">
            <i class="el-icon-loading"></i>
            <span>菜单加载中...</span>
          </div>
          
          <!-- 菜单列表 -->
          <el-button
            v-for="(item, index) in menuList"
            :key="index"
            :class="item.active ? 'active' : ''"
            class="menu-btn"
            @click="switchMenu(item)"
          >
            <img
              :src="
                require(`../../assets/images/pointScreen/header/${
                  item.active ? 'menu_icon_active.png' : 'menu_icon.png'
                }`)
              "
              class="menu-icon"
            />
            {{ item.name }}
          </el-button>
        </div>
      </div>
      
      <div class="right">
        <div class="header-info">
          <div class="header-weather">
            <div class="header-weather-left">
              <img src="../../assets/images/icon/weather_dy.png" alt="" />
              {{ weather }}
            </div>
            <div class="header-weather-right">{{ celsius }}<span>℃</span></div>
          </div>
          <div class="header-datetime">
            <span>{{ currentDate }}</span>
            <span>{{ currentTime }}</span>
          </div>
        </div>
        <div class="goBack-sys" @click="goBackSys">返回后台</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  goToBackend,
  getCurrentEnvironmentInfo,
} from "../../utils/backendConfig.js";
import { getRequest } from "@/utils/axios";
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: "screen-header",
  data() {
    return {
      title: "",
      weather: "多云",
      celsius: 32,
      currentDate: "",
      currentTime: "",
    };
  },
  computed: {
    ...mapState('menu', ['loading']),
    ...mapGetters('menu', ['menuList', 'activeMenu']),
  },
  mounted() {
    // 获取项目信息
    this.getProjectInfo();

    // 初始化时间
    this.updateTime();
    // 每秒更新时间
    setInterval(this.updateTime, 1000);
    console.log(this.$store.state.menu.menuList);
    console.log(this.$store.state.menu.activeMenu);

    // 打印当前环境信息（用于调试）
    console.log("当前环境信息:", getCurrentEnvironmentInfo());
  },
  methods: {
    ...mapActions('menu', ['fetchUserMenuPrivilege']),
    
    getProjectInfo() {
      // 直接使用utils/axios中的getRequest方法
      getRequest('/index/project')
        .then(response => {
          if (response && response.code === 'success' && response.data) {
            const projectName = response.data.platformProjectName || '项目'
            this.title = `${projectName}`
            this.$store.dispatch('user/setProjectInfo', response.data)
          }
        })
        .catch((error) => {
          console.error("获取项目信息失败:", error);
        });
    },
    switchMenu(item) {
      this.$store.commit("menu/setActiveMenu", item);
    },
    updateTime() {
      // 实现时间更新逻辑
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentDate = `${year}-${month}-${day}`;
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },
    goBackSys() {
      // 使用配置工具跳转到后台管理系统
      goToBackend();
    },
    
    // 手动刷新菜单
    async refreshMenu() {
      try {
        await this.fetchUserMenuPrivilege();
        this.$message.success('菜单刷新成功');
      } catch (error) {
        this.$message.error('菜单刷新失败');
        console.error('菜单刷新失败:', error);
      }
    }
  },
  beforeDestroy() {
    // 清除定时器
    clearInterval(this.updateTime);
  },
};
</script>

<style lang="less" scoped>
.header {
  width: 100%;
  background: linear-gradient(135deg, #0a1a2a 60%, #1a3a6a 100%);
  color: #fff;
  font-family: "Microsoft YaHei", Arial, sans-serif;

  .navList {
    width: 100%;
    height: 76px;
    background: url(../../assets/images/pointScreen/top_bg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    .left {
      position: absolute;
      top: 10px;
      left: 20px;
      .title {
        font-family: "YouSheBiaoTiHei";
        font-size: 28px;
        // font-weight: bold;
        letter-spacing: 2px;
        color: #fff;
        text-shadow: 0 0 8px #1e90ff88;
      }
    }
    .center {
      position: absolute;
      top: 20px;
      left: 32vw;
      .header-menu {
        flex: 1;
        display: flex;
        justify-content: center;

        .menu-loading {
          display: flex;
          align-items: center;
          color: #fff;
          font-size: 16px;
          
          i {
            margin-right: 8px;
            font-size: 18px;
          }
        }

        .menu-btn {
          width: 200px;
          height: 45px;
          font-family: "PingFang SC";
          font-size: 24px;
          font-weight: bold;
          border-radius: 0 0 8px 8px;
          background: url(../../assets/images/pointScreen/header/menu_bg.png)
            no-repeat;
          background-size: 100% 100%;
          border: none;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 -4px;
          padding: 0;
          .menu-icon {
            width: 22px;
            height: auto;
            display: inline-block;
            transition: all 0.3s ease;
          }
        }

        .active {
          background: url(../../assets/images/pointScreen/header/menu_bg_active.png)
            no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    .right {
      position: absolute;
      top: 20px;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      height: 36px;
      
      .header-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 14px;
        color: #fff;
        align-items: flex-end;
        height: 28px;

        .header-weather {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 115px;
          font-size: 14px;
          height: 16px;
          .header-weather-left {
            display: flex;
            align-items: center;
            img {
              margin-right: 5px;
              width: 14px;
              height: 14px;
            }
          }
          .header-weather-right {
            font-size: 14px;
          }
        }
        .header-datetime {
          display: flex;
          gap: 10px;
          font-size: 14px;
          color: #fff;
          height: 16px;
          align-items: center;
        }
      }
      .goBack-sys {
        width: auto;
        display: inline-block;
        text-align: right;
        font-weight: bold;
        cursor: pointer;
        padding: 0 20px 0 0;
        color: #fff;
        font-size: 16px;
        line-height: 20px;
        user-select: none;
        white-space: nowrap;
        height: 20px;
      }
    }
  }
}
</style>

.person-label-3d{
    position: absolute;
    .person-alarm-3d{
        background: linear-gradient(90deg, rgba(84, 0, 1, 0.75) 0%, rgba(68, 0, 0, 0.75) 100%);
        border-radius: 0.18vh;
        font-size: 1.29vh;
        padding: 0.37vh 1.48vh;
        line-height: 1.66vh;
        color: #fff;
        margin-bottom: 0.74vh;
        .person-alarm-item-3d{
            padding: 0.37vh 0;
        }
    }
    .person-button-3d{
        display: flex;
        align-items: center;
        column-gap: 0.74vh;
        .person-button-item-3d{
            background: linear-gradient(180deg, rgba(0, 229, 255, 0.5) 0%, rgba(0, 126, 116, 0.5) 50.96%, rgba(0, 62, 69, 0.5) 100%);
            border: 1px solid rgba(121, 242, 255, 1);
            border-radius: 0.18vh;
            font-size: 1.29vh;
            padding: 0.37vh 0.74vh;
            line-height: 1.66vh;
            color: #fff;
            text-shadow: 0 0 0.74vh rgba(0, 0, 0, 0.25);
            cursor: pointer;
        }
    }
}

// 高支模告警标签样式
.highFormwork-label-3d{
    position: absolute;
    width: 29.6vh;
    .highFormwork-close{
        position: absolute;
        right: 0.74vh;
        top: 0;
        color: #fff;
        cursor: pointer;
        font-size: 1.85vh;
    }
    .highFormwork-alarm-3d{
        width: 100%;
        height: 10.648vh;
        display: flex;
        flex-direction: column;
        background: url(../assets/images/civilAirDefense/alarm_bg1.png) no-repeat center top;
        background-size: 100% 100%;
        color: #fff;

        .highFormwork-alarm-title-3d{
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 0.74vh;
            height: 2.59vh;
            align-items: center;
            font-size: 1.29vh;
        line-height: 1.85vh;
            .img{
                width: 1.48vh;
                height: 1.48vh;
                margin-right: 0.37vh;
                background: url(../assets/images/ProjectAnti/warnIcon.png) no-repeat center center;
                background-size: contain;
            }
        }
        .highFormwork-alarm-item-3d{
            padding: 0.74vh;
            font-size: 1.11vh;
        line-height: 1.66vh;
            .txt {
                width: 100%;
                text-align: left;
                margin-bottom: 0.74vh;
                &:last-child{
                    margin-bottom: 0;
                }
                &::before {
                    content: "";
                    display: inline-block;
                    width: 0.37vh;
                    height: 0.37vh;
                    background-color: #fff;
                    margin: 0 0.74vh;
                    transform: rotate(45deg);
                    vertical-align: middle;
                }
            }
        }
    }
}


// 塔吊雷达标签样式
.radar-label {
    position: absolute;
    pointer-events: auto;
    z-index: 1000;

    .radar-label-content {
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        opacity: 1 !important;
        visibility: visible !important;

        .radar-label-content-name {
            color: #fff;
            font-family: 'HuXiaoBo_KuHei', 'Microsoft YaHei', sans-serif;
            white-space: nowrap;
        }

        img {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    }

    // 确保离线状态下标签也显示
    &.radar-label-north,
    &.radar-label-east {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    // 告警标签样式
    &.radar-label-alarm {
        display: block !important;
        opacity: 0.9 !important;
        visibility: visible !important;
        z-index: 1001;

        .radar-label-content {
            background: linear-gradient(90deg, rgba(84, 0, 1, 0.75) 0%, rgba(68, 0, 0, 0.75) 100%) !important;
            border-radius: 6px;
            padding: 10px;
            opacity: 0.8 !important;
            animation: alarmBlink 2s infinite;

            .radar-label-content-name {
                color: #fff;
                font-size: 14px;
                font-family: 'HuXiaoBo_KuHei';
                margin: 4px 0;
            }
        }
    }
}

// 告警闪烁动画
@keyframes alarmBlink {
    0%, 50% {
        opacity: 0.8;
    }
    25%, 75% {
        opacity: 1;
    }
}

// 设备图标样式
.device-icon {
    position: absolute;
    pointer-events: auto;
    transition: transform 0.2s ease;

    &:hover {
        transform: translate(-50%, -50%) scale(1.1);
    }

    // 复合图标容器样式
    .device-icon-container {
        position: relative;
        transition: all 0.2s ease;
        border-radius: 4px;
        overflow: hidden;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    // 兼容单图标样式
    img {
        transition: all 0.2s ease;
        border-radius: 4px;
        // box-shadow: 0 2px 8px rgba(0,0,0,0.2);

        // &:hover {
        //     box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        // }

        &:active {
            transform: scale(0.95);
        }
    }
}

// 设备标签样式
.radar-label-device {
    .radar-label-content {
        min-width: auto;
        width: auto;
        text-align: center;

        .radar-label-content-name {
            white-space: nowrap;
            // 移除文本截断，完全显示设备名称
            // overflow: hidden;
            // text-overflow: ellipsis;
            // max-width: 120px;
        }
    }
}

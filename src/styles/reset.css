/* 清除内外边距 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  overflow: hidden;
}
#app {
  min-height: 100vh;
  position: relative;
}
a {
  text-decoration: none;
  color: inherit;
}
ul,
ol {
  list-style: none;
}
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.side-panel {
  width: 414px;
  background: rgba(20, 40, 70, 0.95);
  color: #fff;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-right: 0;
}
.custom-html {
  pointer-events: none;
  /* 防止HTML元素阻挡鼠标事件 */
  transform: translate(-50%, -100%);
  /* 使元素中心对准坐标点 */
}

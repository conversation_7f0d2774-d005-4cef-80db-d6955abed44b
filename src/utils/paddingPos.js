let list = [
    {
        lng: 123,
        lat: 23,
    },
    {
        lng: 124,
        lat: 24,
    },
    {
        lng: 125,
        lat: 25,
    }
]

export function computedCenter(list, viewer, offest_height) {
    let maxLng = null; // 经度最大值
    let minLng = null; //经度最小值
    let maxLat = null; //维度最大值
    let minLat = null; //维度最小值
    function init(lng, lat) {
        if (maxLng === null) {
            maxLng = lng;
        }
        if (minLng === null) {
            minLng = lng;
        }
        if (maxLat === null) {
            maxLat = lat;
        }
        if (minLat === null) {
            minLat = lat;
        }
    }
    for (let i = 0; i < list.length; i++) {
        let item = list[i];
        const { lng, lat } = item;
        //第一次的时候初始化
        if (i === 0) {
            init(lng, lat);
            continue;
        }
        if (lng > maxLng) {
            maxLng = lng;
        } else if (lng <= minLng) {
            minLng = lng;
        }
        if (lat > maxLat) {
            maxLat = lat;
        } else if (lat <= minLat) {
            minLat = lat;
        }
    }
    // if (padding.length === 1) {
    //     maxLng += padding[0]
    //     minLng += padding[0]
    //     maxLat += padding[0]
    //     minLat += padding[0]
    // } else if (padding.length === 2) {
    //     maxLng += padding[1]
    //     minLng += padding[1]
    //     maxLat += padding[0]
    //     minLat += padding[0]
    // } else if (padding.length === 4) {
    //     maxLng += padding[1]
    //     minLng += padding[3]
    //     maxLat += padding[0]
    //     minLat += padding[2]
    // } else {
    //     throw new Error("padding格式错误");
    // }
    let center = { lng: (maxLng + minLng) / 2, lat: (maxLat + minLat) / 2 }
    // 得到当前三维场景
    let scene = viewer.scene;

    // 得到当前三维场景的椭球体
    let ellipsoid = scene.globe.ellipsoid;
    let canvas = scene.canvas;

    // canvas左上角
    let car3_lt = viewer.camera.pickEllipsoid(new Cesium.Cartesian2(0, 0), ellipsoid);

    // canvas右下角
    let car3_rb = viewer.camera.pickEllipsoid(new Cesium.Cartesian2(canvas.width, canvas.height), ellipsoid);
    let extent = {
        xmin: null,
        ymax: null,
        xmax: null,
        ymin: null,
    }
    // 当canvas左上角和右下角全部在椭球体上
    if (car3_lt && car3_rb) {
        let carto_lt = ellipsoid.cartesianToCartographic(car3_lt);
        let carto_rb = ellipsoid.cartesianToCartographic(car3_rb);
        extent.xmin = Cesium.Math.toDegrees(carto_lt.longitude);
        extent.ymax = Cesium.Math.toDegrees(carto_lt.latitude);
        extent.xmax = Cesium.Math.toDegrees(carto_rb.longitude);
        extent.ymin = Cesium.Math.toDegrees(carto_rb.latitude);
    }
    //当前经度范围
    let current_min_max_lng = extent.xmax - extent.xmin;
    //当前维度范围
    let current_min_max_lat = extent.ymax - extent.ymin;
    //获取当前高度
    let current_height = viewer.camera.positionCartographic.height;

    //计算所需要的高度
    let height_lng = ((maxLng - minLng) * current_height) / current_min_max_lng;
    let height_alt = ((maxLat - minLat) * current_height) / current_min_max_lat;
    center.alt = height_lng > height_alt ? height_lng + offest_height : height_alt + offest_height;
    let pitch = -40.0;
    let distance = Math.tan(Cesium.Math.toRadians(Math.abs(pitch))) * center.alt //需要偏移的米数
    //计算维度
    // 1度纬度大约相当于111公里
    center.lat -= distance / (80 * 1000);
    //将米数转为维度
    viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
            center.lng,
            center.lat,
            center.alt
        ),
        orientation: {
            heading: 0.0, // 方向
            pitch: Cesium.Math.toRadians(pitch), // 倾斜角度
            roll: 0.0 // 旋转角度
        },
        duration: 2, // 飞行时间
    });
    return center;
}
const CryptoJS = require("crypto-js");

// AES ECB PKCS7加密方法
export function aesEcbEncrypt(plainText, secretKey) {
    // 通过UTF-8编码将明文和秘钥转换为字节数组
    const key = CryptoJS.enc.Utf8.parse(secretKey);
    const padding = CryptoJS.pad.Pkcs7;

    // 加密
    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
        mode: CryptoJS.mode.ECB,
        padding: padding
    });

    // 返回 Base64 编码的密文
    return encrypted.toString();
}
export function generateRandomSixDigitNumber() {
    const min = 100000; // 六位数的最小值
    const max = 999999; // 六位数的最大值
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function kkkkk() {
    let APPID = "4bdf6610e8754eca872f54cacae3bfec"
    let APPKEY = "NKLH3KG4C6O4BONBQR6FDZP7R9O64ZYP"
    let content = `${generateRandomSixDigitNumber()}${+new Date()}`
    console.log(content);
    fetch("http://116.63.203.45:8097/japi/sw-scm/api/visual/getLatestData", {
        method: "post",
        data: JSON.stringify({
            deviceType: "SCM3501",
            naturalEnvMonitorId: "04d0ddbdb78fcd77c172c887751dfd38",
        }),
        headers: {
            appId: APPID,
            token: aesEcbEncrypt(content, APPKEY),
            'Content-Type': 'application/json',
        }
    })
        .then((response) => response.json())
        .then((data) => console.log(data));
}


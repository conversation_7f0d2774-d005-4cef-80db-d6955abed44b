/**
 * @descripion:
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @param {String} title
 * @param {String} id
 * @return {*}
 */

import { createApp, h } from 'vue';
import Popup from './popup.vue';
import ElementPlus from 'element-plus';
export default class Bubble {
	constructor(val) {
		this.viewer = val.viewer;
		this.position = val.data.position._value;
		this.id = val.data.monitoItems.data.id; // Bubble标识  用于增删改
		// vue 弹框
		this.vmInstance = createApp({
			render() {
				return h(Popup, { data: val.data.monitoItems.data });
			},
			methods: {
				closeEvent: () => {
					this.windowClose();
				}
			}
		})
		this.vmInstance.use(ElementPlus)
		// 创建一个容器用了装弹框
		this.vueContainer = document.createElement('div');
		this.vueContainer.id = 'popup-container';
		// 添加的cesium中
		val.viewer.cesiumWidget.container.appendChild(this.vueContainer); //将字符串模板生成的内容添加到DOM上
		// 将弹框状态容器中
		this.vmInstance.mount('#popup-container'); //根据模板创建一个面板;
		this.addPostRender();
	}

	//添加场景事件z
	addPostRender() {
		this.viewer.scene.postRender.addEventListener(this.postRender, this);
	}

	//场景渲染事件 实时更新窗口的位置 使其与笛卡尔坐标一致
	postRender() {
		if (!this.vueContainer || !this.vueContainer.style) return;
		const canvasHeight = this.viewer.scene.canvas.height;
		const windowPosition = new Cesium.Cartesian2();
		Cesium.SceneTransforms.wgs84ToWindowCoordinates(this.viewer.scene, this.position, windowPosition);
		this.vueContainer.style.bottom = canvasHeight - windowPosition.y + 'px';
		const elWidth = this.vueContainer.offsetWidth;
		this.vueContainer.style.left = windowPosition.x - elWidth / 2 + 'px';
		// console.log(this.vueContainer.style.bottom, this.vueContainer.style.left, 99999999999)
		const camerPosition = this.viewer.camera.position;
		let height = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
		height += this.viewer.scene.globe.ellipsoid.maximumRadius;
		if (!(Cesium.Cartesian3.distance(camerPosition, this.position) > height) && this.viewer.camera.positionCartographic.height < 50000000) {
			this.vueContainer.style.display = 'block';
		} else {
			this.vueContainer.style.display = 'none'; ``
		}
	}

	//关闭
	windowClose() {
		if (this.vmInstance) {
			this.vueContainer.remove()
			this.vmInstance.unmount();
		}
		this.viewer.scene.postRender.removeEventListener(this.postRender, this); //移除事件监听
	}
}

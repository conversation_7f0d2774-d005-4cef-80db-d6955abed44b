/**
 * 后台跳转配置工具
 */

/**
 * 获取当前userName参数
 * @param {Vue} vm - Vue组件实例，用于访问this.$route
 * @returns {string|null} userName参数值，如果没有则返回null
 */
const getCurrentUserName = (vm = null) => {
  try {
    let userName = null;
    
    // 优先使用Vue路由的query参数
    if (vm && vm.$route && vm.$route.query && vm.$route.query.userName) {
      userName = vm.$route.query.userName;
      console.log('🔍 从Vue路由query中获取到userName:', userName);
    } else {
      // 如果没有Vue实例，尝试从URL查询参数获取
      // 处理Vue hash模式下的查询参数
      const fullUrl = window.location.href;
      const hashIndex = fullUrl.indexOf('#');
      
      if (hashIndex !== -1) {
        // 在hash后面查找查询参数
        const hashPart = fullUrl.substring(hashIndex);
        const queryIndex = hashPart.indexOf('?');
        
        if (queryIndex !== -1) {
          const queryString = hashPart.substring(queryIndex + 1);
          const urlParams = new URLSearchParams(queryString);
          userName = urlParams.get('userName');
          console.log('🔍 从hash后的查询参数中获取userName:', userName);
        }
      } else {
        // 如果没有hash，使用传统的search方法
        const urlParams = new URLSearchParams(window.location.search);
        userName = urlParams.get('userName');
        console.log('🔍 从传统URL查询参数中获取userName:', userName);
      }
    }
    
    console.log('✅ 最终使用的userName:', userName);
    return userName;
  } catch (error) {
    console.warn('获取userName参数失败:', error);
    return null;
  }
};

/**
 * 在Vue组件中获取userName的便捷方法
 * @param {Vue} vm - Vue组件实例
 * @returns {string|null} userName参数值
 */
export function getUserNameFromRoute(vm) {
  if (!vm || !vm.$route) {
    console.warn('Vue实例或路由不存在');
    return null;
  }
  
  const userName = vm.$route.query.userName;
  console.log('🔍 从Vue路由获取userName:', userName);
  return userName;
}

/**
 * 获取当前环境的后台跳转地址
 * @returns {string} 后台跳转的完整URL
 */
export function getBackendUrl() {
  if (!window.globalConfig || !window.globalConfig.backendConfig) {
    console.warn('后台配置未找到，使用默认地址');
    return 'http://************:54946/#/manage/overview';
  }

  const config = window.globalConfig.backendConfig;
  const currentHostname = window.location.hostname;

  // 查找匹配的环境配置
  const matchedEnv = config.environments.find(env =>
    currentHostname.includes(env.ipPattern)
  );

  if (matchedEnv) {
    // 确保 path 以 / 开头
    let path = matchedEnv.path || '';
    if (path && !path.startsWith('/')) {
      path = '/' + path;
    }
    // 拼接时避免多余的 /
    return matchedEnv.backendUrl.replace(/\/+$/, '') + path;
  }

  // 没有匹配环境，使用默认
  let defaultPath = config.environments[0]?.path || '/#/manage/overview';
  if (defaultPath && !defaultPath.startsWith('/')) {
    defaultPath = '/' + defaultPath;
  }
  return config.defaultBackend.replace(/\/+$/, '') + defaultPath;
}

/**
 * 跳转到后台管理系统
 */
export function goToBackend() {
  const backendUrl = getBackendUrl();
  const userName = getCurrentUserName();
  
  let finalUrl = backendUrl;
  
  // 如果获取到userName参数，则添加到URL中
  if (userName) {
    const separator = backendUrl.includes('?') ? '&' : '?';
    finalUrl = `${backendUrl}${separator}userName=${encodeURIComponent(userName)}`;
    console.log('✅ 跳转时添加userName参数:', userName);
  }
  
  console.log('跳转到后台地址:', finalUrl);
  window.open(finalUrl, '_blank');
}

/**
 * 获取当前环境信息（用于调试）
 * @returns {object} 环境信息
 */
export function getCurrentEnvironmentInfo() {
  const currentHostname = window.location.hostname;
  const config = window.globalConfig?.backendConfig;

  if (!config) {
    return {
      hostname: currentHostname,
      config: null,
      matchedEnv: null
    };
  }

  const matchedEnv = config.environments.find(env =>
    currentHostname.includes(env.ipPattern)
  );

  return {
    hostname: currentHostname,
    config: config,
    matchedEnv: matchedEnv,
    backendUrl: getBackendUrl()
  };
} 
// axios封装工具
import axios from "axios";
import Vue from "vue";
import Vuex from "vuex";
import store from "@/store";
import { Message } from "view-design";
import Cookies from "js-cookie";
import { Session } from "./storage"
import { aesEcbEncrypt, generateRandomSixDigitNumber } from "./aes"

Vue.use(Vuex);

/**
 * 从当前URL中获取userName参数
 * @param {Vue} vm - Vue组件实例，用于访问this.$route
 * @returns {string|null} userName参数值，如果没有则返回null
 */
const getCurrentUserName = (vm = null) => {
  try {
    let userName = null;
    
    // 优先使用Vue路由的query参数
    if (vm && vm.$route && vm.$route.query && vm.$route.query.userName) {
      userName = vm.$route.query.userName;
      console.log('🔍 从Vue路由query中获取到userName:', userName);
    } else {
      // 如果没有Vue实例，尝试从URL查询参数获取
      // 处理Vue hash模式下的查询参数
      const fullUrl = window.location.href;
      const hashIndex = fullUrl.indexOf('#');
      
      if (hashIndex !== -1) {
        // 在hash后面查找查询参数
        const hashPart = fullUrl.substring(hashIndex);
        const queryIndex = hashPart.indexOf('?');
        
        if (queryIndex !== -1) {
          const queryString = hashPart.substring(queryIndex + 1);
          const urlParams = new URLSearchParams(queryString);
          userName = urlParams.get('userName');
          console.log('🔍 从hash后的查询参数中获取userName:', userName);
        }
      } else {
        // 如果没有hash，使用传统的search方法
        const urlParams = new URLSearchParams(window.location.search);
        userName = urlParams.get('userName');
        console.log('🔍 从传统URL查询参数中获取userName:', userName);
      }
    }
    
    console.log('✅ 最终使用的userName:', userName);
    return userName;
  } catch (error) {
    console.warn('获取userName参数失败:', error);
    return null;
  }
};

/**
 * 在Vue组件中获取userName的便捷方法
 * @param {Vue} vm - Vue组件实例
 * @returns {string|null} userName参数值
 */
export function getUserNameFromRoute(vm) {
  if (!vm || !vm.$route) {
    console.warn('Vue实例或路由不存在');
    return null;
  }
  
  const userName = vm.$route.query.userName;
  console.log('🔍 从Vue路由获取userName:', userName);
  return userName;
}

// 统一请求路径前缀
// let base = globalConfig.httpConfig.baseApi;
let base = "";
let wuxueBase = "/wuxue";
// let shuwenBase = "http://*************:8097/japi/sw-scm"
// let shuwenBase = "/shuWen"
let shuwenBase = ""
// let shuwenBase = "http://*************:8097"
// 超时设定
// axios.defaults.timeout = globalConfig.httpConfig.timeOut;
axios.defaults.timeout = "/EnjoyGIS".timeOut;

// 获取当前环境对应的后台地址
const getBackendUrl = () => {
  const currentHost = window.location.hostname;
  const config = window.globalConfig?.backendConfig;

  console.log('当前hostname:', currentHost);
  console.log('systemConfig配置:', config);

  if (!config) {
    const defaultUrl = 'http://************:54946';
    console.log('使用默认后台地址:', defaultUrl);
    return defaultUrl;
  }

  // 查找匹配当前IP的环境配置
  const matchedEnv = config.environments?.find(env =>
    currentHost.includes(env.ipPattern)
  );

  const resultUrl = matchedEnv ? matchedEnv.backendUrl : config.defaultBackend;
  console.log('匹配的环境:', matchedEnv);
  console.log('最终后台地址:', resultUrl);

  return resultUrl;
};

/**
 * 请求拦截器
 * @param config - 请求前的控制
 * @param error - 出现错误的时候会提供一个错误信息
 */
axios.interceptors.request.use(
  (config) => {
    //获取Token
    //为请求头对象，添加token验证的Authorization字段
    if (!config.headers.isToken) config.headers.Accesstoken = Session.getToken() ? Session.getToken() : store.state.login.token;
    
    // 获取当前userName参数并设置windowToken请求头
    const userName = getCurrentUserName();
    if (userName) {
      config.headers.windowToken = userName;
      console.log('设置windowToken请求头:', userName);
    }
    
    return config;
  },
  (err) => {
    Message.error("请求超时");
    return Promise.resolve(err);
  }
);

/**
 * 响应拦截器
 * @param res - 服务端返回的东西
 * @param error - 出现错误的时候会提供一个错误信息
 */
axios.interceptors.response.use(
  (response) => {
    if (!response || !response.data) {
      Message.error("服务端未返回数据");
      return Promise.reject(new Error("服务端未返回数据"));
    }
    const data = response.data;
    // 根据返回的code值来做不同的处理(和后端约定)
    if (data.code) {
      switch (data.code) {
        case 401: {
          // 未登录
          console.log('检测到响应数据中code为401');
          Cookies.set("accessToken", "");
          window.localStorage.removeItem("token");
          Message.error("用户未登录，请重新登录。");

          // 获取后台地址并跳转到登录页面
          const backendUrl = getBackendUrl();
          const loginUrl = `${backendUrl}/login.html`;
          console.log('即将跳转到登录页面:', loginUrl);
          window.location.href = loginUrl;
          break;
        }
        case 403: {
          // 没有权限
          Message.error("您无权访问改资源，请联系管理员。");
          break;
        }
        case 500: {
          // 错误
          if (data.message !== null) {
            Message.error(data.message);
          } else {
            Message.error("未知错误");
          }
          break;
        }
        default:
          return data;
      }
      return data;
    } else if (data.type === "FeatureCollection") {
      return data
    }
    return data;
  },
  (err) => {
    // 处理HTTP状态码401
    if (err.response && err.response.status === 401) {
      console.log('检测到HTTP状态码401');
      Cookies.set("accessToken", "");
      window.localStorage.removeItem("token");
      Message.error("用户未登录，请重新登录。");

      // 获取后台地址并跳转到登录页面
      const backendUrl = getBackendUrl();
      const loginUrl = `${backendUrl}/login.html`;
      console.log('即将跳转到登录页面:', loginUrl);
      window.location.href = loginUrl;
      return Promise.reject(err);
    }

    // 返回状态码不为 200 时候的错误处理
    Message.error(err.toString());
    return Promise.resolve(err);
  }
);

const $service = axios.create({
  baseURL: "/api",
  timeout: 500000000,
  headers: {
    "Content-type": "application/json;charset=utf-8",
    'Accesstoken': Cookies.get("accessToken") || window.localStorage.getItem("token"),
  },
});

$service.interceptors.response.use(
  (response) => {
    const data = response.data;

    // 检查响应数据中的code字段
    if (data && data.code === 401) {
      console.log('$service检测到响应数据中code为401');
      Cookies.set("accessToken", "");
      window.localStorage.removeItem("token");
      Message.error("用户未登录，请重新登录。");

      // 获取后台地址并跳转到登录页面
      const backendUrl = getBackendUrl();
      const loginUrl = `${backendUrl}/login.html`;
      console.log('即将跳转到登录页面:', loginUrl);
      window.location.href = loginUrl;
      return Promise.reject(new Error("Unauthorized"));
    }

    return data;
  },
  (err) => {
    // 处理HTTP状态码401
    if (err.response && err.response.status === 401) {
      console.log('$service检测到HTTP状态码401');
      Cookies.set("accessToken", "");
      window.localStorage.removeItem("token");
      Message.error("用户未登录，请重新登录。");

      // 获取后台地址并跳转到登录页面
      const backendUrl = getBackendUrl();
      const loginUrl = `${backendUrl}/login.html`;
      console.log('即将跳转到登录页面:', loginUrl);
      window.location.href = loginUrl;
      return Promise.reject(err);
    }

    Message.error(err.toString());
    return Promise.reject(err);
  }
)

$service.interceptors.request.use((config) => {
  // 获取当前userName参数并设置windowToken请求头
  const userName = getCurrentUserName();
  if (userName) {
    config.headers.windowToken = userName;
    console.log('$service设置windowToken请求头:', userName);
  }
  
  return config;
})

export { $service };

/**
 * 封装统一的get请求
 * @param url - 请求接口地址
 * @param params - 请求接口参数
 */
export const getRequest = (url, params, isFullUrl = false) => {
  return $service({
    method: "get",
    url: isFullUrl ? url : `${base}${url}`,
    params: params,
    headers: getHeaders(),
  });
};
// let key = '183bf23e11b24421a415d182a7220c2c'
let key = '58d147ae03c64b9d84ebc8080c0b15f8'

export const getRequestWarn = (url, params) => {
  return $service({
    method: "get",
    url: `${base}${url}`,
    params: params,
    headers: {
      'X-QW-Api-Key': key
    },
  })
}

export const getRequestNew = (url, params) => {
  return $service({
    method: "get",
    url: `${wuxueBase}${url}`,
    params: params,
    headers: getHeaders(),
  });
};
export const postRequestNew = (url, params) => {
  return $service({
    method: "post",
    url: `${wuxueBase}${url}`,
    data: params,
    headers: getHeaders(),
  });
};
export const exportRequest = (url, params) => {
  return $service({
    method: "get",
    url: `${base}${url}`,
    responseType: "blob",
    params: params,
    headers: Object.assign(
      {
        "Content-Type": "application/json",
      },
      getHeaders()
    ),
  });
};
/**
 * 封装统一的get请求
 * @param url - 请求接口地址
 * @param params - 请求接口参数
 */
export const getRequestJson = (url, params) => {
  return $service({
    method: "get",
    url: `${base}${url}`,
    params: params,
    headers: Object.assign(
      {
        "Content-Type": "application/json",
      },
      getHeaders()
    ),
  });
};

/**
 * 基路径为/xboot的接口请求
 */
export const getRequestByXboot = (url, params) => {
  return $service({
    method: "get",
    url: `${"/xboot"}${url}`,
    params: params,
    headers: Object.assign(
      {
        "Content-Type": "application/json",
      },
      getHeaders()
    ),
  });
};

export const postRequestByXboot = (url, params) => {
  return $service({
    method: "post",
    url: `${"/xboot"}${url}`,
    data: params,
    transformRequest: [
      function (data) {
        let ret = "";
        for (let it in data) {
          ret +=
            encodeURIComponent(it) + "=" + encodeURIComponent(data[it]) + "&";
        }
        ret = ret.substring(0, ret.length - 1);
        return ret;
      },
    ],
    headers: Object.assign(
      {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      getHeaders()
    ),
  });
};

/**
 * 封装统一的post请求
 * @param url - 请求接口地址
 * @param params - 请求接口参数
 */
export const postRequest = (url, params, meta = { isToken: false }) => {
  return $service({
    method: "post",
    url: `${base}${url}`,
    data: params,
    transformRequest: [
      function (data) {
        let ret = "";
        for (let it in data) {
          ret +=
            encodeURIComponent(it) + "=" + encodeURIComponent(data[it]) + "&";
        }
        ret = ret.substring(0, ret.length - 1);
        return ret;
      },
    ],
    headers: Object.assign(
      {
        "Content-Type": "application/x-www-form-urlencoded",
        isToken: Boolean(meta.isToken),
      },
      getHeaders()
    ),
  });
};

/**
 * 封装统一的post请求
 * @param url - 请求接口地址
 * @param params - 请求接口参数
 */
export const postRequestJson = (url, params) => {
  return $service({
    method: "post",
    url: `${base}${url}`,
    data: params,
    headers: Object.assign(
      {
        "Content-Type": "application/json",
      },
      getHeaders()
    ),
  });
};

export const postFullUrlRequestJson = (url, params) => {
  return $service({
    method: "post",
    url: `${url}`,
    data: params,
    headers: Object.assign(
      {
        "Content-Type": "application/json",
      },
      getHeaders()
    ),
  });
};

/**
 * 封装大华的post(token)请求
 * @param url - 请求接口地址
 * @param params - 请求接口参数
 */
export const postRequestTokenJson = (url, params) => {
  return $service({
    method: "post",
    url: `${base}${url}`,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
};

export const formDatapostRequest = (url, params) => {
  return $service({
    method: 'post',
    url: `${base}${url}`,
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const deleteRequest = (url, params) => {
  let accessToken = Cookies.get('accessToken');
  return $service({
    method: 'delete',
    url: `${base}${url}`,
    params: params,
  });
};

// 文件下载
export const postRequestpdfORimg = (url, params) => {
  return $service({
    method: 'post',
    url: `${base}${url}`,
    data: params,
    responseType: 'blob',
    transformRequest: [
      function (data) {
        let ret = '';
        for (let it in data) {
          ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
        }
        return ret;
      },
    ],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
};

//获取 Headers 公共方法
const getHeaders = () => {
  let result = {};
  // if ('/EnjoyGIS'.isCheckAuthority) {
  Object.assign(result, {
    // Accesstoken: store.state.login.token,
  });
  // }
  // if ('/EnjoyGIS'.isClient) {
  //     Object.assign(result, {
  //         'user-client': 1,
  //     });
  // }
  return result;
};
//数文科技的接口
// 设置数文科技公共请求头
const setShuWenHeaders = () => {
  let result = {};
  let content = `${generateRandomSixDigitNumber()}${+new Date()}`
  let APPKEY = "NKLH3KG4C6O4BONBQR6FDZP7R9O64ZYP"
  Object.assign(result, {
    appId: "4bdf6610e8754eca872f54cacae3bfec",
    token: aesEcbEncrypt(content, APPKEY)
  });
  return result;
}
export const getRequestShuWen = (url, params) => {
  return axios({
    method: "get",
    url: `${shuwenBase}/${url}`,
    params: params,
    headers: Object.assign(
      {
        'Content-Type': 'multipart/form-data',
      },
      setShuWenHeaders()
    ),
  });
}
export const getRequestShuWen2 = (url, params) => {
  return axios({
    method: "post",
    url: `${url}`,
    params: params,
    headers: Object.assign(
      {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      setShuWenHeaders()
    ),
  });
}
export const postRequestShuWen = (url, params) => {
  return axios({
    method: "post",
    url: `${shuwenBase}/${url}`,
    data: params,
    headers: Object.assign(
      {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      setShuWenHeaders()
    ),
  });
}
export const postDataRequestShuWen = (url, params) => {
  return axios({
    method: "post",
    url: `${shuwenBase}/${url}`,
    data: params,
    headers: Object.assign(
      {
        'Content-Type': 'application/json',
      },
      setShuWenHeaders()
    ),
  });
}

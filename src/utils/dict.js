export const deviceDict = {
    SCM3501: "气象监测",
    SCM3502: "水质监测",
    SCM3503: "土壤墒情监测",
    SCM3504: "水雨情监测",
}

export const monitoringIndicatorsDict = {
    SCM350101: {
        label: "热量指数",
        key: "",
        warningKey: "",
    },
    SCM350102: {
        label: "空气湿度",
        key: "ambientHumidity", warningKey: "ambientHumidityWarning",
    },
    SCM350103: {
        label: "温度",
        key: "ambientTemperature", warningKey: "ambientTemperatureWarning",
    },
    SCM350104: {
        label: "气压",
        key: "pressure", warningKey: "pressureWarning",
    },
    SCM350105: {
        label: "风向",
        key: "windDirection", warningKey: "windDirectionWarning",
    },
    SCM350106: {
        label: "风力",
        key: "windScale", warningKey: "windScaleWarning",
    },
    SCM350107: {
        label: "风速",
        key: "windSpeed", warningKey: "windSpeedWarning",
    },

    SCM350201: {
        label: "溶解氧",
        key: "dissolvedoxygen", warningKey: "dissolvedoxygenWarning",
    },
    SCM350202: {
        label: "浊度",
        key: "turbidity", warningKey: "turbidityWarning",
    },
    SCM350203: {
        label: "电导率",
        key: "waterEC", warningKey: "waterECWarning",
    },
    SCM350204: {
        label: "水质PH",
        key: "waterPH", warningKey: "waterPHWarning",
    },
    SCM350205: {
        label: "水温",
        key: "waterT", warningKey: "waterTWarning",
    },

    SCM350301: {
        label: "土壤电导率",
        key: "soilCond",
        warningKey: "soilCondWarning",
    },
    SCM350302: {
        label: "土壤湿度",
        key: "soilHumi",
        warningKey: "soilHumiWarning",
    },
    SCM350303: {
        label: "土壤温度",
        key: "soilTemp",
        warningKey: "soilTempWarning",
    },
    SCM350304: {
        label: "土壤PH",
        key: "soilPH",
        warningKey: "soilPHWarning",
    },

    SCM350401: {
        label: "水位",
        key: "level",
        warningKey: "levelWarning",
    },
    SCM350402: {
        label: "电源",
        key: "pow",
        warningKey: "powWarning",
    },
    SCM350403: {
        label: "累计雨量",
        key: "rainfall",
        warningKey: "rainfallWarning",
    },
    SCM350404: {
        label: "信号强度",
        key: "rssi",
        warningKey: "rssiWarning",
    },
}

export const showFields = {
    SCM3501: [
        {
            indicator: "SCM350103",
            unit: "℃",
            icon: '/img/jcyj/wd.png',
        },
        {
            indicator: "SCM350102",
            unit: "%",
            icon: '/img/jcyj/kqsd.png',
        },
        {
            indicator: "SCM350104",
            unit: "kPa",
            icon: '/img/jcyj/qy.png'
        },
        {
            indicator: "SCM350105",
            unit: "°",
            icon: '/img/jcyj/fx.png',
        },
        {
            indicator: "SCM350106",
            unit: "级",
            icon: '/img/jcyj/fl.png'
        },
        {
            indicator: "SCM350107",
            unit: "m/s",
            icon: '/img/jcyj/fs.png'
        },
    ],
    SCM3502: [
        {
            indicator: "SCM350204",
            unit: "",
            icon: '/img/jcyj/ph.png',
        },
        {
            indicator: "SCM350205",
            unit: "℃",
            icon: '/img/jcyj/sw.png',
        },
        {
            indicator: "SCM350203",
            unit: "μS/cm",
            icon: '/img/jcyj/ddl.png',
        },
        {
            indicator: "SCM350202",
            unit: "NTU",
            icon: '/img/jcyj/zd.png',
        },
        {
            indicator: "SCM350201",
            unit: "mg/L",
            icon: '/img/jcyj/rjy.png',
        },
    ],
    SCM3503: [
        {
            indicator: "SCM350302",
            unit: "%",
            icon: '/img/jcyj/kqsd.png',
        },
        {
            indicator: "SCM350301",
            unit: "μS/cm",
            icon: '/img/jcyj/ddl.png',
        },
        {
            indicator: "SCM350304",
            unit: "",
            icon: '/img/jcyj/ph.png',
        },
        {
            //土壤温度
            indicator: "SCM350303",
            unit: "℃",
            icon: '/img/jcyj/soil.png',
        },
    ],
    SCM3504: [
        {
            indicator: "SCM350401",
            unit: "m",
            icon: '/img/jcyj/sw2.png',
        },
        {
            indicator: "SCM350402",
            unit: "v",
            icon: '/img/jcyj/ddl.png',
        },
        {
            indicator: "SCM350404",
            unit: "dbm",
            icon: '/img/jcyj/xhqd.png',
        },
        {
            indicator: "SCM350403",
            unit: "mm",
            icon: '/img/jcyj/ljyl.png',
        },
    ],
}
/**
 * 相机视角管理工具类
 * 用于保存、恢复和管理Cesium地图的视角状态
 */
class CameraViewManager {
  constructor() {
    this.currentPageName = null;
  }

  /**
   * 获取当前缩放等级
   */
  getCurrentZoomLevel() {
    if (!window.viewer || !window.viewer.camera) {
      return 15; // 默认缩放级别
    }

    const height = window.viewer.camera.positionCartographic.height;
    // 将高度转换为缩放级别（类似Web Mercator的计算方式）
    const zoomLevel = Math.max(1, Math.min(20, 20 - Math.log2(height / 156543.03392)));
    return zoomLevel;
  }

  /**
   * 获取当前项目名称，用于构建唯一的缓存key
   * @param {Object} route - Vue路由对象
   */
  getCurrentProjectName(route = null) {
    // 优先级：路由参数 > localStorage > 默认值
    let projectName = 'default';

    // 1. 尝试从路由参数获取projectId
    if (route && route.query && route.query.projectId) {
      projectName = route.query.projectId;
    }
    // 2. 尝试从localStorage获取projectId作为备选
    else if (localStorage.getItem('projectId')) {
      projectName = localStorage.getItem('projectId');
    }
    // 3. 尝试从localStorage获取projectName
    else if (localStorage.getItem('projectName')) {
      projectName = localStorage.getItem('projectName');
    }

    // 清理项目名称，确保可用作缓存键
    if (projectName && typeof projectName === 'string') {
      // 移除特殊字符，只保留字母、数字、中文、下划线和短横线
      projectName = projectName.replace(/[^\w\u4e00-\u9fa5-]/g, '_');
    }

    return projectName || 'default';
  }

  /**
   * 构建完整的存储键名：cesium_camera_view_项目名称_页面名称
   * @param {string} pageName - 页面名称
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  buildStorageKey(pageName = 'default', route = null, includeProject = true) {
    if (includeProject) {
      const projectName = this.getCurrentProjectName(route);
      return `cesium_camera_view_${projectName}_${pageName}`;
    } else {
      // 不包含项目名称，用于集团级总览等通用场景
      return `cesium_camera_view_${pageName}`;
    }
  }

  /**
   * 保存当前地图视角
   * @param {string} pageName - 页面名称
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  saveCameraView(pageName = 'default', route = null, includeProject = true) {
    try {
      if (!window.viewer) {
        console.warn('Cesium viewer 未初始化，无法保存视角');
        return false;
      }

      // 如果页面名称是default，则使用当前页面名称
      if (pageName === 'default') {
        pageName = this.getCurrentPageName();
      }

      const viewer = window.viewer;
      const camera = viewer.scene.camera;
      const position = camera.position;
      const cartographic = Cesium.Cartographic.fromCartesian(position);

      // 获取原始相机参数
      const rawCameraData = {
        longitude: Cesium.Math.toDegrees(cartographic.longitude),
        latitude: Cesium.Math.toDegrees(cartographic.latitude),
        height: cartographic.height,
        heading: Cesium.Math.toDegrees(camera.heading),
        pitch: Cesium.Math.toDegrees(camera.pitch),
        roll: Cesium.Math.toDegrees(camera.roll)
      };

      // 参数验证和修正函数
      const validateAndFixCameraParams = (cameraData) => {
        const fixed = {
          longitude: Math.max(-180, Math.min(180, cameraData.longitude || 0)),
          latitude: Math.max(-90, Math.min(90, cameraData.latitude || 0)),
          height: Math.max(1, cameraData.height || 1000),
          // heading: 0-359.999度
          heading: ((cameraData.heading || 0) % 360 + 360) % 360,
          // pitch: -90到90度，但避免极端值
          pitch: Math.max(-89.9, Math.min(89.9, cameraData.pitch || 0)),
          // roll: -180到180度
          roll: Math.max(0, Math.min(180, cameraData.roll || 0))
        };

        // 特殊情况处理：如果roll=180,设为0
        if (fixed.roll === 180) {
          fixed.roll = 0;
        }

        // 特殊情况处理：如果heading接近360，设为0
        if (fixed.heading >= 359.999) {
          fixed.heading = 0;
        }

        return fixed;
      };

      // 验证和修正参数
      const validCameraData = validateAndFixCameraParams(rawCameraData);

      // 计算视图中心点（相机朝向的地面点）
      const ray = camera.getPickRay(new Cesium.Cartesian2(
        viewer.canvas.clientWidth / 2,
        viewer.canvas.clientHeight / 2
      ));
      const intersection = viewer.scene.globe.pick(ray, viewer.scene);

      let viewCenter = { longitude: validCameraData.longitude, latitude: validCameraData.latitude };
      if (intersection) {
        const intersectionCartographic = Cesium.Cartographic.fromCartesian(intersection);
        viewCenter = {
          longitude: Cesium.Math.toDegrees(intersectionCartographic.longitude),
          latitude: Cesium.Math.toDegrees(intersectionCartographic.latitude)
        };
      }

      // 构建要保存的视角数据
      const cameraViewData = {
        timestamp: new Date().toISOString(),
        projectName: includeProject ? this.getCurrentProjectName(route) : null,
        pageName: pageName,
        viewCenter: viewCenter,
        cameraPosition: {
          longitude: validCameraData.longitude,
          latitude: validCameraData.latitude,
          height: validCameraData.height
        },
        cameraOrientation: {
          heading: validCameraData.heading,
          pitch: validCameraData.pitch,
          roll: validCameraData.roll
        },
        zoomLevel: this.getCurrentZoomLevel(),
        includeProject: includeProject // 记录是否包含项目信息
      };

      // 保存到 localStorage
      const storageKey = this.buildStorageKey(pageName, route, includeProject);
      localStorage.setItem(storageKey, JSON.stringify(cameraViewData));

      console.log('💾 保存视角成功:', {
        '🏗️ 项目名称': cameraViewData.projectName || '(通用)',
        '📄 页面名称': pageName,
        '🔑 存储键': storageKey,
        '📅 保存时间': new Date(cameraViewData.timestamp).toLocaleString('zh-CN'),
        '🔧 包含项目': includeProject,
        '📷 修正数据': `经度: ${validCameraData.longitude.toFixed(6)}°, 纬度: ${validCameraData.latitude.toFixed(6)}°, 高度: ${validCameraData.height.toFixed(1)}m`,
        '🧭 修正方向': `方位角: ${validCameraData.heading.toFixed(2)}°, 俯仰角: ${validCameraData.pitch.toFixed(2)}°, 翻滚角: ${validCameraData.roll.toFixed(2)}°`,
        '🎯 视图中心': `经度: ${viewCenter.longitude.toFixed(6)}°, 纬度: ${viewCenter.latitude.toFixed(6)}°`,
        '🔍 缩放等级': cameraViewData.zoomLevel
      });

      return true;

    } catch (error) {
      console.error('❌ 保存视角时发生错误:', error);
      console.error('错误详情:', {
        pageName,
        includeProject,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * 恢复保存的地图视角
   * @param {string} pageName - 页面名称
   * @param {boolean} showFlyAnimation - 是否显示飞行动画
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  restoreCameraView(pageName = 'default', showFlyAnimation = true, route = null, includeProject = true) {
    try {
      if (!window.viewer) {
        console.warn('Cesium viewer 未初始化，无法恢复视角');
        return false;
      }
      // 如果页面名称是default，则使用当前页面名称
      if (pageName === 'default') {
        pageName = this.getCurrentPageName();
      }

      const storageKey = this.buildStorageKey(pageName, route, includeProject);
      const savedViewData = localStorage.getItem(storageKey);

      if (!savedViewData) {
        const projectInfo = includeProject ? `项目 "${this.getCurrentProjectName(route)}" ` : '';
        console.log(`📍 未找到${projectInfo}页面 "${pageName}" 的保存视角`);
        return false;
      }

      const cameraViewData = JSON.parse(savedViewData);

      // 验证数据完整性
      if (!cameraViewData.cameraPosition || !cameraViewData.cameraOrientation) {
        console.warn('保存的视角数据不完整');
        return false;
      }

      // 参数范围验证和修正函数
      const validateAndFixCameraParams = (position, orientation) => {
        const fixedPosition = {
          longitude: Math.max(-180, Math.min(180, position.longitude || 0)),
          latitude: Math.max(-90, Math.min(90, position.latitude || 0)),
          height: Math.max(1, position.height || 1000) // 最小高度1米
        };

        const fixedOrientation = {
          // heading: 0-359.999度
          heading: ((orientation.heading || 0) % 360 + 360) % 360,
          // pitch: -90到90度，但避免极端值
          pitch: Math.max(-89.9, Math.min(89.9, orientation.pitch || 0)),
          // roll: -180到180度
          roll: Math.max(0, Math.min(180, orientation.roll || 0))
        };

        // 特殊情况处理：如果roll=180,设为0
        if (fixedOrientation.roll === 180) {
          fixedOrientation.roll = 0;
        }

        // 特殊情况处理：如果heading接近360，设为0
        if (fixedOrientation.heading >= 359.999) {
          fixedOrientation.heading = 0;
        }

        return { position: fixedPosition, orientation: fixedOrientation };
      };

      // 验证和修正参数
      const { position: validPosition, orientation: validOrientation } = validateAndFixCameraParams(
        cameraViewData.cameraPosition,
        cameraViewData.cameraOrientation
      );

      // 构建相机目标配置
      const cameraConfig = {
        longitude: validPosition.longitude,
        latitude: validPosition.latitude,
        height: validPosition.height,
        heading: validOrientation.heading,
        pitch: validOrientation.pitch,
        roll: validOrientation.roll
      };

      console.log('🔄 恢复视角:', {
        '📄 页面名称': pageName,
        '🔑 存储键': storageKey,
        '🔧 包含项目': includeProject,
        '📅 保存时间': new Date(cameraViewData.timestamp).toLocaleString('zh-CN'),
        '📷 原始位置': `经度: ${cameraViewData.cameraPosition.longitude}°, 纬度: ${cameraViewData.cameraPosition.latitude}°, 高度: ${cameraViewData.cameraPosition.height}m`,
        '📷 修正位置': `经度: ${cameraConfig.longitude}°, 纬度: ${cameraConfig.latitude}°, 高度: ${cameraConfig.height}m`,
        '🧭 原始方向': `方位角: ${cameraViewData.cameraOrientation.heading}°, 俯仰角: ${cameraViewData.cameraOrientation.pitch}°, 翻滚角: ${cameraViewData.cameraOrientation.roll}°`,
        '🧭 修正方向': `方位角: ${cameraConfig.heading}°, 俯仰角: ${cameraConfig.pitch}°, 翻滚角: ${cameraConfig.roll}°`,
        '🎬 显示动画': showFlyAnimation
      });

      // 使用安全的相机设置方法
      const viewer = window.viewer;
      const camera = viewer.scene.camera;

      if (showFlyAnimation) {
        // 带动画的视角恢复 - 使用Cesium原生API以确保参数安全
        camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            cameraConfig.longitude,
            cameraConfig.latitude,
            cameraConfig.height
          ),
          orientation: {
            heading: Cesium.Math.toRadians(cameraConfig.heading),
            pitch: Cesium.Math.toRadians(cameraConfig.pitch),
            roll: Cesium.Math.toRadians(cameraConfig.roll)
          },
          duration: 1.5,
          complete: () => {
            console.log('✅ 视角恢复完成（带动画）');
          }
        });
      } else {
        // 直接设置相机位置，不带动画
        camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(
            cameraConfig.longitude,
            cameraConfig.latitude,
            cameraConfig.height
          ),
          orientation: {
            heading: Cesium.Math.toRadians(cameraConfig.heading),
            pitch: Cesium.Math.toRadians(cameraConfig.pitch),
            roll: Cesium.Math.toRadians(cameraConfig.roll)
          }
        });
        console.log('✅ 视角恢复完成（无动画）');
      }

      return true;

    } catch (error) {
      console.error('❌ 恢复视角时发生错误:', error);
      console.error('错误详情:', {
        pageName,
        showFlyAnimation,
        includeProject,
        error: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * 获取指定页面的保存视角信息
   * @param {string} pageName - 页面名称
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  getSavedCameraView(pageName = 'default', route = null, includeProject = true) {
    try {
      const storageKey = this.buildStorageKey(pageName, route, includeProject);
      const savedViewData = localStorage.getItem(storageKey);

      if (!savedViewData) {
        return null;
      }

      const cameraViewData = JSON.parse(savedViewData);
      console.log(`📖 读取页面 "${pageName}" 的保存视角:`, cameraViewData);

      return cameraViewData;

    } catch (error) {
      console.error('❌ 读取保存视角时发生错误:', error);
      return null;
    }
  }

  /**
   * 删除指定页面的保存视角
   * @param {string} pageName - 页面名称
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  deleteSavedCameraView(pageName = 'default', route = null, includeProject = true) {
    try {
      // 如果页面名称是default，则使用当前页面名称
      if (pageName === 'default') {
        pageName = this.getCurrentPageName();
      }

      const storageKey = this.buildStorageKey(pageName, route, includeProject);
      const existingData = localStorage.getItem(storageKey);

      if (!existingData) {
        console.log(`📍 页面 "${pageName}" 没有保存的视角数据需要删除`);
        return false;
      }

      localStorage.removeItem(storageKey);
      console.log(`🗑️ 已删除页面 "${pageName}" 的保存视角`);

      return true;

    } catch (error) {
      console.error('❌ 删除保存视角时发生错误:', error);
      return false;
    }
  }

  /**
   * 获取所有保存的视角列表
   */
  getAllSavedCameraViews() {
    try {
      const savedViews = [];
      const prefix = 'cesium_camera_view_';

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const pageName = key.substring(prefix.length);
          const viewData = JSON.parse(localStorage.getItem(key));
          savedViews.push({
            pageName: pageName,
            storageKey: key,
            ...viewData
          });
        }
      }

      return savedViews;

    } catch (error) {
      console.error('获取所有保存的视角时发生错误:', error);
      return [];
    }
  }

  /**
   * 尝试恢复默认视角
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  tryRestoreDefaultView(route = null, includeProject = true) {
    try {
      // 检查是否有 pointScreen 默认页面的保存视角
      const defaultView = this.getSavedCameraView('pointScreen', route, includeProject);

      if (defaultView) {
        const projectInfo = includeProject ? `项目 "${this.getCurrentProjectName(route)}" ` : '';
        console.log('🔄 发现默认页面保存的视角，准备恢复:', {
          '📄 页面名称': 'pointScreen',
          '🏗️ 项目信息': projectInfo,
          '📅 保存时间': new Date(defaultView.timestamp).toLocaleString('zh-CN')
        });

        // 延迟一段时间后恢复视角，确保地图完全加载
        setTimeout(() => {
          this.restoreCameraView('pointScreen', true, route, includeProject);
        }, 500);
      } else {
        console.log('ℹ️ 没有找到默认页面的保存视角，使用系统默认视角');
      }

    } catch (error) {
      console.error('尝试恢复默认视角时发生错误:', error);
    }
  }

  /**
   * 在地图组件销毁前保存当前视角
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  saveCurrentViewBeforeDestroy(route = null, includeProject = true) {
    try {
      // 获取当前页面标识，可以从外部传入或者使用默认值
      const pageName = this.currentPageName || 'pointScreen';

      console.log('💾 组件销毁前保存当前视角:', {
        '📄 页面名称': pageName,
        '🔧 包含项目': includeProject
      });

      this.saveCameraView(pageName, route, includeProject);

    } catch (error) {
      console.error('组件销毁前保存视角时发生错误:', error);
    }
  }

  /**
   * 设置当前页面名称（用于区分不同页面的视角保存）
   * @param {string} pageName - 页面名称
   */
  setCurrentPageName(pageName) {
    if (pageName && typeof pageName === 'string') {
      this.currentPageName = pageName;
      console.log('📄 设置当前页面名称:', {
        '新页面名称': pageName
      });
    } else {
      console.warn('设置页面名称失败: 页面名称必须是非空字符串');
    }
  }

  /**
   * 获取当前页面名称
   */
  getCurrentPageName() {
    return this.currentPageName;
  }

  /**
   * 检查当前页面或子页面是否存在保存的视角
   * @param {string} pageName - 页面名称，如果不指定则使用当前页面名称
   * @param {Object} route - Vue路由对象
   * @param {boolean} includeProject - 是否在存储键中包含项目名称，默认为true
   */
  checkSavedViewExists(pageName = null, route = null, includeProject = true) {
    try {
      // 如果没有指定页面名称，使用当前页面名称
      const currentPageName = pageName || this.currentPageName || 'pointScreen';

      // 检查当前页面的保存视角
      const currentPageView = this.getSavedCameraView(currentPageName, route, includeProject);
      if (currentPageView) {
        console.log(`📍 发现当前页面 "${currentPageName}" 存在保存的视角`);
        return true;
      }

      console.log(`📍 未发现页面 "${currentPageName}" 或相关子页面存在保存的视角`);
      return false;

    } catch (error) {
      console.error('检查保存视角时发生错误:', error);
      return false;
    }
  }

  /**
   * 调试和测试方法：手动检查和管理保存的视角
   */
  debugSavedViews() {
    console.log('🔍 调试保存的视角信息:');

    // 显示当前页面信息
    console.log('📄 当前页面信息:', {
      currentPageName: this.currentPageName
    });

    // 显示所有保存的视角
    const allViews = this.getAllSavedCameraViews();
    console.log('💾 所有保存的视角:', allViews);

    // 检查是否存在保存的视角
    const hasSavedView = this.checkSavedViewExists();
    console.log('✅ 是否存在保存的视角:', hasSavedView);

    return {
      currentPageName: this.currentPageName,
      allViews: allViews,
      hasSavedView: hasSavedView
    };
  }
}

// 创建单例实例
const cameraViewManager = new CameraViewManager();

export default cameraViewManager; 
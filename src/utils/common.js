// import appConfigBase from 'appConfig';
let appConfigBase = window.globalConfig.appConfigBase;
import { getStore, setStore } from '@/utils/storage';
import Cookies from 'js-cookie';
var xbootConfig = appConfigBase.base;
var baseXbootShp = appConfigBase.baseXbootdwg;
var popupWidth = Math.floor(document.body.offsetWidth); //layui的默认宽度（获取网页可见区域宽）
var popupHeight = Math.floor(screen.availHeight); //layui的默认高度（获取屏幕的可用高度）
//处理弹窗文字模糊抖动问题
var setUnShake = () => {
  popupWidth % 2 ? (popupWidth = popupWidth + 1) : '';
  popupHeight % 2 ? (popupHeight = popupHeight + 1) : '';
};
//通用转换json功能，shp/dwg也用到
var initJson = (geoJson, olmap) => {
  var geojson_format = new ol.format.GeoJSON();
  var shpfeatures = geojson_format.readFeatures(geoJson);

  if (olmap && shpfeatures && shpfeatures.length > 0 && shpfeatures[0].getGeometry()) {
    var center = ol.extent.getCenter(shpfeatures[0].getGeometry().getExtent()); //获取边界区域的中心位置
    // that.olmap.getView().setZoom(14);
    olmap.getView().setCenter(center); //设置当前地图的显示中心位置
  }
  var result = {
    polygonWKT: [],
    polygonJson: [],
    geometry: [],
    shpfeatures: shpfeatures,
    currentGeometryType: 'esriGeometryPolygon',
  };
  for (var f = 0; f < shpfeatures.length; f++) {
    var gType = shpfeatures[f].getGeometry().getType();
    result.polygonWKT.push(new ol.format.WKT().writeGeometry(shpfeatures[f].getGeometry(), {})); //上传的shp是多个面
    if (shpfeatures.length == 1) {
      switch (gType) {
        case 'Polyline':
          result.currentGeometryType = 'esriGeometryPolyline';
          break;
        case 'Point':
          result.currentGeometryType = 'esriGeometryPoint';
          break;
        case 'Polygon':
          result.currentGeometryType = 'esriGeometryPolygon';
          break;
        case 'MultiLineString':
          result.currentGeometryType = 'esriGeometryPolyline';
          break;
        case 'MultiPolygon':
          result.currentGeometryType = 'esriGeometryPolygon';
          break;
      }
      result.geometry.push(shpfeatures[f].getGeometry());
      var conds = shpfeatures[f].getGeometry().getCoordinates();
      for (var c = 0; c < conds.length; c++) {
        result.polygonJson.push(conds[c]);
      }
    } else if (gType == 'Polygon') {
      result.geometry.push(shpfeatures[f].getGeometry());
      var conds = shpfeatures[f].getGeometry().getCoordinates();
      for (var c = 0; c < conds.length; c++) {
        result.polygonJson.push(conds[c]);
      }
    } else {
      result.geometry.push(shpfeatures[f].getGeometry());
      var conds = shpfeatures[f].getGeometry().getCoordinates();
      if (gType == 'MultiPolygon' || gType == 'MultiLineString') {
        for (var c = 0; c < conds.length; c++) {
          for (var a = 0; a < conds[c].length; a++) {
            result.polygonJson.push(conds[c][a]);
          }
        }
      } else {
        for (var c = 0; c < conds.length; c++) {
          result.polygonJson.push(conds[c]);
        }
      }
    }
  }
  return result;
};

// 解析txt文件
var selectTextFile = resdata => {
  var result = {};
  let inputPolygons = [];
  var currentGeometryType = '';
  var typeCode = '';
  try {
    //在此运行代码
    //判断是否是部坐标的格式  解析单个部坐标格式的数据
    if (resdata.toString().indexOf('@') > 0) {
      let muti_txtsArr = resdata.split('@');
      if (muti_txtsArr && muti_txtsArr.length >= 2) {
        for (let j = 1; j < muti_txtsArr.length; j++) {
          let wkt = '';
          var mutilpolygon_wkts = '';
          let dotstr = muti_txtsArr[j].trim();
          let loc1_replace = dotstr.replace(/[\r\n]/g, '|');
          let loc1 = loc1_replace.split('|');
          var lnglatIndex = [];
          // for (let k = 0; k < loc1.length - 1; k++) {
          for (let k = 0; k < loc1.length; k++) {
            //首位相连的数据
            let loc2 = loc1[k].trim();
            if (loc2 != '') {
              let loc3 = loc2.split(',');
              if (lnglatIndex.indexOf(loc3[loc3.length - 3]) < 0) {
                lnglatIndex.push(loc3[loc3.length - 3]);
                if (wkt != '') {
                  mutilpolygon_wkts = '(' + wkt.substring(1) + ')';
                }
                wkt = '';
              }
              // 数据的拼接
              wkt += ',' + loc3[loc3.length - 2] + ' '; //下标为三的 txt的x
              wkt += loc3[loc3.length - 1]; //下标为二的 txt的y
              if (k == loc1.length - 1) {
                if (mutilpolygon_wkts != '') {
                  mutilpolygon_wkts += ',' + '(' + wkt.substring(1) + ')';
                } else {
                  mutilpolygon_wkts += '(' + wkt.substring(1) + ')';
                }
              }
            }
          }
          wkt = 'MULTIPOLYGON((' + mutilpolygon_wkts + '))'; //MULTIPOLYGON
          let read = new ol.format.WKT();
          //wkt转geojson
          var resJson = read.readGeometry(wkt, true, 12);
          inputPolygons.push(resJson);
          currentGeometryType = 'esriGeometryPolygon';
        }
        typeCode = 'bzb';
      } else {
        return false;
      }
    } else {
      //非部坐标的格式
      let locxy_str = resdata.trim();
      let locxy_replace = locxy_str.replace(/[\r\n]/g, ';');
      let locxy_arr = locxy_replace.split(';;');
      if (locxy_arr && locxy_arr.length > 0) {
        let wkt_xy = '';
        let locpnt_arr = null;
        if (locxy_arr.length == 1) {
          //只有一个坐标,当作点处理
          var txt_pnt = locxy_arr[0].split(',');
          wkt_xy = 'POINT(' + txt_pnt[0] + ' ' + txt_pnt[1] + ')';
          let read = new ol.format.WKT();
          //wkt转geojson
          var resJson = read.readGeometry(wkt_xy, false, 12);
          inputPolygons.push(resJson);
          currentGeometryType = 'esriGeometryPoint';
        } else if (locxy_arr.length == 2) {
          //只有两个坐标,当作线处理
          for (let i = 0; i < locxy_arr.length; i++) {
            if (locxy_arr[i].trim()) {
              locpnt_arr = locxy_arr[i].trim().split(',');
              wkt_xy += ',' + locpnt_arr[locpnt_arr.length - 2].trim() + ' ';
              wkt_xy += locpnt_arr[locpnt_arr.length - 1].trim();
            }
          }
          wkt_xy = 'LINESTRING(' + wkt_xy.substring(1) + ')';
          let read = new ol.format.WKT();
          //wkt转geojson
          var resJson = read.readGeometry(wkt_xy, false, 12);
          currentGeometryType = 'esriGeometryPolyline';
          inputPolygons.push(resJson);
        } else {
          for (let i = 0; i < locxy_arr.length; i++) {
            if (locxy_arr[i].trim()) {
              locpnt_arr = locxy_arr[i].trim().split(',');
              wkt_xy += ',' + locpnt_arr[locpnt_arr.length - 2].trim() + ' ';
              wkt_xy += locpnt_arr[locpnt_arr.length - 1].trim();
            }
          }
          //首位相连是多边形的
          if (locxy_arr[0].trim() == locxy_arr[locxy_arr.length - 1].trim()) {
            wkt_xy = 'POLYGON((' + wkt_xy.substring(1) + '))';
            let read = new ol.format.WKT();
            //wkt转geojson
            var resJson = read.readGeometry(wkt_xy, false, 12);
            currentGeometryType = 'esriGeometryPolygon';
            inputPolygons.push(resJson);
          } else {
            //首尾不相连，以线处理
            wkt_xy = 'LINESTRING(' + wkt_xy.substring(1) + ')';
            let read = new ol.format.WKT();
            //wkt转geojson
            var resJson = read.readGeometry(wkt_xy, false, 12);
            currentGeometryType = 'esriGeometryPolyline';
            inputPolygons.push(resJson);
          }
        }
      }
      typeCode = 'txtzb';
    }
    result.currentGeometryType = currentGeometryType;
    result.inputPolygons = inputPolygons;
    result.typeCode = typeCode;
    return result;
  } catch (err) {
    //在此处理错误
    return null;
  }
};

//通用绘制方法
var initDrawC = (type, datasource) => {
  var result = {};
  var source = new ol.source.Vector();
  if (datasource) {
    source = datasource;
  }
  var gridDrawLayer = new ol.layer.Vector({
    id: 'query_draw',
    zIndex: 3002,
    source: source,
    style: new ol.style.Style({
      //图层样式
      fill: new ol.style.Fill({
        color: 'rgba(255, 255, 255, 0.5)', //填充颜色
      }),
      stroke: new ol.style.Stroke({
        color: '#008000', //边框颜色
        width: 2, // 边框宽度
      }), //形状
      image: new ol.style.Circle({
        radius: 5,
        fill: new ol.style.Fill({
          color: '#008000',
        }),
      }),
    }),
  });
  var geometryFunction, maxPoints;
  if (type == 'Box') {
    type = 'Circle';
    geometryFunction = ol.interaction.Draw.createBox();
  }
  var gridDraws = new ol.interaction.Draw({
    type: type,
    source: gridDrawLayer.getSource(),
    //几何信息变更时调用函数
    geometryFunction: geometryFunction,
    //最大点数
    maxPoints: maxPoints,
  });
  result.gridDrawLayer = gridDrawLayer;
  result.gridDraws = gridDraws;
  return result;
};

//通用缓冲方法
var creatBufferC = (where, url) => {
  var postData = {};
  var proj = appConfigBase.map.proj.split(':');
  postData.inSR = proj[1];
  postData.outSR = proj[1];
  postData.unionResults = '';
  postData.distances = where.distances;
  postData.geometries = where.geometries;
  postData.f = 'json';
  var returnResult = {
    Features: '',
    ringsGeometries: '',
  };
  var Features;
  $.ajax({
    type: 'post',
    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
    url: url + '/buffer',
    async: false,
    data: postData,
    success: function (json) {
      var result = JSON.parse(json);
      if (result.geometries) {
        var ringsGeometries = result.geometries[0].rings;
        if (ringsGeometries.length <= 0) return result;
        if (ringsGeometries.length > 1) {
          var rings = [];
          for (var j = 0; j < ringsGeometries.length; j++) {
            rings.push(ringsGeometries[j]);
          }
          var ringarry = [];
          ringarry.push(rings);
          Features = new ol.Feature({
            geometry: new ol.geom.MultiPolygon(ringarry),
          });
        } else {
          Features = new ol.Feature({
            geometry: new ol.geom.Polygon(ringsGeometries),
          });
        }
        returnResult.Features = Features;
        returnResult.ringsGeometries = result.geometries[0];
      }
    },
    error: function (info) { },
  });
  return returnResult;
};

//通用arcgis查询方法
var arcgisQuery = (arg, url) => {
  url = url + '/query';
  var result = {};
  var postData = {};
  postData.where = !arg.where ? '1=1' : arg.where;
  postData.text = '';
  postData.geometry = !arg.geometry ? '' : arg.geometry;
  postData.geometryType = !arg.geometryType ? 'esriGeometryPolygon' : arg.geometryType;
  var proj = appConfigBase.map.proj.split(':');
  postData.inSR = proj[1];
  postData.spatialRel = 'esriSpatialRelIntersects';
  postData.relationParam = '';
  postData.outFields = '*';
  postData.returnGeometry = true;
  postData.maxAllowableOffset = '';
  postData.geometryPrecision = '';
  postData.outSR = proj[1];
  postData.returnIdsOnly = false;
  postData.returnCountOnly = false;
  postData.orderByFields = '';
  postData.returnZ = false;
  postData.returnM = false;
  postData.returnDistinctValues = false;
  postData.resultOffset = !arg.resultOffset ? 0 : arg.resultOffset;
  postData.resultRecordCount = 10;
  postData.supportsAdvancedQueries = true;
  postData.distance = arg.distance;
  postData.units = 'esriSRUnit_Meter ';
  postData.f = 'pjson';
  var features = null;
  $.ajax({
    type: 'post',
    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
    url: url,
    async: false,
    data: postData,
    cache: false,
    success: function (json) {
      var result = JSON.parse(json);
      if (result.features) {
        var esrijsonFormat = new ol.format.EsriJSON(); //ESRI的JSON数据格式解析类
        features = esrijsonFormat.readFeatures(result, {
          featureProjection: appConfigBase.map.proj.split(':')[1],
        });
      }
    },
  });
  result.postData = postData;
  result.features = features;
  return result;
};
//通用arcgis查询方法--不支持分页版本
var arcgisQuery2 = (arg, url) => {
  url = url + '/query';
  var result = {};
  var postData = {};
  postData.where = !arg.where ? '1=1' : arg.where;
  postData.text = '';
  postData.geometry = !arg.geometry ? '' : arg.geometry;
  postData.geometryType = !arg.geometryType ? 'esriGeometryPolygon' : arg.geometryType;
  var proj = appConfigBase.map.proj.split(':');
  postData.inSR = proj[1];
  postData.spatialRel = 'esriSpatialRelIntersects';
  postData.relationParam = '';
  postData.outFields = '*';
  postData.returnGeometry = true;
  postData.maxAllowableOffset = '';
  postData.geometryPrecision = '';
  postData.outSR = proj[1];
  postData.returnIdsOnly = false;
  postData.returnCountOnly = false;
  postData.orderByFields = '';
  postData.returnZ = false;
  postData.returnM = false;
  postData.returnDistinctValues = false;
  postData.supportsAdvancedQueries = true;
  postData.distance = arg.distance;
  postData.units = 'esriSRUnit_Meter ';
  postData.f = 'pjson';
  var features = null;
  $.ajax({
    type: 'post',
    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
    url: url,
    async: false,
    data: postData,
    cache: false,
    success: function (json) {
      var result = JSON.parse(json);
      if (result.features) {
        // var features = new ol.format.GeoJSON().readFeatures(result);
        var esrijsonFormat = new ol.format.EsriJSON(); //ESRI的JSON数据格式解析类
        features = esrijsonFormat.readFeatures(result, {
          featureProjection: appConfigBase.map.proj.split(':')[1],
        });
      }
    },
  });
  result.postData = postData;
  result.features = features;
  return result;
};
// 4326查询服务
var arcgisQuery3 = (arg, url) => {
  url = url + '/query';
  var result = {};
  var postData = {};
  postData.where = !arg.where ? '1=1' : arg.where;
  postData.text = '';
  postData.geometry = !arg.geometry ? '' : arg.geometry;
  postData.geometryType = !arg.geometryType ? 'esriGeometryPolygon' : arg.geometryType;
  var proj = appConfigBase.map.proj.split(':');
  postData.inSR = '4326';
  postData.spatialRel = 'esriSpatialRelIntersects';
  postData.relationParam = '';
  postData.outFields = '*';
  postData.returnGeometry = true;
  postData.maxAllowableOffset = '';
  postData.geometryPrecision = '';
  postData.outSR = proj[1];
  postData.returnIdsOnly = false;
  postData.returnCountOnly = false;
  postData.orderByFields = '';
  postData.returnZ = false;
  postData.returnM = false;
  postData.returnDistinctValues = false;
  postData.supportsAdvancedQueries = true;
  postData.distance = arg.distance;
  postData.units = 'esriSRUnit_Meter ';
  postData.f = 'pjson';
  var features = null;
  $.ajax({
    type: 'post',
    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
    url: url,
    async: false,
    data: postData,
    cache: false,
    success: function (json) {
      var result = JSON.parse(json);
      if (result.features) {
        var esrijsonFormat = new ol.format.EsriJSON(); //ESRI的JSON数据格式解析类
        features = esrijsonFormat.readFeatures(result, {
          featureProjection: appConfigBase.map.proj.split(':')[1],
        });
      }
    },
  });
  result.postData = postData;
  result.features = features;
  return result;
};
//通用树结构
var treeData;
// 上传dwg通用绘制图层
var drawshpanddwg = (data, id, olmap) => {
  /**
   *
   * // 绘制图层
   * data-geojson格式
   * */
  if (!id) {
    id = '_shpdwg';
  } else {
    id = id + '_shpdwg';
  }
  //  先移除图层
  var arr = [];
  var layers = olmap.getLayers();
  layers.forEach(function (sublayer, i) {
    if (sublayer.getProperties().id == id) arr.push(sublayer);
  });
  for (var i = 0; i < arr.length; i++) {
    olmap.removeLayer(arr[i]);
  }
  var layerStyle = new ol.style.Style({
    fill: new ol.style.Fill({
      color: 'rgba(255, 255, 255, 0.5)', //填充颜色
    }),
    stroke: new ol.style.Stroke({
      color: '#008000', //边框颜色
      width: 2, // 边框宽度
    }),
    image: new ol.style.Circle({
      radius: 1,
      fill: new ol.style.Fill({
        color: '#008000',
      }),
    }),
    text: new ol.style.Text({
      textBaseline: 'middle',
      rotateWithView: true,
      font: '10px Microsoft YaHei',
    }),
  });
  var shpdwgDrawLayer = new ol.layer.Vector({
    id: id,
    zIndex: 1002,
    source: new ol.source.Vector(),
    style: function (feature) {
      let text = '';
      if (feature.values_ && feature.values_.TxtMemo) {
        text = feature.values_.TxtMemo;

        if (text != '') {
          layerStyle.getText().setText(text);
        }
      }

      return layerStyle;
    },
  });
  shpdwgDrawLayer.set('zIndex', 30003);
  olmap.addLayer(shpdwgDrawLayer);
  //清除所有数据源
  var markersour = shpdwgDrawLayer.getSource();
  markersour.clear();
  var geojson_format = new ol.format.GeoJSON();
  //空间参考转换
  var features = geojson_format.readFeatures(data);
  markersour.addFeatures(features);
};

var deletdwglayer = olmap => {
  var layers = olmap.getLayers();
  var arrs = [];
  //如果图层存在，则移除
  layers.forEach(function (sublayer, i) {
    if (
      sublayer != undefined &&
      (sublayer.getProperties().id === '__querysqew' ||
        sublayer.getProperties().id.indexOf('_shpdwg') >= 0)
    ) {
      sublayer.getSource().clear();
      arrs.push(sublayer);
    }
  });
  for (var i = 0; i < arrs.length; i++) {
    olmap.removeLayer(arrs[i]);
  }
};

/**
 * <AUTHOR>
 * @description geoServer叠加图层的方法
 * @param {Object}  olmap 地图ol对象
 * @param {Number}  zIndex 地图层级
 * @param {Object}  itemObj 数据对象
 * @param {String}  itemObj.id 服务id
 * @param {String}  itemObj.url 服务url地址    '/geoserver/ProjectionData/wms'
 * @param {String}  itemObj.layername 服务名
 */

var createGeoServerWMS = (olmap, itemObj, zIndex, sources) => {

  var layer = null;
  if (
    itemObj.hasOwnProperty('plottedManner') &&
    itemObj.hasOwnProperty('plottedManner') == 'blockPlot'
  ) {
    layer = new olmap.layer.Tile({
      id: itemObj.id,
      zIndex: zIndex,
      source: new olmap.source.TileWMS({
        url: '/egisserver/wms?token=' + Cookies.get('accessToken') + '&source=' + sources,
        crossOrigin: 'Anonymous',
        params: {
          LAYERS: itemObj.layername,
          FORMAT: 'image/png',
          STYLE: itemObj['STYLE'],
          VERSION: '1.1.0',
          token: getStore('accessToken'),
        },
        serverType: 'geoserver',
      }),
    });
  } else {

    layer = new ol.layer.Image({
      id: itemObj.id,
      zIndex: zIndex,
      source: new ol.source.ImageWMS({
        ratio: 1,
        url: '/egisserver/wms?token=' + Cookies.get('accessToken') + '&source=' + sources,
        crossOrigin: 'Anonymous',
        params: {
          LAYERS: itemObj.layername,
          FORMAT: 'image/png',
          VERSION: '1.1.0',
          STYLE: itemObj['STYLE'],
          TOKRN: getStore('accessToken'),
        },
        serverType: 'geoserver',
      }),
    });
  }
  return layer;
};

/*
 *@description: 矢量切片的加载
 *@author: qqjuanqq
 *@date: 2023-04-11 14:32:10
 *@version: V1.0.0
 */
var createGeoServerWMTSXYZ = (olmap, itemObj, mapObj, projection, zIndex, flagColor) => {
  var baseUrl = '';
  if (itemObj.url.indexOf('geoserver') != -1) {
    var arr = itemObj.url.split('geoserver');
    baseUrl = arr[0] + 'geoserver/gwc/service/wmts';
  }
  var layer = null;
  var resolutions = mapObj.resolutions.reso;
  var format = 'application/vnd.mapbox-vector-tile';
  var gridNames = [
    `${mapObj.proj}:0`,
    `${mapObj.proj}:1`,
    `${mapObj.proj}:2`,
    `${mapObj.proj}:3`,
    `${mapObj.proj}:4`,
    `${mapObj.proj}:5`,
    `${mapObj.proj}:6`,
    `${mapObj.proj}:7`,
    `${mapObj.proj}:8`,
    `${mapObj.proj}:9`,
    `${mapObj.proj}:10`,
    `${mapObj.proj}:11`,
    `${mapObj.proj}:12`,
    `${mapObj.proj}:13`,
    `${mapObj.proj}:14`,
    `${mapObj.proj}:15`,
    `${mapObj.proj}:16`,
    `${mapObj.proj}:17`,
    `${mapObj.proj}:18`,
    `${mapObj.proj}:19`,
    `${mapObj.proj}:20`,
    `${mapObj.proj}:21`,
  ];
  var params = {
    REQUEST: 'GetTile',
    SERVICE: 'WMTS',
    VERSION: '1.0.0',
    LAYER: itemObj.layername,
    STYLE: '',
    TILEMATRIX: mapObj.proj + ':{z}',
    TILEMATRIXSET: mapObj.proj,
    FORMAT: format,
    TILECOL: '{x}',
    TILEROW: '{y}',
  };
  var url = baseUrl + '?';
  for (var param in params) {
    url = url + param + '=' + params[param] + '&';
  }
  url = url.slice(0, -1);
  if (flagColor && flagColor.hasOwnProperty('fill')) {
    //设置颜色的
    layer = new olmap.layer.VectorTile({
      zIndex: zIndex,
      id: itemObj.id,
      source: new olmap.source.VectorTile({
        url: url,
        format: new olmap.format.MVT({}),
        projection: projection,
        tileGrid: new olmap.tilegrid.WMTS({
          tileSize: [256, 256],
          origin: [-180.0, 90.0],
          resolutions: resolutions,
          matrixIds: gridNames,
        }),
        wrapX: true,
      }),
      style: new olmap.style.Style({
        fill: new olmap.style.Fill({
          color: flagColor.fill,
        }),
        stroke: new olmap.style.Stroke({
          color: flagColor.stroke,
          width: flagColor.width,
        }),
      }),
    });
  } else {
    //初始加载
    layer = new olmap.layer.VectorTile({
      zIndex: zIndex,
      id: itemObj.id,
      source: new olmap.source.VectorTile({
        url: url,
        format: new olmap.format.MVT({}),
        projection: projection,
        tileGrid: new olmap.tilegrid.WMTS({
          tileSize: [256, 256],
          origin: [-180.0, 90.0],
          resolutions: resolutions,
          matrixIds: gridNames,
        }),
        wrapX: true,
      }),
    });
  }
  // layer = new olmap.layer.VectorTile({
  //   source: new olmap.source.VectorTile({
  //       projection: projection4326,
  //       format: new olmap.format.MVT(),//pbf格式
  //       tileGrid: olmap.tilegrid.createXYZ({
  //           extent: olmap.proj.get('EPSG:4490').getExtent(),
  //           maxZoom: 32
  //       }),
  //       tilePixelRatio: 1,
  //       // 矢量切片服务地址
  //       tileUrlFunction: function(tileCoord){
  //           return 'http://127.0.0.1:8080/geoserver/gwc/service/tms/1.0.0/'
  //               +lyr+'@EPSG%3A4326@pbf/'+(tileCoord[0]-1)
  //               + '/'+tileCoord[1] + '/' + (Math.pow(2,tileCoord[0]-1)+tileCoord[2]) + '.pbf';//pbf矢量切片
  //       }
  //   })
  // });
  return layer;
};

/**
 * <AUTHOR>
 * @description geoServer请求   表格数据123456
 * @param {Object} url  请求url地址
 * @param {Object} param  geoServer请求参数
 * @param {String} param.TypeName  服务名称
 * @param {String} param.SrsName  坐标系  EPSG:4490  、EPSG:4490 、 EPSG:4528
 * @param {String} param.Filter  查询条件
 * @param {String} param.OrderBy  排序字段
 * @param {String} param.PropertyName  要查询的字段 如name id
 * @param {String} param.CqlFilter  过滤条件
 * @param {String} param.startIndex  0   数据起始索引
 * @param {String} param.maxFeatures  10  限制返回数量
 */
var geoServerQuery = (url, param, olmap) => {
  let newurl = '/egisserver/wms';
  var result = {
    resultData: {},
    featureArr: [],
  };
  var featureArr = [];
  newurl += '?token=' + Cookies.get('accessToken') +
    '&service=wfs&request=getfeature&typename=' +
    param.TypeName +
    '&srsname=' +
    param.SrsName +
    '&outputformat=application/json&version=1.0.0';
  if (param.Filter) newurl += '&filter=' + param.Filter; //将查询条件进行转码
  if (param.OrderBy) newurl += '&sortBy=' + param.OrderBy;
  if (param.PropertyName) newurl += '&propertyName=' + param.PropertyName; //返回图形加上the_geom
  if (param.MaxFeatures) newurl += '&maxFeatures=' + param.MaxFeatures; //返回图形加上the_geom
  if (param.startIndex === 0 || param.startIndex) newurl += '&startIndex=' + param.startIndex; //索引
  if (param.maxFeatures) newurl += '&maxFeatures=' + param.maxFeatures; //条数
  if (param.CqlFilter) newurl += '&cql_filter=' + encodeURI(param.CqlFilter.trim()); //将查询条件进行转码
  return new Promise((resolve, reject) => {
    fetch(newurl, {
      method: 'GET',
      headers: {},
    })
      .then(response => {
        response.json().then(res => {
          result.resultData = res;
          if (res.features.length > 0) {
            res.features.forEach((item, index) => {
              var geojson_format = new olmap.format.GeoJSON();
              let featureObj = geojson_format.readFeature(item.geometry);
              featureObj.values_ = Object.assign(featureObj.values_, item.properties);
              featureArr.push(featureObj);
            });
          }
          result.featureArr = featureArr;
          resolve(result);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};
var geoServerQuery2 = (param) => {
  let newurl = '/egisserver/wms';
  var result = {
    resultData: {},
    featureArr: [],
  };
  var featureArr = [];
  newurl += '?token=' + Cookies.get('accessToken') +
    '&service=wfs&request=getfeature&typename=' +
    param.TypeName +
    '&srsname=' +
    param.SrsName +
    '&outputformat=application/json&version=1.0.0';
  if (param.Filter) newurl += '&filter=' + param.Filter; //将查询条件进行转码
  if (param.OrderBy) newurl += '&sortBy=' + param.OrderBy;
  if (param.PropertyName) newurl += '&propertyName=' + param.PropertyName; //返回图形加上the_geom
  if (param.MaxFeatures) newurl += '&maxFeatures=' + param.MaxFeatures; //返回图形加上the_geom
  if (param.startIndex === 0 || param.startIndex) newurl += '&startIndex=' + param.startIndex; //索引
  if (param.maxFeatures) newurl += '&maxFeatures=' + param.maxFeatures; //条数
  if (param.CqlFilter) newurl += '&cql_filter=' + encodeURI(param.CqlFilter.trim()); //将查询条件进行转码
  return new Promise((resolve, reject) => {
    fetch(newurl, {
      method: 'GET',
      headers: {},
    })
      .then(response => {
        response.json().then(res => {
          result.resultData = res;
          // if (res.features.length > 0) {
          // res.features.forEach((item, index) => {
          //   var geojson_format = new olmap.format.GeoJSON();
          //   let featureObj = geojson_format.readFeature(item.geometry);
          //   featureObj.values_ = Object.assign(featureObj.values_, item.properties);
          //   featureArr.push(featureObj);
          // });
          // }
          result.featureArr = res.features;
          resolve(result);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};
// geoServer post请求
var geoServerQueryPost = (url, param, olmap) => {
  var result = {
    resultData: {},
    featureArr: [],
    flagSearch: false,
  };
  let newurl = '/egisserver/wms?token=' + Cookies.get('accessToken');
  var featureArr = [];
  var params =
    'service=wfs&request=getfeature&typename=' +
    param.TypeName +
    '&srsname=' +
    param.SrsName +
    '&outputformat=application/json&version=1.0.0';

  if (param.Filter) params += '&filter=' + param.Filter; //将查询条件进行转码
  if (param.OrderBy) params += '&sortBy=' + param.OrderBy;
  if (param.PropertyName) params += '&propertyName=' + param.PropertyName; //返回图形加上the_geom
  if (param.MaxFeatures) params += '&maxFeatures=' + param.MaxFeatures; //返回图形加上the_geom
  if (param.startIndex === 0 || param.startIndex) params += '&startIndex=' + param.startIndex; //索引
  if (param.maxFeatures) params += '&maxFeatures=' + param.maxFeatures; //条数
  if (param.CqlFilter) params += '&cql_filter=' + encodeURI(param.CqlFilter.trim()); //将查询条件进行转码
  return new Promise((resolve, reject) => {
    fetch(newurl, {
      method: 'POST',
      body: params,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
      .then(response => {
        response
          .json()
          .then(res => {
            result.resultData = res;
            result.flagSearch = true;
            // if (res.features.length > 0) {
            // res.features.forEach((item, index) => {
            // var geojson_format = new olmap.format.GeoJSON();
            // let featureObj = geojson_format.readFeature(item.geometry);
            // featureObj.values_ = Object.assign(featureObj.values_, item.properties);
            // featureArr.push(featureObj);
            // });
            // }
            // result.featureArr = featureArr;
            resolve(result);
          })
          .catch(errCode => {
            result.flagSearch = false;
            resolve(result);
          });
      })
      .catch(res => {
        reject(res);
      });
  });
};
/**
 * 对象转查询字符串
 * @param {Object} tar 要转换的对象
 */
function objectToQueryString_ful(data) {
  var ret = '';
  for (var it in data) {
    var initRet = void 0;
    if (typeof data[it] !== 'string') {
      initRet = '';
      for (var j in data[it]) {
        initRet += encodeURIComponent(j) + '=' + encodeURIComponent(data[it][j]) + '&';
      }
      ret += encodeURIComponent(it) + '=' + encodeURIComponent(initRet) + '&';
    } else {
      ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
    }
  }
  return ret;
}
/**
 * <AUTHOR>
 * @description geoServer请求
 * @param {Object} url  请求url地址
 * @param {Object} param  geoServer请求参数
 * @param {String} param.TypeName  服务名称
 * @param {String} param.SrsName  坐标系  EPSG:4490  、EPSG:4490 、 EPSG:4528
 * @param {String} param.Filter  查询条件
 * @param {String} param.OrderBy  排序字段
 * @param {String} param.PropertyName  要查询的字段 如name id
 * @param {String} param.CqlFilter  过滤条件
 * @param {String} param.startIndex  0   数据起始索引
 * @param {String} param.maxFeatures  10  限制返回数量
 */
var geoServerQueryTemp = (url, param, olmap) => {
  var result = {
    resultData: {},
  };
  url +=
    '?service=wfs&request=getfeature&typename=' +
    param.TypeName +
    '&srsname=' +
    param.SrsName +
    '&outputformat=application/json&version=1.0.0';
  if (param.Filter) url += '&filter=' + param.Filter; //将查询条件进行转码
  if (param.OrderBy) url += '&sortBy=' + param.OrderBy;
  if (param.PropertyName) url += '&propertyName=' + param.PropertyName; //返回图形加上the_geom
  if (param.MaxFeatures) url += '&maxFeatures=' + param.MaxFeatures; //返回图形加上the_geom
  if (param.startIndex === 0 || param.startIndex) url += '&startIndex=' + param.startIndex; //索引
  if (param.maxFeatures) url += '&maxFeatures=' + param.maxFeatures; //条数
  if (param.CqlFilter) url += '&cql_filter=' + encodeURI(param.CqlFilter.trim()); //将查询条件进行转码
  // if (param.Point && param.CqlFilter) url += "&bbox=" + param.Point.toString() + "," + param.Point.toString();
  // if (param.Extent && param.CqlFilter) url += "&bbox=" + param.Extent.toString();
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET',
      headers: {},
    })
      .then(response => {
        response.json().then(res => {
          result.resultData = res;
          resolve(result);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description geoServer请求获取图例的信息
 * @param {String} url  请求url地址
 * @param {String} layerName  图层名称
 */

var getLegend = (url, layerName) => {
  let newurl = '/egisserver/wms?token=' + Cookies.get('accessToken') + `&request = GetLegendGraphic & format=image/png&layer=${layerName}`
  var url = `${url}?request=GetLegendGraphic&format=image/png&layer=${layerName}`;
  return new Promise((resolve, reject) => {
    fetch(newurl, {
      method: 'GET',
      headers: {},
    })
      .then(response => {
        response.text().then(res => {
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description geoServer请求获取\缩放地图的数据信息
 * @param {String} url  请求url地址
 */

var getlayerXML = (url, source) => {
  var url = `${`/egisserver/wms`}?token=${Cookies.get('accessToken')}&source=${source}&service=wms&version=1.0.0&request=GetCapabilities`;
  // var url = '/geoserver/zt/wms?service=wms&version=1.0.0&request=GetCapabilities';
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET',
      dataType: 'xml',
      headers: {},
    })
      .then(response => {
        response.text().then(res => {
          console.log(res, 8888888888888);
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};


var getlayerWMTSXML = (url, source) => {
  var newurl =
    url.indexOf("geoserver") != -1
      ? `${url.split("geoserver")[0]}geoserver/gwc/service/wmts`
      : url;
  // var url1 = `${newurl}?service=WMTS&version=1.0.0&request=GetCapabilities`;
  var url1 = `/egisserver/gwc/service/wmts?token=${Cookies.get('accessToken')}&source=${source}&service=WMTS&version=1.0.0&request=GetCapabilities`;
  console.log(url1);
  return new Promise((resolve, reject) => {
    fetch(url1, {
      method: "GET",
      dataType: "xml",
      headers: {},
    })
      .then((response) => {
        response.text().then((res) => {
          resolve(res);
        });
      })
      .catch((res) => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description ImageArcGISRest arcgis服务的方式的加载方法
 * @param {Object} item 图层对象，包涵url、id 、layerId
 * @param {Number} zindex 层级
 * @param {Object} olmap ol对象
 */
var getImageArcGISRest = (item, zindex, olmap) => {
  let url = item.url;
  var layer = new olmap.layer.Image({
    id: item.id,
    visible: true,
    zIndex: zindex,
    source: new olmap.source.ImageArcGISRest({
      url: url,
      params: {
        layers: item.layerId ? 'show:' + item.layerId : '',
        TRANSPARENT: false,
      },
      crossOrigin: 'anonymous', //防止地图打印出现问题
    }),
  });
  return layer;
};

/**
 * <AUTHOR>
 * @description getArcgisXYZTile XYZ方式加载服务的 (矢量)
 * @param {Object} item 图层对象，包涵url、id
 * @param {String} origin  3.48768E7,1.00021E7
 * @param {Array} resolutions 分辨率的集合
 * @param {Number} zindex 层级
 * @param {Object} olmap ol对象
 * @param {Array} extent 地图范围
 * @param {Array} projection 投影坐标系
 */
var getArcgisXYZTile = (item, origin, resolutions, zindex, olmap, projection, extent) => {
  let url = item.url + '/tile/{z}/{y}/{x}';
  var ori = origin.split(',');
  var origin = [parseFloat(ori[0]), parseFloat(ori[1])];
  var tileGrid = new olmap.tilegrid.TileGrid({
    tileSize: 256,
    origin: origin,
    extent: extent,
    resolutions: resolutions,
  });
  var tileArcGISXYZ = new olmap.source.XYZ({
    tileGrid: tileGrid,
    projection: projection,
    url: url,
    crossOrigin: 'anonymous', //防止地图打印出现问题
  });

  var layer = new olmap.layer.Tile({
    id: item.id,
    zIndex: zindex,
    source: tileArcGISXYZ,
  });
  return layer;
};

/**
 * <AUTHOR>
 * @description TileArcGISRest瓦片服务方式
 * @param {Object} item 图层对象，包涵url、id
 * @param {String} originStr  3.48768E7,1.00021E7
 * @param {Array} resolutions 分辨率的集合
 * @param {Object} olmap 地图对象
 * @param {Array} extent 地图范围
 * @param {Array} projection 投影坐标系
 */
var getTileArcGISRestLayer = (item, originStr, resolutions, extent, projection, olmap) => {
  let url = item.url;
  var ori = originStr.split(',');
  var origin = [parseFloat(ori[0]), parseFloat(ori[1])];
  var tileGrid = new olmap.tilegrid.TileGrid({
    tileSize: 256,
    origin: origin,
    extent: extent,
    resolutions: resolutions,
  });
  var layer = new olmap.layer.Tile({
    id: item.id,
    visible: true,
    source: new olmap.source.TileArcGISRest({
      tileGrid: tileGrid,
      projection: projection,
      url: url,
      params: {
        name: item.id,
      },
      crossOrigin: 'anonymous', //防止地图打印出现问题
    }),
  });
  return layer;
};

/**
 * <AUTHOR>
 * @description getArcgisXYZTileTD XYZ构造天地图底图
 * @param {Object} item 图层对象，包涵url、id
 * @param {Object} olmap 地图对象
 * @param {Number} zindex 层级
 */
var getArcgisXYZTileTD = (item, zindex, olmap) => {
  var layer = new olmap.layer.Tile({
    id: item.id,
    zIndex: zindex,
    source: new olmap.source.XYZ({
      url: item.url,
      crossOrigin: 'anonymous', //防止地图打印出现问题
    }),
  });
  return layer;
};
/**
 * <AUTHOR>
 * @description  geoserver wmts  切片的加载
 * @param {Object} olmap 地图对象
 * @param {String} FillColor 填充颜色
 * @param {String} strokeColor 边框颜色
 * @param {Number} width 边框宽度
 * @param {Number} zIndex 层级
 */
var getGeoServerWMTS = (olmap, projection, extent, item, origin, appConfig, zindex, sources) => {
  console.log(olmap, 222222222222222)

  var layer = null;
  var TILEMATRIXArr = [];
  for (let i = 0; i <= appConfig.map.maxZoom + 2; i++) {
    TILEMATRIXArr.push(`${appConfig.map.proj}:${i}`);
  }
  var params = {
    VERSION: '1.0.0',
    LAYER: item.layername,
    STYLE: '',
    TILEMATRIX: TILEMATRIXArr,
    TILEMATRIXSET: appConfig.map.proj,
    SERVICE: 'WMTS',
    FORMAT: 'image/png',
  };
  // var url =
  //   item.url.indexOf('geoserver') != -1
  //     ? `${item.url.split('geoserver')[0]}geoserver/gwc/service/wmts`
  //     : item.url;
  var url = `/egisserver/gwc/service/wmts?token=${Cookies.get('accessToken')}` + '&source=' + sources;
  var source = new olmap.source.WMTS({
    url: url,
    layer: params['LAYER'],
    matrixSet: params['TILEMATRIXSET'],
    format: params['FORMAT'],
    projection: projection,
    tileGrid: new olmap.tilegrid.WMTS({
      tileSize: [256, 256],
      origin: [-180.0, 90.0],
      resolutions: appConfig.map.resolutions.reso,
      matrixIds: params['TILEMATRIX'],
    }),
    style: params['STYLE'],
    wrapX: true,
  });
  layer = new olmap.layer.Tile({
    id: item.id,
    zIndex: zindex,
    source: source,
  });
  return layer;
};

var getGeoServerWMTSNew = (ol, service) => {
  console.log(ol, 123234445)
  // 首先设置好WMTS瓦片地图的瓦片坐标系
  let projection = ol.proj.get('EPSG:4326');          // 获取web墨卡托投影坐标系
  let projectionExtent = projection.getExtent();      // web墨卡托投影坐标系的四至范围
  let width = ol.extent.getWidth(projectionExtent);   // web墨卡托投影坐标系的水平宽度，单位米
  let resolutions = [];                               // 瓦片地图分辨率
  let matrixIds = [];                                  //矩阵ID
  for (let z = 0; z < 17; z++) {
    resolutions[z] = width / (256 * Math.pow(2, z + 1));
    matrixIds[z] = "EPSG:4326:" + z;              // 注意这里的matrixId的格式为EPSG:900913:z
  }
  let wmtsTileGrid = new ol.tilegrid.WMTS({
    tileSize: [256, 256],
    origin: [-180.0, 90.0],
    resolutions: resolutions,
    matrixIds: matrixIds,

  });

  // WMTS数据源与地图
  let wmtsSource = new ol.source.WMTS({
    url: service.url,
    layer: service.layername,        // 对应的图层名称
    matrixSet: 'EPSG:4326',           // 投影坐标系参数矩阵集
    format: 'image/png',           // 图片格式
    projection: projection,             // 投影坐标系
    // 投影坐标系
    tileGrid: wmtsTileGrid
  });

  let wmtsLayer = new ol.layer.Tile({
    source: wmtsSource
  });
  return wmtsLayer

  // let map = new ol.Map({
  //   target: 'map',
  //   layers: [

  //     new ol.layer.Tile({
  //       source: new ol.source.XYZ({
  //         url: 'http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=32297570976be65f3b20adb9a71db4c9',
  //         wrapX: false,
  //         crossOrigin: "Anonymous"
  //       }),
  //     }),
  //     wmtsLayer
  //   ],
  //   view: new ol.View({
  //     center: [118.04724, 31.48141],
  //     zoom: 10,
  //     projection: projection,
  //   })
  // });
}

/**
 * <AUTHOR>
 * @description getattributeValue g根据属性值获取属性数据
 * @param {Object} param  参数对象
 * @param {String} param.strCode  请求参数
 */
var getattributeValue = (typeName, strName) => {
  var url = '/geoserver/wps';
  var param = {
    strCode: `<?xml version="1.0" encoding="UTF-8"?><wps:Execute version="1.0.0" service="WPS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.opengis.net/wps/1.0.0" xmlns:wfs="http://www.opengis.net/wfs" xmlns:wps="http://www.opengis.net/wps/1.0.0" xmlns:ows="http://www.opengis.net/ows/1.1" xmlns:gml="http://www.opengis.net/gml" xmlns:ogc="http://www.opengis.net/ogc" xmlns:wcs="http://www.opengis.net/wcs/1.1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xsi:schemaLocation="http://www.opengis.net/wps/1.0.0 http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
        <ows:Identifier>gs:Unique</ows:Identifier>
        <wps:DataInputs>
          <wps:Input>
            <ows:Identifier>features</ows:Identifier>
            <wps:Reference mimeType="text/xml" xlink:href="http://geoserver/wfs" method="POST">
              <wps:Body>
                <wfs:GetFeature service="WFS" version="1.0.0" outputFormat="GML2" >
                  <wfs:Query typeName="${typeName}"/>
                </wfs:GetFeature>
              </wps:Body>
            </wps:Reference>
          </wps:Input>
          <wps:Input>
            <ows:Identifier>attribute</ows:Identifier>
            <wps:Data>
              <wps:LiteralData>${strName}</wps:LiteralData>
            </wps:Data>
          </wps:Input>
        </wps:DataInputs>
        <wps:ResponseForm>
          <wps:RawDataOutput mimeType="text/xml; subtype=wfs-collection/1.0">
            <ows:Identifier>result</ows:Identifier>
          </wps:RawDataOutput>
        </wps:ResponseForm>
      </wps:Execute>`,
  };
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'POST',
      body: param.strCode,
      contentType: 'text/xml',
      headers: {},
    })
      .then(response => {
        response.text().then(res => {
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};
// 字符串xml数据转换成xml数据；
var xmlStr2XmlObj = xmlStr => {
  var xmlObj = {};
  if (document.all) {
    var xmlDom = new ActiveXObject('Microsoft.XMLDOM');
    xmlDom.loadXML(xmlStr);
    xmlObj = xmlDom;
  } else {
    xmlObj = new DOMParser().parseFromString(xmlStr, 'text/xml');
  }
  return xmlObj;
};
/**
 * <AUTHOR>
 * @description geoServer根据条件对图层进行过滤显示
 * @param {Object}  layer  图层对象
 * @param {Object}  params  对象参数
 * @param {String}  params.CQL_FILTER  过滤条件   "PAC like '3701%' and JT='1'"
 */
var filterGeoServerLayer = (layer, paramsStr) => {
  var params = layer.getSource().getParams();
  params.CQL_FILTER = paramsStr;
  layer.getSource().updateParams(params);
};

/**
 * <AUTHOR>
 * @description feature数据进行高亮显示
 * @param {Object} zxQueryLayer map查询的方法
 * @param {Object} featuresArr  需要高亮的数据
 */
var addFeatures = (zxQueryLayer, featuresArr) => {
  var source = zxQueryLayer.getSource();
  source.clear();
  source.addFeatures(featuresArr);
};

/**
 * <AUTHOR>
 * @description geojson数据转换成feature
 * @param {Object} zxQueryLayer map查询的方法
 * @param {Object} featuresArr  需要高亮的数据
 */
var jsonChangeFeature = (olmap, jsonData) => {
  var feature = null;
  var geojson_format = new olmap.format.GeoJSON();
  feature = geojson_format.readFeatures(jsonData);
  return feature;
};

/**
 * <AUTHOR>
 * @description 清除叠加的feature属性
 * @param {Object} zxQueryLayer map查询的方法
 */
var clearFeatures = zxQueryLayer => {
  var source = zxQueryLayer.getSource();
  source.clear();
};

/**
 * <AUTHOR>
 * @description geoServer 根据点线面json来缓冲半径来获取新的json数据  buffer
 * @param {String} number 缓冲半径
 * @param {Object} jsonData json数据
 */
var getGeoServerBuffer = (jsonData, number) => {
  var url = '/geoserver/ows?strict=true';
  var param = {
    strCode: `<?xml version="1.0" encoding="UTF-8"?><wps:Execute version="1.0.0" service="WPS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.opengis.net/wps/1.0.0" xmlns:wfs="http://www.opengis.net/wfs" xmlns:wps="http://www.opengis.net/wps/1.0.0" xmlns:ows="http://www.opengis.net/ows/1.1" xmlns:gml="http://www.opengis.net/gml" xmlns:ogc="http://www.opengis.net/ogc" xmlns:wcs="http://www.opengis.net/wcs/1.1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xsi:schemaLocation="http://www.opengis.net/wps/1.0.0 http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
        <ows:Identifier>geo:buffer</ows:Identifier>
        <wps:DataInputs>
          <wps:Input>
            <ows:Identifier>geom</ows:Identifier>
            <wps:Data>
              <wps:ComplexData mimeType="application/json"><![CDATA[${jsonData}]]></wps:ComplexData>
            </wps:Data>
          </wps:Input>
          <wps:Input>
            <ows:Identifier>distance</ows:Identifier>
            <wps:Data>
              <wps:LiteralData>${number}</wps:LiteralData>
            </wps:Data>
          </wps:Input>
        </wps:DataInputs>
        <wps:ResponseForm>
          <wps:RawDataOutput mimeType="application/json">
            <ows:Identifier>result</ows:Identifier>
          </wps:RawDataOutput>
        </wps:ResponseForm>
      </wps:Execute>`,
  };
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'POST',
      body: param.strCode,
      contentType: 'text/xml',
      headers: {},
    })
      .then(response => {
        response.text().then(res => {
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description geoServer 获取属性字段类型
 */
var getPropertyType = (url, params) => {
  var url = `${url}?service=${params.service}&version=${params.version}&request=${params.request}&typeName=${params.typeName}&outputFormat=${params.outputFormat}`;
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET',
      dataType: 'xml',
      headers: {},
    })
      .then(response => {
        response.json().then(res => {
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description getStatisticsAll wps统计的接口
 * @param {Object} param  参数对象
 * @param {String} typeName  图层名称
 * @param {String} jsonData  选择的区域数据
 */
var getStatisticsAll = (
  typeName,
  jsonData,
  aggregationAttributeStr,
  functionStr,
  groupByAttributesStr,
  jsonStr
) => {
  var url = '/geoserver/wps';
  var param = {
    strCode: `<?xml version="1.0" encoding="UTF-8"?><wps:Execute version="1.0.0" service="WPS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.opengis.net/wps/1.0.0" xmlns:wfs="http://www.opengis.net/wfs" xmlns:wps="http://www.opengis.net/wps/1.0.0" xmlns:ows="http://www.opengis.net/ows/1.1" xmlns:gml="http://www.opengis.net/gml" xmlns:ogc="http://www.opengis.net/ogc" xmlns:wcs="http://www.opengis.net/wcs/1.1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xsi:schemaLocation="http://www.opengis.net/wps/1.0.0 http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
        <ows:Identifier>gs:Aggregate</ows:Identifier>
        <wps:DataInputs>
          <wps:Input>
            <ows:Identifier>features</ows:Identifier>
            <wps:Reference mimeType="text/xml" xlink:href="http://geoserver/wfs" method="POST">
              <wps:Body>
                <wfs:GetFeature service="WFS" version="1.0.0" outputFormat="GML2">
                  <wfs:Query typeName="${typeName}"/>
                </wfs:GetFeature>
              </wps:Body>
            </wps:Reference>
          </wps:Input>
          <wps:Input>
            <ows:Identifier>aggregationAttribute</ows:Identifier>
            <wps:Data>
                <wps:LiteralData>${aggregationAttributeStr}</wps:LiteralData>
            </wps:Data>
          </wps:Input>
          ${functionStr}
          <wps:Input>
            <ows:Identifier>singlePass</ows:Identifier>
            <wps:Data>
              <wps:LiteralData>true</wps:LiteralData>
            </wps:Data>
          </wps:Input>
          ${groupByAttributesStr}
        </wps:DataInputs>
        <wps:ResponseForm>
          <wps:RawDataOutput mimeType="text/xml">
            <ows:Identifier>result</ows:Identifier>
          </wps:RawDataOutput>
        </wps:ResponseForm>
      </wps:Execute>`,
  };
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'POST',
      body: param.strCode,
      contentType: 'text/xml',
      headers: {},
    })
      .then(response => {
        response.text().then(res => {
          resolve(res);
        });
      })
      .catch(res => {
        reject(res);
      });
  });
};

/**
 * <AUTHOR>
 * @description 创建自己上传的图层 createPolygonLayer
 * @param {Object} config  图层对象数据
 * @param {Object} olmap  地图ol对象
 * @param {String} idCode  图层id
 * @param {Number} zindex  图层层级
 * @param {Object} layerStyle  样式对象
 */
var createPolygonLayer = (olmap, config, zindex, idCode, layerStyle) => {
  var layer = null;
  layer = new olmap.layer.Vector({
    id: idCode,
    zIndex: zindex,
    source: new olmap.source.Vector(),
    style: function (feature) {
      let text = '';
      if (feature.values_ && feature.values_.TxtMemo) {
        text = feature.values_.TxtMemo;
      }
      layerStyle.getText().setText(text);
      return layerStyle;
    },
  });
  return layer;
};
/**
 * <AUTHOR>
 * @description 创建边界线点线叠加图层 公共方法叠加
 * @param {*} val 当前选择行政区划编码
 * @date 2021-10-26
 */
var createLayerLine = (olmap, data, color, Citycode) => {
  let polygonFeature = new olmap.Feature({
    type: 'polygon',
    geometry: new olmap.geom.Polygon(data.geometry.coordinates[0]),
  });
  polygonFeature.setId(data.properties.name);
  polygonFeature.setStyle(
    new olmap.style.Style({
      stroke: new olmap.style.Stroke({
        width: 2,
        color: color,
      }),
      fill: new olmap.style.Fill({
        color: [255, 255, 255, 0],
      }),
    })
  );
  var polygonLayer = new ol.layer.Vector({
    source: new ol.source.Vector({
      features: [polygonFeature],
    }),
    id: Citycode,
  });
  return polygonLayer;
};


var popupWidth = Math.floor(document.body.offsetWidth); //layui的默认宽度（获取网页可见区域宽）
var popupHeight = Math.floor(screen.availHeight); //layui的默认高度（获取屏幕的可用高度）
//处理弹窗文字模糊抖动问题
var setUnShake = () => {
  popupWidth % 2 ? (popupWidth = popupWidth + 1) : "";
  popupHeight % 2 ? (popupHeight = popupHeight + 1) : "";
};

export default {
  xbootConfig,
  baseXbootShp,
  appConfigBase,
  initJson,
  initDrawC,
  selectTextFile,
  creatBufferC,
  arcgisQuery,
  arcgisQuery2,
  drawshpanddwg,
  getArcgisXYZTileTD,
  deletdwglayer,
  treeData,
  arcgisQuery3,
  createGeoServerWMS,
  geoServerQuery,
  geoServerQuery2,
  geoServerQueryTemp,
  getLegend,
  getlayerXML,
  getlayerWMTSXML,
  getImageArcGISRest,
  getArcgisXYZTile,
  getTileArcGISRestLayer,
  getattributeValue,
  filterGeoServerLayer,
  addFeatures,
  clearFeatures,
  xmlStr2XmlObj,
  getGeoServerBuffer,
  jsonChangeFeature,
  getPropertyType,
  getStatisticsAll,
  createPolygonLayer,
  createLayerLine,
  geoServerQueryPost,
  popupWidth,
  popupHeight,
  getGeoServerWMTS,
  createGeoServerWMTSXYZ,
  getGeoServerWMTSNew,
  popupWidth,
  popupHeight,
  setUnShake,
};




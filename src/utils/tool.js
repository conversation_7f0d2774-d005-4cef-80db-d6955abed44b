/**
 * 将十六进制颜色转换为 rgba 颜色
 * @param {string} hex 十六进制颜色
 * @param {number} opacity 透明度
 * @returns {string} rgba 颜色
 */
export const hexToRgba = (hex, opacity = 1) => {
  const hexRegex = /^#?[0-9A-Fa-f]+$/;
  if (!hexRegex.test(hex)) {
    return hex;
  } else {
    // 将 # 号去掉
    hex = hex.replace("#", "");
    // 将 R、G、B 分别转换为十进制值
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);
    // 返回 RGB 值
    return "rgba(" + r + ", " + g + ", " + b + ", " + opacity + ")";
  }
};
